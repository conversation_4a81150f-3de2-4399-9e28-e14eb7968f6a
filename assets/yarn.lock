# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alpinejs/collapse@^3.14.1":
  version "3.14.1"
  resolved "https://registry.npmjs.org/@alpinejs/collapse/-/collapse-3.14.1.tgz"
  integrity sha512-aI0pq8SjK7c43/nMIVL1Lt8naowPRepqQGNSb9KaG7adEneOwj/vq4ZaeZYjuGbd8sq1LKPwWU+klIZIXXujUA==

"@fortawesome/fontawesome-free@^6.6.0":
  version "6.7.2"
  resolved "https://registry.npmjs.org/@fortawesome/fontawesome-free/-/fontawesome-free-6.7.2.tgz"
  integrity sha512-JUOtgFW6k9u4Y+xeIaEiLr3+cjoUPiAuLXoyKOJSia6Duzb7pq+A76P9ZdPDoAoxHdHzq6gE9/jKBGXlZT8FbA==

"@kurkle/color@^0.3.0":
  version "0.3.4"
  resolved "https://registry.npmjs.org/@kurkle/color/-/color-0.3.4.tgz"
  integrity sha512-M5UknZPHRu3DEDWoipU6sE8PdkZ6Z/S+v4dD+Ke8IaNlpdSQah50lz1KtcFBa2vsdOnwbbnxJwVM4wty6udA5w==

"@vue/reactivity@~3.1.1":
  version "3.1.5"
  resolved "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.1.5.tgz"
  integrity sha512-1tdfLmNjWG6t/CsPldh+foumYFo3cpyCHgBYQ34ylaMsJ+SNHQ1kApMIa8jN+i593zQuaw3AdWH0nJTARzCFhg==
  dependencies:
    "@vue/shared" "3.1.5"

"@vue/shared@3.1.5":
  version "3.1.5"
  resolved "https://registry.npmjs.org/@vue/shared/-/shared-3.1.5.tgz"
  integrity sha512-oJ4F3TnvpXaQwZJNF3ZK+kLPHKarDmJjJ6jyzVNDKH9md1dptjC7lWR//jrGuLdek/U6iltWxqAnYOu8gCiOvA==

ace-builds@^1.43.2:
  version "1.43.2"
  resolved "https://registry.npmjs.org/ace-builds/-/ace-builds-1.43.2.tgz"
  integrity sha512-3wzJUJX0RpMc03jo0V8Q3bSb/cKPnS7Nqqw8fVHsCCHweKMiTIxT3fP46EhjmVy6MCuxwP801ere+RW245phGw==

alpinejs@^3.14.1:
  version "3.14.1"
  resolved "https://registry.npmjs.org/alpinejs/-/alpinejs-3.14.1.tgz"
  integrity sha512-ICar8UsnRZAYvv/fCNfNeKMXNoXGUfwHrjx7LqXd08zIP95G2d9bAOuaL97re+1mgt/HojqHsfdOLo/A5LuWgQ==
  dependencies:
    "@vue/reactivity" "~3.1.1"

chart.js@^4.3.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/chart.js/-/chart.js-4.5.0.tgz"
  integrity sha512-aYeC/jDgSEx8SHWZvANYMioYMZ2KX02W6f6uVfyteuCGcadDLcYVHdfdygsTQkQ4TKn5lghoojAsPj5pu0SnvQ==
  dependencies:
    "@kurkle/color" "^0.3.0"

cropper@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/cropper/-/cropper-4.1.0.tgz"
  integrity sha512-dNbkWNT606oMgRQ2aYMerDnPpSVLBMTWyERDHsDwih1ahJiVpyfSM9ev/n6G4ElfRG8t0shUZ5FXLg7YtmDdBQ==
  dependencies:
    cropperjs "^1.5.6"

cropperjs@^1.5.6:
  version "1.6.2"
  resolved "https://registry.npmjs.org/cropperjs/-/cropperjs-1.6.2.tgz"
  integrity sha512-nhymn9GdnV3CqiEHJVai54TULFAE3VshJTXSqSJKa8yXAKyBKDWdhHarnlIPrshJ0WMFTGuFvG02YjLXfPiuOA==

jquery@>=1.9.1:
  version "3.7.1"
  resolved "https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz"
  integrity sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg==
