# Billers API Documentation

This document provides comprehensive documentation for the Billers API endpoints, including request formats, response examples, and usage instructions.

## Base URLs

- **API Scope**: `/api/billers`
- **Wallets Scope**: `/api/wallets/billers`

## Authentication

All endpoints require proper authentication headers. The API supports:
- Bearer tokens for user authentication
- Basic authentication for service-to-service calls

## Content Type

All requests should use `Content-Type: application/json`

## Request Method

**All endpoints use POST requests** following the cards controller pattern. All parameters are passed in the request body as JSON.

---

## Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/billers/account-details` | Get account details |
| POST | `/api/billers/payment` | Process payment transaction |
| POST | `/api/billers/invoice` | Get invoice details |
| POST | `/api/billers/invoice-confirm` | Confirm invoice payment |
| POST | `/api/billers/bundle-details` | Get bundle details |
| POST | `/api/billers/bundle-confirm` | Confirm bundle purchase |
| POST | `/api/billers/validate` | Validate account |
| POST | `/api/billers/transactions` | List transactions |
| POST | `/api/billers/transaction` | Get specific transaction |
| POST | `/api/billers/transaction-by-reference` | Get transaction by reference |
| POST | `/api/billers/transaction-retry` | Retry failed transaction |
| POST | `/api/billers/types` | Get available biller types |

---

## 1. Account Details

### Get Account Details
**Endpoint**: `POST /api/billers/account-details`

**Supported Biller Types**:
- `bwb_postpaid` - Blantyre Water Board Postpaid
- `lwb_postpaid` - Lilongwe Water Board Postpaid  
- `srwb_postpaid` - Southern Region Water Board Postpaid
- `masm` - Malawi Savings Bank

**Request Body Parameters**:
- `biller_type` (required) - The type of biller
- `account_number` (required) - Customer account number
- `account_type` (optional) - Account type for MASM (defaults to "account")

**Request Examples**:

```bash
# BWB Postpaid Account Details
curl -X POST "http://localhost:4000/api/billers/account-details" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "biller_type": "bwb_postpaid",
    "account_number": "********"
  }'

# MASM Account Details with Account Type
curl -X POST "http://localhost:4000/api/billers/account-details" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "biller_type": "masm",
    "account_number": "********",
    "account_type": "M"
  }'
```

**Response Example**:
```json
{
  "status": "success",
  "data": {
    "transaction": {
      "id": 123,
      "our_transaction_id": "****************",
      "biller_type": "bwb_postpaid",
      "biller_name": "BWB Postpaid",
      "account_number": "********",
      "transaction_type": "account_details",
      "status": "completed",
      "response_payload": {
        "account_name": "John Doe",
        "account_balance": "1500.00",
        "currency": "MWK"
      },
      "processed_at": "2025-07-05T20:15:30Z",
      "inserted_at": "2025-07-05T20:15:25Z"
    }
  }
}
```

---

## 2. Payments

### Process Payment
**Endpoint**: `POST /api/billers/payment`

**Request Body Parameters**:
- `biller_type` (required) - The type of biller
- `account_number` (required) - Customer account number
- `amount` (required) - Payment amount
- `currency` (optional) - Currency code (defaults to "MWK")
- `credit_account` (required) - Destination account
- `credit_account_type` (required) - Type of destination account ("account" or "wallet")
- `debit_account` (required) - Source account
- `debit_account_type` (required) - Type of source account ("account" or "wallet")

**Request Body Example**:
```json
{
  "biller_type": "bwb_postpaid",
  "account_number": "********",
  "amount": "500.00",
  "currency": "MWK",
  "credit_account": "********90",
  "credit_account_type": "account",
  "debit_account": "09********",
  "debit_account_type": "wallet"
}
```

**Request Example**:
```bash
curl -X POST "http://localhost:4000/api/billers/payment" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "biller_type": "lwb_postpaid",
    "account_number": "********",
    "amount": "750.00",
    "currency": "MWK",
    "credit_account": "**********",
    "credit_account_type": "account",
    "debit_account": "**********",
    "debit_account_type": "wallet"
  }'
```

**Response Example**:
```json
{
  "status": "success",
  "data": {
    "transaction": {
      "id": 124,
      "our_transaction_id": "****************",
      "biller_type": "lwb_postpaid",
      "biller_name": "LWB Postpaid",
      "account_number": "********",
      "amount": "750.00",
      "currency": "MWK",
      "transaction_type": "post_transaction",
      "status": "completed",
      "credit_account": "**********",
      "debit_account": "**********",
      "response_payload": {
        "payment_reference": "PAY********9",
        "receipt_number": "RCP001122334"
      },
      "processed_at": "2025-07-05T20:20:15Z"
    }
  }
}
```

---

## 3. Invoices

### Get Invoice Details
**Endpoint**: `POST /api/billers/invoice`

**Supported Biller Types**:
- `register_general` - Register General
- `srwb_prepaid` - Southern Region Water Board Prepaid

**Request Body Parameters**:
- `biller_type` (required) - The type of biller
- `account_number` (required) - Customer account number

**Request Example**:
```bash
curl -X POST "http://localhost:4000/api/billers/invoice" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "biller_type": "register_general",
    "account_number": "REG123456"
  }'
```

### Confirm Invoice Payment
**Endpoint**: `POST /api/billers/invoice-confirm`

**Request Body Parameters**:
- `biller_type` (required) - The type of biller
- `account_number` (required) - Customer account number
- `amount` (required) - Payment amount
- `currency` (optional) - Currency code (defaults to "MWK")
- `credit_account` (required) - Destination account
- `credit_account_type` (required) - Type of destination account ("account" or "wallet")
- `debit_account` (required) - Source account
- `debit_account_type` (required) - Type of source account ("account" or "wallet")

**Request Body Example**:
```json
{
  "biller_type": "register_general",
  "account_number": "REG123456",
  "amount": "1000.00",
  "currency": "MWK",
  "credit_account": "**********",
  "credit_account_type": "account",
  "debit_account": "**********",
  "debit_account_type": "wallet"
}
```

**Request Example**:
```bash
curl -X POST "http://localhost:4000/api/billers/invoice-confirm" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "biller_type": "srwb_prepaid",
    "account_number": "SRWB654321",
    "amount": "300.00",
    "currency": "MWK",
    "credit_account": "**********",
    "credit_account_type": "account",
    "debit_account": "**********",
    "debit_account_type": "wallet"
  }'
```

---

## 4. Bundles

### Get Bundle Details
**Endpoint**: `POST /api/billers/bundle-details`

**Request Body Parameters**:
- `bundle_id` (required) - TNM bundle identifier

**Request Example**:
```bash
curl -X POST "http://localhost:4000/api/billers/bundle-details" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "bundle_id": "239678"
  }'
```

**Response Example**:
```json
{
  "status": "success",
  "data": {
    "transaction": {
      "id": 125,
      "our_transaction_id": "****************",
      "biller_type": "tnm_bundles",
      "biller_name": "TNM Bundles",
      "bundle_id": "239678",
      "transaction_type": "bundle_details",
      "status": "completed",
      "response_payload": {
        "bundle_name": "1GB Monthly",
        "bundle_price": "2500.00",
        "validity_period": "30 days",
        "description": "1GB data bundle valid for 30 days"
      },
      "processed_at": "2025-07-05T20:25:10Z"
    }
  }
}
```

### Confirm Bundle Purchase
**Endpoint**: `POST /api/billers/bundle-confirm`

**Request Body Parameters**:
- `bundle_id` (required) - TNM bundle identifier
- `phone_number` (required) - Mobile phone number for the bundle
- `amount` (required) - Bundle price
- `currency` (optional) - Currency code (defaults to "MWK")

**Request Body Example**:
```json
{
  "bundle_id": "239678",
  "phone_number": "**********",
  "amount": "2500.00",
  "currency": "MWK"
}
```

**Request Example**:
```bash
curl -X POST "http://localhost:4000/api/billers/bundle-confirm" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "bundle_id": "239678",
    "phone_number": "**********",
    "amount": "2500.00",
    "currency": "MWK"
  }'
```

---

## 5. Validation

### Validate Account
**Endpoint**: `GET /api/billers/validate/{account_number}`

**Note**: This endpoint is specifically for Airtel Money validation.

**Request Example**:
```bash
curl -X GET "http://localhost:4000/api/billers/validate/**********" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

**Response Example**:
```json
{
  "status": "success",
  "data": {
    "transaction": {
      "id": 126,
      "our_transaction_id": "****************",
      "biller_type": "airtel_validation",
      "biller_name": "Airtel Validation",
      "account_number": "**********",
      "transaction_type": "validation",
      "status": "completed",
      "response_payload": {
        "is_valid": true,
        "account_name": "Jane Smith",
        "account_status": "active"
      },
      "processed_at": "2025-07-05T20:30:05Z"
    }
  }
}
```

---

## 6. Transaction Management

### List Transactions
**Endpoint**: `GET /api/billers/transactions`

**Query Parameters**:
- `biller_type` (optional) - Filter by biller type
- `status` (optional) - Filter by status (`pending`, `processing`, `completed`, `failed`, `cancelled`)
- `limit` (optional) - Number of records per page (default: 50)
- `offset` (optional) - Number of records to skip (default: 0)

**Request Example**:
```bash
curl -X GET "http://localhost:4000/api/billers/transactions?biller_type=bwb_postpaid&status=completed&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### Get Specific Transaction
**Endpoint**: `GET /api/billers/transactions/{transaction_id}`

**Request Example**:
```bash
curl -X GET "http://localhost:4000/api/billers/transactions/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### Retry Failed Transaction
**Endpoint**: `POST /api/billers/transactions/{transaction_id}/retry`

**Request Example**:
```bash
curl -X POST "http://localhost:4000/api/billers/transactions/123/retry" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

---

## Wallet Scope Endpoints

All the above endpoints are also available under the `/api/wallets/billers` scope with the same functionality:

- `GET /api/wallets/billers/account-details/{biller_type}/{account_number}`
- `POST /api/wallets/billers/payments`
- `GET /api/wallets/billers/invoices/{biller_type}/{account_number}`
- `POST /api/wallets/billers/invoices/confirm`
- `GET /api/wallets/billers/bundles/{bundle_id}`
- `POST /api/wallets/billers/bundles/confirm`
- `GET /api/wallets/billers/validate/{account_number}`
- `GET /api/wallets/billers/transactions`
- `GET /api/wallets/billers/transactions/{transaction_id}`
- `POST /api/wallets/billers/transactions/{transaction_id}/retry`

---

## Error Responses

### Validation Error Example
```json
{
  "status": "error",
  "errors": {
    "account_number": ["can't be blank"],
    "amount": ["must be greater than 0"]
  }
}
```

### Service Error Example
```json
{
  "status": "error",
  "message": "Biller service temporarily unavailable"
}
```

### Not Found Error Example
```json
{
  "status": "error",
  "message": "Transaction not found"
}
```

---

## Supported Billers Configuration

| Biller Type | Name | Supported Operations |
|-------------|------|---------------------|
| `register_general` | Register General | invoice, confirm_invoice |
| `bwb_postpaid` | BWB Postpaid | account_details, payment |
| `lwb_postpaid` | LWB Postpaid | account_details, payment |
| `srwb_postpaid` | SRWB Postpaid | account_details, payment |
| `srwb_prepaid` | SRWB Prepaid | invoice, confirm_invoice |
| `masm` | MASM | account_details |
| `airtel_validation` | Airtel Validation | validation |
| `tnm_bundles` | TNM Bundles | bundle_details, confirm_bundle |

---

## Rate Limiting

- Maximum 100 requests per minute per user
- Bulk operations limited to 10 concurrent requests

## Request/Response Logging

All requests and responses are logged for audit purposes. Sensitive information is masked in logs.

## Webhook Notifications

For long-running transactions, webhook notifications can be configured to receive status updates.

---

## Testing

Use the provided Postman collection or curl commands for testing. Ensure you have:

1. Valid authentication tokens
2. Test account numbers for each biller
3. Sufficient balance for payment transactions

## Support

For API support or questions, contact the development team or refer to the technical documentation.