# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Phoenix Commands
- `mix setup` - Install and setup dependencies, create/migrate database, setup and build assets
- `mix phx.server` - Start Phoenix server (development)
- `iex -S mix phx.server` - Start Phoenix server with interactive console
- `mix test` - Run tests (automatically creates test DB if needed)
- `mix ecto.reset` - Drop and recreate database with seeds
- `mix deps.get` - Install dependencies
- `mix credo` - Run code analysis with Credo

### Asset Commands
- `mix assets.build` - Build Tailwind CSS and JavaScript assets
- `mix assets.deploy` - Build and minify assets for production

### Database Commands
- `mix ecto.migrate` - Run database migrations
- `mix ecto.rollback` - Rollback last migration
- `mix ecto.gen.migration <name>` - Generate new migration

## Architecture Overview

### Application Structure
This is a comprehensive Elixir/Phoenix banking and financial services platform with multiple authentication systems and a sophisticated dynamic API framework.

### Key Architectural Patterns

#### Multi-Authentication System
- **System Users** (`ServiceManagerWeb.Plugs.SystemUserAuth`) - Admin backend users
- **Account Users** (`ServiceManagerWeb.UserAuth`) - Mobile banking users  
- **Wallet Users** (`ServiceManagerWeb.WalletUserAuth`) - Digital wallet users
- **Third Party APIs** (`ServiceManagerWeb.Plugs.ThirdPartyPlug`) - External integrations

#### Dynamic API Framework
The application features a unique runtime-configurable API system:
- **Dynamic Routes** - API endpoints defined in database, routed via `/dynamic/*` to `DynamicRouteController`
- **Dynamic Forms** - JSON schema-based request validation linked to routes
- **Dynamic Processes** - Runtime-compiled Elixir code for business logic execution
- **Process Chaining** - Sequential execution of multiple processes for complex workflows
- **Admin Interface** - LiveView-based management UI at `/mobileBanking/dynamic-forms/`

#### Context Organization
Business logic is organized into contexts in `lib/service_manager/contexts/`:
- `t_24_context.ex` - Core banking system integration
- `wallets_context.ex` - Digital wallet operations
- `transactions_context.ex` - Transaction processing
- `user_management_context.ex` - User account management
- 30+ other specialized contexts

#### Background Processing
- **Oban** - Background job processing for async operations
- **Finch** - HTTP client pool for external API calls
- **GenServer processors** - Real-time transaction and callback processing

### Database Schema Highlights
- **Multi-tenant transactions** - Supports both traditional banking and wallet transactions
- **Audit trails** - Comprehensive logging via `system_log` and `t24_log` tables
- **Beneficiary management** - Linked to both accounts and wallets
- **Card management** - Virtual and physical card support
- **Dynamic schema tables** - Runtime API configuration storage

### Frontend Architecture
- **Phoenix LiveView** - Interactive admin interface
- **Tailwind CSS** - Utility-first styling
- **Alpine.js hooks** - Enhanced interactivity (`assets/js/hooks/`)
- **Code editor integration** - Monaco editor for dynamic process editing

### Third-Party Integrations
- **T24 Banking System** - Core banking operations via `lib/service_manager/services/t24/`
- **SMS Services** - Multiple providers for notifications
- **Telegram** - Uptime monitoring alerts
- **Payment Gateways** - Multiple payment method support

### Security Features
- **JWT Authentication** - Multiple token systems for different user types
- **IP Whitelisting** - API access control
- **Rate Limiting** - Protection against abuse
- **Session Management** - Secure session handling across user types
- **OTP Verification** - Two-factor authentication support

### Key Libraries and Dependencies
- **Phoenix LiveView 1.0.0-rc.1** - Interactive UI framework
- **Ecto/PostgreSQL** - Database layer
- **Joken** - JWT token handling
- **Swoosh** - Email notifications
- **Oban** - Background job processing
- **Finch** - HTTP client
- **Credo** - Code analysis
- **ExRated** - Rate limiting

### Development Guidelines
- Database migrations follow timestamp naming convention
- Contexts contain related business logic groupings
- LiveView components in `lib/service_manager_web/live/`
- API controllers in `lib/service_manager_web/controllers/api/`
- Background processors in `lib/service_manager/processors/`
- Test files mirror the lib structure in `test/`

### Environment Configuration
- `config/dev.exs` - Development settings
- `config/prod.exs` - Production configuration  
- `config/test.exs` - Test environment
- `config/runtime.exs` - Runtime configuration

### Deployment
- **Docker** support via Dockerfile and docker-compose files
- **Asset pipeline** - Esbuild and Tailwind compilation
- **Database** - PostgreSQL with comprehensive migration system
- **Monitoring** - Built-in uptime monitoring and logging systems