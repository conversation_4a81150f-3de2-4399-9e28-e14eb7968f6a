defmodule ServiceManager.MixProject do
  use Mix.Project

  def project do
    [
      app: :service_manager,
      version: "4.1.0",
      elixir: "~> 1.14",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {ServiceManager.Application, []},
      extra_applications: [:logger, :runtime_tools, :ex_rated, :os_mon]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:bcrypt_elixir, "~> 3.0"},
      {:phoenix, "~> 1.7.14"},
      {:phoenix_ecto, "~> 4.5"},
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      {:phoenix_html, "~> 4.1"},
      {:phoenix_live_reload, "~> 1.2", only: [:dev, :stage]},
      # TODO bump on release to {:phoenix_live_view, "~> 1.0.0"},
      {:phoenix_live_view, "~> 1.0.0-rc.1", override: true},
      {:floki, ">= 0.30.0", only: :test},
      {:phoenix_live_dashboard, "~> 0.8.3"},
      {:esbuild, "~> 0.8", runtime: Mix.env() in [:dev, :stage]},
      {:tailwind, "~> 0.2", runtime: Mix.env() in [:dev, :stage]},
      {:heroicons,
       github: "tailwindlabs/heroicons",
       tag: "v2.1.1",
       sparse: "optimized",
       app: false,
       compile: false,
       depth: 1},
      {:swoosh, "~> 1.17.1"},
      {:gen_smtp, "~> 1.1"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.20"},
      {:jason, "~> 1.2"},
      {:dns_cluster, "~> 0.1.1"},
      {:bandit, "~> 1.5"},
      {:joken, "~> 2.6"},
      {:endon, "~> 1.0"},
      {:httpoison, "~> 2.2"},
      {:scrivener, "~> 2.7"},
      {:scrivener_ecto, "~> 2.7"},
      {:timex, ">= 0.0.0"},
      {:xlsxir, "~> 1.6.2"},
      {:atomic_map, "~> 0.8"},
      {:csv, "~> 2.4"},
      {:number, "~> 1.0"},
      {:nimble_csv, "~> 1.0"},
      {:skooma, "~> 0.2.0"},
      {:decimal, "~> 2.0.0", override: true},
      {:cachex, "~> 4.0"},
      {:elixlsx, "~> 0.6.0"},
      {:finch, "~> 0.16"},
      {:fuse, "~> 2.4"},
      {:ex_rated, "~> 2.1"},
      # ex_rated depends on this
      {:ex2ms, "~> 1.6"},
      {:telemetry, "~> 1.2"},
      # Added Oban for background job processing
      {:oban, "~> 2.17"},
      {:ecto_network, "~> 1.3.0"},
      {:logger_file_backend, "~> 0.0.13"},
      {:ecto_psql_extras, "~> 0.6"},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:mogrify, "~> 0.9.3"},
      {:telegex, "~> 1.9.0-rc.0"},
      {:cloak_ecto, "~> 1.3"}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind service_manager", "esbuild service_manager"],
      "assets.deploy": [
        "tailwind service_manager --minify",
        "esbuild service_manager --minify",
        "phx.digest"
      ]
    ]
  end
end
