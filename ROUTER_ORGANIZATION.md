# Router Organization by API Access Type

The router.ex file has been reorganized to clearly separate routes by API access type, making it easier to understand security boundaries and maintain different authentication flows.

## Organization Structure

### 1. THIRD PARTY API ROUTES
**Lines: 95-207**
- **Access Type:** Third party systems with API keys
- **Pipeline:** `:third_party_api_key` (IP whitelist + third party auth + account status)
- **Base Path:** `/api/third-party`
- **Routes Include:**
  - Mobile Forms API (`/mobile-forms/*`)
  - Card Management (`/cards/*`)
  - Callback Registration (`/callback/*`)
  - Wallet operations (`/wallet/*`)
  - Mobile Banking (`/mobile-banking/*`)
  - Transaction Reports (`/reports/transactions/*`)

### 2. WALLET API ROUTES
**Lines: 208-398**
- **Access Type:** Wallet users and wallet API key integrations  
- **Pipelines:** `:api`, `:authenticated_wallet_api`, `:authenticated_wallet_api_key`
- **Base Paths:** `/api/auth/wallet`, `/api/integration/wallets`, `/api/wallets`, `/api/wallet/*`, `/api/transactions/wallet`
- **Routes Include:**
  - Wallet Authentication (Public) - sign-in, sign-up, forgot password
  - Wallet Integration API (API Key Auth) - CRUD operations with API keys
  - Wallet Transaction Reports - reporting endpoints
  - Wallet Beneficiaries - beneficiary management
  - Main Wallet API - full wallet functionality for authenticated users

### 3. PUBLIC API ROUTES  
**Lines: 399-460**
- **Access Type:** Public access (with IP whitelist only)
- **Pipeline:** `:api` (IP whitelist only)
- **Base Paths:** `/api/general/otp`, `/dynamic/*`, `/api/auth`, `/api/profile`, `/api`
- **Routes Include:**
  - General OTP Service
  - Dynamic Route Handler
  - Authentication API (sign-in, sign-up, refresh)
  - Profile Management (registration, account linking)
  - General API endpoints (memorable word, public billers)

### 4. AUTHENTICATED USER API ROUTES
**Lines: 461-663**
- **Access Type:** Mobile banking users with valid JWT tokens
- **Pipeline:** `:authenticated_api` (IP whitelist + auth + user status + account status)
- **Base Path:** `/api`
- **Routes Include:**
  - Device Management (`/device/*`, `/activeDevices/*`)
  - OTP operations (`/otp/*`)
  - Profile management (`/profile/*`)
  - Notification settings (`/notification-settings/*`)
  - Account operations (`/accounts/*`)
  - Wallet basic operations (`/wallet/*`)
  - Card Management (`/cards/*`)
  - Merchant payments (`/merchant/*`)
  - Biometrics (`/biometrics/*`)
  - Memorable words (`/memorable_word/*`)
  - Beneficiaries (`/beneficiaries/*`)
  - Bills (`/bills/*`)
  - Billers (authenticated) (`/billers/*`)
  - Transfers (`/transfers/*`)
  - Cardless Withdrawals (`/withdraws/*`)
  - Transactions and reporting (`/transactions/*`)
  - Cheque operations (`/chequebook/*`, `/cheque/*`)
  - Loans (`/loans/*`)
  - Mobile Forms API (`/mobile-forms/*`)

### 5. BACKEND/ADMIN WEB INTERFACE ROUTES
**Lines: 664-1107**
- **Access Type:** System admin users via web browser
- **Pipelines:** `:backend_browser`, `:auth_browser`, `:info`, `:browser` (various browser auth)
- **Base Paths:** `/monitor`, `/mobileBanking`, `/mobileBankingReporting`, `/users`, `/walletusers`, `/about-us`
- **Routes Include:**
  - System Monitoring (LiveDashboard, logs)
  - Main Admin Backend (comprehensive LiveView management interface)
  - Backend Reporting (Excel exports)
  - System User Authentication Pages (login, registration, password reset)
  - Public Info Pages
  - System User Settings (authenticated)
  - System User Session Management
  - Wallet User Web Interface (authentication, settings, session management)

## Key Benefits

1. **Clear Security Boundaries:** Each section has well-defined authentication requirements
2. **Easy Maintenance:** Related routes are grouped together
3. **Better Documentation:** Clear headers explain access types and pipelines
4. **Simplified Debugging:** Easy to find routes by authentication type
5. **Scalability:** New routes can be easily added to the appropriate section

## Pipeline Summary

- **`:third_party_api_key`** - Third party integrations
- **`:authenticated_wallet_api_key`** - Wallet API key integrations  
- **`:authenticated_wallet_api`** - Wallet user authentication
- **`:authenticated_api`** - Mobile banking user authentication
- **`:api`** - Public API with IP whitelist
- **`:backend_browser`** - Admin web interface
- **`:auth_browser`** - Authentication pages
- **`:browser`** - General browser access
- **`:info`** - Public information pages

## File Size Impact

The router file remains the same size (1107 lines) but is now much more organized and maintainable, with clear section headers that make it easy to understand the purpose and security requirements of each group of routes.