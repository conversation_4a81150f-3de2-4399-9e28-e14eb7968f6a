#!/usr/bin/env elixir

# Interactive MCP Server Explorer
# Usage: elixir scripts/mcp_explorer.exs

defmodule MCPExplorer do
  @moduledoc """
  Interactive explorer for the Dynamic Forms MCP Server.
  This script connects to the MCP server and demonstrates its capabilities.
  """
  
  def start() do
    IO.puts("🔍 MCP Server Explorer")
    IO.puts("=" |> String.duplicate(60))
    
    # Start the application
    case Application.ensure_all_started(:service_manager) do
      {:ok, _} ->
        IO.puts("✅ ServiceManager application started")
        
        # Wait for MCP components
        Process.sleep(3000)
        
        # Explore the MCP server
        explore_mcp_server()
        
      {:error, reason} ->
        IO.puts("❌ Failed to start application: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  defp explore_mcp_server() do
    IO.puts("\n🚀 Exploring MCP Server Capabilities...")
    IO.puts("-" |> String.duplicate(60))
    
    # 1. Get server information
    explore_server_info()
    
    # 2. Explore resources
    explore_resources()
    
    # 3. Explore tools
    explore_tools()
    
    # 4. Explore prompts
    explore_prompts()
    
    # 5. Interactive menu
    interactive_menu()
  end
  
  defp explore_server_info() do
    IO.puts("\n📊 SERVER INFORMATION")
    IO.puts("-" |> String.duplicate(30))
    
    case ServiceManager.MCP.Server.get_server_info() do
      {:ok, info} ->
        IO.puts("Name: #{info.name}")
        IO.puts("Version: #{info.version}")
        IO.puts("Description: #{info.description}")
        IO.puts("Author: #{info.author}")
        IO.puts("License: #{info.license}")
        IO.puts("Status: #{info.status}")
        IO.puts("Uptime: #{info.uptime} seconds")
        IO.puts("Connections: #{info.connections}")
        IO.puts("Requests Handled: #{info.requests_handled}")
        
        if info.config do
          IO.puts("\nConfiguration:")
          IO.puts("  Host: #{info.config.host}")
          IO.puts("  Port: #{info.config.port}")
          IO.puts("  Enabled: #{info.config.enabled}")
          IO.puts("  Auth Required: #{info.config.auth_required}")
          IO.puts("  Max Connections: #{info.config.max_connections}")
        end
        
      {:error, reason} ->
        IO.puts("❌ Failed to get server info: #{inspect(reason)}")
    end
  end
  
  defp explore_resources() do
    IO.puts("\n📚 AVAILABLE RESOURCES")
    IO.puts("-" |> String.duplicate(30))
    
    case ServiceManager.MCP.Server.list_resources() do
      {:ok, resources} ->
        IO.puts("Found #{length(resources)} resources:")
        
        Enum.with_index(resources, 1)
        |> Enum.each(fn {resource, index} ->
          IO.puts("\n#{index}. #{resource.name}")
          IO.puts("   URI: #{resource.uri}")
          IO.puts("   Description: #{resource.description}")
          IO.puts("   MIME Type: #{resource.mimeType}")
        end)
        
      {:error, reason} ->
        IO.puts("❌ Failed to list resources: #{inspect(reason)}")
    end
  end
  
  defp explore_tools() do
    IO.puts("\n🛠️  AVAILABLE TOOLS")
    IO.puts("-" |> String.duplicate(30))
    
    case ServiceManager.MCP.Server.list_tools() do
      {:ok, tools} ->
        IO.puts("Found #{length(tools)} tools:")
        
        # Group tools by category
        grouped_tools = Enum.group_by(tools, fn tool ->
          cond do
            String.contains?(tool.name, "route") -> "Route Management"
            String.contains?(tool.name, "form") -> "Form Management"
            String.contains?(tool.name, "plugin") -> "Plugin Management"
            String.contains?(tool.name, "link") -> "Entity Linking"
            true -> "Other"
          end
        end)
        
        Enum.each(grouped_tools, fn {category, category_tools} ->
          IO.puts("\n📂 #{category}:")
          Enum.each(category_tools, fn tool ->
            IO.puts("  • #{tool.name}")
            IO.puts("    #{tool.description}")
          end)
        end)
        
      {:error, reason} ->
        IO.puts("❌ Failed to list tools: #{inspect(reason)}")
    end
  end
  
  defp explore_prompts() do
    IO.puts("\n💡 AVAILABLE PROMPTS")
    IO.puts("-" |> String.duplicate(30))
    
    case ServiceManager.MCP.Server.list_prompts() do
      {:ok, prompts} ->
        IO.puts("Found #{length(prompts)} AI-assisted prompts:")
        
        Enum.with_index(prompts, 1)
        |> Enum.each(fn {prompt, index} ->
          IO.puts("\n#{index}. #{prompt.name}")
          IO.puts("   #{prompt.description}")
          
          if length(prompt.arguments) > 0 do
            IO.puts("   Arguments:")
            Enum.each(prompt.arguments, fn arg ->
              required = if arg.required, do: "(required)", else: "(optional)"
              IO.puts("     - #{arg.name} #{required}: #{arg.description}")
            end)
          end
        end)
        
      {:error, reason} ->
        IO.puts("❌ Failed to list prompts: #{inspect(reason)}")
    end
  end
  
  defp interactive_menu() do
    IO.puts("\n" <> ("=" |> String.duplicate(60)))
    IO.puts("🎮 INTERACTIVE MENU")
    IO.puts("=" |> String.duplicate(60))
    IO.puts("1. Test a resource")
    IO.puts("2. Test a tool")
    IO.puts("3. Test a prompt")
    IO.puts("4. Show detailed tool information")
    IO.puts("5. Show detailed prompt information")
    IO.puts("6. Exit")
    IO.puts("")
    
    choice = IO.gets("Enter your choice (1-6): ") |> String.trim()
    
    case choice do
      "1" -> test_resource()
      "2" -> test_tool()
      "3" -> test_prompt()
      "4" -> show_tool_details()
      "5" -> show_prompt_details()
      "6" -> 
        IO.puts("👋 Goodbye!")
        System.halt(0)
      _ -> 
        IO.puts("❌ Invalid choice. Please try again.")
        interactive_menu()
    end
  end
  
  defp test_resource() do
    IO.puts("\n📚 Testing Resource...")
    IO.puts("Available resources:")
    IO.puts("1. mcp://dynamic-forms/routes")
    IO.puts("2. mcp://dynamic-forms/forms")
    IO.puts("3. mcp://dynamic-forms/plugins")
    IO.puts("4. mcp://dynamic-forms/connections")
    IO.puts("5. mcp://dynamic-forms/stats")
    
    choice = IO.gets("Choose a resource (1-5): ") |> String.trim()
    
    uri = case choice do
      "1" -> "mcp://dynamic-forms/routes"
      "2" -> "mcp://dynamic-forms/forms"
      "3" -> "mcp://dynamic-forms/plugins"
      "4" -> "mcp://dynamic-forms/connections"
      "5" -> "mcp://dynamic-forms/stats"
      _ -> 
        IO.puts("❌ Invalid choice")
        return
    end
    
    IO.puts("\n🔄 Reading resource: #{uri}")
    
    case ServiceManager.MCP.Resources.read_resource(uri) do
      {:ok, resource} ->
        IO.puts("✅ Resource read successfully!")
        IO.puts("URI: #{resource.uri}")
        IO.puts("Data count: #{length(resource.data || [])}")
        IO.puts("Metadata: #{inspect(resource.metadata)}")
        
        if is_list(resource.data) and length(resource.data) > 0 do
          IO.puts("\nFirst item preview:")
          IO.puts(inspect(Enum.at(resource.data, 0), pretty: true, limit: 3))
        end
        
      {:error, reason} ->
        IO.puts("❌ Failed to read resource: #{inspect(reason)}")
    end
    
    IO.puts("\nPress Enter to continue...")
    IO.gets("")
    interactive_menu()
  end
  
  defp test_tool() do
    IO.puts("\n🛠️  Testing Tool...")
    IO.puts("This would demonstrate calling a tool with parameters.")
    IO.puts("For safety, we'll just show the tool schema.")
    
    case ServiceManager.MCP.Server.list_tools() do
      {:ok, tools} ->
        tool = Enum.at(tools, 0)
        if tool do
          IO.puts("\nExample tool: #{tool.name}")
          IO.puts("Description: #{tool.description}")
          IO.puts("Input Schema:")
          IO.puts(inspect(tool.inputSchema, pretty: true))
        end
      _ ->
        IO.puts("❌ No tools available")
    end
    
    IO.puts("\nPress Enter to continue...")
    IO.gets("")
    interactive_menu()
  end
  
  defp test_prompt() do
    IO.puts("\n💡 Testing Prompt...")
    IO.puts("Example: Getting route design assistance")
    
    args = %{
      "purpose" => "Create a user authentication endpoint",
      "data_type" => "user credentials",
      "http_method" => "POST"
    }
    
    case ServiceManager.MCP.Prompts.get_prompt("route_designer", args) do
      {:ok, prompt_result} ->
        IO.puts("✅ Prompt generated successfully!")
        IO.puts("\nPrompt preview (first 500 chars):")
        prompt_text = prompt_result.prompt
        preview = String.slice(prompt_text, 0, 500)
        IO.puts(preview <> "...")
        
        IO.puts("\nContext:")
        IO.puts(inspect(prompt_result.context, pretty: true))
        
      {:error, reason} ->
        IO.puts("❌ Failed to generate prompt: #{inspect(reason)}")
    end
    
    IO.puts("\nPress Enter to continue...")
    IO.gets("")
    interactive_menu()
  end
  
  defp show_tool_details() do
    IO.puts("\n🔍 Detailed Tool Information...")
    
    case ServiceManager.MCP.Server.list_tools() do
      {:ok, tools} ->
        Enum.with_index(tools, 1)
        |> Enum.take(5)  # Show first 5 tools
        |> Enum.each(fn {tool, index} ->
          IO.puts("\n#{index}. #{tool.name}")
          IO.puts("   Description: #{tool.description}")
          IO.puts("   Input Schema:")
          IO.puts(inspect(tool.inputSchema, pretty: true, limit: 10))
        end)
      _ ->
        IO.puts("❌ No tools available")
    end
    
    IO.puts("\nPress Enter to continue...")
    IO.gets("")
    interactive_menu()
  end
  
  defp show_prompt_details() do
    IO.puts("\n🔍 Detailed Prompt Information...")
    
    case ServiceManager.MCP.Server.list_prompts() do
      {:ok, prompts} ->
        Enum.take(prompts, 3)  # Show first 3 prompts
        |> Enum.each(fn prompt ->
          IO.puts("\n📝 #{prompt.name}")
          IO.puts("   #{prompt.description}")
          IO.puts("   Arguments:")
          Enum.each(prompt.arguments, fn arg ->
            required = if arg.required, do: "✓", else: "○"
            IO.puts("     #{required} #{arg.name}: #{arg.description}")
          end)
        end)
      _ ->
        IO.puts("❌ No prompts available")
    end
    
    IO.puts("\nPress Enter to continue...")
    IO.gets("")
    interactive_menu()
  end
end

# Configure Mix for the project
Mix.install([
  {:service_manager, path: "."}
])

# Start the explorer
MCPExplorer.start()
