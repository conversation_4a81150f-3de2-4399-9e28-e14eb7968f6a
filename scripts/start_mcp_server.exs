#!/usr/bin/env elixir

# Standalone script to start the MCP server for Dynamic Forms system
# Usage: elixir scripts/start_mcp_server.exs

defmodule MCPServerStarter do
  @moduledoc """
  Standalone script to start the MCP server for Dynamic Forms system.
  
  This script can be used by Claude Code or other MCP clients to start
  the server independently of the main Phoenix application.
  """
  
  def start() do
    IO.puts("🚀 Starting MCP Server for Dynamic Forms System...")
    IO.puts("=" |> String.duplicate(60))

    # Check environment
    env = System.get_env("MIX_ENV") || "dev"
    IO.puts("🌍 Environment: #{env}")
    IO.puts("📁 Working Directory: #{File.cwd!()}")

    # Check if mix.exs exists
    if File.exists?("mix.exs") do
      IO.puts("✅ Found mix.exs file")
    else
      IO.puts("❌ mix.exs not found - make sure you're in the project root")
      System.halt(1)
    end

    # Load application dependencies
    IO.puts("📦 Loading application dependencies...")
    Mix.install([
      {:service_manager, path: "."}
    ])

    # Check configuration
    IO.puts("⚙️  Checking MCP configuration...")
    mcp_config_raw = Application.get_env(:service_manager, :mcp_server, %{})
    IO.puts("   Config (raw): #{inspect(mcp_config_raw)}")

    # Convert keyword list to map if needed
    mcp_config = case mcp_config_raw do
      config when is_list(config) -> Enum.into(config, %{})
      config when is_map(config) -> config
      _ -> %{}
    end

    IO.puts("   Config (normalized): #{inspect(mcp_config)}")

    unless Map.get(mcp_config, :enabled, false) do
      IO.puts("❌ MCP Server is disabled in configuration!")
      System.halt(1)
    end

    # Start the application
    IO.puts("🔄 Starting ServiceManager application...")
    case Application.ensure_all_started(:service_manager) do
      {:ok, apps} ->
        IO.puts("✅ Application started successfully")
        IO.puts("📋 Started applications: #{inspect(apps)}")

        # Wait for services to initialize
        IO.puts("⏳ Waiting for services to initialize...")
        Process.sleep(5000)

        # Check MCP supervisor
        check_mcp_supervisor()

        # Check MCP server status with retries
        case wait_for_mcp_server(15) do
          {:ok, _info} ->
            IO.puts("✅ MCP Server is ready!")

            # Print connection information
            print_connection_info()

            # Keep server running
            IO.puts("\n🚀 MCP Server is running!")
            IO.puts("Press Ctrl+C to stop the server")

            # Keep alive
            Process.sleep(:infinity)

          {:error, reason} ->
            IO.puts("❌ MCP Server failed to start: #{reason}")
            print_troubleshooting_info()
            System.halt(1)
        end

      {:error, {app, reason}} ->
        IO.puts("❌ Failed to start application #{app}: #{inspect(reason)}")
        System.halt(1)

      {:error, reason} ->
        IO.puts("❌ Failed to start application: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  defp check_mcp_supervisor() do
    IO.puts("🔍 Checking MCP Supervisor...")
    case Process.whereis(ServiceManager.MCP.Supervisor) do
      nil ->
        IO.puts("❌ MCP Supervisor is not running!")
      pid ->
        IO.puts("✅ MCP Supervisor is running (PID: #{inspect(pid)})")

        # Check children
        children = Supervisor.which_children(ServiceManager.MCP.Supervisor)
        IO.puts("👥 MCP Supervisor children: #{length(children)}")

        Enum.each(children, fn {id, child_pid, type, _modules} ->
          status = if is_pid(child_pid) and Process.alive?(child_pid), do: "✅", else: "❌"
          IO.puts("   #{status} #{id} (#{type}): #{inspect(child_pid)}")
        end)
    end
  end

  defp wait_for_mcp_server(retries) when retries > 0 do
    IO.puts("🔍 Checking MCP server status... (#{retries} retries left)")

    case ServiceManager.MCP.Server.get_server_info() do
      {:ok, info} ->
        IO.puts("✅ MCP Server is running")
        IO.puts("   Name: #{info.name}")
        IO.puts("   Version: #{info.version}")
        IO.puts("   Status: #{info.status}")
        {:ok, info}

      {:error, reason} ->
        IO.puts("⏳ MCP server not ready: #{inspect(reason)}")
        Process.sleep(1000)
        wait_for_mcp_server(retries - 1)
    end
  end

  defp wait_for_mcp_server(0) do
    {:error, "MCP server failed to start after multiple retries"}
  end

  defp print_troubleshooting_info() do
    IO.puts("\n" <> ("=" |> String.duplicate(60)))
    IO.puts("🔧 TROUBLESHOOTING INFORMATION")
    IO.puts("=" |> String.duplicate(60))
    IO.puts("1. Check if the database is running and accessible")
    IO.puts("2. Verify MCP server configuration is correct")
    IO.puts("3. Check application logs for detailed error messages")
    IO.puts("4. Ensure all required dependencies are available")
    IO.puts("5. Try running: mix deps.get && mix compile")
    IO.puts("\nFor more help, check the application startup logs above.")
  end
  
  defp print_connection_info() do
    config_raw = Application.get_env(:service_manager, :mcp_server, %{})
    config = case config_raw do
      c when is_list(c) -> Enum.into(c, %{})
      c when is_map(c) -> c
      _ -> %{}
    end

    host = Map.get(config, :host, "localhost")
    port = Map.get(config, :port, 8080)
    
    IO.puts("\n" <> ("=" |> String.duplicate(50)))
    IO.puts("MCP SERVER CONNECTION INFORMATION")
    IO.puts("=" |> String.duplicate(50))
    IO.puts("Host: #{host}")
    IO.puts("Port: #{port}")
    IO.puts("URL: http://#{host}:#{port}")
    IO.puts("")
    IO.puts("Available Resources:")
    
    case ServiceManager.MCP.Server.list_resources() do
      {:ok, resources} ->
        Enum.each(resources, fn resource ->
          IO.puts("  • #{resource.name} - #{resource.uri}")
        end)
      _ -> 
        IO.puts("  (Unable to list resources)")
    end
    
    IO.puts("\nAvailable Tools:")
    case ServiceManager.MCP.Server.list_tools() do
      {:ok, tools} ->
        Enum.take(tools, 5)
        |> Enum.each(fn tool ->
          IO.puts("  • #{tool.name} - #{tool.description}")
        end)
        
        if length(tools) > 5 do
          IO.puts("  ... and #{length(tools) - 5} more tools")
        end
      _ -> 
        IO.puts("  (Unable to list tools)")
    end
    
    IO.puts("\nMCP Client Configuration Example:")
    IO.puts("""
    {
      "mcpServers": {
        "dynamic-forms": {
          "command": "elixir",
          "args": ["scripts/start_mcp_server.exs"],
          "cwd": "#{File.cwd!()}",
          "env": {
            "MIX_ENV": "dev"
          }
        }
      }
    }
    """)
    
    IO.puts("=" |> String.duplicate(50))
  end
end

# Start the server
MCPServerStarter.start()