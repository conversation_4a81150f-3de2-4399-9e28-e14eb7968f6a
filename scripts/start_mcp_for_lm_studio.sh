#!/bin/bash

# MCP Server startup script for LM Studio
# This script sets up the environment and starts the MCP stdio server

set -e

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_DIR"

# Source asdf if available
if [ -f "$HOME/.asdf/asdf.sh" ]; then
    source "$HOME/.asdf/asdf.sh"
fi

# Set environment
export MIX_ENV=dev

# Start the MCP stdio server
exec mix mcp stdio
