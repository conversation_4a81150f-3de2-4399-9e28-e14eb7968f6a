
In the controller lib/service_manager_web/controllers/api/withdraws_controller.ex there are plain fucntions


Services 1:

cardless_withdraw
cardless_wallet_withdraw

Services 2:

funds_request
funds_wallet_request

Services: 3

agent_cashout
agent_wallet_cashout

Services: 4

merchant_cashout
merchant_wallet_cashout

------------------------------------------------

resource modules:
lib/service_manager/accounts/user.ex - user schema
lib/service_manager/wallet_accounts/wallet_user.ex - user wallet schema

lib/service_manager/accounts.ex - user context
lib/service_manager/contexts/wallets_context.ex - wallet user context

lib/service_manager/schemas/transactions/transactions_schema.ex - user transactions schema
lib/service_manager/schemas/transactions/wallet_transactions_schema.ex - wallet transactions schema

lib/service_manager_web/controllers/api/services/local/cross_transfers_service.ex - cross transfer transaction service_manager
lib/service_manager_web/controllers/api/services/local/transfer_service.ex - transfer service

------------------------------------------------------------------------

using the information provided design and create schemas and migrations using the elixir command
below is the location of the empty schemas

the directory is lib/service_manager/schemas/withdraws

These schemas will be used to record transactions for each type, each schema must have a relationships using belongs to 
for wallet respective and user respective

lib/service_manager/schemas/withdraws/agent_withdraws/agent_withdraw_schema.ex
lib/service_manager/schemas/withdraws/agent_withdraws/agent_withdraw_Wallet_schema.ex
lib/service_manager/schemas/withdraws/cardless_withdraw/cardless_withdraw_schema.ex
lib/service_manager/schemas/withdraws/cardless_withdraw/cardless_withdraw_wallet_schema.ex
lib/service_manager/schemas/withdraws/fund_requests/fund_requests_schema.ex
lib/service_manager/schemas/withdraws/fund_requests/fund_requests_wallet_schema.ex
lib/service_manager/schemas/withdraws/merchant_withdraws/merchant_withdraw_schema.ex
lib/service_manager/schemas/withdraws/merchant_withdraws/merchant_withdraw_wallet_schema.ex

create respective contexts for each schema

Notifications:

lib/service_manager/notifications/sms_notification.ex is the schema used to send sms notifications

One time password controller for sending OTPs lib/service_manager_web/controllers/otp_controller.ex

Now the purpose of each

1. Cardless withdraw - This has to create a withdraw request, when the request is saved send an sms with instructions of how to withdraw
to withdraw an api must be called to settle the request and perform a transfer call, in the function add a variable to keep the suspense account

use bank to bank service in lib/service_manager_web/controllers/api/transfers_controller.ex to create the final transaction call 




