#!/usr/bin/env elixir

# Script to create authentication flow using the running MCP server
# This demonstrates real MCP server interactions

defmodule AuthFlowWithMCP do
  @moduledoc """
  Creates a complete authentication flow by making calls to the running MCP server.
  This demonstrates the full MCP workflow for building banking authentication.
  """
  
  def create_complete_auth_flow() do
    IO.puts("🔐 Creating Complete Authentication Flow with MCP Server")
    IO.puts("=" |> String.duplicate(70))
    
    # Start the application to access MCP server
    case Application.ensure_all_started(:service_manager) do
      {:ok, _} ->
        IO.puts("✅ Connected to ServiceManager application")
        
        # Wait for MCP server to be ready
        Process.sleep(2000)
        
        # Execute the complete authentication flow creation
        execute_auth_flow_creation()
        
      {:error, reason} ->
        IO.puts("❌ Failed to connect to application: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  defp execute_auth_flow_creation() do
    IO.puts("\n🚀 Starting Authentication Flow Creation...")
    
    # Step 1: Use AI to design the authentication endpoints
    design_auth_endpoints()
    
    # Step 2: Create the authentication routes
    create_auth_routes()
    
    # Step 3: Create validation forms
    create_auth_forms()
    
    # Step 4: Create business logic plugins
    create_auth_plugins()
    
    # Step 5: Link everything together
    link_auth_components()
    
    # Step 6: Verify the complete flow
    verify_auth_flow()
    
    # Step 7: Show the final result
    show_auth_flow_summary()
  end
  
  defp design_auth_endpoints() do
    IO.puts("\n💡 Step 1: Designing Authentication Endpoints with AI...")
    
    # Use the route_designer prompt for login endpoint
    login_design_params = %{
      "purpose" => "User authentication with JWT access_token generation",
      "data_type" => "username/email and password credentials",
      "http_method" => "POST",
      "security_requirements" => "High security - handles sensitive user credentials",
      "response_format" => "JSON with access_token, refresh_token, and user info",
      "banking_compliance" => "Must include audit logging and rate limiting"
    }
    
    case ServiceManager.MCP.Prompts.get_prompt("route_designer", login_design_params) do
      {:ok, design_result} ->
        IO.puts("✅ Login endpoint design generated")
        IO.puts("📝 AI Recommendation: #{String.slice(design_result.prompt, 0, 200)}...")
        
      {:error, reason} ->
        IO.puts("❌ Failed to generate login design: #{inspect(reason)}")
    end
    
    # Use workflow_creator for complete auth flow
    workflow_params = %{
      "workflow_type" => "authentication",
      "components" => "login, token refresh, logout, validation",
      "security_level" => "banking_grade",
      "integration_points" => "JWT tokens, database, audit logs"
    }
    
    case ServiceManager.MCP.Prompts.get_prompt("workflow_creator", workflow_params) do
      {:ok, workflow_result} ->
        IO.puts("✅ Complete authentication workflow designed")
        IO.puts("📋 Workflow includes: #{length(workflow_result.context["steps"] || [])} steps")
        
      {:error, reason} ->
        IO.puts("❌ Failed to generate workflow: #{inspect(reason)}")
    end
  end
  
  defp create_auth_routes() do
    IO.puts("\n🛣️  Step 2: Creating Authentication Routes...")
    
    # Create login route
    login_route_params = %{
      "name" => "auth_login",
      "method" => "POST",
      "path" => "/api/v1/auth/login",
      "category" => "Authentication",
      "group_name" => "auth",
      "priority" => 100,
      "description" => "User login endpoint that generates JWT access_token and refresh_token",
      "tags" => ["authentication", "login", "jwt", "access_token"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", login_route_params) do
      {:ok, result} ->
        IO.puts("✅ Login route created: #{result.route.path}")
        IO.puts("   Route ID: #{result.route.id}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create login route: #{inspect(reason)}")
    end
    
    # Create token refresh route
    refresh_route_params = %{
      "name" => "auth_refresh",
      "method" => "POST",
      "path" => "/api/v1/auth/refresh",
      "category" => "Authentication",
      "group_name" => "auth",
      "priority" => 90,
      "description" => "Refresh access_token using valid refresh_token",
      "tags" => ["authentication", "refresh", "jwt", "token_renewal"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", refresh_route_params) do
      {:ok, result} ->
        IO.puts("✅ Refresh route created: #{result.route.path}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create refresh route: #{inspect(reason)}")
    end
    
    # Create logout route
    logout_route_params = %{
      "name" => "auth_logout",
      "method" => "POST",
      "path" => "/api/v1/auth/logout",
      "category" => "Authentication",
      "group_name" => "auth",
      "priority" => 80,
      "description" => "User logout endpoint that invalidates access_token and refresh_token",
      "tags" => ["authentication", "logout", "token_invalidation"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", logout_route_params) do
      {:ok, result} ->
        IO.puts("✅ Logout route created: #{result.route.path}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create logout route: #{inspect(reason)}")
    end
    
    # Create token validation route
    validate_route_params = %{
      "name" => "auth_validate",
      "method" => "GET",
      "path" => "/api/v1/auth/validate",
      "category" => "Authentication",
      "group_name" => "auth",
      "priority" => 70,
      "description" => "Validate access_token and return user information",
      "tags" => ["authentication", "validation", "token_check"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", validate_route_params) do
      {:ok, result} ->
        IO.puts("✅ Validation route created: #{result.route.path}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create validation route: #{inspect(reason)}")
    end
  end
  
  defp create_auth_forms() do
    IO.puts("\n📋 Step 3: Creating Authentication Forms...")
    
    # Use form_builder AI to design login form
    login_form_design = %{
      "api_purpose" => "User authentication with credential validation",
      "data_fields" => "username/email, password, remember_me",
      "validation_level" => "strict",
      "security_requirements" => "banking_grade",
      "field_types" => "email validation, password strength, boolean flag"
    }
    
    case ServiceManager.MCP.Prompts.get_prompt("form_builder", login_form_design) do
      {:ok, form_design} ->
        IO.puts("✅ Login form design generated by AI")
        
      {:error, reason} ->
        IO.puts("❌ Failed to generate form design: #{inspect(reason)}")
    end
    
    # Create login form
    login_form_params = %{
      "name" => "login_form",
      "http_method" => "POST",
      "description" => "User login form with comprehensive validation",
      "schema" => %{
        "type" => "object",
        "required" => ["username", "password"],
        "properties" => %{
          "username" => %{
            "type" => "string",
            "format" => "email",
            "minLength" => 3,
            "maxLength" => 100,
            "description" => "User email address or username"
          },
          "password" => %{
            "type" => "string",
            "minLength" => 8,
            "maxLength" => 128,
            "description" => "User password (minimum 8 characters)"
          },
          "remember_me" => %{
            "type" => "boolean",
            "default" => false,
            "description" => "Extended session duration flag"
          },
          "device_id" => %{
            "type" => "string",
            "description" => "Device identifier for security tracking"
          }
        }
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_form", login_form_params) do
      {:ok, result} ->
        IO.puts("✅ Login form created: #{result.form.name}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create login form: #{inspect(reason)}")
    end
    
    # Create refresh token form
    refresh_form_params = %{
      "name" => "refresh_form",
      "http_method" => "POST",
      "description" => "Token refresh form with refresh_token validation",
      "schema" => %{
        "type" => "object",
        "required" => ["refresh_token"],
        "properties" => %{
          "refresh_token" => %{
            "type" => "string",
            "minLength" => 20,
            "description" => "Valid JWT refresh token"
          }
        }
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_form", refresh_form_params) do
      {:ok, result} ->
        IO.puts("✅ Refresh form created: #{result.form.name}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create refresh form: #{inspect(reason)}")
    end
  end
  
  defp create_auth_plugins() do
    IO.puts("\n🔌 Step 4: Creating Authentication Business Logic Plugins...")
    
    # Use plugin_generator AI to design authentication logic
    login_plugin_design = %{
      "functionality" => "User authentication with JWT token generation",
      "input_data" => "username, password, device_id, remember_me",
      "output_data" => "access_token, refresh_token, user_info, expires_in",
      "external_apis" => "Database user lookup, JWT generation, audit logging",
      "security_features" => "Password hashing, rate limiting, device tracking"
    }
    
    case ServiceManager.MCP.Prompts.get_prompt("plugin_generator", login_plugin_design) do
      {:ok, plugin_design} ->
        IO.puts("✅ Login plugin design generated by AI")
        
      {:error, reason} ->
        IO.puts("❌ Failed to generate plugin design: #{inspect(reason)}")
    end
    
    # Create login processor plugin
    login_plugin_params = %{
      "name" => "auth_login_processor",
      "category" => "authentication",
      "type" => "processor",
      "description" => "Processes user login and generates JWT access_token and refresh_token",
      "code" => generate_login_plugin_code(),
      "input_schema" => %{
        "username" => "string",
        "password" => "string",
        "remember_me" => "boolean",
        "device_id" => "string"
      },
      "output_schema" => %{
        "access_token" => "string",
        "refresh_token" => "string",
        "expires_in" => "integer",
        "user_id" => "string",
        "user_info" => "object"
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_plugin", login_plugin_params) do
      {:ok, result} ->
        IO.puts("✅ Login processor plugin created: #{result.plugin.name}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create login plugin: #{inspect(reason)}")
    end
    
    # Create token validator plugin
    validator_plugin_params = %{
      "name" => "auth_token_validator",
      "category" => "authentication",
      "type" => "validator",
      "description" => "Validates JWT access_token and extracts user information",
      "code" => generate_validator_plugin_code(),
      "input_schema" => %{
        "access_token" => "string"
      },
      "output_schema" => %{
        "valid" => "boolean",
        "user_id" => "string",
        "user_info" => "object",
        "expires_at" => "datetime"
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_plugin", validator_plugin_params) do
      {:ok, result} ->
        IO.puts("✅ Token validator plugin created: #{result.plugin.name}")
        
      {:error, reason} ->
        IO.puts("❌ Failed to create validator plugin: #{inspect(reason)}")
    end
  end
  
  defp link_auth_components() do
    IO.puts("\n🔗 Step 5: Linking Authentication Components...")
    
    # Link login route to login form
    case ServiceManager.MCP.Tools.call_tool("link_route_form", %{
      "route_name" => "auth_login",
      "form_name" => "login_form"
    }) do
      {:ok, _result} ->
        IO.puts("✅ Login route linked to login form")
      {:error, reason} ->
        IO.puts("❌ Failed to link login route to form: #{inspect(reason)}")
    end
    
    # Link login route to login processor
    case ServiceManager.MCP.Tools.call_tool("link_route_plugin", %{
      "route_name" => "auth_login",
      "plugin_name" => "auth_login_processor"
    }) do
      {:ok, _result} ->
        IO.puts("✅ Login route linked to login processor")
      {:error, reason} ->
        IO.puts("❌ Failed to link login route to processor: #{inspect(reason)}")
    end
    
    # Link validation route to token validator
    case ServiceManager.MCP.Tools.call_tool("link_route_plugin", %{
      "route_name" => "auth_validate",
      "plugin_name" => "auth_token_validator"
    }) do
      {:ok, _result} ->
        IO.puts("✅ Validation route linked to token validator")
      {:error, reason} ->
        IO.puts("❌ Failed to link validation route to validator: #{inspect(reason)}")
    end
  end
  
  defp verify_auth_flow() do
    IO.puts("\n🔍 Step 6: Verifying Complete Authentication Flow...")
    
    # Get all authentication routes
    case ServiceManager.MCP.Resources.read_resource("mcp://dynamic-forms/routes") do
      {:ok, routes_resource} ->
        auth_routes = Enum.filter(routes_resource.data || [], fn route ->
          String.contains?(route["category"] || "", "Authentication")
        end)
        
        IO.puts("✅ Found #{length(auth_routes)} authentication routes")
        
      {:error, reason} ->
        IO.puts("❌ Failed to verify routes: #{inspect(reason)}")
    end
    
    # Get all authentication forms
    case ServiceManager.MCP.Resources.read_resource("mcp://dynamic-forms/forms") do
      {:ok, forms_resource} ->
        auth_forms = Enum.filter(forms_resource.data || [], fn form ->
          String.contains?(form["name"] || "", "auth") or 
          String.contains?(form["name"] || "", "login") or
          String.contains?(form["name"] || "", "refresh")
        end)
        
        IO.puts("✅ Found #{length(auth_forms)} authentication forms")
        
      {:error, reason} ->
        IO.puts("❌ Failed to verify forms: #{inspect(reason)}")
    end
    
    # Get all authentication plugins
    case ServiceManager.MCP.Resources.read_resource("mcp://dynamic-forms/plugins") do
      {:ok, plugins_resource} ->
        auth_plugins = Enum.filter(plugins_resource.data || [], fn plugin ->
          String.contains?(plugin["category"] || "", "authentication")
        end)
        
        IO.puts("✅ Found #{length(auth_plugins)} authentication plugins")
        
      {:error, reason} ->
        IO.puts("❌ Failed to verify plugins: #{inspect(reason)}")
    end
  end
  
  defp show_auth_flow_summary() do
    IO.puts("\n" <> ("=" |> String.duplicate(70)))
    IO.puts("🎉 AUTHENTICATION FLOW CREATION COMPLETE!")
    IO.puts("=" |> String.duplicate(70))
    IO.puts("✅ Created complete access_token-based authentication system:")
    IO.puts("")
    IO.puts("🛣️  AUTHENTICATION ENDPOINTS:")
    IO.puts("   • POST /api/v1/auth/login    - User login with access_token generation")
    IO.puts("   • POST /api/v1/auth/refresh  - Refresh access_token using refresh_token")
    IO.puts("   • POST /api/v1/auth/logout   - Logout and invalidate tokens")
    IO.puts("   • GET  /api/v1/auth/validate - Validate access_token and get user info")
    IO.puts("")
    IO.puts("📋 VALIDATION FORMS:")
    IO.puts("   • login_form    - Username/password validation with security features")
    IO.puts("   • refresh_form  - Refresh token validation")
    IO.puts("")
    IO.puts("🔌 BUSINESS LOGIC PLUGINS:")
    IO.puts("   • auth_login_processor  - Handles authentication and JWT generation")
    IO.puts("   • auth_token_validator  - Validates tokens and extracts user info")
    IO.puts("")
    IO.puts("🔗 COMPONENT LINKS:")
    IO.puts("   • Login route ↔ Login form ↔ Login processor")
    IO.puts("   • Validation route ↔ Token validator")
    IO.puts("")
    IO.puts("🔐 AUTHENTICATION FLOW USAGE:")
    IO.puts("=" |> String.duplicate(70))
    IO.puts("1. LOGIN:")
    IO.puts("   POST /api/v1/auth/login")
    IO.puts("   {\"username\": \"<EMAIL>\", \"password\": \"securepass123\"}")
    IO.puts("   → Returns: {\"access_token\": \"jwt...\", \"refresh_token\": \"jwt...\"}")
    IO.puts("")
    IO.puts("2. USE ACCESS TOKEN:")
    IO.puts("   Authorization: Bearer <access_token>")
    IO.puts("   → Include in all authenticated API requests")
    IO.puts("")
    IO.puts("3. REFRESH TOKEN:")
    IO.puts("   POST /api/v1/auth/refresh")
    IO.puts("   {\"refresh_token\": \"jwt...\"}")
    IO.puts("   → Returns: {\"access_token\": \"new_jwt...\"}")
    IO.puts("")
    IO.puts("4. VALIDATE TOKEN:")
    IO.puts("   GET /api/v1/auth/validate")
    IO.puts("   Authorization: Bearer <access_token>")
    IO.puts("   → Returns: {\"valid\": true, \"user_info\": {...}}")
    IO.puts("")
    IO.puts("🚀 Authentication system is ready for banking operations!")
  end
  
  defp generate_login_plugin_code() do
    """
    defmodule AuthLoginProcessor do
      @moduledoc "Processes user login and generates JWT access tokens for banking system"
      
      def process(params) do
        with {:ok, user} <- authenticate_user(params["username"], params["password"]),
             {:ok, tokens} <- generate_jwt_tokens(user, params["remember_me"]),
             :ok <- log_authentication_event(user, params["device_id"]) do
          {:ok, %{
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token,
            expires_in: tokens.expires_in,
            user_id: user.id,
            user_info: %{
              username: user.username,
              email: user.email,
              roles: user.roles,
              last_login: DateTime.utc_now()
            }
          }}
        else
          {:error, :invalid_credentials} ->
            {:error, %{message: "Invalid username or password", code: "AUTH_001"}}
          {:error, :account_locked} ->
            {:error, %{message: "Account is locked", code: "AUTH_002"}}
          {:error, :account_disabled} ->
            {:error, %{message: "Account is disabled", code: "AUTH_003"}}
          error ->
            {:error, %{message: "Authentication failed", details: error}}
        end
      end
      
      defp authenticate_user(username, password) do
        # Banking-grade authentication with security features
        ServiceManager.Authentication.authenticate_with_security(username, password)
      end
      
      defp generate_jwt_tokens(user, remember_me \\\\ false) do
        expires_in = if remember_me, do: 7 * 24 * 3600, else: 8 * 3600  # 7 days or 8 hours
        
        access_claims = %{
          user_id: user.id,
          username: user.username,
          roles: user.roles,
          permissions: user.permissions,
          exp: System.system_time(:second) + expires_in,
          iat: System.system_time(:second),
          iss: "ServiceManager Banking",
          aud: "banking_api"
        }
        
        refresh_claims = %{
          user_id: user.id,
          type: "refresh",
          exp: System.system_time(:second) + (30 * 24 * 3600),  # 30 days
          iat: System.system_time(:second)
        }
        
        with {:ok, access_token} <- ServiceManager.JWT.generate_token(access_claims),
             {:ok, refresh_token} <- ServiceManager.JWT.generate_token(refresh_claims) do
          {:ok, %{
            access_token: access_token,
            refresh_token: refresh_token,
            expires_in: expires_in
          }}
        end
      end
      
      defp log_authentication_event(user, device_id) do
        ServiceManager.AuditLog.log_event(%{
          event_type: "user_login",
          user_id: user.id,
          device_id: device_id,
          timestamp: DateTime.utc_now(),
          ip_address: get_client_ip(),
          success: true
        })
      end
      
      defp get_client_ip() do
        # Extract client IP from request context
        "127.0.0.1"  # Placeholder
      end
    end
    """
  end
  
  defp generate_validator_plugin_code() do
    """
    defmodule AuthTokenValidator do
      @moduledoc "Validates JWT access tokens and extracts user information"
      
      def process(params) do
        access_token = params["access_token"]
        
        with {:ok, claims} <- ServiceManager.JWT.verify_token(access_token),
             {:ok, user} <- get_current_user_info(claims["user_id"]),
             :ok <- check_token_validity(claims) do
          {:ok, %{
            valid: true,
            user_id: claims["user_id"],
            user_info: %{
              username: user.username,
              email: user.email,
              roles: claims["roles"],
              permissions: claims["permissions"]
            },
            expires_at: DateTime.from_unix!(claims["exp"]),
            issued_at: DateTime.from_unix!(claims["iat"])
          }}
        else
          {:error, :token_expired} ->
            {:ok, %{valid: false, reason: "Token expired", code: "TOKEN_001"}}
          {:error, :invalid_signature} ->
            {:ok, %{valid: false, reason: "Invalid token signature", code: "TOKEN_002"}}
          {:error, :user_not_found} ->
            {:ok, %{valid: false, reason: "User not found", code: "TOKEN_003"}}
          {:error, :user_disabled} ->
            {:ok, %{valid: false, reason: "User account disabled", code: "TOKEN_004"}}
          error ->
            {:error, %{message: "Token validation failed", details: error}}
        end
      end
      
      defp get_current_user_info(user_id) do
        case ServiceManager.Accounts.get_user(user_id) do
          %{status: "active"} = user -> {:ok, user}
          %{status: "disabled"} -> {:error, :user_disabled}
          nil -> {:error, :user_not_found}
        end
      end
      
      defp check_token_validity(claims) do
        current_time = System.system_time(:second)
        
        cond do
          claims["exp"] <= current_time -> {:error, :token_expired}
          claims["iss"] != "ServiceManager Banking" -> {:error, :invalid_issuer}
          true -> :ok
        end
      end
    end
    """
  end
end

# Configure Mix for the project
Mix.install([
  {:service_manager, path: "."}
])

# Create the complete authentication flow
AuthFlowWithMCP.create_complete_auth_flow()
