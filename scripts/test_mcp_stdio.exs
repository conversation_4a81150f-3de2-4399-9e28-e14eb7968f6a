#!/usr/bin/env elixir

# Test script for MCP stdio server
# Usage: elixir scripts/test_mcp_stdio.exs

defmodule MCPStdioTester do
  @moduledoc """
  Test script to verify the MCP stdio server works correctly.
  """
  
  def test() do
    IO.puts("🧪 Testing MCP Stdio Server...")
    IO.puts("=" |> String.duplicate(50))
    
    # Start the application
    case Application.ensure_all_started(:service_manager) do
      {:ok, _} ->
        IO.puts("✅ Application started")
        
        # Wait for components
        Process.sleep(3000)
        
        # Test MCP components directly
        test_mcp_components()
        
      {:error, reason} ->
        IO.puts("❌ Failed to start application: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  defp test_mcp_components() do
    IO.puts("\n🔍 Testing MCP Components...")
    
    # Test Server
    case ServiceManager.MCP.Server.get_server_info() do
      {:ok, info} ->
        IO.puts("✅ MCP Server: #{info.name} v#{info.version}")
      {:error, reason} ->
        IO.puts("❌ MCP Server failed: #{inspect(reason)}")
        return
    end
    
    # Test Resources
    case ServiceManager.MCP.Resources.list_all() do
      {:ok, resources} ->
        IO.puts("✅ MCP Resources: #{length(resources)} available")
      {:error, reason} ->
        IO.puts("❌ MCP Resources failed: #{inspect(reason)}")
    end
    
    # Test Tools
    case ServiceManager.MCP.Tools.list_all() do
      {:ok, tools} ->
        IO.puts("✅ MCP Tools: #{length(tools)} available")
      {:error, reason} ->
        IO.puts("❌ MCP Tools failed: #{inspect(reason)}")
    end
    
    # Test Prompts
    case ServiceManager.MCP.Prompts.list_all() do
      {:ok, prompts} ->
        IO.puts("✅ MCP Prompts: #{length(prompts)} available")
      {:error, reason} ->
        IO.puts("❌ MCP Prompts failed: #{inspect(reason)}")
    end
    
    IO.puts("\n🎉 MCP Components test completed!")
    IO.puts("\n💡 To test with LM Studio, use:")
    IO.puts("   elixir scripts/mcp_stdio_server.exs")
  end
end

# Configure Mix for the project
Mix.install([
  {:service_manager, path: "."}
])

# Run the test
MCPStdioTester.test()
