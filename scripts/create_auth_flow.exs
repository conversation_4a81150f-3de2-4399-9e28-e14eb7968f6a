#!/usr/bin/env elixir

# Script to create a complete authentication flow using MCP tools
# This demonstrates using the MCP server to design and implement auth endpoints

defmodule AuthFlowCreator do
  @moduledoc """
  Creates a complete authentication flow using the MCP server tools.
  This includes login, token refresh, logout, and token validation endpoints.
  """
  
  def create_auth_flow() do
    IO.puts("🔐 Creating Access Token Authentication Flow using MCP")
    IO.puts("=" |> String.duplicate(60))
    
    # Start the application
    case Application.ensure_all_started(:service_manager) do
      {:ok, _} ->
        IO.puts("✅ ServiceManager application started")
        
        # Wait for MCP components
        Process.sleep(3000)
        
        # Create the authentication flow
        create_authentication_endpoints()
        
      {:error, reason} ->
        IO.puts("❌ Failed to start application: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  defp create_authentication_endpoints() do
    IO.puts("\n🚀 Creating Authentication Endpoints...")
    
    # 1. Design login endpoint
    design_login_endpoint()
    
    # 2. Design token refresh endpoint  
    design_refresh_endpoint()
    
    # 3. Design logout endpoint
    design_logout_endpoint()
    
    # 4. Design token validation endpoint
    design_validation_endpoint()
    
    # 5. Create authentication forms
    create_auth_forms()
    
    # 6. Create authentication plugins
    create_auth_plugins()
    
    # 7. Link everything together
    link_auth_components()
    
    IO.puts("\n🎉 Authentication flow created successfully!")
    print_auth_flow_summary()
  end
  
  defp design_login_endpoint() do
    IO.puts("\n📝 Designing Login Endpoint...")
    
    # Use route_designer prompt
    prompt_args = %{
      "purpose" => "User authentication with access_token generation",
      "data_type" => "user credentials (username/email, password)",
      "http_method" => "POST",
      "security_requirements" => "High - handles sensitive credentials",
      "response_format" => "JSON with access_token and refresh_token"
    }
    
    case ServiceManager.MCP.Prompts.get_prompt("route_designer", prompt_args) do
      {:ok, prompt_result} ->
        IO.puts("✅ Login endpoint design generated")
        IO.puts("Route suggestion: #{extract_route_suggestion(prompt_result.prompt)}")
        
        # Create the actual route
        create_login_route()
        
      {:error, reason} ->
        IO.puts("❌ Failed to generate login design: #{inspect(reason)}")
    end
  end
  
  defp create_login_route() do
    route_params = %{
      "name" => "auth_login",
      "method" => "POST", 
      "path" => "/api/v1/auth/login",
      "category" => "Authentication",
      "group_name" => "auth",
      "priority" => 100,
      "description" => "User login endpoint that returns access_token and refresh_token",
      "tags" => ["authentication", "login", "access_token"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", route_params) do
      {:ok, result} ->
        IO.puts("✅ Login route created: #{result.route.path}")
      {:error, reason} ->
        IO.puts("❌ Failed to create login route: #{inspect(reason)}")
    end
  end
  
  defp design_refresh_endpoint() do
    IO.puts("\n🔄 Designing Token Refresh Endpoint...")
    
    prompt_args = %{
      "purpose" => "Refresh access_token using refresh_token",
      "data_type" => "refresh_token",
      "http_method" => "POST",
      "security_requirements" => "High - token refresh mechanism",
      "response_format" => "JSON with new access_token"
    }
    
    case ServiceManager.MCP.Prompts.get_prompt("route_designer", prompt_args) do
      {:ok, _prompt_result} ->
        IO.puts("✅ Refresh endpoint design generated")
        create_refresh_route()
      {:error, reason} ->
        IO.puts("❌ Failed to generate refresh design: #{inspect(reason)}")
    end
  end
  
  defp create_refresh_route() do
    route_params = %{
      "name" => "auth_refresh",
      "method" => "POST",
      "path" => "/api/v1/auth/refresh", 
      "category" => "Authentication",
      "group_name" => "auth",
      "priority" => 90,
      "description" => "Refresh access_token using valid refresh_token",
      "tags" => ["authentication", "refresh", "access_token"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", route_params) do
      {:ok, result} ->
        IO.puts("✅ Refresh route created: #{result.route.path}")
      {:error, reason} ->
        IO.puts("❌ Failed to create refresh route: #{inspect(reason)}")
    end
  end
  
  defp design_logout_endpoint() do
    IO.puts("\n🚪 Designing Logout Endpoint...")
    
    route_params = %{
      "name" => "auth_logout",
      "method" => "POST",
      "path" => "/api/v1/auth/logout",
      "category" => "Authentication", 
      "group_name" => "auth",
      "priority" => 80,
      "description" => "User logout endpoint that invalidates access_token and refresh_token",
      "tags" => ["authentication", "logout", "token_invalidation"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", route_params) do
      {:ok, result} ->
        IO.puts("✅ Logout route created: #{result.route.path}")
      {:error, reason} ->
        IO.puts("❌ Failed to create logout route: #{inspect(reason)}")
    end
  end
  
  defp design_validation_endpoint() do
    IO.puts("\n🔍 Designing Token Validation Endpoint...")
    
    route_params = %{
      "name" => "auth_validate",
      "method" => "GET",
      "path" => "/api/v1/auth/validate",
      "category" => "Authentication",
      "group_name" => "auth", 
      "priority" => 70,
      "description" => "Validate access_token and return user information",
      "tags" => ["authentication", "validation", "access_token"],
      "enabled" => true
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_route", route_params) do
      {:ok, result} ->
        IO.puts("✅ Validation route created: #{result.route.path}")
      {:error, reason} ->
        IO.puts("❌ Failed to create validation route: #{inspect(reason)}")
    end
  end
  
  defp create_auth_forms() do
    IO.puts("\n📋 Creating Authentication Forms...")
    
    # Login form
    login_form_params = %{
      "name" => "login_form",
      "http_method" => "POST",
      "description" => "User login form with credential validation",
      "schema" => %{
        "type" => "object",
        "required" => ["username", "password"],
        "properties" => %{
          "username" => %{
            "type" => "string",
            "minLength" => 3,
            "maxLength" => 50,
            "description" => "Username or email address"
          },
          "password" => %{
            "type" => "string", 
            "minLength" => 8,
            "description" => "User password"
          },
          "remember_me" => %{
            "type" => "boolean",
            "default" => false,
            "description" => "Extended session duration"
          }
        }
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_form", login_form_params) do
      {:ok, _result} ->
        IO.puts("✅ Login form created")
      {:error, reason} ->
        IO.puts("❌ Failed to create login form: #{inspect(reason)}")
    end
    
    # Refresh form
    refresh_form_params = %{
      "name" => "refresh_form",
      "http_method" => "POST", 
      "description" => "Token refresh form",
      "schema" => %{
        "type" => "object",
        "required" => ["refresh_token"],
        "properties" => %{
          "refresh_token" => %{
            "type" => "string",
            "minLength" => 10,
            "description" => "Valid refresh token"
          }
        }
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_form", refresh_form_params) do
      {:ok, _result} ->
        IO.puts("✅ Refresh form created")
      {:error, reason} ->
        IO.puts("❌ Failed to create refresh form: #{inspect(reason)}")
    end
  end
  
  defp create_auth_plugins() do
    IO.puts("\n🔌 Creating Authentication Plugins...")
    
    # Login plugin
    login_plugin_params = %{
      "name" => "auth_login_processor",
      "category" => "authentication",
      "type" => "processor",
      "description" => "Processes user login and generates access_token",
      "code" => generate_login_plugin_code(),
      "input_schema" => %{
        "username" => "string",
        "password" => "string", 
        "remember_me" => "boolean"
      },
      "output_schema" => %{
        "access_token" => "string",
        "refresh_token" => "string",
        "expires_in" => "integer",
        "user_id" => "string"
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_plugin", login_plugin_params) do
      {:ok, _result} ->
        IO.puts("✅ Login plugin created")
      {:error, reason} ->
        IO.puts("❌ Failed to create login plugin: #{inspect(reason)}")
    end
    
    # Token validation plugin
    validation_plugin_params = %{
      "name" => "auth_token_validator",
      "category" => "authentication",
      "type" => "validator",
      "description" => "Validates access_token and extracts user information",
      "code" => generate_validation_plugin_code(),
      "input_schema" => %{
        "access_token" => "string"
      },
      "output_schema" => %{
        "valid" => "boolean",
        "user_id" => "string",
        "expires_at" => "datetime"
      }
    }
    
    case ServiceManager.MCP.Tools.call_tool("create_plugin", validation_plugin_params) do
      {:ok, _result} ->
        IO.puts("✅ Validation plugin created")
      {:error, reason} ->
        IO.puts("❌ Failed to create validation plugin: #{inspect(reason)}")
    end
  end
  
  defp link_auth_components() do
    IO.puts("\n🔗 Linking Authentication Components...")
    
    # Link login route to login form
    case ServiceManager.MCP.Tools.call_tool("link_route_form", %{
      "route_name" => "auth_login",
      "form_name" => "login_form"
    }) do
      {:ok, _result} ->
        IO.puts("✅ Login route linked to login form")
      {:error, reason} ->
        IO.puts("❌ Failed to link login components: #{inspect(reason)}")
    end
    
    # Link login route to login plugin
    case ServiceManager.MCP.Tools.call_tool("link_route_plugin", %{
      "route_name" => "auth_login", 
      "plugin_name" => "auth_login_processor"
    }) do
      {:ok, _result} ->
        IO.puts("✅ Login route linked to login plugin")
      {:error, reason} ->
        IO.puts("❌ Failed to link login route to plugin: #{inspect(reason)}")
    end
    
    # Link validation route to validation plugin
    case ServiceManager.MCP.Tools.call_tool("link_route_plugin", %{
      "route_name" => "auth_validate",
      "plugin_name" => "auth_token_validator"
    }) do
      {:ok, _result} ->
        IO.puts("✅ Validation route linked to validation plugin")
      {:error, reason} ->
        IO.puts("❌ Failed to link validation components: #{inspect(reason)}")
    end
  end
  
  defp generate_login_plugin_code() do
    """
    defmodule AuthLoginProcessor do
      @moduledoc "Processes user login and generates JWT access tokens"
      
      def process(params) do
        with {:ok, user} <- authenticate_user(params["username"], params["password"]),
             {:ok, tokens} <- generate_tokens(user, params["remember_me"]) do
          {:ok, %{
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token,
            expires_in: tokens.expires_in,
            user_id: user.id,
            message: "Login successful"
          }}
        else
          {:error, :invalid_credentials} ->
            {:error, %{message: "Invalid username or password"}}
          {:error, :user_disabled} ->
            {:error, %{message: "Account is disabled"}}
          error ->
            {:error, %{message: "Authentication failed", details: error}}
        end
      end
      
      defp authenticate_user(username, password) do
        # Implement user authentication logic
        # This would typically:
        # 1. Find user by username/email
        # 2. Verify password hash
        # 3. Check account status
        # 4. Log authentication attempt
        ServiceManager.Authentication.authenticate(username, password)
      end
      
      defp generate_tokens(user, remember_me \\\\ false) do
        expires_in = if remember_me, do: 7 * 24 * 3600, else: 24 * 3600
        
        access_token_claims = %{
          user_id: user.id,
          username: user.username,
          roles: user.roles,
          exp: System.system_time(:second) + expires_in
        }
        
        refresh_token_claims = %{
          user_id: user.id,
          type: "refresh",
          exp: System.system_time(:second) + (30 * 24 * 3600) # 30 days
        }
        
        with {:ok, access_token} <- ServiceManager.JWT.generate_token(access_token_claims),
             {:ok, refresh_token} <- ServiceManager.JWT.generate_token(refresh_token_claims) do
          {:ok, %{
            access_token: access_token,
            refresh_token: refresh_token,
            expires_in: expires_in
          }}
        end
      end
    end
    """
  end
  
  defp generate_validation_plugin_code() do
    """
    defmodule AuthTokenValidator do
      @moduledoc "Validates JWT access tokens and extracts user information"
      
      def process(params) do
        access_token = params["access_token"]
        
        with {:ok, claims} <- ServiceManager.JWT.verify_token(access_token),
             {:ok, user} <- get_user_info(claims["user_id"]) do
          {:ok, %{
            valid: true,
            user_id: claims["user_id"],
            username: claims["username"],
            roles: claims["roles"],
            expires_at: DateTime.from_unix!(claims["exp"])
          }}
        else
          {:error, :token_expired} ->
            {:ok, %{valid: false, reason: "Token expired"}}
          {:error, :invalid_token} ->
            {:ok, %{valid: false, reason: "Invalid token"}}
          {:error, :user_not_found} ->
            {:ok, %{valid: false, reason: "User not found"}}
          error ->
            {:error, %{message: "Token validation failed", details: error}}
        end
      end
      
      defp get_user_info(user_id) do
        # Verify user still exists and is active
        case ServiceManager.Accounts.get_user(user_id) do
          %{status: "active"} = user -> {:ok, user}
          %{status: "disabled"} -> {:error, :user_disabled}
          nil -> {:error, :user_not_found}
        end
      end
    end
    """
  end
  
  defp extract_route_suggestion(prompt_text) do
    # Extract route suggestion from the generated prompt
    # This is a simple extraction - in practice you'd parse the AI response
    "POST /api/v1/auth/login"
  end
  
  defp print_auth_flow_summary() do
    IO.puts("\n" <> ("=" |> String.duplicate(60)))
    IO.puts("🎯 AUTHENTICATION FLOW SUMMARY")
    IO.puts("=" |> String.duplicate(60))
    IO.puts("✅ Created 4 authentication endpoints:")
    IO.puts("   • POST /api/v1/auth/login - User login with access_token generation")
    IO.puts("   • POST /api/v1/auth/refresh - Refresh access_token using refresh_token")
    IO.puts("   • POST /api/v1/auth/logout - Logout and invalidate tokens")
    IO.puts("   • GET  /api/v1/auth/validate - Validate access_token")
    IO.puts("")
    IO.puts("✅ Created 2 validation forms:")
    IO.puts("   • login_form - Username/password validation")
    IO.puts("   • refresh_form - Refresh token validation")
    IO.puts("")
    IO.puts("✅ Created 2 processing plugins:")
    IO.puts("   • auth_login_processor - Handles login and token generation")
    IO.puts("   • auth_token_validator - Validates tokens and extracts user info")
    IO.puts("")
    IO.puts("✅ Linked all components together for complete workflow")
    IO.puts("")
    IO.puts("🔐 USAGE EXAMPLES:")
    IO.puts("=" |> String.duplicate(60))
    IO.puts("1. Login:")
    IO.puts("   POST /api/v1/auth/login")
    IO.puts("   {\"username\": \"<EMAIL>\", \"password\": \"password123\"}")
    IO.puts("")
    IO.puts("2. Use access_token in requests:")
    IO.puts("   Authorization: Bearer <access_token>")
    IO.puts("")
    IO.puts("3. Refresh token:")
    IO.puts("   POST /api/v1/auth/refresh")
    IO.puts("   {\"refresh_token\": \"<refresh_token>\"}")
    IO.puts("")
    IO.puts("4. Validate token:")
    IO.puts("   GET /api/v1/auth/validate")
    IO.puts("   Authorization: Bearer <access_token>")
    IO.puts("")
    IO.puts("🚀 Authentication flow is ready for use!")
  end
end

# Configure Mix for the project
Mix.install([
  {:service_manager, path: "."}
])

# Create the authentication flow
AuthFlowCreator.create_auth_flow()
