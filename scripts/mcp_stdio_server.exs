#!/usr/bin/env elixir

# Standalone MCP stdio server for LM Studio and other MCP clients
# Usage: elixir scripts/mcp_stdio_server.exs

defmodule MCPStdioStarter do
  @moduledoc """
  Standalone script to start the MCP stdio server for LM Studio.
  
  This script starts the ServiceManager application and then runs
  the MCP stdio server that communicates via stdin/stdout.
  """
  
  def start() do
    # Suppress normal application startup logs for clean stdio
    Logger.configure(level: :error)
    
    # Start the application silently
    case Application.ensure_all_started(:service_manager) do
      {:ok, _apps} ->
        # Wait a moment for initialization
        Process.sleep(2000)
        
        # Start the stdio server
        ServiceManager.MCP.StdioServer.start()
        
      {:error, {app, reason}} ->
        IO.puts(:stderr, "Failed to start application #{app}: #{inspect(reason)}")
        System.halt(1)
        
      {:error, reason} ->
        IO.puts(:stderr, "Failed to start application: #{inspect(reason)}")
        System.halt(1)
    end
  end
end

# Configure Mix for the project
Mix.install([
  {:service_manager, path: "."}
])

# Start the stdio server
MCPStdioStarter.start()
