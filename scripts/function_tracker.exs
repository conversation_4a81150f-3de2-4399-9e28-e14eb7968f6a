# # function_tracker.exs

# defmodule FunctionTracker do
#   defmacro __using__(_opts) do
#     quote do
#       import FunctionTracker, only: [track: 1]
#     end
#   end

#   defmacro track(do: {:def, meta, [{name, _, args} = fun_head, [do: body]]}) do
#     args = args || []
    
#     quote do
#       def unquote(fun_head) do
#         # Log function entry
#         timestamp = DateTime.utc_now()
#                    |> DateTime.add(2 * 3600, :second)
#                    |> Calendar.strftime("%Y-%m-%d %H:%M:%S")

#         IO.puts("\n==== Function Tracking: #{unquote(Atom.to_string(name))}/#{length(unquote(args))} at #{timestamp} ====")
#         IO.puts("Module: #{__MODULE__}")
#         IO.puts("Line: #{unquote(meta[:line])}")

#         # Log arguments
#         args = binding()
#         IO.puts("\nInput parameters:")
#         Enum.each(args, fn {name, value} ->
#           IO.puts("  #{name}: #{inspect(value)}")
#         end)

#         # Execute the function body
#         result = unquote(body)

#         # Log the result
#         IO.puts("\nResult: #{inspect(result)}")
#         IO.puts(String.duplicate("=", 50))

#         result
#       end
#     end
#   end
# end

# # Example usage of the FunctionTracker module
# defmodule ExampleModule do
#   use FunctionTracker

#   # Use the track macro to tag functions for tracking
#   track do
#     def add(a, b) do
#       a + b
#     end
#   end

#   track do
#     def greet(name) do
#       "Hello, #{name}!"
#     end
#   end
# end

# # Test the tracked functions
# IO.puts("\nTesting tracked functions:")
# ExampleModule.add(2, 3)
# ExampleModule.greet("Alice")
