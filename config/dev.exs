import Config

# Configure your database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
config :service_manager, ServiceManager.Repo,
  username: "postgres",
  password: "d377ad34ecbc3c26",
  hostname: "**************",
  database: "fdh_service_manager",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10,
  port: 5555,
  queue_target: 60_000,
  queue_interval: 5_000,
  timeout: 60_000,
  ownership_timeout: 60_000,
  connect_timeout: 10_000,
  idle_interval: 300_000,
  queue_size: 1000

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources ************************************************************************************************************.
config :service_manager, ServiceManagerWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  http: [ip: {127, 0, 0, 1}, port: String.to_integer(System.get_env("PORT") || "4000")],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "Cv1c5wsyhnmitpcZkUS8BTqlFqXNb0zo6c/Sge5ZGFkFJMeL+PSB8DDuyVQsx8h9",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:service_manager, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:service_manager, ~w(--watch)]}
  ],
  # Socket configurations
  pubsub_server: ServiceManager.PubSub,
  live_view: [
    signing_salt: "Cv1c5wsyhnmitpcZkUS8BTqlFqXNb0zo6c",
    timeout: 60_000
  ],
  socket_options: [
    backlog: 1024,
    nodelay: true,
    linger: {true, 0},
    buffer: 10_000,
    packet: :raw,
    reuseaddr: true
  ],
  transport_options: [
    socket_opts: [
      send_timeout: 30_000,
      send_timeout_close: true
    ]
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :service_manager, ServiceManagerWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/service_manager_web/(controllers|live|components)/.*(ex|heex)$"
    ]
  ]

# Enable dev routes for dashboard and mailbox
config :service_manager, dev_routes: true

# Configure logging
config :logger,
  level: :info,
  backends: [:console, {LoggerFileBackend, :info_log}, ServiceManagerWeb.LoggerBackend]

# Configure console backend
config :logger, :console,
  format: "[$level] $message\n",
  metadata: [:request_id]

# Configure file backend
config :logger, :info_log,
  path: "logs/info.log",
  format: "[$level][$date $time] $message\n",
  rotate: %{max_bytes: 52_428_800, keep: 19},
  level: :info

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  # Include HEEx debug annotations as HTML comments in rendered markup
  debug_heex_annotations: true,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false
