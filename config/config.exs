# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration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
import Config

config :service_manager, :urls,
  local: "http://localhost:4000",
  remote: "https://fdh-mockup-bank.abakula.com",
  active: System.get_env("ACTIVE_URL") || "remote"

config :service_manager,
  ecto_repos: [ServiceManager.Repo],
  generators: [timestamp_type: :utc_datetime]

config :endon,
  repo: ServiceManager.Repo

config :telegex,
  token: "**********:AAHUZ31012ZH_fyZ9CSQLHaw6fDvZRpzcIc",
  caller_adapter: Finch

# Configure Oban for background jobs
config :service_manager, Oban,
  repo: ServiceManager.Repo,
  plugins: [
    # Prune jobs after 1 day
    {Oban.Plugins.Pruner, max_age: 60 * 60 * 24},
    {Oban.Plugins.Stager, interval: :timer.minutes(5)}
  ],
  queues: [
    # Allow 10 concurrent Finch request jobs
    finch_requests: 10
  ]

# config/dev.exs
config :joken, default_signer: "Jdvt/1XbL1ecis+x3e+lebqF4NFT9HH+yr7pWGQGbsx7TbOzVXZRgbwaauR8mLjh"

# Configures the endpoint
config :service_manager, ServiceManagerWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: ServiceManagerWeb.ErrorHTML, json: ServiceManagerWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: ServiceManager.PubSub,
  live_view: [signing_salt: "41VW8Spi"],
  check_origin: [
    "https://mobile-banking-v3.abakula.com",
    "https://mobile-banking-v2.abakula.com",
    "https://mobile-banking.abakula.com",
    "http://localhost:4000",
    "http://localhost:4001"
  ]

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
config :service_manager, ServiceManager.Mailer, adapter: Swoosh.Adapters.Local

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  service_manager: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "3.4.3",
  service_manager: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/app.css
      --output=../priv/static/assets/app.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ]

# Configures Elixir's Logger
config :logger,
  backends: [:console, ServiceManagerWeb.LoggerBackend],
  console: [
    format: "$time $metadata[$level] $message\n",
    metadata: [:request_id]
  ]

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

config :service_manager, ServiceManager.Mailer,
  adapter: Swoosh.Adapters.SMTP,
  relay: "http://v2202110158106165840.bestsrv.de",
  username: "<EMAIL>",
  password: "66w,,vwf+mjC",
  auth: :always,
  port: 465,
  # dkim: [
  #   s: "default", d: "domain.com",
  #   private_key: {:pem_plain, File.read!("priv/keys/domain.private")}
  # ],
  retries: 2,
  no_mx_lookups: false

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"

config :service_manager,
  query_params: %{
    "page" => 1,
    "sort_field" => "inserted_at",
    "sort_order" => "desc",
    "search" => "",
    "page_size" => 10,
    "from_date" => "",
    "to_date" => "",
    "operator" => "",
    "query_field" => "",
    "query_search" => "",
    "start_date" => "",
    "end_date" => "",
    "status" => "",
    "type" => "",
    "amount_from" => "",
    "amount_to" => "",
    "reference" => "",
    "from_account" => "",
    "to_account" => ""
  }

config :service_manager, operators: [">", "<", ">=", "<=", "==", "!=", "like", "ilike"]
# Optional: override default timeout
config :service_manager, :t24_timeout, :timer.seconds(45)
config :service_manager, :t24_base_url, "https://fdh-esb.ngrok.dev"
config :service_manager, :t24_username, "admin"
config :service_manager, :t24_password, "admin"
config :service_manager, :telegram_channel_id, "-*************"

config :service_manager,
       :t24_token,
       "eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZDY6jmwwKkkqFFGNINLQBVTNN3xCX63q6wC76_6yUUnf9RCUp0FXyPbZeGzWTwdZiicG88i6EzowUGYv568bkAayBrltnn51i5LHNMTp9LLdJcgrehZWP_bbFx4GO099KXWTxC1HPYAl2jcnnLU5rAnLaxQdlK1gl7jm0GGbKr0x61M-2EOoe2cA6MH3a2CKxKY2T80Eky7AlkIyIk-7LATwMY43Kg5WA9mqk8ew5AxLM6g8tZWs50LGZQPKDgVuh2NiKxB9VVR5sZigsPx6ZxErooLKqxpL83_naUawZg3VrFhiWkVAZTCEUOfiv2QapNSH0slShBrJluwhr4dT1Q"

# Uptime Monitor Configuration
config :service_manager, ServiceManager.Monitoring.UptimeMonitor,
  # 1 minute
  check_interval: 60_000,
  endpoints_file: "endpoints.txt"

# Application environment setting
config :service_manager, :environment, System.get_env("MIX_ENV") || "development"

# MCP Server Configuration
config :service_manager, :mcp_server,
  enabled: true,
  port: 8080,
  host: "localhost",
  auth_required: false,
  max_connections: 100
