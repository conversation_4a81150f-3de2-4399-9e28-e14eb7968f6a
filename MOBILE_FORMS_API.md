# Mobile Forms API Documentation

This document provides comprehensive examples for all mobile forms API endpoints. The APIs are designed for dynamic form configuration management for mobile applications.

## Base URLs

The mobile forms APIs are available under two different authentication scopes:

- **Third Party API**: `/api/third-party/mobile-forms/` (requires third party API key)
- **Authenticated API**: `/api/mobile-forms/` (requires user authentication)

## Authentication

### Third Party API
Requires a third party API key in the request headers.

### Authenticated API
Requires user authentication token in the request headers.

## API Endpoints

### 1. Mobile App Form Retrieval

**Endpoint**: `POST /form` (Third Party) or `POST /form/:form_type` (Authenticated)

**Description**: Retrieve form configuration for mobile app consumption

**Request Body**:
```json
{
  "form": "transfer",
  "screen": "main",
  "page": "step1",
  "version": "1.0"
}
```

**Response**:
```json
{
  "success": true,
  "form_fields": [
    {
      "isRequired": true,
      "type": "string",
      "label": "Account Number",
      "field": "a7c4effe-5b85-434d-a66a-08e8c134a631"
    },
    {
      "isRequired": true,
      "type": "number",
      "label": "Amount",
      "field": "b8d5f00f-6c96-544e-b77b-19f9d245c742"
    }
  ]
}
```

**cURL Example**:
```bash
curl -X POST https://your-domain.com/api/third-party/mobile-forms/form \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "form": "transfer",
    "screen": "main",
    "version": "1.0"
  }'
```

---

### 2. Field Management APIs

#### 2.1 List Fields

**Endpoint**: `POST /fields/list`

**Description**: Retrieve all form fields with optional filtering

**Request Body**:
```json
{
  "form": "transfer",
  "screen": "main",
  "page": "step1",
  "version": "1.0",
  "active": true
}
```

**Response**:
```json
{
  "success": true,
  "fields": [
    {
      "field_id": "a7c4effe-5b85-434d-a66a-08e8c134a631",
      "form": "transfer",
      "screen": "main",
      "page": "step1",
      "version": "1.0",
      "field_name": "account_number",
      "field_type": "string",
      "label": "Account Number",
      "is_required": true,
      "field_order": 0,
      "active": true,
      "inserted_at": "2025-07-26T11:32:03Z",
      "updated_at": "2025-07-26T11:32:03Z"
    }
  ]
}
```

**cURL Example**:
```bash
curl -X POST https://your-domain.com/api/mobile-forms/fields/list \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-auth-token" \
  -d '{
    "form": "transfer",
    "active": true
  }'
```

#### 2.2 Get Single Field

**Endpoint**: `POST /fields/get`

**Description**: Retrieve a specific field by ID

**Request Body**:
```json
{
  "field_id": "a7c4effe-5b85-434d-a66a-08e8c134a631"
}
```

**Response**:
```json
{
  "success": true,
  "field": {
    "field_id": "a7c4effe-5b85-434d-a66a-08e8c134a631",
    "form": "transfer",
    "screen": "main",
    "page": "step1",
    "version": "1.0",
    "field_name": "account_number",
    "field_type": "string",
    "label": "Account Number",
    "is_required": true,
    "field_order": 0,
    "active": true
  }
}
```

#### 2.3 Create Field

**Endpoint**: `POST /fields/create`

**Description**: Create a new form field

**Request Body**:
```json
{
  "form": "beneficiary",
  "screen": "add_beneficiary",
  "page": "details",
  "version": "1.0",
  "field_name": "beneficiary_name",
  "field_type": "string",
  "label": "Beneficiary Name",
  "is_required": true,
  "field_order": 1,
  "active": true
}
```

**Response**:
```json
{
  "success": true,
  "message": "Field created successfully",
  "field": {
    "field_id": "c9e6f11f-7d07-655f-c88c-20f0e356d853",
    "form": "beneficiary",
    "screen": "add_beneficiary",
    "page": "details",
    "version": "1.0",
    "field_name": "beneficiary_name",
    "field_type": "string",
    "label": "Beneficiary Name",
    "is_required": true,
    "field_order": 1,
    "active": true
  }
}
```

#### 2.4 Update Field

**Endpoint**: `POST /fields/update`

**Description**: Update an existing form field

**Request Body**:
```json
{
  "field_id": "a7c4effe-5b85-434d-a66a-08e8c134a631",
  "label": "Updated Account Number",
  "is_required": false,
  "field_order": 2
}
```

**Response**:
```json
{
  "success": true,
  "message": "Field updated successfully",
  "field": {
    "field_id": "a7c4effe-5b85-434d-a66a-08e8c134a631",
    "form": "transfer",
    "screen": "main",
    "page": "step1",
    "version": "1.0",
    "field_name": "account_number",
    "field_type": "string",
    "label": "Updated Account Number",
    "is_required": false,
    "field_order": 2,
    "active": true
  }
}
```

#### 2.5 Delete Field

**Endpoint**: `POST /fields/delete`

**Description**: Delete a form field

**Request Body**:
```json
{
  "field_id": "a7c4effe-5b85-434d-a66a-08e8c134a631"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Field deleted successfully"
}
```

---

### 3. Hierarchy Management APIs

#### 3.1 Create Form

**Endpoint**: `POST /forms/create`

**Description**: Create a new form in the hierarchy

**Request Body**:
```json
{
  "form_name": "loan_application",
  "version": "2.0"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Form 'loan_application' created successfully"
}
```

#### 3.2 List Forms

**Endpoint**: `POST /forms/list`

**Description**: Retrieve all available forms

**Request Body**:
```json
{}
```

**Response**:
```json
{
  "success": true,
  "forms": [
    "transfer",
    "beneficiary",
    "loan_application",
    "bill_payment"
  ]
}
```

#### 3.3 Create Screen

**Endpoint**: `POST /screens/create`

**Description**: Create a new screen within a form

**Request Body**:
```json
{
  "form_name": "transfer",
  "screen_name": "confirmation",
  "version": "1.0"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Screen 'confirmation' created successfully"
}
```

#### 3.4 List Screens

**Endpoint**: `POST /screens/list`

**Description**: Retrieve all screens for a specific form

**Request Body**:
```json
{
  "form": "transfer"
}
```

**Response**:
```json
{
  "success": true,
  "screens": [
    "main",
    "confirmation",
    "receipt"
  ]
}
```

#### 3.5 Create Page

**Endpoint**: `POST /pages/create`

**Description**: Create a new page within a screen

**Request Body**:
```json
{
  "form_name": "transfer",
  "screen_name": "main",
  "page_name": "step2",
  "version": "1.0"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Page 'step2' created successfully"
}
```

#### 3.6 List Pages

**Endpoint**: `POST /pages/list`

**Description**: Retrieve all pages for a specific screen

**Request Body**:
```json
{
  "form": "transfer",
  "screen": "main"
}
```

**Response**:
```json
{
  "success": true,
  "pages": [
    "step1",
    "step2",
    "step3"
  ]
}
```

---

## Field Types

The API supports the following field types:

- `string` - Text input
- `number` - Numeric input
- `integer` - Integer input
- `boolean` - Boolean/checkbox
- `date` - Date picker
- `datetime` - Date and time picker
- `email` - Email input
- `password` - Password input
- `phone` - Phone number input
- `select` - Dropdown selection
- `multiselect` - Multiple selection
- `textarea` - Multi-line text input

## Hierarchy Structure

The mobile forms are organized hierarchically:

```
Form (Required)
└── Screen (Optional)
    └── Page (Optional, requires Screen)
        └── Field (Required)
```

**Examples:**
- Form: `transfer` → Screen: `main` → Page: `step1` → Field: `account_number`
- Form: `beneficiary` → Field: `name` (no screen or page)
- Form: `loan` → Screen: `application` → Field: `amount` (no page)

## Error Responses

All APIs return standardized error responses:

```json
{
  "success": false,
  "error": "Error message describing what went wrong"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error

## Integration Examples

### Mobile App Integration

```javascript
// Fetch form configuration for transfer screen
const getTransferForm = async () => {
  try {
    const response = await fetch('/api/third-party/mobile-forms/form', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'your-api-key'
      },
      body: JSON.stringify({
        form: 'transfer',
        screen: 'main',
        version: '1.0'
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      return data.form_fields;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error('Failed to fetch form:', error);
    throw error;
  }
};
```

### Admin Panel Integration

```javascript
// Create a new field
const createField = async (fieldData) => {
  try {
    const response = await fetch('/api/mobile-forms/fields/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(fieldData)
    });
    
    const data = await response.json();
    
    if (data.success) {
      return data.field;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error('Failed to create field:', error);
    throw error;
  }
};
```

## Notes

1. All request bodies should be sent as JSON with `Content-Type: application/json`
2. Field IDs are UUIDs and should be treated as opaque strings
3. The `version` field allows for form versioning and gradual rollouts
4. Filtering parameters in list endpoints are optional and can be combined
5. The hierarchy validation ensures pages can only exist within screens
6. Field ordering determines the display sequence in mobile apps
7. Inactive fields (`active: false`) are excluded from mobile app responses