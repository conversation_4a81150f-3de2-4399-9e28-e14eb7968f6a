defmodule ServiceManager.Contexts.RolesAndPermissionsContextFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Contexts.RolesAndPermissionsContext` context.
  """

  @doc """
  Generate a roles_and_permission.
  """
  def roles_and_permission_fixture(attrs \\ %{}) do
    {:ok, roles_and_permission} =
      attrs
      |> Enum.into(%{
        created_by: 42,
        name: "some name",
        rights: %{},
        status: "some status",
        updated_by: 42
      })
      |> ServiceManager.Contexts.RolesAndPermissionsContext.create_roles_and_permission()

    roles_and_permission
  end
end
