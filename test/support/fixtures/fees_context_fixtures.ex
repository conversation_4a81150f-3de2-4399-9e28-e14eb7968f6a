defmodule ServiceManager.Contexts.FeesContextFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Contexts.FeesContext` context.
  """

  @doc """
  Generate a fee.
  """
  def fee_fixture(attrs \\ %{}) do
    {:ok, fee} =
      attrs
      |> Enum.into(%{
        amount: "120.5",
        charge_type: "some charge_type",
        code: "some code",
        created_by: 42,
        currency_code: "some currency_code",
        description: "some description",
        effective_date: ~D[2024-11-04],
        expiration_date: ~D[2024-11-04],
        is_feature: true,
        name: "some name",
        status: "some status",
        updated_by: 42
      })
      |> ServiceManager.Contexts.FeesContext.create_fee()

    fee
  end
end
