defmodule ServiceManager.Contexts.CurrencyContextFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Contexts.CurrencyContext` context.
  """

  @doc """
  Generate a currency.
  """
  def currency_fixture(attrs \\ %{}) do
    {:ok, currency} =
      attrs
      |> Enum.into(%{
        code: "some code",
        country: "some country",
        currency_id: "7488a646-e31f-11e4-aace-600308960662",
        exchange_rate: "120.5",
        minor_unit: 42,
        name: "some name",
        notes: "some notes",
        status: "some status",
        symbol: "some symbol"
      })
      |> ServiceManager.Contexts.CurrencyContext.create_currency()

    currency
  end
end
