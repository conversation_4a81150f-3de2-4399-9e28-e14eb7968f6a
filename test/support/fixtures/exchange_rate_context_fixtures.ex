defmodule ServiceManager.Contexts.ExchangeRateContextFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Contexts.ExchangeRateContext` context.
  """

  @doc """
  Generate a exchange_rate.
  """
  def exchange_rate_fixture(attrs \\ %{}) do
    {:ok, exchange_rate} =
      attrs
      |> Enum.into(%{
        from_currency_code: "some from_currency_code",
        rate: "120.5",
        to_currency_code: "some to_currency_code"
      })
      |> ServiceManager.Contexts.ExchangeRateContext.create_exchange_rate()

    exchange_rate
  end
end
