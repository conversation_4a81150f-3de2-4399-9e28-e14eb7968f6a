defmodule ServiceManager.Contexts.VirtualCardsAContextsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Contexts.VirtualCardsAContexts` context.
  """

  @doc """
  Generate a api_config.
  """
  def api_config_fixture(attrs \\ %{}) do
    {:ok, api_config} =
      attrs
      |> Enum.into(%{
        api_id: "7488a646-e31f-11e4-aace-600308960662",
        api_key: "some api_key",
        api_secret: "some api_secret",
        auth_token: "some auth_token",
        base_url: "some base_url",
        notes: "some notes",
        provider_name: "some provider_name",
        status: "some status",
        timeout: 42,
        version: "some version"
      })
      |> ServiceManager.Contexts.VirtualCardsAContexts.create_api_config()

    api_config
  end
end
