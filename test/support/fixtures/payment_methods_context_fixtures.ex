defmodule ServiceManager.Contexts.PaymentMethodsContextFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Contexts.PaymentMethodsContext` context.
  """

  @doc """
  Generate a payment_method.
  """
  def payment_method_fixture(attrs \\ %{}) do
    {:ok, payment_method} =
      attrs
      |> Enum.into(%{
        created_by: 42,
        description: "some description",
        fees_id: 42,
        name: "some name",
        status: true,
        type: "some type",
        updated_by: 42
      })
      |> ServiceManager.Contexts.PaymentMethodsContext.create_payment_method()

    payment_method
  end
end
