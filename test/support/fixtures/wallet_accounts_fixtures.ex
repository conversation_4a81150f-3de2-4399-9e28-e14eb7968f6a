defmodule ServiceManager.WalletAccountsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.WalletAccounts` context.
  """

  def unique_wallet_user_email, do: "wallet_user#{System.unique_integer()}@example.com"
  def valid_wallet_user_password, do: "hello world!"
  def valid_malawi_mobile_number, do: "+26588#{Enum.random(1_000_000..9_999_999)}"
  def invalid_malawi_mobile_number, do: "+26078#{Enum.random(1_000_000..9_999_999)}"

  def valid_wallet_user_attributes(attrs \\ %{}) do
    Enum.into(attrs, %{
      email: unique_wallet_user_email(),
      password: valid_wallet_user_password(),
      mobile_number: valid_malawi_mobile_number()
    })
  end

  def wallet_user_fixture(attrs \\ %{}) do
    {:ok, wallet_user} =
      attrs
      |> valid_wallet_user_attributes()
      |> ServiceManager.WalletAccounts.register_wallet_user()

    wallet_user
  end

  def extract_wallet_user_token(fun) do
    {:ok, captured_email} = fun.(&"[TOKEN]#{&1}[TOKEN]")
    [_, token | _] = String.split(captured_email.text_body, "[TOKEN]")
    token
  end
end
