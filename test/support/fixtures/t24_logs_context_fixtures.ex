defmodule ServiceManager.Contexts.T24LogsContextFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Contexts.T24LogsContext` context.
  """

  @doc """
  Generate a t24_log.
  """
  def t24_log_fixture(attrs \\ %{}) do
    {:ok, t24_log} =
      attrs
      |> Enum.into(%{
        headers: "some headers",
        message_id: "some message_id",
        method: "some method",
        request: "some request",
        response: "some response",
        url: "some url"
      })
      |> ServiceManager.Contexts.T24LogsContext.create_t24_log()

    t24_log
  end
end
