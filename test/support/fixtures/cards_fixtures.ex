defmodule ServiceManager.CardsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `ServiceManager.Cards` context.
  """

  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.SchemaCard

  @doc """
  Generate a card.
  """
  def card_fixture(attrs \\ %{}) do
    {:ok, card} =
      attrs
      |> Enum.into(%{
        card_type: "visa",
        currency: "USD",
        daily_limit: "1000.00",
        monthly_limit: "5000.00"
      })
      |> then(&SchemaCard.changeset(%SchemaCard{}, &1))
      |> Repo.insert()

    card
  end

  @doc """
  Generate a blocked card.
  """
  def blocked_card_fixture(attrs \\ %{}) do
    {:ok, card} =
      attrs
      |> Enum.into(%{
        card_type: "visa",
        currency: "USD",
        daily_limit: "1000.00",
        monthly_limit: "5000.00",
        status: "blocked"
      })
      |> then(&SchemaCard.changeset(%SchemaCard{}, &1))
      |> Repo.insert()

    card
  end

  @doc """
  Generate an activated card.
  """
  def activated_card_fixture(attrs \\ %{}) do
    {:ok, card} =
      attrs
      |> Enum.into(%{
        card_type: "visa",
        currency: "USD",
        daily_limit: "1000.00",
        monthly_limit: "5000.00",
        activation_status: "activated",
        pin_hash: Bcrypt.hash_pwd_salt("1234")
      })
      |> then(&SchemaCard.changeset(%SchemaCard{}, &1))
      |> Repo.insert()

    card
  end

  @doc """
  Generate a card linked to a user.
  """
  def user_card_fixture(%{user: user} = attrs) do
    attrs = Map.delete(attrs, :user)
    card_fixture(Map.put(attrs, :user_id, user.id))
  end

  @doc """
  Generate a card linked to a wallet user.
  """
  def wallet_user_card_fixture(%{wallet_user: wallet_user} = attrs) do
    attrs = Map.delete(attrs, :wallet_user)
    card_fixture(Map.put(attrs, :wallet_user_id, wallet_user.id))
  end

  @doc """
  Generate a card linked to a beneficiary.
  """
  def beneficiary_card_fixture(%{beneficiary: beneficiary} = attrs) do
    attrs = Map.delete(attrs, :beneficiary)
    card_fixture(Map.put(attrs, :beneficiary_id, beneficiary.id))
  end

  @doc """
  Generate a card linked to a third party API key.
  """
  def third_party_card_fixture(%{api_key: api_key} = attrs) do
    attrs = Map.delete(attrs, :api_key)
    card_fixture(Map.put(attrs, :third_party_api_key_id, api_key.id))
  end
end
