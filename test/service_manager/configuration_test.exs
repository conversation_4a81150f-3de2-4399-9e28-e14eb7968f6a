defmodule ServiceManager.Contexts.VirtualCardsAContextsTest do
  use ServiceManager.DataCase

  alias ServiceManager.Contexts.VirtualCardsAContexts

  describe "api_configs" do
    alias ServiceManager.Schemas.VirtualCardsApiConfig

    import ServiceManager.Contexts.VirtualCardsAContextsFixtures

    @invalid_attrs %{
      timeout: nil,
      status: nil,
      version: nil,
      api_key: nil,
      api_id: nil,
      provider_name: nil,
      base_url: nil,
      auth_token: nil,
      api_secret: nil,
      notes: nil
    }

    test "list_api_configs/0 returns all api_configs" do
      api_config = api_config_fixture()
      assert Configuration.list_api_configs() == [api_config]
    end

    test "get_api_config!/1 returns the api_config with given id" do
      api_config = api_config_fixture()
      assert Configuration.get_api_config!(api_config.id) == api_config
    end

    test "create_api_config/1 with valid data creates a api_config" do
      valid_attrs = %{
        timeout: 42,
        status: "some status",
        version: "some version",
        api_key: "some api_key",
        api_id: "7488a646-e31f-11e4-aace-600308960662",
        provider_name: "some provider_name",
        base_url: "some base_url",
        auth_token: "some auth_token",
        api_secret: "some api_secret",
        notes: "some notes"
      }

      assert {:ok, %ApiConfig{} = api_config} = Configuration.create_api_config(valid_attrs)
      assert api_config.timeout == 42
      assert api_config.status == "some status"
      assert api_config.version == "some version"
      assert api_config.api_key == "some api_key"
      assert api_config.api_id == "7488a646-e31f-11e4-aace-600308960662"
      assert api_config.provider_name == "some provider_name"
      assert api_config.base_url == "some base_url"
      assert api_config.auth_token == "some auth_token"
      assert api_config.api_secret == "some api_secret"
      assert api_config.notes == "some notes"
    end

    test "create_api_config/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Configuration.create_api_config(@invalid_attrs)
    end

    test "update_api_config/2 with valid data updates the api_config" do
      api_config = api_config_fixture()

      update_attrs = %{
        timeout: 43,
        status: "some updated status",
        version: "some updated version",
        api_key: "some updated api_key",
        api_id: "7488a646-e31f-11e4-aace-600308960668",
        provider_name: "some updated provider_name",
        base_url: "some updated base_url",
        auth_token: "some updated auth_token",
        api_secret: "some updated api_secret",
        notes: "some updated notes"
      }

      assert {:ok, %ApiConfig{} = api_config} =
               Configuration.update_api_config(api_config, update_attrs)

      assert api_config.timeout == 43
      assert api_config.status == "some updated status"
      assert api_config.version == "some updated version"
      assert api_config.api_key == "some updated api_key"
      assert api_config.api_id == "7488a646-e31f-11e4-aace-600308960668"
      assert api_config.provider_name == "some updated provider_name"
      assert api_config.base_url == "some updated base_url"
      assert api_config.auth_token == "some updated auth_token"
      assert api_config.api_secret == "some updated api_secret"
      assert api_config.notes == "some updated notes"
    end

    test "update_api_config/2 with invalid data returns error changeset" do
      api_config = api_config_fixture()

      assert {:error, %Ecto.Changeset{}} =
               Configuration.update_api_config(api_config, @invalid_attrs)

      assert api_config == Configuration.get_api_config!(api_config.id)
    end

    test "delete_api_config/1 deletes the api_config" do
      api_config = api_config_fixture()
      assert {:ok, %ApiConfig{}} = Configuration.delete_api_config(api_config)
      assert_raise Ecto.NoResultsError, fn -> Configuration.get_api_config!(api_config.id) end
    end

    test "change_api_config/1 returns a api_config changeset" do
      api_config = api_config_fixture()
      assert %Ecto.Changeset{} = Configuration.change_api_config(api_config)
    end
  end
end
