defmodule ServiceManager.WalletAccountsTest do
  use ServiceManager.DataCase

  alias ServiceManager.WalletAccounts

  import ServiceManager.WalletAccountsFixtures
  alias ServiceManager.WalletAccounts.{WalletUser, WalletUserToken}

  describe "get_wallet_user_by_email/1" do
    test "does not return the wallet_user if the email does not exist" do
      refute WalletAccounts.get_wallet_user_by_email("<EMAIL>")
    end

    test "returns the wallet_user if the email exists" do
      %{id: id} = wallet_user = wallet_user_fixture()
      assert %WalletUser{id: ^id} = WalletAccounts.get_wallet_user_by_email(wallet_user.email)
    end
  end

  describe "get_wallet_user_by_email_and_password/2" do
    test "does not return the wallet_user if the email does not exist" do
      refute WalletAccounts.get_wallet_user_by_email_and_password(
               "<EMAIL>",
               "hello world!"
             )
    end

    test "does not return the wallet_user if the password is not valid" do
      wallet_user = wallet_user_fixture()
      refute WalletAccounts.get_wallet_user_by_email_and_password(wallet_user.email, "invalid")
    end

    test "returns the wallet_user if the email and password are valid" do
      %{id: id} = wallet_user = wallet_user_fixture()

      assert %WalletUser{id: ^id} =
               WalletAccounts.get_wallet_user_by_email_and_password(
                 wallet_user.email,
                 valid_wallet_user_password()
               )
    end
  end

  describe "get_wallet_user!/1" do
    test "raises if id is invalid" do
      assert_raise Ecto.NoResultsError, fn ->
        WalletAccounts.get_wallet_user!(-1)
      end
    end

    test "returns the wallet_user with the given id" do
      %{id: id} = wallet_user = wallet_user_fixture()
      assert %WalletUser{id: ^id} = WalletAccounts.get_wallet_user!(wallet_user.id)
    end
  end

  describe "register_wallet_user/1" do
    test "requires email and password to be set" do
      {:error, changeset} = WalletAccounts.register_wallet_user(%{})

      assert %{
               password: ["can't be blank"],
               email: ["can't be blank"],
               mobile_number: ["can't be blank"]
             } = errors_on(changeset)
    end

    test "validates email and password when given" do
      {:error, changeset} =
        WalletAccounts.register_wallet_user(%{email: "not valid", password: "not valid"})

      assert %{
               email: ["must have the @ sign and no spaces"],
               password: ["should be at least 12 character(s)"],
               mobile_number: ["can't be blank"]
             } = errors_on(changeset)
    end

    test "validates maximum values for email and password for security" do
      too_long = String.duplicate("db", 100)

      {:error, changeset} =
        WalletAccounts.register_wallet_user(%{email: too_long, password: too_long})

      assert "should be at most 160 character(s)" in errors_on(changeset).email
      assert "should be at most 72 character(s)" in errors_on(changeset).password
    end

    test "validates email uniqueness" do
      %{email: email} = wallet_user_fixture()
      {:error, changeset} = WalletAccounts.register_wallet_user(%{email: email})
      assert "has already been taken" in errors_on(changeset).email

      # Now try with the upper cased email too, to check that email case is ignored.
      {:error, changeset} = WalletAccounts.register_wallet_user(%{email: String.upcase(email)})
      assert "has already been taken" in errors_on(changeset).email
    end

    test "validates mobile number format for Malawian numbers" do
      # Valid Malawian mobile number formats
      valid_formats = [
        "+26588#{Enum.random(1_000_000..9_999_999)}",
        "+26599#{Enum.random(1_000_000..9_999_999)}",
        "26588#{Enum.random(1_000_000..9_999_999)}",
        "088#{Enum.random(1_000_000..9_999_999)}"
      ]

      # Invalid mobile number formats
      invalid_formats = [
        "+26078#{Enum.random(1_000_000..9_999_999)}", # Wrong country code
        "+*********", # Too short
        "+*************", # Too long
        "+26577#{Enum.random(1_000_000..9_999_999)}", # Invalid prefix
        "abcdefghij" # Non-numeric
      ]

      # Test valid formats
      Enum.each(valid_formats, fn mobile_number ->
        attrs = valid_wallet_user_attributes(mobile_number: mobile_number)
        changeset = WalletAccounts.change_wallet_user_registration(%WalletUser{}, attrs)
        assert changeset.valid?, "Expected #{mobile_number} to be valid, but got errors: #{inspect(errors_on(changeset))}"
      end)

      # Test invalid formats
      Enum.each(invalid_formats, fn mobile_number ->
        attrs = valid_wallet_user_attributes(mobile_number: mobile_number)
        changeset = WalletAccounts.change_wallet_user_registration(%WalletUser{}, attrs)
        assert changeset.errors[:mobile_number], "Expected #{mobile_number} to be invalid"
      end)
    end

    test "validates mobile number uniqueness" do
      %{mobile_number: mobile_number} = wallet_user_fixture()

      {:error, changeset} =
        WalletAccounts.register_wallet_user(valid_wallet_user_attributes(mobile_number: mobile_number))

      assert "has already been taken" in errors_on(changeset).mobile_number
    end

    test "registers walletusers with a hashed password" do
      email = unique_wallet_user_email()

      {:ok, wallet_user} =
        WalletAccounts.register_wallet_user(valid_wallet_user_attributes(email: email))

      assert wallet_user.email == email
      assert is_binary(wallet_user.hashed_password)
      assert is_nil(wallet_user.confirmed_at)
      assert is_nil(wallet_user.password)
    end
  end

  describe "change_wallet_user_registration/2" do
    test "returns a changeset" do
      assert %Ecto.Changeset{} =
               changeset = WalletAccounts.change_wallet_user_registration(%WalletUser{})

      assert changeset.required == [:password, :email]
    end

    test "allows fields to be set" do
      email = unique_wallet_user_email()
      password = valid_wallet_user_password()

      changeset =
        WalletAccounts.change_wallet_user_registration(
          %WalletUser{},
          valid_wallet_user_attributes(email: email, password: password)
        )

      assert changeset.valid?
      assert get_change(changeset, :email) == email
      assert get_change(changeset, :password) == password
      assert is_nil(get_change(changeset, :hashed_password))
    end
  end

  describe "change_wallet_user_email/2" do
    test "returns a wallet_user changeset" do
      assert %Ecto.Changeset{} =
               changeset = WalletAccounts.change_wallet_user_email(%WalletUser{})

      assert changeset.required == [:email]
    end
  end

  describe "apply_wallet_user_email/3" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "requires email to change", %{wallet_user: wallet_user} do
      {:error, changeset} =
        WalletAccounts.apply_wallet_user_email(wallet_user, valid_wallet_user_password(), %{})

      assert %{email: ["did not change"]} = errors_on(changeset)
    end

    test "validates email", %{wallet_user: wallet_user} do
      {:error, changeset} =
        WalletAccounts.apply_wallet_user_email(wallet_user, valid_wallet_user_password(), %{
          email: "not valid"
        })

      assert %{email: ["must have the @ sign and no spaces"]} = errors_on(changeset)
    end

    test "validates maximum value for email for security", %{wallet_user: wallet_user} do
      too_long = String.duplicate("db", 100)

      {:error, changeset} =
        WalletAccounts.apply_wallet_user_email(wallet_user, valid_wallet_user_password(), %{
          email: too_long
        })

      assert "should be at most 160 character(s)" in errors_on(changeset).email
    end

    test "validates email uniqueness", %{wallet_user: wallet_user} do
      %{email: email} = wallet_user_fixture()
      password = valid_wallet_user_password()

      {:error, changeset} =
        WalletAccounts.apply_wallet_user_email(wallet_user, password, %{email: email})

      assert "has already been taken" in errors_on(changeset).email
    end

    test "validates current password", %{wallet_user: wallet_user} do
      {:error, changeset} =
        WalletAccounts.apply_wallet_user_email(wallet_user, "invalid", %{
          email: unique_wallet_user_email()
        })

      assert %{current_password: ["is not valid"]} = errors_on(changeset)
    end

    test "applies the email without persisting it", %{wallet_user: wallet_user} do
      email = unique_wallet_user_email()

      {:ok, wallet_user} =
        WalletAccounts.apply_wallet_user_email(wallet_user, valid_wallet_user_password(), %{
          email: email
        })

      assert wallet_user.email == email
      assert WalletAccounts.get_wallet_user!(wallet_user.id).email != email
    end
  end

  describe "deliver_wallet_user_update_email_instructions/3" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "sends token through notification", %{wallet_user: wallet_user} do
      token =
        extract_wallet_user_token(fn url ->
          WalletAccounts.deliver_wallet_user_update_email_instructions(
            wallet_user,
            "<EMAIL>",
            url
          )
        end)

      {:ok, token} = Base.url_decode64(token, padding: false)
      assert wallet_user_token = Repo.get_by(WalletUserToken, token: :crypto.hash(:sha256, token))
      assert wallet_user_token.wallet_user_id == wallet_user.id
      assert wallet_user_token.sent_to == wallet_user.email
      assert wallet_user_token.context == "change:<EMAIL>"
    end
  end

  describe "update_wallet_user_email/2" do
    setup do
      wallet_user = wallet_user_fixture()
      email = unique_wallet_user_email()

      token =
        extract_wallet_user_token(fn url ->
          WalletAccounts.deliver_wallet_user_update_email_instructions(
            %{wallet_user | email: email},
            wallet_user.email,
            url
          )
        end)

      %{wallet_user: wallet_user, token: token, email: email}
    end

    test "updates the email with a valid token", %{
      wallet_user: wallet_user,
      token: token,
      email: email
    } do
      assert WalletAccounts.update_wallet_user_email(wallet_user, token) == :ok
      changed_wallet_user = Repo.get!(WalletUser, wallet_user.id)
      assert changed_wallet_user.email != wallet_user.email
      assert changed_wallet_user.email == email
      assert changed_wallet_user.confirmed_at
      assert changed_wallet_user.confirmed_at != wallet_user.confirmed_at
      refute Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end

    test "does not update email with invalid token", %{wallet_user: wallet_user} do
      assert WalletAccounts.update_wallet_user_email(wallet_user, "oops") == :error
      assert Repo.get!(WalletUser, wallet_user.id).email == wallet_user.email
      assert Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end

    test "does not update email if wallet_user email changed", %{
      wallet_user: wallet_user,
      token: token
    } do
      assert WalletAccounts.update_wallet_user_email(
               %{wallet_user | email: "<EMAIL>"},
               token
             ) == :error

      assert Repo.get!(WalletUser, wallet_user.id).email == wallet_user.email
      assert Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end

    test "does not update email if token expired", %{wallet_user: wallet_user, token: token} do
      {1, nil} = Repo.update_all(WalletUserToken, set: [inserted_at: ~N[2020-01-01 00:00:00]])
      assert WalletAccounts.update_wallet_user_email(wallet_user, token) == :error
      assert Repo.get!(WalletUser, wallet_user.id).email == wallet_user.email
      assert Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end
  end

  describe "change_wallet_user_password/2" do
    test "returns a wallet_user changeset" do
      assert %Ecto.Changeset{} =
               changeset = WalletAccounts.change_wallet_user_password(%WalletUser{})

      assert changeset.required == [:password]
    end

    test "allows fields to be set" do
      changeset =
        WalletAccounts.change_wallet_user_password(%WalletUser{}, %{
          "password" => "new valid password"
        })

      assert changeset.valid?
      assert get_change(changeset, :password) == "new valid password"
      assert is_nil(get_change(changeset, :hashed_password))
    end
  end

  describe "update_wallet_user_password/3" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "validates password", %{wallet_user: wallet_user} do
      {:error, changeset} =
        WalletAccounts.update_wallet_user_password(wallet_user, valid_wallet_user_password(), %{
          password: "not valid",
          password_confirmation: "another"
        })

      assert %{
               password: ["should be at least 12 character(s)"],
               password_confirmation: ["does not match password"]
             } = errors_on(changeset)
    end

    test "validates maximum values for password for security", %{wallet_user: wallet_user} do
      too_long = String.duplicate("db", 100)

      {:error, changeset} =
        WalletAccounts.update_wallet_user_password(wallet_user, valid_wallet_user_password(), %{
          password: too_long
        })

      assert "should be at most 72 character(s)" in errors_on(changeset).password
    end

    test "validates current password", %{wallet_user: wallet_user} do
      {:error, changeset} =
        WalletAccounts.update_wallet_user_password(wallet_user, "invalid", %{
          password: valid_wallet_user_password()
        })

      assert %{current_password: ["is not valid"]} = errors_on(changeset)
    end

    test "updates the password", %{wallet_user: wallet_user} do
      {:ok, wallet_user} =
        WalletAccounts.update_wallet_user_password(wallet_user, valid_wallet_user_password(), %{
          password: "new valid password"
        })

      assert is_nil(wallet_user.password)

      assert WalletAccounts.get_wallet_user_by_email_and_password(
               wallet_user.email,
               "new valid password"
             )
    end

    test "deletes all tokens for the given wallet_user", %{wallet_user: wallet_user} do
      _ = WalletAccounts.generate_wallet_user_session_token(wallet_user)

      {:ok, _} =
        WalletAccounts.update_wallet_user_password(wallet_user, valid_wallet_user_password(), %{
          password: "new valid password"
        })

      refute Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end
  end

  describe "generate_wallet_user_session_token/1" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "generates a token", %{wallet_user: wallet_user} do
      token = WalletAccounts.generate_wallet_user_session_token(wallet_user)
      assert wallet_user_token = Repo.get_by(WalletUserToken, token: token)
      assert wallet_user_token.context == "session"

      # Creating the same token for another wallet_user should fail
      assert_raise Ecto.ConstraintError, fn ->
        Repo.insert!(%WalletUserToken{
          token: wallet_user_token.token,
          wallet_user_id: wallet_user_fixture().id,
          context: "session"
        })
      end
    end
  end

  describe "get_wallet_user_by_session_token/1" do
    setup do
      wallet_user = wallet_user_fixture()
      token = WalletAccounts.generate_wallet_user_session_token(wallet_user)
      %{wallet_user: wallet_user, token: token}
    end

    test "returns wallet_user by token", %{wallet_user: wallet_user, token: token} do
      assert session_wallet_user = WalletAccounts.get_wallet_user_by_session_token(token)
      assert session_wallet_user.id == wallet_user.id
    end

    test "does not return wallet_user for invalid token" do
      refute WalletAccounts.get_wallet_user_by_session_token("oops")
    end

    test "does not return wallet_user for expired token", %{token: token} do
      {1, nil} = Repo.update_all(WalletUserToken, set: [inserted_at: ~N[2020-01-01 00:00:00]])
      refute WalletAccounts.get_wallet_user_by_session_token(token)
    end
  end

  describe "delete_wallet_user_session_token/1" do
    test "deletes the token" do
      wallet_user = wallet_user_fixture()
      token = WalletAccounts.generate_wallet_user_session_token(wallet_user)
      assert WalletAccounts.delete_wallet_user_session_token(token) == :ok
      refute WalletAccounts.get_wallet_user_by_session_token(token)
    end
  end

  describe "deliver_wallet_user_confirmation_instructions/2" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "sends token through notification", %{wallet_user: wallet_user} do
      token =
        extract_wallet_user_token(fn url ->
          WalletAccounts.deliver_wallet_user_confirmation_instructions(wallet_user, url)
        end)

      {:ok, token} = Base.url_decode64(token, padding: false)
      assert wallet_user_token = Repo.get_by(WalletUserToken, token: :crypto.hash(:sha256, token))
      assert wallet_user_token.wallet_user_id == wallet_user.id
      assert wallet_user_token.sent_to == wallet_user.email
      assert wallet_user_token.context == "confirm"
    end
  end

  describe "confirm_wallet_user/1" do
    setup do
      wallet_user = wallet_user_fixture()

      token =
        extract_wallet_user_token(fn url ->
          WalletAccounts.deliver_wallet_user_confirmation_instructions(wallet_user, url)
        end)

      %{wallet_user: wallet_user, token: token}
    end

    test "confirms the email with a valid token", %{wallet_user: wallet_user, token: token} do
      assert {:ok, confirmed_wallet_user} = WalletAccounts.confirm_wallet_user(token)
      assert confirmed_wallet_user.confirmed_at
      assert confirmed_wallet_user.confirmed_at != wallet_user.confirmed_at
      assert Repo.get!(WalletUser, wallet_user.id).confirmed_at
      refute Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end

    test "does not confirm with invalid token", %{wallet_user: wallet_user} do
      assert WalletAccounts.confirm_wallet_user("oops") == :error
      refute Repo.get!(WalletUser, wallet_user.id).confirmed_at
      assert Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end

    test "does not confirm email if token expired", %{wallet_user: wallet_user, token: token} do
      {1, nil} = Repo.update_all(WalletUserToken, set: [inserted_at: ~N[2020-01-01 00:00:00]])
      assert WalletAccounts.confirm_wallet_user(token) == :error
      refute Repo.get!(WalletUser, wallet_user.id).confirmed_at
      assert Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end
  end

  describe "deliver_wallet_user_reset_password_instructions/2" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "sends token through notification", %{wallet_user: wallet_user} do
      token =
        extract_wallet_user_token(fn url ->
          WalletAccounts.deliver_wallet_user_reset_password_instructions(wallet_user, url)
        end)

      {:ok, token} = Base.url_decode64(token, padding: false)
      assert wallet_user_token = Repo.get_by(WalletUserToken, token: :crypto.hash(:sha256, token))
      assert wallet_user_token.wallet_user_id == wallet_user.id
      assert wallet_user_token.sent_to == wallet_user.email
      assert wallet_user_token.context == "reset_password"
    end
  end

  describe "get_wallet_user_by_reset_password_token/1" do
    setup do
      wallet_user = wallet_user_fixture()

      token =
        extract_wallet_user_token(fn url ->
          WalletAccounts.deliver_wallet_user_reset_password_instructions(wallet_user, url)
        end)

      %{wallet_user: wallet_user, token: token}
    end

    test "returns the wallet_user with valid token", %{wallet_user: %{id: id}, token: token} do
      assert %WalletUser{id: ^id} = WalletAccounts.get_wallet_user_by_reset_password_token(token)
      assert Repo.get_by(WalletUserToken, wallet_user_id: id)
    end

    test "does not return the wallet_user with invalid token", %{wallet_user: wallet_user} do
      refute WalletAccounts.get_wallet_user_by_reset_password_token("oops")
      assert Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end

    test "does not return the wallet_user if token expired", %{
      wallet_user: wallet_user,
      token: token
    } do
      {1, nil} = Repo.update_all(WalletUserToken, set: [inserted_at: ~N[2020-01-01 00:00:00]])
      refute WalletAccounts.get_wallet_user_by_reset_password_token(token)
      assert Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end
  end

  describe "reset_wallet_user_password/2" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "validates password", %{wallet_user: wallet_user} do
      {:error, changeset} =
        WalletAccounts.reset_wallet_user_password(wallet_user, %{
          password: "not valid",
          password_confirmation: "another"
        })

      assert %{
               password: ["should be at least 12 character(s)"],
               password_confirmation: ["does not match password"]
             } = errors_on(changeset)
    end

    test "validates maximum values for password for security", %{wallet_user: wallet_user} do
      too_long = String.duplicate("db", 100)

      {:error, changeset} =
        WalletAccounts.reset_wallet_user_password(wallet_user, %{password: too_long})

      assert "should be at most 72 character(s)" in errors_on(changeset).password
    end

    test "updates the password", %{wallet_user: wallet_user} do
      {:ok, updated_wallet_user} =
        WalletAccounts.reset_wallet_user_password(wallet_user, %{password: "new valid password"})

      assert is_nil(updated_wallet_user.password)

      assert WalletAccounts.get_wallet_user_by_email_and_password(
               wallet_user.email,
               "new valid password"
             )
    end

    test "deletes all tokens for the given wallet_user", %{wallet_user: wallet_user} do
      _ = WalletAccounts.generate_wallet_user_session_token(wallet_user)

      {:ok, _} =
        WalletAccounts.reset_wallet_user_password(wallet_user, %{password: "new valid password"})

      refute Repo.get_by(WalletUserToken, wallet_user_id: wallet_user.id)
    end
  end

  describe "inspect/2 for the WalletUser module" do
    test "does not include password" do
      refute inspect(%WalletUser{password: "123456"}) =~ "password: \"123456\""
    end
  end
end
