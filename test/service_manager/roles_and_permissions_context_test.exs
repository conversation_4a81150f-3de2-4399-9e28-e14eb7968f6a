defmodule ServiceManager.Contexts.RolesAndPermissionsContextTest do
  use ServiceManager.DataCase

  alias ServiceManager.Contexts.RolesAndPermissionsContext

  describe "roles_and_permissions" do
    alias ServiceManager.Schemas.RolesAndPermission

    import ServiceManager.Contexts.RolesAndPermissionsContextFixtures

    @invalid_attrs %{name: nil, status: nil, rights: nil, created_by: nil, updated_by: nil}

    test "list_roles_and_permissions/0 returns all roles_and_permissions" do
      roles_and_permission = roles_and_permission_fixture()
      assert RolesAndPermissionsContext.list_roles_and_permissions() == [roles_and_permission]
    end

    test "get_roles_and_permission!/1 returns the roles_and_permission with given id" do
      roles_and_permission = roles_and_permission_fixture()

      assert RolesAndPermissionsContext.get_roles_and_permission!(roles_and_permission.id) ==
               roles_and_permission
    end

    test "create_roles_and_permission/1 with valid data creates a roles_and_permission" do
      valid_attrs = %{
        name: "some name",
        status: "some status",
        rights: %{},
        created_by: 42,
        updated_by: 42
      }

      assert {:ok, %RolesAndPermission{} = roles_and_permission} =
               RolesAndPermissionsContext.create_roles_and_permission(valid_attrs)

      assert roles_and_permission.name == "some name"
      assert roles_and_permission.status == "some status"
      assert roles_and_permission.rights == %{}
      assert roles_and_permission.created_by == 42
      assert roles_and_permission.updated_by == 42
    end

    test "create_roles_and_permission/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} =
               RolesAndPermissionsContext.create_roles_and_permission(@invalid_attrs)
    end

    test "update_roles_and_permission/2 with valid data updates the roles_and_permission" do
      roles_and_permission = roles_and_permission_fixture()

      update_attrs = %{
        name: "some updated name",
        status: "some updated status",
        rights: %{},
        created_by: 43,
        updated_by: 43
      }

      assert {:ok, %RolesAndPermission{} = roles_and_permission} =
               RolesAndPermissionsContext.update_roles_and_permission(
                 roles_and_permission,
                 update_attrs
               )

      assert roles_and_permission.name == "some updated name"
      assert roles_and_permission.status == "some updated status"
      assert roles_and_permission.rights == %{}
      assert roles_and_permission.created_by == 43
      assert roles_and_permission.updated_by == 43
    end

    test "update_roles_and_permission/2 with invalid data returns error changeset" do
      roles_and_permission = roles_and_permission_fixture()

      assert {:error, %Ecto.Changeset{}} =
               RolesAndPermissionsContext.update_roles_and_permission(
                 roles_and_permission,
                 @invalid_attrs
               )

      assert roles_and_permission ==
               RolesAndPermissionsContext.get_roles_and_permission!(roles_and_permission.id)
    end

    test "delete_roles_and_permission/1 deletes the roles_and_permission" do
      roles_and_permission = roles_and_permission_fixture()

      assert {:ok, %RolesAndPermission{}} =
               RolesAndPermissionsContext.delete_roles_and_permission(roles_and_permission)

      assert_raise Ecto.NoResultsError, fn ->
        RolesAndPermissionsContext.get_roles_and_permission!(roles_and_permission.id)
      end
    end

    test "change_roles_and_permission/1 returns a roles_and_permission changeset" do
      roles_and_permission = roles_and_permission_fixture()

      assert %Ecto.Changeset{} =
               RolesAndPermissionsContext.change_roles_and_permission(roles_and_permission)
    end
  end
end
