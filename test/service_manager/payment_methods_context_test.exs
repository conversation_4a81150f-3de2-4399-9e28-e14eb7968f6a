defmodule ServiceManager.Contexts.PaymentMethodsContextTest do
  use ServiceManager.DataCase

  alias ServiceManager.Contexts.PaymentMethodsContext

  describe "payment_methods" do
    alias ServiceManager.Schemas.PaymentMethod

    import ServiceManager.Contexts.PaymentMethodsContextFixtures

    @invalid_attrs %{
      name: nil,
      status: nil,
      type: nil,
      description: nil,
      fees_id: nil,
      created_by: nil,
      updated_by: nil
    }

    test "list_payment_methods/0 returns all payment_methods" do
      payment_method = payment_method_fixture()
      assert PaymentMethodsContext.list_payment_methods() == [payment_method]
    end

    test "get_payment_method!/1 returns the payment_method with given id" do
      payment_method = payment_method_fixture()
      assert PaymentMethodsContext.get_payment_method!(payment_method.id) == payment_method
    end

    test "create_payment_method/1 with valid data creates a payment_method" do
      valid_attrs = %{
        name: "some name",
        status: true,
        type: "some type",
        description: "some description",
        fees_id: 42,
        created_by: 42,
        updated_by: 42
      }

      assert {:ok, %PaymentMethod{} = payment_method} =
               PaymentMethodsContext.create_payment_method(valid_attrs)

      assert payment_method.name == "some name"
      assert payment_method.status == true
      assert payment_method.type == "some type"
      assert payment_method.description == "some description"
      assert payment_method.fees_id == 42
      assert payment_method.created_by == 42
      assert payment_method.updated_by == 42
    end

    test "create_payment_method/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} =
               PaymentMethodsContext.create_payment_method(@invalid_attrs)
    end

    test "update_payment_method/2 with valid data updates the payment_method" do
      payment_method = payment_method_fixture()

      update_attrs = %{
        name: "some updated name",
        status: false,
        type: "some updated type",
        description: "some updated description",
        fees_id: 43,
        created_by: 43,
        updated_by: 43
      }

      assert {:ok, %PaymentMethod{} = payment_method} =
               PaymentMethodsContext.update_payment_method(payment_method, update_attrs)

      assert payment_method.name == "some updated name"
      assert payment_method.status == false
      assert payment_method.type == "some updated type"
      assert payment_method.description == "some updated description"
      assert payment_method.fees_id == 43
      assert payment_method.created_by == 43
      assert payment_method.updated_by == 43
    end

    test "update_payment_method/2 with invalid data returns error changeset" do
      payment_method = payment_method_fixture()

      assert {:error, %Ecto.Changeset{}} =
               PaymentMethodsContext.update_payment_method(payment_method, @invalid_attrs)

      assert payment_method == PaymentMethodsContext.get_payment_method!(payment_method.id)
    end

    test "delete_payment_method/1 deletes the payment_method" do
      payment_method = payment_method_fixture()
      assert {:ok, %PaymentMethod{}} = PaymentMethodsContext.delete_payment_method(payment_method)

      assert_raise Ecto.NoResultsError, fn ->
        PaymentMethodsContext.get_payment_method!(payment_method.id)
      end
    end

    test "change_payment_method/1 returns a payment_method changeset" do
      payment_method = payment_method_fixture()
      assert %Ecto.Changeset{} = PaymentMethodsContext.change_payment_method(payment_method)
    end
  end
end
