defmodule ServiceManager.Billers.BillersServiceTest do
  use ServiceManager.DataCase

  alias ServiceManager.Billers.BillersService
  alias ServiceManager.Schemas.Billers.BillerTransaction

  describe "create_transaction/1" do
    test "creates a transaction with valid attributes" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "pending"
      }

      assert {:ok, transaction} = BillersService.create_transaction(attrs)
      assert transaction.biller_type == "bwb_postpaid"
      assert transaction.account_number == "********"
      assert transaction.status == "pending"
    end

    test "returns error with invalid attributes" do
      attrs = %{
        biller_type: "invalid_type"
      }

      assert {:error, changeset} = BillersService.create_transaction(attrs)
      refute changeset.valid?
    end
  end

  describe "get_transaction/1" do
    test "returns transaction when it exists" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "pending"
      })

      retrieved = BillersService.get_transaction(transaction.id)
      assert retrieved.id == transaction.id
    end

    test "returns nil when transaction doesn't exist" do
      assert BillersService.get_transaction(999999) == nil
    end
  end

  describe "get_transaction_by_reference/1" do
    test "returns transaction when reference exists" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "pending"
      })

      retrieved = BillersService.get_transaction_by_reference("TT202501010001")
      assert retrieved.id == transaction.id
    end

    test "returns nil when reference doesn't exist" do
      assert BillersService.get_transaction_by_reference("NONEXISTENT") == nil
    end
  end

  describe "update_transaction/2" do
    test "updates transaction with valid attributes" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "pending"
      })

      assert {:ok, updated} = BillersService.update_transaction(transaction, %{status: "processing"})
      assert updated.status == "processing"
    end
  end

  describe "complete_transaction/2" do
    test "marks transaction as completed" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "processing"
      })

      response_payload = %{"status" => "success", "balance" => "1000.00"}
      
      assert {:ok, completed} = BillersService.complete_transaction(transaction, response_payload)
      assert completed.status == "completed"
      assert completed.response_payload == response_payload
      assert completed.processed_at
    end
  end

  describe "fail_transaction/2" do
    test "marks transaction as failed" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "processing"
      })

      error_message = "Account not found"
      
      assert {:ok, failed} = BillersService.fail_transaction(transaction, error_message)
      assert failed.status == "failed"
      assert failed.error_message == error_message
      assert failed.processed_at
    end

    test "includes response payload when provided" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "processing"
      })

      error_message = "Account not found"
      response_payload = %{"error" => "ACCOUNT_NOT_FOUND"}
      
      assert {:ok, failed} = BillersService.fail_transaction(transaction, error_message, response_payload)
      assert failed.status == "failed"
      assert failed.error_message == error_message
      assert failed.response_payload == response_payload
    end
  end

  describe "process_account_details/3" do
    test "creates and processes account details transaction" do
      # Mock the actual API call since we're testing the service logic
      result = BillersService.process_account_details("bwb_postpaid", "********")
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_type == "bwb_postpaid"
      assert transaction.account_number == "********"
      assert transaction.transaction_type == "account_details"
      assert transaction.status in ["completed", "failed"] # Depends on simulation
    end

    test "includes account_type for MASM" do
      result = BillersService.process_account_details("masm", "********", account_type: "M")
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.account_type == "M"
    end

    test "generates unique transaction ID" do
      result1 = BillersService.process_account_details("bwb_postpaid", "********")
      result2 = BillersService.process_account_details("bwb_postpaid", "********")
      
      assert {:ok, %{transaction: transaction1}} = result1
      assert {:ok, %{transaction: transaction2}} = result2
      assert transaction1.our_transaction_id != transaction2.our_transaction_id
    end
  end

  describe "process_payment/2" do
    test "creates and processes payment transaction" do
      payment_attrs = %{
        account_number: "********",
        amount: Decimal.new("100.00"),
        currency: "MWK",
        credit_account: "*************",
        credit_account_type: "account",
        debit_account: "************",
        debit_account_type: "account",
        customer_account_number: "************",
        customer_account_name: "Test User"
      }

      result = BillersService.process_payment("bwb_postpaid", payment_attrs)
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_type == "bwb_postpaid"
      assert transaction.transaction_type == "post_transaction"
      assert transaction.amount == Decimal.new("100.00")
      assert transaction.customer_account_name == "Test User"
    end

    test "validates required payment fields" do
      payment_attrs = %{
        account_number: "********"
        # Missing required fields
      }

      result = BillersService.process_payment("bwb_postpaid", payment_attrs)
      
      assert {:error, %{transaction: changeset}} = result
      refute changeset.valid?
    end
  end

  describe "process_invoice_request/2" do
    test "creates and processes invoice request" do
      result = BillersService.process_invoice_request("register_general", "BEDPVE")
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_type == "register_general"
      assert transaction.account_number == "BEDPVE"
      assert transaction.transaction_type == "get_invoice"
    end
  end

  describe "process_invoice_confirmation/2" do
    test "creates and processes invoice confirmation" do
      confirmation_attrs = %{
        account_number: "BEDPVE",
        amount: Decimal.new("800.00"),
        currency: "MWK",
        credit_account: "*************",
        credit_account_type: "account",
        debit_account: "************",
        debit_account_type: "account",
        customer_account_number: "************",
        customer_account_name: "Fanuel Jeckey Mzizah"
      }

      result = BillersService.process_invoice_confirmation("register_general", confirmation_attrs)
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_type == "register_general"
      assert transaction.transaction_type == "confirm_invoice"
      assert transaction.amount == Decimal.new("800.00")
    end
  end

  describe "process_bundle_details/1" do
    test "creates and processes bundle details request" do
      result = BillersService.process_bundle_details("239678")
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_type == "tnm_bundles"
      assert transaction.bundle_id == "239678"
      assert transaction.transaction_type == "bundle_details"
    end
  end

  describe "process_bundle_confirmation/1" do
    test "creates and processes bundle confirmation" do
      bundle_attrs = %{
        bundle_id: "239678",
        account_number: "**********",
        amount: Decimal.new("50.00"),
        currency: "MWK"
      }

      result = BillersService.process_bundle_confirmation(bundle_attrs)
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_type == "tnm_bundles"
      assert transaction.transaction_type == "confirm_bundle"
      assert transaction.bundle_id == "239678"
    end
  end

  describe "process_validation/1" do
    test "creates and processes validation request" do
      result = BillersService.process_validation("*********")
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_type == "airtel_validation"
      assert transaction.account_number == "*********"
      assert transaction.transaction_type == "validation"
    end
  end

  describe "get_transactions_by_biller/2" do
    test "returns transactions for specific biller" do
      # Create transactions for different billers
      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT001",
        transaction_type: "account_details",
        status: "pending"
      })

      BillersService.create_transaction(%{
        biller_type: "lwb_postpaid",
        biller_name: "LWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT002",
        transaction_type: "account_details",
        status: "pending"
      })

      bwb_transactions = BillersService.get_transactions_by_biller("bwb_postpaid")
      lwb_transactions = BillersService.get_transactions_by_biller("lwb_postpaid")

      assert length(bwb_transactions) == 1
      assert length(lwb_transactions) == 1
      assert hd(bwb_transactions).biller_type == "bwb_postpaid"
      assert hd(lwb_transactions).biller_type == "lwb_postpaid"
    end

    test "supports limit and offset options" do
      # Create multiple transactions
      Enum.each(1..5, fn i ->
        BillersService.create_transaction(%{
          biller_type: "bwb_postpaid",
          biller_name: "BWB Postpaid",
          account_number: "1234567#{i}",
          our_transaction_id: "TT00#{i}",
          transaction_type: "account_details",
          status: "pending"
        })
      end)

      transactions = BillersService.get_transactions_by_biller("bwb_postpaid", limit: 2, offset: 1)
      assert length(transactions) == 2
    end
  end

  describe "get_transactions_by_status/2" do
    test "returns transactions with specific status" do
      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT001",
        transaction_type: "account_details",
        status: "pending"
      })

      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT002",
        transaction_type: "account_details",
        status: "completed"
      })

      pending_transactions = BillersService.get_transactions_by_status("pending")
      completed_transactions = BillersService.get_transactions_by_status("completed")

      assert length(pending_transactions) == 1
      assert length(completed_transactions) == 1
      assert hd(pending_transactions).status == "pending"
      assert hd(completed_transactions).status == "completed"
    end
  end

  describe "get_transactions_by_account/2" do
    test "returns transactions for specific account" do
      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT001",
        transaction_type: "account_details",
        status: "pending"
      })

      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT002",
        transaction_type: "account_details",
        status: "pending"
      })

      account_transactions = BillersService.get_transactions_by_account("********")

      assert length(account_transactions) == 1
      assert hd(account_transactions).account_number == "********"
    end
  end

  describe "retry_transaction/1" do
    test "successfully retries a failed transaction" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "failed",
        error_message: "Temporary failure"
      })

      result = BillersService.retry_transaction(transaction.id)
      
      # Result depends on the simulation, but should not be an error
      assert {:ok, _} = result
    end

    test "returns error for non-existent transaction" do
      result = BillersService.retry_transaction(999999)
      assert {:error, "Transaction not found"} = result
    end

    test "returns error for non-failed transaction" do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "completed"
      })

      result = BillersService.retry_transaction(transaction.id)
      assert {:error, "Only failed transactions can be retried"} = result
    end
  end

  describe "private functions behavior" do
    test "generate_transaction_id produces unique IDs" do
      # We can't directly test private functions, but we can test the behavior
      result1 = BillersService.process_account_details("bwb_postpaid", "********")
      result2 = BillersService.process_account_details("bwb_postpaid", "********")

      assert {:ok, %{transaction: transaction1}} = result1
      assert {:ok, %{transaction: transaction2}} = result2
      
      assert transaction1.our_transaction_id != transaction2.our_transaction_id
      assert String.starts_with?(transaction1.our_transaction_id, "TT")
      assert String.starts_with?(transaction2.our_transaction_id, "TT")
    end

    test "get_biller_name returns correct names" do
      # Test through the actual service calls
      result = BillersService.process_account_details("bwb_postpaid", "********")
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_name == "BWB Postpaid"

      result = BillersService.process_account_details("lwb_postpaid", "********")
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.biller_name == "LWB Postpaid"
    end

    test "simulated API responses vary" do
      # Run multiple transactions to see if we get different outcomes
      results = Enum.map(1..10, fn i ->
        result = BillersService.process_account_details("bwb_postpaid", "1234567#{i}")
        {:ok, %{transaction: transaction}} = result
        transaction.status
      end)

      # Should have some variety in results due to simulation
      unique_statuses = Enum.uniq(results)
      assert length(unique_statuses) >= 1
      assert Enum.all?(unique_statuses, fn status -> status in ["completed", "failed"] end)
    end
  end

  describe "payload building" do
    test "payment transactions include request payload" do
      payment_attrs = %{
        account_number: "********",
        amount: Decimal.new("100.00"),
        currency: "MWK",
        credit_account: "*************",
        credit_account_type: "account",
        debit_account: "************",
        debit_account_type: "account",
        customer_account_number: "************",
        customer_account_name: "Test User"
      }

      result = BillersService.process_payment("bwb_postpaid", payment_attrs)
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.request_payload
      assert transaction.request_payload["method"] == "POST"
      assert transaction.request_payload["body"]
      assert String.contains?(transaction.request_payload["body"], "********")
      assert String.contains?(transaction.request_payload["body"], "Test User")
    end

    test "account details transactions include request payload" do
      result = BillersService.process_account_details("bwb_postpaid", "********")
      
      assert {:ok, %{transaction: transaction}} = result
      assert transaction.request_payload
      assert transaction.request_payload["method"] == "GET"
      assert transaction.request_payload["account_number"] == "********"
    end
  end
end