defmodule ServiceManager.LoansTest do
  use ServiceManager.DataCase

  alias ServiceManager.Loans

  describe "loan_transactions" do
    alias ServiceManager.Loans.Transactions

    import ServiceManager.LoansFixtures

    @invalid_attrs %{}

    test "list_loan_transactions/0 returns all loan_transactions" do
      transactions = transactions_fixture()
      assert Loans.list_loan_transactions() == [transactions]
    end

    test "get_transactions!/1 returns the transactions with given id" do
      transactions = transactions_fixture()
      assert Loans.get_transactions!(transactions.id) == transactions
    end

    test "create_transactions/1 with valid data creates a transactions" do
      valid_attrs = %{}

      assert {:ok, %Transactions{} = transactions} = Loans.create_transactions(valid_attrs)
    end

    test "create_transactions/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Loans.create_transactions(@invalid_attrs)
    end

    test "update_transactions/2 with valid data updates the transactions" do
      transactions = transactions_fixture()
      update_attrs = %{}

      assert {:ok, %Transactions{} = transactions} =
               Loans.update_transactions(transactions, update_attrs)
    end

    test "update_transactions/2 with invalid data returns error changeset" do
      transactions = transactions_fixture()
      assert {:error, %Ecto.Changeset{}} = Loans.update_transactions(transactions, @invalid_attrs)
      assert transactions == Loans.get_transactions!(transactions.id)
    end

    test "delete_transactions/1 deletes the transactions" do
      transactions = transactions_fixture()
      assert {:ok, %Transactions{}} = Loans.delete_transactions(transactions)
      assert_raise Ecto.NoResultsError, fn -> Loans.get_transactions!(transactions.id) end
    end

    test "change_transactions/1 returns a transactions changeset" do
      transactions = transactions_fixture()
      assert %Ecto.Changeset{} = Loans.change_transactions(transactions)
    end
  end
end
