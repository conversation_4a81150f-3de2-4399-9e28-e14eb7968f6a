defmodule ServiceManager.LogsTest do
  use ServiceManager.DataCase

  alias ServiceManager.Logs

  describe "system_logs" do
    alias ServiceManager.Schamas.SystemLog

    import ServiceManager.LogsFixtures

    @invalid_attrs %{
      message: nil,
      timestamp: nil,
      level: nil,
      metadata: nil,
      source: nil,
      category: nil,
      trace_id: nil,
      user_id: nil,
      ip_address: nil
    }

    test "list_system_logs/0 returns all system_logs" do
      system_log = system_log_fixture()
      assert Logs.list_system_logs() == [system_log]
    end

    test "get_system_log!/1 returns the system_log with given id" do
      system_log = system_log_fixture()
      assert Logs.get_system_log!(system_log.id) == system_log
    end

    test "create_system_log/1 with valid data creates a system_log" do
      valid_attrs = %{
        message: "some message",
        timestamp: ~U[2024-12-01 12:25:00.000000Z],
        level: "some level",
        metadata: %{},
        source: "some source",
        category: "some category",
        trace_id: "some trace_id",
        user_id: 42,
        ip_address: "some ip_address"
      }

      assert {:ok, %SystemLog{} = system_log} = Logs.create_system_log(valid_attrs)
      assert system_log.message == "some message"
      assert system_log.timestamp == ~U[2024-12-01 12:25:00.000000Z]
      assert system_log.level == "some level"
      assert system_log.metadata == %{}
      assert system_log.source == "some source"
      assert system_log.category == "some category"
      assert system_log.trace_id == "some trace_id"
      assert system_log.user_id == 42
      assert system_log.ip_address == "some ip_address"
    end

    test "create_system_log/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Logs.create_system_log(@invalid_attrs)
    end

    test "update_system_log/2 with valid data updates the system_log" do
      system_log = system_log_fixture()

      update_attrs = %{
        message: "some updated message",
        timestamp: ~U[2024-12-02 12:25:00.000000Z],
        level: "some updated level",
        metadata: %{},
        source: "some updated source",
        category: "some updated category",
        trace_id: "some updated trace_id",
        user_id: 43,
        ip_address: "some updated ip_address"
      }

      assert {:ok, %SystemLog{} = system_log} = Logs.update_system_log(system_log, update_attrs)
      assert system_log.message == "some updated message"
      assert system_log.timestamp == ~U[2024-12-02 12:25:00.000000Z]
      assert system_log.level == "some updated level"
      assert system_log.metadata == %{}
      assert system_log.source == "some updated source"
      assert system_log.category == "some updated category"
      assert system_log.trace_id == "some updated trace_id"
      assert system_log.user_id == 43
      assert system_log.ip_address == "some updated ip_address"
    end

    test "update_system_log/2 with invalid data returns error changeset" do
      system_log = system_log_fixture()
      assert {:error, %Ecto.Changeset{}} = Logs.update_system_log(system_log, @invalid_attrs)
      assert system_log == Logs.get_system_log!(system_log.id)
    end

    test "delete_system_log/1 deletes the system_log" do
      system_log = system_log_fixture()
      assert {:ok, %SystemLog{}} = Logs.delete_system_log(system_log)
      assert_raise Ecto.NoResultsError, fn -> Logs.get_system_log!(system_log.id) end
    end

    test "change_system_log/1 returns a system_log changeset" do
      system_log = system_log_fixture()
      assert %Ecto.Changeset{} = Logs.change_system_log(system_log)
    end
  end
end
