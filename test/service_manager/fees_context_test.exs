defmodule ServiceManager.Contexts.FeesContextTest do
  use ServiceManager.DataCase

  alias ServiceManager.Contexts.FeesContext

  describe "fees" do
    alias ServiceManager.Schemas.Fee

    import ServiceManager.Contexts.FeesContextFixtures

    @invalid_attrs %{
      code: nil,
      name: nil,
      status: nil,
      description: nil,
      amount: nil,
      currency_code: nil,
      charge_type: nil,
      effective_date: nil,
      expiration_date: nil,
      is_feature: nil,
      created_by: nil,
      updated_by: nil
    }

    test "list_fees/0 returns all fees" do
      fee = fee_fixture()
      assert FeesContext.list_fees() == [fee]
    end

    test "get_fee!/1 returns the fee with given id" do
      fee = fee_fixture()
      assert FeesContext.get_fee!(fee.id) == fee
    end

    test "create_fee/1 with valid data creates a fee" do
      valid_attrs = %{
        code: "some code",
        name: "some name",
        status: "some status",
        description: "some description",
        amount: "120.5",
        currency_code: "some currency_code",
        charge_type: "some charge_type",
        effective_date: ~D[2024-11-04],
        expiration_date: ~D[2024-11-04],
        is_feature: true,
        created_by: 42,
        updated_by: 42
      }

      assert {:ok, %Fee{} = fee} = FeesContext.create_fee(valid_attrs)
      assert fee.code == "some code"
      assert fee.name == "some name"
      assert fee.status == "some status"
      assert fee.description == "some description"
      assert fee.amount == Decimal.new("120.5")
      assert fee.currency_code == "some currency_code"
      assert fee.charge_type == "some charge_type"
      assert fee.effective_date == ~D[2024-11-04]
      assert fee.expiration_date == ~D[2024-11-04]
      assert fee.is_feature == true
      assert fee.created_by == 42
      assert fee.updated_by == 42
    end

    test "create_fee/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = FeesContext.create_fee(@invalid_attrs)
    end

    test "update_fee/2 with valid data updates the fee" do
      fee = fee_fixture()

      update_attrs = %{
        code: "some updated code",
        name: "some updated name",
        status: "some updated status",
        description: "some updated description",
        amount: "456.7",
        currency_code: "some updated currency_code",
        charge_type: "some updated charge_type",
        effective_date: ~D[2024-11-05],
        expiration_date: ~D[2024-11-05],
        is_feature: false,
        created_by: 43,
        updated_by: 43
      }

      assert {:ok, %Fee{} = fee} = FeesContext.update_fee(fee, update_attrs)
      assert fee.code == "some updated code"
      assert fee.name == "some updated name"
      assert fee.status == "some updated status"
      assert fee.description == "some updated description"
      assert fee.amount == Decimal.new("456.7")
      assert fee.currency_code == "some updated currency_code"
      assert fee.charge_type == "some updated charge_type"
      assert fee.effective_date == ~D[2024-11-05]
      assert fee.expiration_date == ~D[2024-11-05]
      assert fee.is_feature == false
      assert fee.created_by == 43
      assert fee.updated_by == 43
    end

    test "update_fee/2 with invalid data returns error changeset" do
      fee = fee_fixture()
      assert {:error, %Ecto.Changeset{}} = FeesContext.update_fee(fee, @invalid_attrs)
      assert fee == FeesContext.get_fee!(fee.id)
    end

    test "delete_fee/1 deletes the fee" do
      fee = fee_fixture()
      assert {:ok, %Fee{}} = FeesContext.delete_fee(fee)
      assert_raise Ecto.NoResultsError, fn -> FeesContext.get_fee!(fee.id) end
    end

    test "change_fee/1 returns a fee changeset" do
      fee = fee_fixture()
      assert %Ecto.Changeset{} = FeesContext.change_fee(fee)
    end
  end
end
