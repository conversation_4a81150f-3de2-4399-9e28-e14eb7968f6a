defmodule ServiceManager.Cache.RouteHelperTest do
  use ServiceManager.DataCase
  alias ServiceManager.Cache.{RouteHelper, RouteCache}
  alias ServiceManager.Schemas.Route
  alias ServiceManager.Repo

  setup do
    # Create test routes
    routes = [
      %{
        name: "get_account_balance",
        host: "**************",
        path: "/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************",
        method: "GET"
      },
      %{
        name: "create_account",
        host: "fdh-esb.ngrok.dev",
        path: "/api/esb/cb/accounts/v1/create/account/*************",
        method: "POST"
      }
    ]

    # Insert routes
    Enum.each(routes, fn route ->
      %Route{}
      |> Route.changeset(route)
      |> Repo.insert!()
    end)

    # Start the cache
    start_supervised!(RouteCache)
    :ok
  end

  describe "route helper" do
    test "get_route_details/1 returns full details" do
      details = RouteHelper.get_route_details("get_account_balance")

      assert details.url ==
               "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"

      assert details.method == "GET"

      assert_raise RuntimeError, fn ->
        RouteHelper.get_route_details("non_existent")
      end
    end

    test "get_url/1 returns just the URL" do
      url = RouteHelper.get_url("create_account")
      assert url == "fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************"

      assert_raise RuntimeError, fn ->
        RouteHelper.get_url("non_existent")
      end
    end

    test "get_method/1 returns just the method" do
      assert RouteHelper.get_method("create_account") == "POST"
      assert RouteHelper.get_method("get_account_balance") == "GET"

      assert_raise RuntimeError, fn ->
        RouteHelper.get_method("non_existent")
      end
    end

    test "build_url/1 returns complete URL with protocol" do
      # IP address should use http://
      assert RouteHelper.build_url("get_account_balance") ==
               "http://**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"

      # Domain should use https://
      assert RouteHelper.build_url("create_account") ==
               "https://fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************"

      assert_raise RuntimeError, fn ->
        RouteHelper.build_url("non_existent")
      end
    end

    test "get_route_details_safe/1 returns tuple" do
      assert {:ok, details} = RouteHelper.get_route_details_safe("get_account_balance")

      assert details.url ==
               "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"

      assert details.method == "GET"

      assert {:error, :not_found} = RouteHelper.get_route_details_safe("non_existent")
    end

    test "get_url_safe/1 returns tuple" do
      assert {:ok, url} = RouteHelper.get_url_safe("create_account")
      assert url == "fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************"

      assert {:error, :not_found} = RouteHelper.get_url_safe("non_existent")
    end

    test "get_method_safe/1 returns tuple" do
      assert {:ok, "POST"} = RouteHelper.get_method_safe("create_account")
      assert {:ok, "GET"} = RouteHelper.get_method_safe("get_account_balance")
      assert {:error, :not_found} = RouteHelper.get_method_safe("non_existent")
    end
  end
end
