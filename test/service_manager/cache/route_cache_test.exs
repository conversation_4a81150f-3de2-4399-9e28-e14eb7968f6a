defmodule ServiceManager.Cache.RouteCacheTest do
  use ServiceManager.DataCase
  alias ServiceManager.Cache.RouteCache
  alias ServiceManager.Schemas.Route
  alias ServiceManager.Repo

  setup do
    # Create some test routes
    routes = [
      %{
        name: "get_account_balance",
        host: "**************",
        path: "/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************",
        method: "GET"
      },
      %{
        name: "create_account",
        host: "fdh-esb.ngrok.dev",
        path: "/api/esb/cb/accounts/v1/create/account/*************",
        method: "POST"
      }
    ]

    # Insert routes into the database
    Enum.each(routes, fn route ->
      %Route{}
      |> Route.changeset(route)
      |> Repo.insert!()
    end)

    # Start the cache
    start_supervised!(RouteCache)
    :ok
  end

  describe "route cache" do
    test "get_route/1 returns route by name" do
      assert {:ok, route} = RouteCache.get_route("get_account_balance")
      assert route.host == "**************"

      assert route.path ==
               "/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"

      assert route.method == "GET"
    end

    test "get_route/1 returns error for non-existent route" do
      assert {:error, :not_found} = RouteCache.get_route("non_existent")
    end

    test "get_route!/1 returns route or raises" do
      route = RouteCache.get_route!("create_account")
      assert route.host == "fdh-esb.ngrok.dev"
      assert route.path == "/api/esb/cb/accounts/v1/create/account/*************"
      assert route.method == "POST"

      assert_raise RuntimeError, "Route not found: non_existent", fn ->
        RouteCache.get_route!("non_existent")
      end
    end

    test "get_full_url/1 returns complete URL" do
      assert {:ok, url} = RouteCache.get_full_url("get_account_balance")

      assert url ==
               "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"
    end

    test "get_full_url!/1 returns URL or raises" do
      url = RouteCache.get_full_url!("create_account")
      assert url == "fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************"

      assert_raise RuntimeError, "Route not found: non_existent", fn ->
        RouteCache.get_full_url!("non_existent")
      end
    end

    test "get_route_info/1 returns URL and method" do
      assert {:ok, info} = RouteCache.get_route_info("get_account_balance")

      assert info.url ==
               "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"

      assert info.method == "GET"
    end

    test "get_route_info!/1 returns info or raises" do
      info = RouteCache.get_route_info!("create_account")
      assert info.url == "fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************"
      assert info.method == "POST"

      assert_raise RuntimeError, "Route not found: non_existent", fn ->
        RouteCache.get_route_info!("non_existent")
      end
    end

    test "refresh updates the cache" do
      # Insert a new route
      %Route{}
      |> Route.changeset(%{
        name: "new_route",
        host: "example.com",
        path: "/api/new",
        method: "GET"
      })
      |> Repo.insert!()

      # Refresh the cache
      RouteCache.refresh()

      # Verify the new route is in the cache
      assert {:ok, route} = RouteCache.get_route("new_route")
      assert route.host == "example.com"
      assert route.path == "/api/new"
      assert route.method == "GET"
    end
  end
end
