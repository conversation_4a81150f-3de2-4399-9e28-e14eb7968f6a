defmodule ServiceManager.Contexts.CurrencyContextTest do
  use ServiceManager.DataCase

  alias ServiceManager.Contexts.CurrencyContext

  describe "currencies" do
    alias ServiceManager.Schemas.Currency

    import ServiceManager.Contexts.CurrencyContextFixtures

    @invalid_attrs %{
      code: nil,
      name: nil,
      status: nil,
      symbol: nil,
      currency_id: nil,
      country: nil,
      exchange_rate: nil,
      minor_unit: nil,
      notes: nil
    }

    test "list_currencies/0 returns all currencies" do
      currency = currency_fixture()
      assert CurrencyContext.list_currencies() == [currency]
    end

    test "get_currency!/1 returns the currency with given id" do
      currency = currency_fixture()
      assert CurrencyContext.get_currency!(currency.id) == currency
    end

    test "create_currency/1 with valid data creates a currency" do
      valid_attrs = %{
        code: "some code",
        name: "some name",
        status: "some status",
        symbol: "some symbol",
        currency_id: "7488a646-e31f-11e4-aace-600308960662",
        country: "some country",
        exchange_rate: "120.5",
        minor_unit: 42,
        notes: "some notes"
      }

      assert {:ok, %Currency{} = currency} = CurrencyContext.create_currency(valid_attrs)
      assert currency.code == "some code"
      assert currency.name == "some name"
      assert currency.status == "some status"
      assert currency.symbol == "some symbol"
      assert currency.currency_id == "7488a646-e31f-11e4-aace-600308960662"
      assert currency.country == "some country"
      assert currency.exchange_rate == Decimal.new("120.5")
      assert currency.minor_unit == 42
      assert currency.notes == "some notes"
    end

    test "create_currency/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = CurrencyContext.create_currency(@invalid_attrs)
    end

    test "update_currency/2 with valid data updates the currency" do
      currency = currency_fixture()

      update_attrs = %{
        code: "some updated code",
        name: "some updated name",
        status: "some updated status",
        symbol: "some updated symbol",
        currency_id: "7488a646-e31f-11e4-aace-600308960668",
        country: "some updated country",
        exchange_rate: "456.7",
        minor_unit: 43,
        notes: "some updated notes"
      }

      assert {:ok, %Currency{} = currency} =
               CurrencyContext.update_currency(currency, update_attrs)

      assert currency.code == "some updated code"
      assert currency.name == "some updated name"
      assert currency.status == "some updated status"
      assert currency.symbol == "some updated symbol"
      assert currency.currency_id == "7488a646-e31f-11e4-aace-600308960668"
      assert currency.country == "some updated country"
      assert currency.exchange_rate == Decimal.new("456.7")
      assert currency.minor_unit == 43
      assert currency.notes == "some updated notes"
    end

    test "update_currency/2 with invalid data returns error changeset" do
      currency = currency_fixture()

      assert {:error, %Ecto.Changeset{}} =
               CurrencyContext.update_currency(currency, @invalid_attrs)

      assert currency == CurrencyContext.get_currency!(currency.id)
    end

    test "delete_currency/1 deletes the currency" do
      currency = currency_fixture()
      assert {:ok, %Currency{}} = CurrencyContext.delete_currency(currency)
      assert_raise Ecto.NoResultsError, fn -> CurrencyContext.get_currency!(currency.id) end
    end

    test "change_currency/1 returns a currency changeset" do
      currency = currency_fixture()
      assert %Ecto.Changeset{} = CurrencyContext.change_currency(currency)
    end
  end
end
