defmodule ServiceManager.Schemas.Billers.BillerConfigTest do
  use ServiceManager.DataCase

  alias ServiceManager.Schemas.Billers.BillerConfig

  describe "changeset/2" do
    test "valid changeset with required fields" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Blantyre Water Board - Postpaid",
        base_url: "https://api.example.com",
        endpoints: %{
          "account_details" => "/accounts/{account_number}",
          "post_transaction" => "/transactions"
        }
      }

      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      assert changeset.valid?
    end

    test "invalid changeset without required fields" do
      changeset = BillerConfig.changeset(%BillerConfig{}, %{})
      
      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).biller_type
      assert "can't be blank" in errors_on(changeset).biller_name
      assert "can't be blank" in errors_on(changeset).display_name
      assert "can't be blank" in errors_on(changeset).base_url
      assert "can't be blank" in errors_on(changeset).endpoints
    end

    test "validates biller_type inclusion" do
      attrs = %{
        biller_type: "invalid_biller",
        biller_name: "Test Biller",
        display_name: "Test Display Name",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"}
      }

      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).biller_type
    end

    test "validates default_currency inclusion" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Test Display Name",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"},
        default_currency: "INVALID"
      }

      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).default_currency
    end

    test "validates timeout_ms is greater than 0" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Test Display Name",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"},
        timeout_ms: 0
      }

      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      refute changeset.valid?
      assert "must be greater than 0" in errors_on(changeset).timeout_ms
    end

    test "validates timeout_ms is less than 300000" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Test Display Name",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"},
        timeout_ms: 300001
      }

      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      refute changeset.valid?
      assert "must be less than 300000" in errors_on(changeset).timeout_ms
    end

    test "validates retry_attempts range" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Test Display Name",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"},
        retry_attempts: -1
      }

      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      refute changeset.valid?
      assert "must be greater than or equal to 0" in errors_on(changeset).retry_attempts

      attrs = Map.put(attrs, :retry_attempts, 10)
      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      refute changeset.valid?
      assert "must be less than 10" in errors_on(changeset).retry_attempts
    end

    test "validates base_url format" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Test Display Name",
        base_url: "invalid-url",
        endpoints: %{"test" => "/test"}
      }

      changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
      refute changeset.valid?
      assert "has invalid format" in errors_on(changeset).base_url
    end

    test "accepts valid URLs" do
      valid_urls = [
        "http://example.com",
        "https://api.example.com",
        "https://api.example.com:8080"
      ]

      Enum.each(valid_urls, fn url ->
        attrs = %{
          biller_type: "bwb_postpaid",
          biller_name: "BWB Postpaid",
          display_name: "Test Display Name",
          base_url: url,
          endpoints: %{"test" => "/test"}
        }

        changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
        assert changeset.valid?, "#{url} should be valid"
      end)
    end

    test "accepts valid biller types" do
      valid_types = [
        "register_general",
        "bwb_postpaid",
        "lwb_postpaid",
        "srwb_postpaid",
        "srwb_prepaid",
        "masm",
        "airtel_validation",
        "tnm_bundles"
      ]

      Enum.each(valid_types, fn biller_type ->
        attrs = %{
          biller_type: biller_type,
          biller_name: "Test Biller",
          display_name: "Test Display Name",
          base_url: "https://api.example.com",
          endpoints: %{"test" => "/test"}
        }

        changeset = BillerConfig.changeset(%BillerConfig{}, attrs)
        assert changeset.valid?, "#{biller_type} should be valid"
      end)
    end
  end

  describe "create_changeset/1" do
    test "creates changeset for new biller config" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Blantyre Water Board - Postpaid",
        base_url: "https://api.example.com",
        endpoints: %{"account_details" => "/accounts/{account_number}"}
      }

      changeset = BillerConfig.create_changeset(attrs)
      assert changeset.valid?
      assert changeset.data.__struct__ == BillerConfig
    end
  end

  describe "update_changeset/2" do
    test "updates existing biller config" do
      existing_config = %BillerConfig{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Old Display Name"
      }

      attrs = %{display_name: "New Display Name"}
      changeset = BillerConfig.update_changeset(existing_config, attrs)
      
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :display_name) == "New Display Name"
    end
  end

  describe "activate_changeset/1" do
    test "sets is_active to true" do
      config = %BillerConfig{is_active: false}
      changeset = BillerConfig.activate_changeset(config)
      
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :is_active) == true
    end
  end

  describe "deactivate_changeset/1" do
    test "sets is_active to false" do
      config = %BillerConfig{is_active: true}
      changeset = BillerConfig.deactivate_changeset(config)
      
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :is_active) == false
    end
  end

  describe "default_configs/0" do
    test "returns configurations for all biller types" do
      configs = BillerConfig.default_configs()
      
      assert is_map(configs)
      assert Map.has_key?(configs, "register_general")
      assert Map.has_key?(configs, "bwb_postpaid")
      assert Map.has_key?(configs, "lwb_postpaid")
      assert Map.has_key?(configs, "srwb_postpaid")
      assert Map.has_key?(configs, "srwb_prepaid")
      assert Map.has_key?(configs, "masm")
      assert Map.has_key?(configs, "airtel_validation")
      assert Map.has_key?(configs, "tnm_bundles")
    end

    test "each config has required fields" do
      configs = BillerConfig.default_configs()
      
      Enum.each(configs, fn {_key, config} ->
        assert config[:biller_type]
        assert config[:biller_name]
        assert config[:display_name]
        assert config[:endpoints]
        assert config[:authentication]
        assert config[:features]
        assert config[:validation_rules]
      end)
    end
  end

  describe "get_endpoint_url/3" do
    test "returns full URL with base_url and endpoint" do
      config = %BillerConfig{
        base_url: "https://api.example.com",
        endpoints: %{
          "account_details" => "/accounts/{account_number}"
        }
      }

      url = BillerConfig.get_endpoint_url(config, "account_details", %{account_number: "12345"})
      assert url == "https://api.example.com/accounts/12345"
    end

    test "returns nil for non-existent operation" do
      config = %BillerConfig{
        base_url: "https://api.example.com",
        endpoints: %{
          "account_details" => "/accounts/{account_number}"
        }
      }

      url = BillerConfig.get_endpoint_url(config, "non_existent", %{})
      assert url == nil
    end

    test "replaces multiple parameters" do
      config = %BillerConfig{
        base_url: "https://api.example.com",
        endpoints: %{
          "bundle_confirm" => "/bundles/{transaction_id}/{phone_number}/{bundle_id}"
        }
      }

      url = BillerConfig.get_endpoint_url(config, "bundle_confirm", %{
        transaction_id: "TT123",
        phone_number: "**********",
        bundle_id: "239678"
      })
      
      assert url == "https://api.example.com/bundles/TT123/**********/239678"
    end
  end

  describe "supports_feature?/2" do
    test "returns true when feature is supported" do
      config = %BillerConfig{
        features: %{
          "supports_payment" => true,
          "supports_invoice" => false
        }
      }

      assert BillerConfig.supports_feature?(config, "supports_payment")
      refute BillerConfig.supports_feature?(config, "supports_invoice")
    end

    test "returns false for non-existent feature" do
      config = %BillerConfig{
        features: %{
          "supports_payment" => true
        }
      }

      refute BillerConfig.supports_feature?(config, "non_existent_feature")
    end
  end

  describe "validate_account_number/2" do
    test "returns :ok for valid account number" do
      config = %BillerConfig{
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{8,12}$",
            "min_length" => 8,
            "max_length" => 12
          }
        }
      }

      assert BillerConfig.validate_account_number(config, "********") == :ok
      assert BillerConfig.validate_account_number(config, "************") == :ok
    end

    test "returns error for missing required account number" do
      config = %BillerConfig{
        validation_rules: %{
          "account_number" => %{
            "required" => true
          }
        }
      }

      assert BillerConfig.validate_account_number(config, nil) == {:error, "Account number is required"}
      assert BillerConfig.validate_account_number(config, "") == {:error, "Account number is required"}
    end

    test "returns error for invalid pattern" do
      config = %BillerConfig{
        validation_rules: %{
          "account_number" => %{
            "pattern" => "^[0-9]{8,12}$"
          }
        }
      }

      assert BillerConfig.validate_account_number(config, "abc123") == {:error, "Invalid account number format"}
    end

    test "returns error for length violations" do
      config = %BillerConfig{
        validation_rules: %{
          "account_number" => %{
            "min_length" => 8,
            "max_length" => 12
          }
        }
      }

      assert BillerConfig.validate_account_number(config, "1234567") == {:error, "Account number too short"}
      assert BillerConfig.validate_account_number(config, "************3") == {:error, "Account number too long"}
    end

    test "returns :ok when no validation rules exist" do
      config = %BillerConfig{validation_rules: %{}}
      
      assert BillerConfig.validate_account_number(config, "anything") == :ok
    end

    test "allows nil when not required" do
      config = %BillerConfig{
        validation_rules: %{
          "account_number" => %{
            "required" => false
          }
        }
      }

      assert BillerConfig.validate_account_number(config, nil) == :ok
    end
  end

  describe "database integration" do
    test "can insert and retrieve biller config" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Blantyre Water Board - Postpaid",
        description: "Test description",
        base_url: "https://api.example.com",
        endpoints: %{
          "account_details" => "/accounts/{account_number}",
          "post_transaction" => "/transactions"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_payment" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{8,12}$"
          }
        }
      }

      {:ok, config} = %BillerConfig{}
      |> BillerConfig.changeset(attrs)
      |> Repo.insert()

      retrieved = Repo.get(BillerConfig, config.id)
      
      assert retrieved.biller_type == attrs.biller_type
      assert retrieved.display_name == attrs.display_name
      assert retrieved.endpoints == attrs.endpoints
      assert retrieved.authentication == attrs.authentication
      assert retrieved.features == attrs.features
      assert retrieved.validation_rules == attrs.validation_rules
    end

    test "enforces unique constraint on biller_type" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Blantyre Water Board - Postpaid",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"}
      }

      # Insert first config
      %BillerConfig{}
      |> BillerConfig.changeset(attrs)
      |> Repo.insert!()

      # Try to insert duplicate
      changeset = %BillerConfig{}
      |> BillerConfig.changeset(attrs)

      assert {:error, changeset} = Repo.insert(changeset)
      assert "has already been taken" in errors_on(changeset).biller_type
    end

    test "enforces unique constraint on biller_name" do
      attrs1 = %{
        biller_type: "bwb_postpaid",
        biller_name: "Unique Name",
        display_name: "Display Name 1",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"}
      }

      attrs2 = %{
        biller_type: "lwb_postpaid",
        biller_name: "Unique Name",
        display_name: "Display Name 2",
        base_url: "https://api.example.com",
        endpoints: %{"test" => "/test"}
      }

      # Insert first config
      %BillerConfig{}
      |> BillerConfig.changeset(attrs1)
      |> Repo.insert!()

      # Try to insert with same biller_name
      changeset = %BillerConfig{}
      |> BillerConfig.changeset(attrs2)

      assert {:error, changeset} = Repo.insert(changeset)
      assert "has already been taken" in errors_on(changeset).biller_name
    end
  end
end