defmodule ServiceManager.Schemas.Billers.BillerTransactionTest do
  use ServiceManager.DataCase

  alias ServiceManager.Schemas.Billers.BillerTransaction

  describe "changeset/2" do
    test "valid changeset with required fields" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "pending"
      }

      changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
      assert changeset.valid?
    end

    test "invalid changeset without required fields" do
      changeset = BillerTransaction.changeset(%BillerTransaction{}, %{})
      
      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).biller_type
      assert "can't be blank" in errors_on(changeset).biller_name
      assert "can't be blank" in errors_on(changeset).account_number
      assert "can't be blank" in errors_on(changeset).our_transaction_id
      assert "can't be blank" in errors_on(changeset).transaction_type
      assert "can't be blank" in errors_on(changeset).status
    end

    test "validates biller_type inclusion" do
      attrs = %{
        biller_type: "invalid_biller",
        biller_name: "Test Biller",
        account_number: "12345",
        our_transaction_id: "TT123",
        transaction_type: "account_details",
        status: "pending"
      }

      changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).biller_type
    end

    test "validates transaction_type inclusion" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "12345",
        our_transaction_id: "TT123",
        transaction_type: "invalid_type",
        status: "pending"
      }

      changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).transaction_type
    end

    test "validates status inclusion" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "12345",
        our_transaction_id: "TT123",
        transaction_type: "account_details",
        status: "invalid_status"
      }

      changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).status
    end

    test "validates amount is greater than 0 when provided" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "12345",
        our_transaction_id: "TT123",
        transaction_type: "post_transaction",
        status: "pending",
        amount: -100
      }

      changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "must be greater than 0" in errors_on(changeset).amount
    end

    test "validates currency inclusion" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "12345",
        our_transaction_id: "TT123",
        transaction_type: "post_transaction",
        status: "pending",
        amount: 100,
        currency: "INVALID"
      }

      changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).currency
    end

    test "accepts valid biller types" do
      valid_types = [
        "register_general",
        "bwb_postpaid",
        "lwb_postpaid",
        "srwb_postpaid",
        "srwb_prepaid",
        "masm",
        "airtel_validation",
        "tnm_bundles"
      ]

      Enum.each(valid_types, fn biller_type ->
        attrs = %{
          biller_type: biller_type,
          biller_name: "Test Biller",
          account_number: "12345",
          our_transaction_id: "TT123",
          transaction_type: "account_details",
          status: "pending"
        }

        changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
        assert changeset.valid?, "#{biller_type} should be valid"
      end)
    end

    test "accepts valid transaction types" do
      valid_types = [
        "account_details",
        "post_transaction",
        "get_invoice",
        "confirm_invoice",
        "bundle_details",
        "confirm_bundle",
        "validation"
      ]

      Enum.each(valid_types, fn transaction_type ->
        attrs = %{
          biller_type: "bwb_postpaid",
          biller_name: "Test Biller",
          account_number: "12345",
          our_transaction_id: "TT123",
          transaction_type: transaction_type,
          status: "pending"
        }

        changeset = BillerTransaction.changeset(%BillerTransaction{}, attrs)
        assert changeset.valid?, "#{transaction_type} should be valid"
      end)
    end
  end

  describe "account_details_changeset/2" do
    test "sets transaction_type to account_details" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT123",
        status: "pending"
      }

      changeset = BillerTransaction.account_details_changeset(%BillerTransaction{}, attrs)
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :transaction_type) == "account_details"
    end

    test "requires account_number" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        our_transaction_id: "TT123",
        status: "pending"
      }

      changeset = BillerTransaction.account_details_changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).account_number
    end
  end

  describe "payment_changeset/2" do
    test "sets transaction_type to post_transaction" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT123",
        status: "pending",
        amount: 100,
        credit_account: "*************",
        debit_account: "************",
        customer_account_number: "************",
        customer_account_name: "Test User"
      }

      changeset = BillerTransaction.payment_changeset(%BillerTransaction{}, attrs)
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :transaction_type) == "post_transaction"
    end

    test "requires payment fields" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT123",
        status: "pending"
      }

      changeset = BillerTransaction.payment_changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).amount
      assert "can't be blank" in errors_on(changeset).credit_account
      assert "can't be blank" in errors_on(changeset).debit_account
      assert "can't be blank" in errors_on(changeset).customer_account_number
      assert "can't be blank" in errors_on(changeset).customer_account_name
    end
  end

  describe "invoice_changeset/2" do
    test "validates transaction_type inclusion for invoices" do
      attrs = %{
        biller_type: "register_general",
        biller_name: "Register General",
        account_number: "BEDPVE",
        our_transaction_id: "TT123",
        status: "pending",
        transaction_type: "get_invoice"
      }

      changeset = BillerTransaction.invoice_changeset(%BillerTransaction{}, attrs)
      assert changeset.valid?
    end

    test "rejects invalid transaction_type for invoices" do
      attrs = %{
        biller_type: "register_general",
        biller_name: "Register General",
        account_number: "BEDPVE",
        our_transaction_id: "TT123",
        status: "pending",
        transaction_type: "account_details"
      }

      changeset = BillerTransaction.invoice_changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).transaction_type
    end
  end

  describe "bundle_changeset/2" do
    test "sets biller_type to tnm_bundles and requires bundle_id" do
      attrs = %{
        bundle_id: "239678",
        our_transaction_id: "TT123",
        status: "pending",
        transaction_type: "bundle_details"
      }

      changeset = BillerTransaction.bundle_changeset(%BillerTransaction{}, attrs)
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :biller_type) == "tnm_bundles"
    end

    test "requires bundle_id" do
      attrs = %{
        our_transaction_id: "TT123",
        status: "pending",
        transaction_type: "bundle_details"
      }

      changeset = BillerTransaction.bundle_changeset(%BillerTransaction{}, attrs)
      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).bundle_id
    end
  end

  describe "complete_transaction/2" do
    test "updates status to completed and sets response_payload" do
      transaction = %BillerTransaction{
        biller_type: "bwb_postpaid",
        status: "processing"
      }
      
      response_payload = %{"status" => "success", "balance" => "1000.00"}
      
      changeset = BillerTransaction.complete_transaction(transaction, response_payload)
      
      assert Ecto.Changeset.get_change(changeset, :status) == "completed"
      assert Ecto.Changeset.get_change(changeset, :response_payload) == response_payload
      assert Ecto.Changeset.get_change(changeset, :processed_at)
    end
  end

  describe "fail_transaction/2" do
    test "updates status to failed and sets error_message" do
      transaction = %BillerTransaction{
        biller_type: "bwb_postpaid",
        status: "processing"
      }
      
      error_message = "Account not found"
      
      changeset = BillerTransaction.fail_transaction(transaction, error_message)
      
      assert Ecto.Changeset.get_change(changeset, :status) == "failed"
      assert Ecto.Changeset.get_change(changeset, :error_message) == error_message
      assert Ecto.Changeset.get_change(changeset, :processed_at)
    end

    test "sets response_payload when provided" do
      transaction = %BillerTransaction{
        biller_type: "bwb_postpaid",
        status: "processing"
      }
      
      error_message = "Account not found"
      response_payload = %{"error" => "ACCOUNT_NOT_FOUND"}
      
      changeset = BillerTransaction.fail_transaction(transaction, error_message, response_payload)
      
      assert Ecto.Changeset.get_change(changeset, :status) == "failed"
      assert Ecto.Changeset.get_change(changeset, :error_message) == error_message
      assert Ecto.Changeset.get_change(changeset, :response_payload) == response_payload
    end
  end

  describe "database integration" do
    test "can insert and retrieve biller transaction" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "pending",
        request_payload: %{"method" => "GET", "endpoint" => "/accounts/********"},
        response_payload: %{"balance" => "1000.00", "status" => "ACTIVE"}
      }

      {:ok, transaction} = %BillerTransaction{}
      |> BillerTransaction.changeset(attrs)
      |> Repo.insert()

      retrieved = Repo.get(BillerTransaction, transaction.id)
      
      assert retrieved.biller_type == attrs.biller_type
      assert retrieved.account_number == attrs.account_number
      assert retrieved.our_transaction_id == attrs.our_transaction_id
      assert retrieved.request_payload == attrs.request_payload
      assert retrieved.response_payload == attrs.response_payload
    end

    test "enforces unique constraint on our_transaction_id" do
      attrs = %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "pending"
      }

      # Insert first transaction
      %BillerTransaction{}
      |> BillerTransaction.changeset(attrs)
      |> Repo.insert!()

      # Try to insert duplicate
      changeset = %BillerTransaction{}
      |> BillerTransaction.changeset(attrs)

      assert {:error, changeset} = Repo.insert(changeset)
      assert "has already been taken" in errors_on(changeset).our_transaction_id
    end
  end
end