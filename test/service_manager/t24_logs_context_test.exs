defmodule ServiceManager.Contexts.T24LogsContextTest do
  use ServiceManager.DataCase

  alias ServiceManager.Contexts.T24LogsContext

  describe "t_24_logs" do
    alias ServiceManager.Schemas.T24Log

    import ServiceManager.Contexts.T24LogsContextFixtures

    @invalid_attrs %{
      request: nil,
      response: nil,
      url: nil,
      headers: nil,
      message_id: nil,
      method: nil
    }

    test "list_t_24_logs/0 returns all t_24_logs" do
      t24_log = t24_log_fixture()
      assert T24LogsContext.list_t_24_logs() == [t24_log]
    end

    test "get_t24_log!/1 returns the t24_log with given id" do
      t24_log = t24_log_fixture()
      assert T24LogsContext.get_t24_log!(t24_log.id) == t24_log
    end

    test "create_t24_log/1 with valid data creates a t24_log" do
      valid_attrs = %{
        request: "some request",
        response: "some response",
        url: "some url",
        headers: "some headers",
        message_id: "some message_id",
        method: "some method"
      }

      assert {:ok, %T24Log{} = t24_log} = T24LogsContext.create_t24_log(valid_attrs)
      assert t24_log.request == "some request"
      assert t24_log.response == "some response"
      assert t24_log.url == "some url"
      assert t24_log.headers == "some headers"
      assert t24_log.message_id == "some message_id"
      assert t24_log.method == "some method"
    end

    test "create_t24_log/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = T24LogsContext.create_t24_log(@invalid_attrs)
    end

    test "update_t24_log/2 with valid data updates the t24_log" do
      t24_log = t24_log_fixture()

      update_attrs = %{
        request: "some updated request",
        response: "some updated response",
        url: "some updated url",
        headers: "some updated headers",
        message_id: "some updated message_id",
        method: "some updated method"
      }

      assert {:ok, %T24Log{} = t24_log} = T24LogsContext.update_t24_log(t24_log, update_attrs)
      assert t24_log.request == "some updated request"
      assert t24_log.response == "some updated response"
      assert t24_log.url == "some updated url"
      assert t24_log.headers == "some updated headers"
      assert t24_log.message_id == "some updated message_id"
      assert t24_log.method == "some updated method"
    end

    test "update_t24_log/2 with invalid data returns error changeset" do
      t24_log = t24_log_fixture()
      assert {:error, %Ecto.Changeset{}} = T24LogsContext.update_t24_log(t24_log, @invalid_attrs)
      assert t24_log == T24LogsContext.get_t24_log!(t24_log.id)
    end

    test "delete_t24_log/1 deletes the t24_log" do
      t24_log = t24_log_fixture()
      assert {:ok, %T24Log{}} = T24LogsContext.delete_t24_log(t24_log)
      assert_raise Ecto.NoResultsError, fn -> T24LogsContext.get_t24_log!(t24_log.id) end
    end

    test "change_t24_log/1 returns a t24_log changeset" do
      t24_log = t24_log_fixture()
      assert %Ecto.Changeset{} = T24LogsContext.change_t24_log(t24_log)
    end
  end
end
