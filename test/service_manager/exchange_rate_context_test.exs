defmodule ServiceManager.Contexts.ExchangeRateContextTest do
  use ServiceManager.DataCase

  alias ServiceManager.Contexts.ExchangeRateContext

  describe "exchange_rates" do
    alias ServiceManager.Schemas.ExchangeRate

    import ServiceManager.Contexts.ExchangeRateContextFixtures

    @invalid_attrs %{from_currency_code: nil, to_currency_code: nil, rate: nil}

    test "list_exchange_rates/0 returns all exchange_rates" do
      exchange_rate = exchange_rate_fixture()
      assert ExchangeRateContext.list_exchange_rates() == [exchange_rate]
    end

    test "get_exchange_rate!/1 returns the exchange_rate with given id" do
      exchange_rate = exchange_rate_fixture()
      assert ExchangeRateContext.get_exchange_rate!(exchange_rate.id) == exchange_rate
    end

    test "create_exchange_rate/1 with valid data creates a exchange_rate" do
      valid_attrs = %{
        from_currency_code: "some from_currency_code",
        to_currency_code: "some to_currency_code",
        rate: "120.5"
      }

      assert {:ok, %ExchangeRate{} = exchange_rate} =
               ExchangeRateContext.create_exchange_rate(valid_attrs)

      assert exchange_rate.from_currency_code == "some from_currency_code"
      assert exchange_rate.to_currency_code == "some to_currency_code"
      assert exchange_rate.rate == Decimal.new("120.5")
    end

    test "create_exchange_rate/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} =
               ExchangeRateContext.create_exchange_rate(@invalid_attrs)
    end

    test "update_exchange_rate/2 with valid data updates the exchange_rate" do
      exchange_rate = exchange_rate_fixture()

      update_attrs = %{
        from_currency_code: "some updated from_currency_code",
        to_currency_code: "some updated to_currency_code",
        rate: "456.7"
      }

      assert {:ok, %ExchangeRate{} = exchange_rate} =
               ExchangeRateContext.update_exchange_rate(exchange_rate, update_attrs)

      assert exchange_rate.from_currency_code == "some updated from_currency_code"
      assert exchange_rate.to_currency_code == "some updated to_currency_code"
      assert exchange_rate.rate == Decimal.new("456.7")
    end

    test "update_exchange_rate/2 with invalid data returns error changeset" do
      exchange_rate = exchange_rate_fixture()

      assert {:error, %Ecto.Changeset{}} =
               ExchangeRateContext.update_exchange_rate(exchange_rate, @invalid_attrs)

      assert exchange_rate == ExchangeRateContext.get_exchange_rate!(exchange_rate.id)
    end

    test "delete_exchange_rate/1 deletes the exchange_rate" do
      exchange_rate = exchange_rate_fixture()
      assert {:ok, %ExchangeRate{}} = ExchangeRateContext.delete_exchange_rate(exchange_rate)

      assert_raise Ecto.NoResultsError, fn ->
        ExchangeRateContext.get_exchange_rate!(exchange_rate.id)
      end
    end

    test "change_exchange_rate/1 returns a exchange_rate changeset" do
      exchange_rate = exchange_rate_fixture()
      assert %Ecto.Changeset{} = ExchangeRateContext.change_exchange_rate(exchange_rate)
    end
  end
end
