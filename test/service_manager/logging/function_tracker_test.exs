defmodule ServiceManager.Logging.FunctionTrackerTest do
  use ExUnit.Case
  alias ServiceManager.Logging.TestTracker

  test "tracks function calls in separate processes" do
    run_basic_tracking_test()
  end

  test "tracks function calls with chain ID" do
    chain_id = "test-chain-123"

    # Execute chained functions in the same process to maintain chain ID
    {:ok, amount} = TestTracker.process_transaction(chain_id, 100)
    {:ok, final_amount} = TestTracker.update_balance(amount)

    # Verify results
    assert amount == 100
    assert final_amount == 100

    # Get today's date for the log directory
    date = Calendar.strftime(DateTime.utc_now(), "%Y%m%d")
    log_dir = "logs/processes/tracking/#{date}"

    # Verify log files were created with chain ID prefix
    log_files = File.ls!(log_dir)
    chain_files = Enum.filter(log_files, &String.starts_with?(&1, chain_id))
    # At least one file for our chain
    assert length(chain_files) > 0

    # Print the log directory path for manual inspection
    IO.puts("\nChained log files created in: #{log_dir}")
    IO.puts("Chain files: #{inspect(chain_files)}")

    # Verify chain ID is cleared after the chain completes
    assert Process.get(:function_tracker_chain_id) == nil
  end

  defp run_basic_tracking_test do
    # Spawn multiple processes to test concurrent logging
    tasks = [
      Task.async(fn -> TestTracker.add(10, 20) end),
      Task.async(fn -> TestTracker.greet("Alice") end),
      Task.async(fn -> TestTracker.process_data([1, 2, 3]) end),
      Task.async(fn -> TestTracker.add(30, 40) end),
      Task.async(fn -> TestTracker.greet("Bob") end)
    ]

    # Wait for all tasks to complete
    results = Task.await_many(tasks)

    # Verify results
    assert Enum.at(results, 0) == 30
    assert Enum.at(results, 1) == "Hello, Alice!"
    assert Enum.at(results, 2) == [2, 4, 6]
    assert Enum.at(results, 3) == 70
    assert Enum.at(results, 4) == "Hello, Bob!"

    # Get today's date for the log directory
    date = Calendar.strftime(DateTime.utc_now(), "%Y%m%d")
    log_dir = "logs/processes/tracking/#{date}"

    # Verify log files were created
    log_files = File.ls!(log_dir)
    # One file per process
    assert length(log_files) == 5

    # Print the log directory path for manual inspection
    IO.puts("\nLog files created in: #{log_dir}")
    IO.puts("Log files: #{inspect(log_files)}")
  end
end
