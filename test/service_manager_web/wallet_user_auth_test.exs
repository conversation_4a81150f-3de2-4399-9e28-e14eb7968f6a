defmodule ServiceManagerWeb.WalletUserAuthTest do
  use ServiceManagerWeb.ConnCase, async: true

  alias Phoenix.LiveView
  alias ServiceManager.WalletAccounts
  alias ServiceManagerWeb.WalletUserAuth
  import ServiceManager.WalletAccountsFixtures

  @remember_me_cookie "_service_manager_web_wallet_user_remember_me"

  setup %{conn: conn} do
    conn =
      conn
      |> Map.replace!(:secret_key_base, ServiceManagerWeb.Endpoint.config(:secret_key_base))
      |> init_test_session(%{})

    %{wallet_user: wallet_user_fixture(), conn: conn}
  end

  describe "log_in_wallet_user/3" do
    test "stores the wallet_user token in the session", %{conn: conn, wallet_user: wallet_user} do
      conn = WalletUserAuth.log_in_wallet_user(conn, wallet_user)
      assert token = get_session(conn, :wallet_user_token)

      assert get_session(conn, :live_socket_id) ==
               "walletusers_sessions:#{Base.url_encode64(token)}"

      assert redirected_to(conn) == ~p"/"
      assert WalletAccounts.get_wallet_user_by_session_token(token)
    end

    test "clears everything previously stored in the session", %{
      conn: conn,
      wallet_user: wallet_user
    } do
      conn =
        conn
        |> put_session(:to_be_removed, "value")
        |> WalletUserAuth.log_in_wallet_user(wallet_user)

      refute get_session(conn, :to_be_removed)
    end

    test "redirects to the configured path", %{conn: conn, wallet_user: wallet_user} do
      conn =
        conn
        |> put_session(:wallet_user_return_to, "/hello")
        |> WalletUserAuth.log_in_wallet_user(wallet_user)

      assert redirected_to(conn) == "/hello"
    end

    test "writes a cookie if remember_me is configured", %{conn: conn, wallet_user: wallet_user} do
      conn =
        conn
        |> fetch_cookies()
        |> WalletUserAuth.log_in_wallet_user(wallet_user, %{"remember_me" => "true"})

      assert get_session(conn, :wallet_user_token) == conn.cookies[@remember_me_cookie]

      assert %{value: signed_token, max_age: max_age} = conn.resp_cookies[@remember_me_cookie]
      assert signed_token != get_session(conn, :wallet_user_token)
      assert max_age == 5_184_000
    end
  end

  describe "logout_wallet_user/1" do
    test "erases session and cookies", %{conn: conn, wallet_user: wallet_user} do
      wallet_user_token = WalletAccounts.generate_wallet_user_session_token(wallet_user)

      conn =
        conn
        |> put_session(:wallet_user_token, wallet_user_token)
        |> put_req_cookie(@remember_me_cookie, wallet_user_token)
        |> fetch_cookies()
        |> WalletUserAuth.log_out_wallet_user()

      refute get_session(conn, :wallet_user_token)
      refute conn.cookies[@remember_me_cookie]
      assert %{max_age: 0} = conn.resp_cookies[@remember_me_cookie]
      assert redirected_to(conn) == ~p"/"
      refute WalletAccounts.get_wallet_user_by_session_token(wallet_user_token)
    end

    test "broadcasts to the given live_socket_id", %{conn: conn} do
      live_socket_id = "walletusers_sessions:abcdef-token"
      ServiceManagerWeb.Endpoint.subscribe(live_socket_id)

      conn
      |> put_session(:live_socket_id, live_socket_id)
      |> WalletUserAuth.log_out_wallet_user()

      assert_receive %Phoenix.Socket.Broadcast{event: "disconnect", topic: ^live_socket_id}
    end

    test "works even if wallet_user is already logged out", %{conn: conn} do
      conn = conn |> fetch_cookies() |> WalletUserAuth.log_out_wallet_user()
      refute get_session(conn, :wallet_user_token)
      assert %{max_age: 0} = conn.resp_cookies[@remember_me_cookie]
      assert redirected_to(conn) == ~p"/"
    end
  end

  describe "fetch_current_wallet_user/2" do
    test "authenticates wallet_user from session", %{conn: conn, wallet_user: wallet_user} do
      wallet_user_token = WalletAccounts.generate_wallet_user_session_token(wallet_user)

      conn =
        conn
        |> put_session(:wallet_user_token, wallet_user_token)
        |> WalletUserAuth.fetch_current_wallet_user([])

      assert conn.assigns.current_wallet_user.id == wallet_user.id
    end

    test "authenticates wallet_user from cookies", %{conn: conn, wallet_user: wallet_user} do
      logged_in_conn =
        conn
        |> fetch_cookies()
        |> WalletUserAuth.log_in_wallet_user(wallet_user, %{"remember_me" => "true"})

      wallet_user_token = logged_in_conn.cookies[@remember_me_cookie]
      %{value: signed_token} = logged_in_conn.resp_cookies[@remember_me_cookie]

      conn =
        conn
        |> put_req_cookie(@remember_me_cookie, signed_token)
        |> WalletUserAuth.fetch_current_wallet_user([])

      assert conn.assigns.current_wallet_user.id == wallet_user.id
      assert get_session(conn, :wallet_user_token) == wallet_user_token

      assert get_session(conn, :live_socket_id) ==
               "walletusers_sessions:#{Base.url_encode64(wallet_user_token)}"
    end

    test "does not authenticate if data is missing", %{conn: conn, wallet_user: wallet_user} do
      _ = WalletAccounts.generate_wallet_user_session_token(wallet_user)
      conn = WalletUserAuth.fetch_current_wallet_user(conn, [])
      refute get_session(conn, :wallet_user_token)
      refute conn.assigns.current_wallet_user
    end
  end

  describe "on_mount :mount_current_wallet_user" do
    test "assigns current_wallet_user based on a valid wallet_user_token", %{
      conn: conn,
      wallet_user: wallet_user
    } do
      wallet_user_token = WalletAccounts.generate_wallet_user_session_token(wallet_user)
      session = conn |> put_session(:wallet_user_token, wallet_user_token) |> get_session()

      {:cont, updated_socket} =
        WalletUserAuth.on_mount(:mount_current_wallet_user, %{}, session, %LiveView.Socket{})

      assert updated_socket.assigns.current_wallet_user.id == wallet_user.id
    end

    test "assigns nil to current_wallet_user assign if there isn't a valid wallet_user_token", %{
      conn: conn
    } do
      wallet_user_token = "invalid_token"
      session = conn |> put_session(:wallet_user_token, wallet_user_token) |> get_session()

      {:cont, updated_socket} =
        WalletUserAuth.on_mount(:mount_current_wallet_user, %{}, session, %LiveView.Socket{})

      assert updated_socket.assigns.current_wallet_user == nil
    end

    test "assigns nil to current_wallet_user assign if there isn't a wallet_user_token", %{
      conn: conn
    } do
      session = conn |> get_session()

      {:cont, updated_socket} =
        WalletUserAuth.on_mount(:mount_current_wallet_user, %{}, session, %LiveView.Socket{})

      assert updated_socket.assigns.current_wallet_user == nil
    end
  end

  describe "on_mount :ensure_authenticated" do
    test "authenticates current_wallet_user based on a valid wallet_user_token", %{
      conn: conn,
      wallet_user: wallet_user
    } do
      wallet_user_token = WalletAccounts.generate_wallet_user_session_token(wallet_user)
      session = conn |> put_session(:wallet_user_token, wallet_user_token) |> get_session()

      {:cont, updated_socket} =
        WalletUserAuth.on_mount(:ensure_authenticated, %{}, session, %LiveView.Socket{})

      assert updated_socket.assigns.current_wallet_user.id == wallet_user.id
    end

    test "redirects to login page if there isn't a valid wallet_user_token", %{conn: conn} do
      wallet_user_token = "invalid_token"
      session = conn |> put_session(:wallet_user_token, wallet_user_token) |> get_session()

      socket = %LiveView.Socket{
        endpoint: ServiceManagerWeb.Endpoint,
        assigns: %{__changed__: %{}, flash: %{}}
      }

      {:halt, updated_socket} =
        WalletUserAuth.on_mount(:ensure_authenticated, %{}, session, socket)

      assert updated_socket.assigns.current_wallet_user == nil
    end

    test "redirects to login page if there isn't a wallet_user_token", %{conn: conn} do
      session = conn |> get_session()

      socket = %LiveView.Socket{
        endpoint: ServiceManagerWeb.Endpoint,
        assigns: %{__changed__: %{}, flash: %{}}
      }

      {:halt, updated_socket} =
        WalletUserAuth.on_mount(:ensure_authenticated, %{}, session, socket)

      assert updated_socket.assigns.current_wallet_user == nil
    end
  end

  describe "on_mount :redirect_if_wallet_user_is_authenticated" do
    test "redirects if there is an authenticated  wallet_user ", %{
      conn: conn,
      wallet_user: wallet_user
    } do
      wallet_user_token = WalletAccounts.generate_wallet_user_session_token(wallet_user)
      session = conn |> put_session(:wallet_user_token, wallet_user_token) |> get_session()

      assert {:halt, _updated_socket} =
               WalletUserAuth.on_mount(
                 :redirect_if_wallet_user_is_authenticated,
                 %{},
                 session,
                 %LiveView.Socket{}
               )
    end

    test "doesn't redirect if there is no authenticated wallet_user", %{conn: conn} do
      session = conn |> get_session()

      assert {:cont, _updated_socket} =
               WalletUserAuth.on_mount(
                 :redirect_if_wallet_user_is_authenticated,
                 %{},
                 session,
                 %LiveView.Socket{}
               )
    end
  end

  describe "redirect_if_wallet_user_is_authenticated/2" do
    test "redirects if wallet_user is authenticated", %{conn: conn, wallet_user: wallet_user} do
      conn =
        conn
        |> assign(:current_wallet_user, wallet_user)
        |> WalletUserAuth.redirect_if_wallet_user_is_authenticated([])

      assert conn.halted
      assert redirected_to(conn) == ~p"/"
    end

    test "does not redirect if wallet_user is not authenticated", %{conn: conn} do
      conn = WalletUserAuth.redirect_if_wallet_user_is_authenticated(conn, [])
      refute conn.halted
      refute conn.status
    end
  end

  describe "require_authenticated_wallet_user/2" do
    test "redirects if wallet_user is not authenticated", %{conn: conn} do
      conn = conn |> fetch_flash() |> WalletUserAuth.require_authenticated_wallet_user([])
      assert conn.halted

      assert redirected_to(conn) == ~p"/walletusers/log_in"

      assert Phoenix.Flash.get(conn.assigns.flash, :error) ==
               "You must log in to access this page."
    end

    test "stores the path to redirect to on GET", %{conn: conn} do
      halted_conn =
        %{conn | path_info: ["foo"], query_string: ""}
        |> fetch_flash()
        |> WalletUserAuth.require_authenticated_wallet_user([])

      assert halted_conn.halted
      assert get_session(halted_conn, :wallet_user_return_to) == "/foo"

      halted_conn =
        %{conn | path_info: ["foo"], query_string: "bar=baz"}
        |> fetch_flash()
        |> WalletUserAuth.require_authenticated_wallet_user([])

      assert halted_conn.halted
      assert get_session(halted_conn, :wallet_user_return_to) == "/foo?bar=baz"

      halted_conn =
        %{conn | path_info: ["foo"], query_string: "bar", method: "POST"}
        |> fetch_flash()
        |> WalletUserAuth.require_authenticated_wallet_user([])

      assert halted_conn.halted
      refute get_session(halted_conn, :wallet_user_return_to)
    end

    test "does not redirect if wallet_user is authenticated", %{
      conn: conn,
      wallet_user: wallet_user
    } do
      conn =
        conn
        |> assign(:current_wallet_user, wallet_user)
        |> WalletUserAuth.require_authenticated_wallet_user([])

      refute conn.halted
      refute conn.status
    end
  end
end
