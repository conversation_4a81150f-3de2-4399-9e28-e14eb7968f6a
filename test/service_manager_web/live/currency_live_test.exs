defmodule ServiceManagerWeb.Backend.CurrencyLiveTest do
  use ServiceManagerWeb.ConnCase

  import Phoenix.LiveViewTest
  import ServiceManager.Contexts.CurrencyContextFixtures

  @create_attrs %{
    code: "some code",
    name: "some name",
    status: "some status",
    symbol: "some symbol",
    currency_id: "7488a646-e31f-11e4-aace-600308960662",
    country: "some country",
    exchange_rate: "120.5",
    minor_unit: 42,
    notes: "some notes"
  }
  @update_attrs %{
    code: "some updated code",
    name: "some updated name",
    status: "some updated status",
    symbol: "some updated symbol",
    currency_id: "7488a646-e31f-11e4-aace-600308960668",
    country: "some updated country",
    exchange_rate: "456.7",
    minor_unit: 43,
    notes: "some updated notes"
  }
  @invalid_attrs %{
    code: nil,
    name: nil,
    status: nil,
    symbol: nil,
    currency_id: nil,
    country: nil,
    exchange_rate: nil,
    minor_unit: nil,
    notes: nil
  }

  defp create_currency(_) do
    currency = currency_fixture()
    %{currency: currency}
  end

  describe "Index" do
    setup [:create_currency]

    test "lists all currencies", %{conn: conn, currency: currency} do
      {:ok, _index_live, html} = live(conn, ~p"/mobileBanking/currencies")

      assert html =~ "Listing Currencies"
      assert html =~ currency.code
    end

    test "saves new currency", %{conn: conn} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/currencies")

      assert index_live |> element("a", "New Currency") |> render_click() =~
               "New Currency"

      assert_patch(index_live, ~p"/mobileBanking/currencies/new")

      assert index_live
             |> form("#currency-form", currency: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#currency-form", currency: @create_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/currencies")

      html = render(index_live)
      assert html =~ "Currency created successfully"
      assert html =~ "some code"
    end

    test "updates currency in listing", %{conn: conn, currency: currency} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/currencies")

      assert index_live |> element("#currencies-#{currency.id} a", "Edit") |> render_click() =~
               "Edit Currency"

      assert_patch(index_live, ~p"/mobileBanking/currencies/#{currency}/edit")

      assert index_live
             |> form("#currency-form", currency: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#currency-form", currency: @update_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/currencies")

      html = render(index_live)
      assert html =~ "Currency updated successfully"
      assert html =~ "some updated code"
    end

    test "deletes currency in listing", %{conn: conn, currency: currency} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/currencies")

      assert index_live |> element("#currencies-#{currency.id} a", "Delete") |> render_click()
      refute has_element?(index_live, "#currencies-#{currency.id}")
    end
  end

  describe "Show" do
    setup [:create_currency]

    test "displays currency", %{conn: conn, currency: currency} do
      {:ok, _show_live, html} = live(conn, ~p"/mobileBanking/currencies/#{currency}")

      assert html =~ "Show Currency"
      assert html =~ currency.code
    end

    test "updates currency within modal", %{conn: conn, currency: currency} do
      {:ok, show_live, _html} = live(conn, ~p"/mobileBanking/currencies/#{currency}")

      assert show_live |> element("a", "Edit") |> render_click() =~
               "Edit Currency"

      assert_patch(show_live, ~p"/mobileBanking/currencies/#{currency}/show/edit")

      assert show_live
             |> form("#currency-form", currency: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert show_live
             |> form("#currency-form", currency: @update_attrs)
             |> render_submit()

      assert_patch(show_live, ~p"/mobileBanking/currencies/#{currency}")

      html = render(show_live)
      assert html =~ "Currency updated successfully"
      assert html =~ "some updated code"
    end
  end
end
