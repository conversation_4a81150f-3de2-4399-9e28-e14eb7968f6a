defmodule ServiceManagerWeb.WalletUserConfirmationInstructionsLiveTest do
  use ServiceManagerWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import ServiceManager.WalletAccountsFixtures

  alias ServiceManager.WalletAccounts
  alias ServiceManager.Repo

  setup do
    %{wallet_user: wallet_user_fixture()}
  end

  describe "Resend confirmation" do
    test "renders the resend confirmation page", %{conn: conn} do
      {:ok, _lv, html} = live(conn, ~p"/walletusers/confirm")
      assert html =~ "Resend confirmation instructions"
    end

    test "sends a new confirmation token", %{conn: conn, wallet_user: wallet_user} do
      {:ok, lv, _html} = live(conn, ~p"/walletusers/confirm")

      {:ok, conn} =
        lv
        |> form("#resend_confirmation_form", wallet_user: %{email: wallet_user.email})
        |> render_submit()
        |> follow_redirect(conn, ~p"/")

      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~
               "If your email is in our system"

      assert Repo.get_by!(WalletAccounts.WalletUserToken, wallet_user_id: wallet_user.id).context ==
               "confirm"
    end

    test "does not send confirmation token if wallet_user is confirmed", %{
      conn: conn,
      wallet_user: wallet_user
    } do
      Repo.update!(WalletAccounts.WalletUser.confirm_changeset(wallet_user))

      {:ok, lv, _html} = live(conn, ~p"/walletusers/confirm")

      {:ok, conn} =
        lv
        |> form("#resend_confirmation_form", wallet_user: %{email: wallet_user.email})
        |> render_submit()
        |> follow_redirect(conn, ~p"/")

      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~
               "If your email is in our system"

      refute Repo.get_by(WalletAccounts.WalletUserToken, wallet_user_id: wallet_user.id)
    end

    test "does not send confirmation token if email is invalid", %{conn: conn} do
      {:ok, lv, _html} = live(conn, ~p"/walletusers/confirm")

      {:ok, conn} =
        lv
        |> form("#resend_confirmation_form", wallet_user: %{email: "<EMAIL>"})
        |> render_submit()
        |> follow_redirect(conn, ~p"/")

      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~
               "If your email is in our system"

      assert Repo.all(WalletAccounts.WalletUserToken) == []
    end
  end
end
