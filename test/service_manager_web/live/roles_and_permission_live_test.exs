defmodule ServiceManagerWeb.RolesAndPermissionLiveTest do
  use ServiceManagerWeb.ConnCase

  import Phoenix.LiveViewTest
  import ServiceManager.Contexts.RolesAndPermissionsContextFixtures

  @create_attrs %{
    name: "some name",
    status: "some status",
    rights: %{},
    created_by: 42,
    updated_by: 42
  }
  @update_attrs %{
    name: "some updated name",
    status: "some updated status",
    rights: %{},
    created_by: 43,
    updated_by: 43
  }
  @invalid_attrs %{name: nil, status: nil, rights: nil, created_by: nil, updated_by: nil}

  defp create_roles_and_permission(_) do
    roles_and_permission = roles_and_permission_fixture()
    %{roles_and_permission: roles_and_permission}
  end

  describe "Index" do
    setup [:create_roles_and_permission]

    test "lists all roles_and_permissions", %{
      conn: conn,
      roles_and_permission: roles_and_permission
    } do
      {:ok, _index_live, html} = live(conn, ~p"/mobileBanking/ruserRoles&permissions")

      assert html =~ "Listing Roles and permissions"
      assert html =~ roles_and_permission.name
    end

    test "saves new roles_and_permission", %{conn: conn} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/ruserRoles&permissions")

      assert index_live |> element("a", "New Roles and permission") |> render_click() =~
               "New Roles and permission"

      assert_patch(index_live, ~p"/mobileBanking/ruserRoles&permissions/new")

      assert index_live
             |> form("#roles_and_permission-form", roles_and_permission: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#roles_and_permission-form", roles_and_permission: @create_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/ruserRoles&permissions")

      html = render(index_live)
      assert html =~ "Roles and permission created successfully"
      assert html =~ "some name"
    end

    test "updates roles_and_permission in listing", %{
      conn: conn,
      roles_and_permission: roles_and_permission
    } do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/ruserRoles&permissions")

      assert index_live
             |> element("#roles_and_permissions-#{roles_and_permission.id} a", "Edit")
             |> render_click() =~
               "Edit Roles and permission"

      assert_patch(
        index_live,
        ~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}/edit"
      )

      assert index_live
             |> form("#roles_and_permission-form", roles_and_permission: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#roles_and_permission-form", roles_and_permission: @update_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/ruserRoles&permissions")

      html = render(index_live)
      assert html =~ "Roles and permission updated successfully"
      assert html =~ "some updated name"
    end

    test "deletes roles_and_permission in listing", %{
      conn: conn,
      roles_and_permission: roles_and_permission
    } do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/ruserRoles&permissions")

      assert index_live
             |> element("#roles_and_permissions-#{roles_and_permission.id} a", "Delete")
             |> render_click()

      refute has_element?(index_live, "#roles_and_permissions-#{roles_and_permission.id}")
    end
  end

  describe "Show" do
    setup [:create_roles_and_permission]

    test "displays roles_and_permission", %{
      conn: conn,
      roles_and_permission: roles_and_permission
    } do
      {:ok, _show_live, html} =
        live(conn, ~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}")

      assert html =~ "Show Roles and permission"
      assert html =~ roles_and_permission.name
    end

    test "updates roles_and_permission within modal", %{
      conn: conn,
      roles_and_permission: roles_and_permission
    } do
      {:ok, show_live, _html} =
        live(conn, ~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}")

      assert show_live |> element("a", "Edit") |> render_click() =~
               "Edit Roles and permission"

      assert_patch(
        show_live,
        ~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}/show/edit"
      )

      assert show_live
             |> form("#roles_and_permission-form", roles_and_permission: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert show_live
             |> form("#roles_and_permission-form", roles_and_permission: @update_attrs)
             |> render_submit()

      assert_patch(show_live, ~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}")

      html = render(show_live)
      assert html =~ "Roles and permission updated successfully"
      assert html =~ "some updated name"
    end
  end
end
