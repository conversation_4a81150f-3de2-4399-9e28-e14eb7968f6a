defmodule ServiceManagerWeb.Backend.PaymentMethodLiveTest do
  use ServiceManagerWeb.ConnCase

  import Phoenix.LiveViewTest
  import ServiceManager.Contexts.PaymentMethodsContextFixtures

  @create_attrs %{
    name: "some name",
    status: true,
    type: "some type",
    description: "some description",
    fees_id: 42,
    created_by: 42,
    updated_by: 42
  }
  @update_attrs %{
    name: "some updated name",
    status: false,
    type: "some updated type",
    description: "some updated description",
    fees_id: 43,
    created_by: 43,
    updated_by: 43
  }
  @invalid_attrs %{
    name: nil,
    status: false,
    type: nil,
    description: nil,
    fees_id: nil,
    created_by: nil,
    updated_by: nil
  }

  defp create_payment_method(_) do
    payment_method = payment_method_fixture()
    %{payment_method: payment_method}
  end

  describe "Index" do
    setup [:create_payment_method]

    test "lists all payment_methods", %{conn: conn, payment_method: payment_method} do
      {:ok, _index_live, html} = live(conn, ~p"/mobileBanking/paymentMethods")

      assert html =~ "Listing Payment methods"
      assert html =~ payment_method.name
    end

    test "saves new payment_method", %{conn: conn} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/paymentMethods")

      assert index_live |> element("a", "New Payment method") |> render_click() =~
               "New Payment method"

      assert_patch(index_live, ~p"/mobileBanking/paymentMethods/new")

      assert index_live
             |> form("#payment_method-form", payment_method: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#payment_method-form", payment_method: @create_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/paymentMethods")

      html = render(index_live)
      assert html =~ "Payment method created successfully"
      assert html =~ "some name"
    end

    test "updates payment_method in listing", %{conn: conn, payment_method: payment_method} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/paymentMethods")

      assert index_live
             |> element("#payment_methods-#{payment_method.id} a", "Edit")
             |> render_click() =~
               "Edit Payment method"

      assert_patch(index_live, ~p"/mobileBanking/paymentMethods/#{payment_method}/edit")

      assert index_live
             |> form("#payment_method-form", payment_method: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#payment_method-form", payment_method: @update_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/paymentMethods")

      html = render(index_live)
      assert html =~ "Payment method updated successfully"
      assert html =~ "some updated name"
    end

    test "deletes payment_method in listing", %{conn: conn, payment_method: payment_method} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/paymentMethods")

      assert index_live
             |> element("#payment_methods-#{payment_method.id} a", "Delete")
             |> render_click()

      refute has_element?(index_live, "#payment_methods-#{payment_method.id}")
    end
  end

  describe "Show" do
    setup [:create_payment_method]

    test "displays payment_method", %{conn: conn, payment_method: payment_method} do
      {:ok, _show_live, html} = live(conn, ~p"/mobileBanking/paymentMethods/#{payment_method}")

      assert html =~ "Show Payment method"
      assert html =~ payment_method.name
    end

    test "updates payment_method within modal", %{conn: conn, payment_method: payment_method} do
      {:ok, show_live, _html} = live(conn, ~p"/mobileBanking/paymentMethods/#{payment_method}")

      assert show_live |> element("a", "Edit") |> render_click() =~
               "Edit Payment method"

      assert_patch(show_live, ~p"/mobileBanking/paymentMethods/#{payment_method}/show/edit")

      assert show_live
             |> form("#payment_method-form", payment_method: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert show_live
             |> form("#payment_method-form", payment_method: @update_attrs)
             |> render_submit()

      assert_patch(show_live, ~p"/mobileBanking/paymentMethods/#{payment_method}")

      html = render(show_live)
      assert html =~ "Payment method updated successfully"
      assert html =~ "some updated name"
    end
  end
end
