defmodule ServiceManagerWeb.Backend.ExchangeRateLiveTest do
  use ServiceManagerWeb.ConnCase

  import Phoenix.LiveViewTest
  import ServiceManager.Contexts.ExchangeRateContextFixtures

  @create_attrs %{
    from_currency_code: "some from_currency_code",
    to_currency_code: "some to_currency_code",
    rate: "120.5"
  }
  @update_attrs %{
    from_currency_code: "some updated from_currency_code",
    to_currency_code: "some updated to_currency_code",
    rate: "456.7"
  }
  @invalid_attrs %{from_currency_code: nil, to_currency_code: nil, rate: nil}

  defp create_exchange_rate(_) do
    exchange_rate = exchange_rate_fixture()
    %{exchange_rate: exchange_rate}
  end

  describe "Index" do
    setup [:create_exchange_rate]

    test "lists all exchange_rates", %{conn: conn, exchange_rate: exchange_rate} do
      {:ok, _index_live, html} = live(conn, ~p"/mobileBanking/exchange_rates")

      assert html =~ "Listing Exchange rates"
      assert html =~ exchange_rate.from_currency_code
    end

    test "saves new exchange_rate", %{conn: conn} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/exchange_rates")

      assert index_live |> element("a", "New Exchange rate") |> render_click() =~
               "New Exchange rate"

      assert_patch(index_live, ~p"/mobileBanking/exchange_rates/new")

      assert index_live
             |> form("#exchange_rate-form", exchange_rate: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#exchange_rate-form", exchange_rate: @create_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/exchange_rates")

      html = render(index_live)
      assert html =~ "Exchange rate created successfully"
      assert html =~ "some from_currency_code"
    end

    test "updates exchange_rate in listing", %{conn: conn, exchange_rate: exchange_rate} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/exchange_rates")

      assert index_live
             |> element("#exchange_rates-#{exchange_rate.id} a", "Edit")
             |> render_click() =~
               "Edit Exchange rate"

      assert_patch(index_live, ~p"/mobileBanking/exchange_rates/#{exchange_rate}/edit")

      assert index_live
             |> form("#exchange_rate-form", exchange_rate: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#exchange_rate-form", exchange_rate: @update_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/exchange_rates")

      html = render(index_live)
      assert html =~ "Exchange rate updated successfully"
      assert html =~ "some updated from_currency_code"
    end

    test "deletes exchange_rate in listing", %{conn: conn, exchange_rate: exchange_rate} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/exchange_rates")

      assert index_live
             |> element("#exchange_rates-#{exchange_rate.id} a", "Delete")
             |> render_click()

      refute has_element?(index_live, "#exchange_rates-#{exchange_rate.id}")
    end
  end

  describe "Show" do
    setup [:create_exchange_rate]

    test "displays exchange_rate", %{conn: conn, exchange_rate: exchange_rate} do
      {:ok, _show_live, html} = live(conn, ~p"/mobileBanking/exchange_rates/#{exchange_rate}")

      assert html =~ "Show Exchange rate"
      assert html =~ exchange_rate.from_currency_code
    end

    test "updates exchange_rate within modal", %{conn: conn, exchange_rate: exchange_rate} do
      {:ok, show_live, _html} = live(conn, ~p"/mobileBanking/exchange_rates/#{exchange_rate}")

      assert show_live |> element("a", "Edit") |> render_click() =~
               "Edit Exchange rate"

      assert_patch(show_live, ~p"/mobileBanking/exchange_rates/#{exchange_rate}/show/edit")

      assert show_live
             |> form("#exchange_rate-form", exchange_rate: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert show_live
             |> form("#exchange_rate-form", exchange_rate: @update_attrs)
             |> render_submit()

      assert_patch(show_live, ~p"/mobileBanking/exchange_rates/#{exchange_rate}")

      html = render(show_live)
      assert html =~ "Exchange rate updated successfully"
      assert html =~ "some updated from_currency_code"
    end
  end
end
