defmodule ServiceManagerWeb.WalletUserForgotPasswordLiveTest do
  use ServiceManagerWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import ServiceManager.WalletAccountsFixtures

  alias ServiceManager.WalletAccounts
  alias ServiceManager.Repo

  describe "Forgot password page" do
    test "renders email page", %{conn: conn} do
      {:ok, lv, html} = live(conn, ~p"/walletusers/reset_password")

      assert html =~ "Forgot your password?"
      assert has_element?(lv, ~s|a[href="#{~p"/walletusers/register"}"]|, "Register")
      assert has_element?(lv, ~s|a[href="#{~p"/walletusers/log_in"}"]|, "Log in")
    end

    test "redirects if already logged in", %{conn: conn} do
      result =
        conn
        |> log_in_wallet_user(wallet_user_fixture())
        |> live(~p"/walletusers/reset_password")
        |> follow_redirect(conn, ~p"/")

      assert {:ok, _conn} = result
    end
  end

  describe "Reset link" do
    setup do
      %{wallet_user: wallet_user_fixture()}
    end

    test "sends a new reset password token", %{conn: conn, wallet_user: wallet_user} do
      {:ok, lv, _html} = live(conn, ~p"/walletusers/reset_password")

      {:ok, conn} =
        lv
        |> form("#reset_password_form", wallet_user: %{"email" => wallet_user.email})
        |> render_submit()
        |> follow_redirect(conn, "/")

      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~ "If your email is in our system"

      assert Repo.get_by!(WalletAccounts.WalletUserToken, wallet_user_id: wallet_user.id).context ==
               "reset_password"
    end

    test "does not send reset password token if email is invalid", %{conn: conn} do
      {:ok, lv, _html} = live(conn, ~p"/walletusers/reset_password")

      {:ok, conn} =
        lv
        |> form("#reset_password_form", wallet_user: %{"email" => "<EMAIL>"})
        |> render_submit()
        |> follow_redirect(conn, "/")

      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~ "If your email is in our system"
      assert Repo.all(WalletAccounts.WalletUserToken) == []
    end
  end
end
