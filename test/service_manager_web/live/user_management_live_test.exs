defmodule ServiceManagerWeb.UserManagementLiveTest do
  use ServiceManagerWeb.ConnCase

  import Phoenix.LiveViewTest
  import ServiceManager.UserManagementContextFixtures

  @create_attrs %{
    name: "some name",
    state: "some state",
    zip: "some zip",
    address: "some address",
    email: "some email",
    nickname: "some nickname",
    first_name: "some first_name",
    last_name: "some last_name",
    phone_number: "some phone_number",
    date_of_birth: "2024-10-01",
    city: "some city",
    country: "some country"
  }
  @update_attrs %{
    name: "some updated name",
    state: "some updated state",
    zip: "some updated zip",
    address: "some updated address",
    email: "some updated email",
    nickname: "some updated nickname",
    first_name: "some updated first_name",
    last_name: "some updated last_name",
    phone_number: "some updated phone_number",
    date_of_birth: "2024-10-02",
    city: "some updated city",
    country: "some updated country"
  }
  @invalid_attrs %{
    name: nil,
    state: nil,
    zip: nil,
    address: nil,
    email: nil,
    nickname: nil,
    first_name: nil,
    last_name: nil,
    phone_number: nil,
    date_of_birth: nil,
    city: nil,
    country: nil
  }

  defp create_user_management(_) do
    user_management = user_management_fixture()
    %{user_management: user_management}
  end

  describe "Index" do
    setup [:create_user_management]

    test "lists all user_managements", %{conn: conn, user_management: user_management} do
      {:ok, _index_live, html} = live(conn, ~p"/user_managements")

      assert html =~ "Listing User managements"
      assert html =~ user_management.name
    end

    test "saves new user_management", %{conn: conn} do
      {:ok, index_live, _html} = live(conn, ~p"/user_managements")

      assert index_live |> element("a", "New User management") |> render_click() =~
               "New User management"

      assert_patch(index_live, ~p"/user_managements/new")

      assert index_live
             |> form("#user_management-form", user_management: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#user_management-form", user_management: @create_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/user_managements")

      html = render(index_live)
      assert html =~ "User management created successfully"
      assert html =~ "some name"
    end

    test "updates user_management in listing", %{conn: conn, user_management: user_management} do
      {:ok, index_live, _html} = live(conn, ~p"/user_managements")

      assert index_live
             |> element("#user_managements-#{user_management.id} a", "Edit")
             |> render_click() =~
               "Edit User management"

      assert_patch(index_live, ~p"/user_managements/#{user_management}/edit")

      assert index_live
             |> form("#user_management-form", user_management: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#user_management-form", user_management: @update_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/user_managements")

      html = render(index_live)
      assert html =~ "User management updated successfully"
      assert html =~ "some updated name"
    end

    test "deletes user_management in listing", %{conn: conn, user_management: user_management} do
      {:ok, index_live, _html} = live(conn, ~p"/user_managements")

      assert index_live
             |> element("#user_managements-#{user_management.id} a", "Delete")
             |> render_click()

      refute has_element?(index_live, "#user_managements-#{user_management.id}")
    end
  end

  describe "Show" do
    setup [:create_user_management]

    test "displays user_management", %{conn: conn, user_management: user_management} do
      {:ok, _show_live, html} = live(conn, ~p"/user_managements/#{user_management}")

      assert html =~ "Show User management"
      assert html =~ user_management.name
    end

    test "updates user_management within modal", %{conn: conn, user_management: user_management} do
      {:ok, show_live, _html} = live(conn, ~p"/user_managements/#{user_management}")

      assert show_live |> element("a", "Edit") |> render_click() =~
               "Edit User management"

      assert_patch(show_live, ~p"/user_managements/#{user_management}/show/edit")

      assert show_live
             |> form("#user_management-form", user_management: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert show_live
             |> form("#user_management-form", user_management: @update_attrs)
             |> render_submit()

      assert_patch(show_live, ~p"/user_managements/#{user_management}")

      html = render(show_live)
      assert html =~ "User management updated successfully"
      assert html =~ "some updated name"
    end
  end
end
