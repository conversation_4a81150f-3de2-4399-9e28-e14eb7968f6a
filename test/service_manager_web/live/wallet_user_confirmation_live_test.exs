defmodule ServiceManagerWeb.WalletUserConfirmationLiveTest do
  use ServiceManagerWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import ServiceManager.WalletAccountsFixtures

  alias ServiceManager.WalletAccounts
  alias ServiceManager.Repo

  setup do
    %{wallet_user: wallet_user_fixture()}
  end

  describe "Confirm wallet_user" do
    test "renders confirmation page", %{conn: conn} do
      {:ok, _lv, html} = live(conn, ~p"/walletusers/confirm/some-token")
      assert html =~ "Confirm Account"
    end

    test "confirms the given token once", %{conn: conn, wallet_user: wallet_user} do
      token =
        extract_wallet_user_token(fn url ->
          WalletAccounts.deliver_wallet_user_confirmation_instructions(wallet_user, url)
        end)

      {:ok, lv, _html} = live(conn, ~p"/walletusers/confirm/#{token}")

      result =
        lv
        |> form("#confirmation_form")
        |> render_submit()
        |> follow_redirect(conn, "/")

      assert {:ok, conn} = result

      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~
               "WalletUser confirmed successfully"

      assert WalletAccounts.get_wallet_user!(wallet_user.id).confirmed_at
      refute get_session(conn, :wallet_user_token)
      assert Repo.all(WalletAccounts.WalletUserToken) == []

      # when not logged in
      {:ok, lv, _html} = live(conn, ~p"/walletusers/confirm/#{token}")

      result =
        lv
        |> form("#confirmation_form")
        |> render_submit()
        |> follow_redirect(conn, "/")

      assert {:ok, conn} = result

      assert Phoenix.Flash.get(conn.assigns.flash, :error) =~
               "WalletUser confirmation link is invalid or it has expired"

      # when logged in
      conn =
        build_conn()
        |> log_in_wallet_user(wallet_user)

      {:ok, lv, _html} = live(conn, ~p"/walletusers/confirm/#{token}")

      result =
        lv
        |> form("#confirmation_form")
        |> render_submit()
        |> follow_redirect(conn, "/")

      assert {:ok, conn} = result
      refute Phoenix.Flash.get(conn.assigns.flash, :error)
    end

    test "does not confirm email with invalid token", %{conn: conn, wallet_user: wallet_user} do
      {:ok, lv, _html} = live(conn, ~p"/walletusers/confirm/invalid-token")

      {:ok, conn} =
        lv
        |> form("#confirmation_form")
        |> render_submit()
        |> follow_redirect(conn, ~p"/")

      assert Phoenix.Flash.get(conn.assigns.flash, :error) =~
               "WalletUser confirmation link is invalid or it has expired"

      refute WalletAccounts.get_wallet_user!(wallet_user.id).confirmed_at
    end
  end
end
