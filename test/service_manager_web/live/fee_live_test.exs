defmodule ServiceManagerWeb.Backend.FeeLiveTest do
  use ServiceManagerWeb.ConnCase

  import Phoenix.LiveViewTest
  import ServiceManager.Contexts.FeesContextFixtures

  @create_attrs %{
    code: "some code",
    name: "some name",
    status: "some status",
    description: "some description",
    amount: "120.5",
    currency_code: "some currency_code",
    charge_type: "some charge_type",
    effective_date: "2024-11-04",
    expiration_date: "2024-11-04",
    is_feature: true,
    created_by: 42,
    updated_by: 42
  }
  @update_attrs %{
    code: "some updated code",
    name: "some updated name",
    status: "some updated status",
    description: "some updated description",
    amount: "456.7",
    currency_code: "some updated currency_code",
    charge_type: "some updated charge_type",
    effective_date: "2024-11-05",
    expiration_date: "2024-11-05",
    is_feature: false,
    created_by: 43,
    updated_by: 43
  }
  @invalid_attrs %{
    code: nil,
    name: nil,
    status: nil,
    description: nil,
    amount: nil,
    currency_code: nil,
    charge_type: nil,
    effective_date: nil,
    expiration_date: nil,
    is_feature: false,
    created_by: nil,
    updated_by: nil
  }

  defp create_fee(_) do
    fee = fee_fixture()
    %{fee: fee}
  end

  describe "Index" do
    setup [:create_fee]

    test "lists all fees", %{conn: conn, fee: fee} do
      {:ok, _index_live, html} = live(conn, ~p"/mobileBanking/fees&Chargers")

      assert html =~ "Listing Fees"
      assert html =~ fee.code
    end

    test "saves new fee", %{conn: conn} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/fees&Chargers")

      assert index_live |> element("a", "New Fee") |> render_click() =~
               "New Fee"

      assert_patch(index_live, ~p"/mobileBanking/fees&Chargers/new")

      assert index_live
             |> form("#fee-form", fee: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#fee-form", fee: @create_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/fees&Chargers")

      html = render(index_live)
      assert html =~ "Fee created successfully"
      assert html =~ "some code"
    end

    test "updates fee in listing", %{conn: conn, fee: fee} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/fees&Chargers")

      assert index_live |> element("#fees-#{fee.id} a", "Edit") |> render_click() =~
               "Edit Fee"

      assert_patch(index_live, ~p"/mobileBanking/fees&Chargers/#{fee}/edit")

      assert index_live
             |> form("#fee-form", fee: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#fee-form", fee: @update_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/mobileBanking/fees&Chargers")

      html = render(index_live)
      assert html =~ "Fee updated successfully"
      assert html =~ "some updated code"
    end

    test "deletes fee in listing", %{conn: conn, fee: fee} do
      {:ok, index_live, _html} = live(conn, ~p"/mobileBanking/fees&Chargers")

      assert index_live |> element("#fees-#{fee.id} a", "Delete") |> render_click()
      refute has_element?(index_live, "#fees-#{fee.id}")
    end
  end

  describe "Show" do
    setup [:create_fee]

    test "displays fee", %{conn: conn, fee: fee} do
      {:ok, _show_live, html} = live(conn, ~p"/mobileBanking/fees&Chargers/#{fee}")

      assert html =~ "Show Fee"
      assert html =~ fee.code
    end

    test "updates fee within modal", %{conn: conn, fee: fee} do
      {:ok, show_live, _html} = live(conn, ~p"/mobileBanking/fees&Chargers/#{fee}")

      assert show_live |> element("a", "Edit") |> render_click() =~
               "Edit Fee"

      assert_patch(show_live, ~p"/mobileBanking/fees&Chargers/#{fee}/show/edit")

      assert show_live
             |> form("#fee-form", fee: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert show_live
             |> form("#fee-form", fee: @update_attrs)
             |> render_submit()

      assert_patch(show_live, ~p"/mobileBanking/fees&Chargers/#{fee}")

      html = render(show_live)
      assert html =~ "Fee updated successfully"
      assert html =~ "some updated code"
    end
  end
end
