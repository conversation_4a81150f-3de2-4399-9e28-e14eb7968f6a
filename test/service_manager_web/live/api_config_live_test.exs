defmodule ServiceManagerWeb.Backend.VirtualCardsApiConfigLiveTest do
  use ServiceManagerWeb.ConnCase

  import Phoenix.LiveViewTest
  import ServiceManager.Contexts.VirtualCardsAContextsFixtures

  @create_attrs %{
    timeout: 42,
    status: "some status",
    version: "some version",
    api_key: "some api_key",
    api_id: "7488a646-e31f-11e4-aace-600308960662",
    provider_name: "some provider_name",
    base_url: "some base_url",
    auth_token: "some auth_token",
    api_secret: "some api_secret",
    notes: "some notes"
  }
  @update_attrs %{
    timeout: 43,
    status: "some updated status",
    version: "some updated version",
    api_key: "some updated api_key",
    api_id: "7488a646-e31f-11e4-aace-600308960668",
    provider_name: "some updated provider_name",
    base_url: "some updated base_url",
    auth_token: "some updated auth_token",
    api_secret: "some updated api_secret",
    notes: "some updated notes"
  }
  @invalid_attrs %{
    timeout: nil,
    status: nil,
    version: nil,
    api_key: nil,
    api_id: nil,
    provider_name: nil,
    base_url: nil,
    auth_token: nil,
    api_secret: nil,
    notes: nil
  }

  defp create_api_config(_) do
    api_config = api_config_fixture()
    %{api_config: api_config}
  end

  describe "Index" do
    setup [:create_api_config]

    test "lists all api_configs", %{conn: conn, api_config: api_config} do
      {:ok, _index_live, html} = live(conn, ~p"/VirtualCardsAPIConfigs")

      assert html =~ "Listing Api configs"
      assert html =~ api_config.status
    end

    test "saves new api_config", %{conn: conn} do
      {:ok, index_live, _html} = live(conn, ~p"/VirtualCardsAPIConfigs")

      assert index_live |> element("a", "New Api config") |> render_click() =~
               "New Api config"

      assert_patch(index_live, ~p"/VirtualCardsAPIConfigs/new")

      assert index_live
             |> form("#api_config-form", api_config: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#api_config-form", api_config: @create_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/VirtualCardsAPIConfigs")

      html = render(index_live)
      assert html =~ "Api config created successfully"
      assert html =~ "some status"
    end

    test "updates api_config in listing", %{conn: conn, api_config: api_config} do
      {:ok, index_live, _html} = live(conn, ~p"/VirtualCardsAPIConfigs")

      assert index_live |> element("#api_configs-#{api_config.id} a", "Edit") |> render_click() =~
               "Edit Api config"

      assert_patch(index_live, ~p"/VirtualCardsAPIConfigs/#{api_config}/edit")

      assert index_live
             |> form("#api_config-form", api_config: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert index_live
             |> form("#api_config-form", api_config: @update_attrs)
             |> render_submit()

      assert_patch(index_live, ~p"/VirtualCardsAPIConfigs")

      html = render(index_live)
      assert html =~ "Api config updated successfully"
      assert html =~ "some updated status"
    end

    test "deletes api_config in listing", %{conn: conn, api_config: api_config} do
      {:ok, index_live, _html} = live(conn, ~p"/VirtualCardsAPIConfigs")

      assert index_live |> element("#api_configs-#{api_config.id} a", "Delete") |> render_click()
      refute has_element?(index_live, "#api_configs-#{api_config.id}")
    end
  end

  describe "Show" do
    setup [:create_api_config]

    test "displays api_config", %{conn: conn, api_config: api_config} do
      {:ok, _show_live, html} = live(conn, ~p"/VirtualCardsAPIConfigs/#{api_config}")

      assert html =~ "Show Api config"
      assert html =~ api_config.status
    end

    test "updates api_config within modal", %{conn: conn, api_config: api_config} do
      {:ok, show_live, _html} = live(conn, ~p"/VirtualCardsAPIConfigs/#{api_config}")

      assert show_live |> element("a", "Edit") |> render_click() =~
               "Edit Api config"

      assert_patch(show_live, ~p"/VirtualCardsAPIConfigs/#{api_config}/show/edit")

      assert show_live
             |> form("#api_config-form", api_config: @invalid_attrs)
             |> render_change() =~ "can&#39;t be blank"

      assert show_live
             |> form("#api_config-form", api_config: @update_attrs)
             |> render_submit()

      assert_patch(show_live, ~p"/VirtualCardsAPIConfigs/#{api_config}")

      html = render(show_live)
      assert html =~ "Api config updated successfully"
      assert html =~ "some updated status"
    end
  end
end
