defmodule ServiceManagerWeb.WalletUserSessionControllerTest do
  use ServiceManagerWeb.ConnCase, async: true

  import ServiceManager.WalletAccountsFixtures

  setup do
    %{wallet_user: wallet_user_fixture()}
  end

  describe "POST /walletusers/log_in" do
    test "logs the wallet_user in", %{conn: conn, wallet_user: wallet_user} do
      conn =
        post(conn, ~p"/walletusers/log_in", %{
          "wallet_user" => %{
            "email" => wallet_user.email,
            "password" => valid_wallet_user_password()
          }
        })

      assert get_session(conn, :wallet_user_token)
      assert redirected_to(conn) == ~p"/"

      # Now do a logged in request and assert on the menu
      conn = get(conn, ~p"/")
      response = html_response(conn, 200)
      assert response =~ wallet_user.email
      assert response =~ ~p"/walletusers/settings"
      assert response =~ ~p"/walletusers/log_out"
    end

    test "logs the wallet_user in with remember me", %{conn: conn, wallet_user: wallet_user} do
      conn =
        post(conn, ~p"/walletusers/log_in", %{
          "wallet_user" => %{
            "email" => wallet_user.email,
            "password" => valid_wallet_user_password(),
            "remember_me" => "true"
          }
        })

      assert conn.resp_cookies["_service_manager_web_wallet_user_remember_me"]
      assert redirected_to(conn) == ~p"/"
    end

    test "logs the wallet_user in with return to", %{conn: conn, wallet_user: wallet_user} do
      conn =
        conn
        |> init_test_session(wallet_user_return_to: "/foo/bar")
        |> post(~p"/walletusers/log_in", %{
          "wallet_user" => %{
            "email" => wallet_user.email,
            "password" => valid_wallet_user_password()
          }
        })

      assert redirected_to(conn) == "/foo/bar"
      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~ "Welcome back!"
    end

    test "login following registration", %{conn: conn, wallet_user: wallet_user} do
      conn =
        conn
        |> post(~p"/walletusers/log_in", %{
          "_action" => "registered",
          "wallet_user" => %{
            "email" => wallet_user.email,
            "password" => valid_wallet_user_password()
          }
        })

      assert redirected_to(conn) == ~p"/"
      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~ "Account created successfully"
    end

    test "login following password update", %{conn: conn, wallet_user: wallet_user} do
      conn =
        conn
        |> post(~p"/walletusers/log_in", %{
          "_action" => "password_updated",
          "wallet_user" => %{
            "email" => wallet_user.email,
            "password" => valid_wallet_user_password()
          }
        })

      assert redirected_to(conn) == ~p"/walletusers/settings"
      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~ "Password updated successfully"
    end

    test "redirects to login page with invalid credentials", %{conn: conn} do
      conn =
        post(conn, ~p"/walletusers/log_in", %{
          "wallet_user" => %{"email" => "<EMAIL>", "password" => "invalid_password"}
        })

      assert Phoenix.Flash.get(conn.assigns.flash, :error) == "Invalid email or password"
      assert redirected_to(conn) == ~p"/walletusers/log_in"
    end
  end

  describe "DELETE /walletusers/log_out" do
    test "logs the wallet_user out", %{conn: conn, wallet_user: wallet_user} do
      conn = conn |> log_in_wallet_user(wallet_user) |> delete(~p"/walletusers/log_out")
      assert redirected_to(conn) == ~p"/"
      refute get_session(conn, :wallet_user_token)
      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~ "Logged out successfully"
    end

    test "succeeds even if the wallet_user is not logged in", %{conn: conn} do
      conn = delete(conn, ~p"/walletusers/log_out")
      assert redirected_to(conn) == ~p"/"
      refute get_session(conn, :wallet_user_token)
      assert Phoenix.Flash.get(conn.assigns.flash, :info) =~ "Logged out successfully"
    end
  end
end
