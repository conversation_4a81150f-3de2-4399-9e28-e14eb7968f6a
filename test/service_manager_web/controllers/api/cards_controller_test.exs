defmodule ServiceManagerWeb.Api.CardsControllerTest do
  use ServiceManagerWeb.ConnCase
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.SchemaCard
  alias ServiceManager.Accounts.User
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Schemas.Accounts.SchemaBeneficiary
  alias ServiceManager.ThirdParty.ThirdPartyApiKey

  @valid_card_attrs %{
    card_type: "visa",
    currency: "USD",
    daily_limit: "1000.00",
    monthly_limit: "5000.00"
  }

  setup %{conn: conn} do
    # Create a test user
    {:ok, user} =
      %User{}
      |> User.changeset(%{
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        email: "<EMAIL>",
        phone_number: "**********",
        password: "strong_password123"
      })
      |> Repo.insert()

    # Create a test wallet user
    {:ok, wallet_user} =
      %WalletUser{}
      |> WalletUser.registration_changeset(%{
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        mobile_number: "************",
        password: "strong_password123",
        id_number: "ABC123",
        memorable_word: "test123"
      })
      |> Repo.insert()

    # Create a test beneficiary
    {:ok, beneficiary} =
      %SchemaBeneficiary{}
      |> SchemaBeneficiary.changeset(%{
        name: "Test Beneficiary",
        account_number: "**********",
        bank_code: "TEST001",
        currency: "USD"
      })
      |> Repo.insert()

    # Create a test third party API key
    {:ok, api_key} =
      %ThirdPartyApiKey{}
      |> ThirdPartyApiKey.changeset(%{
        api_key: "test_api_key_123",
        description: "Test API Key"
      })
      |> Repo.insert()

    {:ok,
     %{
       conn: conn,
       user: user,
       wallet_user: wallet_user,
       beneficiary: beneficiary,
       api_key: api_key
     }}
  end

  describe "create card" do
    test "creates card successfully with valid data", %{conn: conn, user: user} do
      conn = post(conn, ~p"/api/cards/create", Map.put(@valid_card_attrs, :user_id, user.id))
      assert %{"card_number" => card_number} = json_response(conn, 201)
      assert Repo.get_by(SchemaCard, card_number: card_number)
    end

    test "fails with invalid data", %{conn: conn} do
      conn = post(conn, ~p"/api/cards/create", %{})
      assert json_response(conn, 422)["errors"] != %{}
    end
  end

  describe "validate card" do
    setup [:create_card]

    test "validates card successfully with correct details", %{conn: conn, card: card} do
      conn =
        post(conn, ~p"/api/cards/validate", %{
          "card_number" => card.card_number,
          "cvv" => card.cvv,
          "expiry_date" => Date.to_iso8601(card.expiry_date)
        })

      assert json_response(conn, 200)["valid"]
    end

    test "fails validation with incorrect details", %{conn: conn, card: card} do
      conn =
        post(conn, ~p"/api/cards/validate", %{
          "card_number" => card.card_number,
          "cvv" => "000",
          "expiry_date" => Date.to_iso8601(card.expiry_date)
        })

      refute json_response(conn, 200)["valid"]
    end
  end

  describe "card status" do
    setup [:create_card]

    test "returns correct card status", %{conn: conn, card: card} do
      conn = post(conn, ~p"/api/cards/status", %{"card_number" => card.card_number})
      response = json_response(conn, 200)
      assert response["status"] == card.status
      assert response["activation_status"] == card.activation_status
    end
  end

  describe "link card" do
    setup [:create_card]

    test "links card to wallet user", %{conn: conn, card: card, wallet_user: wallet_user} do
      conn =
        post(conn, ~p"/api/cards/link", %{
          "card_number" => card.card_number,
          "wallet_user_id" => wallet_user.id
        })

      assert json_response(conn, 200)["message"] == "Card linked successfully"
      updated_card = Repo.get(SchemaCard, card.id)
      assert updated_card.wallet_user_id == wallet_user.id
    end

    test "links card to beneficiary", %{conn: conn, card: card, beneficiary: beneficiary} do
      conn =
        post(conn, ~p"/api/cards/link", %{
          "card_number" => card.card_number,
          "beneficiary_id" => beneficiary.id
        })

      assert json_response(conn, 200)["message"] == "Card linked successfully"
      updated_card = Repo.get(SchemaCard, card.id)
      assert updated_card.beneficiary_id == beneficiary.id
    end
  end

  describe "card operations" do
    setup [:create_card]

    test "blocks card successfully", %{conn: conn, card: card} do
      conn = post(conn, ~p"/api/cards/block", %{"card_number" => card.card_number})
      assert json_response(conn, 200)["status"] == "blocked"
      updated_card = Repo.get(SchemaCard, card.id)
      assert updated_card.status == "blocked"
    end

    test "activates card successfully", %{conn: conn, card: card} do
      conn =
        post(conn, ~p"/api/cards/activate", %{
          "card_number" => card.card_number,
          "pin" => "1234"
        })

      assert json_response(conn, 200)["status"] == "activated"
      updated_card = Repo.get(SchemaCard, card.id)
      assert updated_card.activation_status == "activated"
      assert updated_card.pin_hash != nil
    end
  end

  # Helper function to create a test card
  defp create_card(%{user: user}) do
    {:ok, card} =
      %SchemaCard{}
      |> SchemaCard.changeset(Map.put(@valid_card_attrs, :user_id, user.id))
      |> Repo.insert()

    {:ok, card: card}
  end
end
