defmodule ServiceManagerWeb.Api.BillersControllerTest do
  use ServiceManagerWeb.ConnCase

  alias ServiceManager.Billers.BillersService
  alias ServiceManager.Schemas.Billers.BillerTransaction

  describe "GET /api/billers/types" do
    test "returns available biller types", %{conn: conn} do
      conn = get(conn, ~p"/api/billers/types")
      
      assert %{
        "data" => biller_types,
        "status" => true,
        "message" => "Biller types retrieved successfully"
      } = json_response(conn, 200)
      
      assert is_list(biller_types)
      assert length(biller_types) == 8
      
      # Check for specific biller types
      biller_type_names = Enum.map(biller_types, & &1["type"])
      assert "bwb_postpaid" in biller_type_names
      assert "register_general" in biller_type_names
      assert "tnm_bundles" in biller_type_names
    end
  end

  describe "POST /api/billers/account-details/:biller_type/:account_number" do
    test "processes account details request successfully", %{conn: conn} do
      conn = post(conn, ~p"/api/billers/account-details/bwb_postpaid/********")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "transaction_id" => transaction_id,
          "biller_type" => "bwb_postpaid",
          "account_number" => "********",
          "status" => status
        },
        "status" => true,
        "message" => "Account details retrieved successfully"
      } = response
      
      assert is_binary(transaction_id)
      assert status in ["completed", "failed"] # Depends on simulation
    end

    test "includes account_type for MASM", %{conn: conn} do
      conn = post(conn, ~p"/api/billers/account-details/masm/********", %{"account_type" => "M"})
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "account_type" => "M"
        }
      } = response
    end

    test "returns error for invalid biller type", %{conn: conn} do
      conn = post(conn, ~p"/api/billers/account-details/invalid_biller/********")
      
      assert %{
        "error" => %{
          "message" => "Validation failed"
        },
        "status" => false
      } = json_response(conn, 400)
    end
  end

  describe "POST /api/billers/payment/:biller_type" do
    test "processes payment successfully", %{conn: conn} do
      payment_params = %{
        "account_number" => "********",
        "amount" => "100.00",
        "currency" => "MWK",
        "credit_account" => "*************",
        "credit_account_type" => "account",
        "debit_account" => "************",
        "debit_account_type" => "account",
        "customer_account_number" => "************",
        "customer_account_name" => "Test User"
      }

      conn = post(conn, ~p"/api/billers/payment/bwb_postpaid", payment_params)
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "transaction_id" => transaction_id,
          "biller_type" => "bwb_postpaid",
          "amount" => amount,
          "customer_account_name" => "Test User"
        },
        "status" => true,
        "message" => "Payment processed successfully"
      } = response
      
      assert is_binary(transaction_id)
      assert Decimal.equal?(Decimal.new(amount), Decimal.new("100.00"))
    end

    test "returns error for missing required fields", %{conn: conn} do
      payment_params = %{
        "account_number" => "********"
        # Missing required fields
      }

      conn = post(conn, ~p"/api/billers/payment/bwb_postpaid", payment_params)
      
      assert %{
        "error" => %{
          "message" => "Validation failed"
        },
        "status" => false
      } = json_response(conn, 400)
    end
  end

  describe "GET /api/billers/invoice/:biller_type/:account_number" do
    test "retrieves invoice details", %{conn: conn} do
      conn = get(conn, ~p"/api/billers/invoice/register_general/BEDPVE")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "transaction_id" => transaction_id,
          "biller_type" => "register_general",
          "account_number" => "BEDPVE"
        },
        "status" => true,
        "message" => "Invoice details retrieved successfully"
      } = response
      
      assert is_binary(transaction_id)
    end
  end

  describe "POST /api/billers/invoice-confirm/:biller_type" do
    test "confirms invoice payment", %{conn: conn} do
      confirmation_params = %{
        "account_number" => "BEDPVE",
        "amount" => "800.00",
        "currency" => "MWK",
        "credit_account" => "*************",
        "credit_account_type" => "account",
        "debit_account" => "************",
        "debit_account_type" => "account",
        "customer_account_number" => "************",
        "customer_account_name" => "Fanuel Jeckey Mzizah"
      }

      conn = post(conn, ~p"/api/billers/invoice-confirm/register_general", confirmation_params)
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "transaction_id" => transaction_id,
          "biller_type" => "register_general",
          "account_number" => "BEDPVE",
          "amount" => amount
        },
        "status" => true,
        "message" => "Invoice payment confirmed successfully"
      } = response
      
      assert is_binary(transaction_id)
      assert Decimal.equal?(Decimal.new(amount), Decimal.new("800.00"))
    end
  end

  describe "GET /api/billers/bundle/:bundle_id" do
    test "retrieves bundle details", %{conn: conn} do
      conn = get(conn, ~p"/api/billers/bundle/239678")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "transaction_id" => transaction_id,
          "bundle_id" => "239678",
          "biller_type" => "tnm_bundles"
        },
        "status" => true,
        "message" => "Bundle details retrieved successfully"
      } = response
      
      assert is_binary(transaction_id)
    end
  end

  describe "POST /api/billers/bundle-confirm" do
    test "confirms bundle purchase", %{conn: conn} do
      bundle_params = %{
        "bundle_id" => "239678",
        "phone_number" => "**********",
        "amount" => "50.00",
        "currency" => "MWK"
      }

      conn = post(conn, ~p"/api/billers/bundle-confirm", bundle_params)
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "transaction_id" => transaction_id,
          "bundle_id" => "239678",
          "phone_number" => "**********"
        },
        "status" => true,
        "message" => "Bundle purchase confirmed successfully"
      } = response
      
      assert is_binary(transaction_id)
    end
  end

  describe "POST /api/billers/validate/:account_number" do
    test "validates account", %{conn: conn} do
      conn = post(conn, ~p"/api/billers/validate/*********")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "transaction_id" => transaction_id,
          "account_number" => "*********",
          "biller_type" => "airtel_validation"
        },
        "status" => true,
        "message" => "Account validation completed successfully"
      } = response
      
      assert is_binary(transaction_id)
    end
  end

  describe "GET /api/billers/transaction/:id" do
    test "retrieves transaction by ID", %{conn: conn} do
      # Create a transaction first
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "completed",
        response_payload: %{"balance" => "1000.00"}
      })

      conn = get(conn, ~p"/api/billers/transaction/#{transaction.id}")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "id" => id,
          "transaction_id" => "TT202501010001",
          "biller_type" => "bwb_postpaid",
          "account_number" => "********",
          "status" => "completed"
        },
        "status" => true
      } = response
      
      assert id == transaction.id
    end

    test "returns 404 for non-existent transaction", %{conn: conn} do
      conn = get(conn, ~p"/api/billers/transaction/999999")
      
      assert %{
        "error" => %{
          "message" => "Transaction not found"
        },
        "status" => false
      } = json_response(conn, 404)
    end
  end

  describe "GET /api/billers/transaction/reference/:reference" do
    test "retrieves transaction by reference", %{conn: conn} do
      # Create a transaction first
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "completed"
      })

      conn = get(conn, ~p"/api/billers/transaction/reference/TT202501010001")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "id" => id,
          "transaction_id" => "TT202501010001"
        }
      } = response
      
      assert id == transaction.id
    end

    test "returns 404 for non-existent reference", %{conn: conn} do
      conn = get(conn, ~p"/api/billers/transaction/reference/NONEXISTENT")
      
      assert %{
        "error" => %{
          "message" => "Transaction not found"
        },
        "status" => false
      } = json_response(conn, 404)
    end
  end

  describe "GET /api/billers/transactions/biller/:biller_type" do
    test "retrieves transactions by biller type", %{conn: conn} do
      # Create transactions for different billers
      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT001",
        transaction_type: "account_details",
        status: "completed"
      })

      BillersService.create_transaction(%{
        biller_type: "lwb_postpaid",
        biller_name: "LWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT002",
        transaction_type: "account_details",
        status: "completed"
      })

      conn = get(conn, ~p"/api/billers/transactions/biller/bwb_postpaid")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => transactions,
        "status" => true,
        "count" => 1
      } = response
      
      assert length(transactions) == 1
      assert hd(transactions)["biller_type"] == "bwb_postpaid"
    end

    test "supports limit and offset parameters", %{conn: conn} do
      # Create multiple transactions
      Enum.each(1..5, fn i ->
        BillersService.create_transaction(%{
          biller_type: "bwb_postpaid",
          biller_name: "BWB Postpaid",
          account_number: "1234567#{i}",
          our_transaction_id: "TT00#{i}",
          transaction_type: "account_details",
          status: "completed"
        })
      end)

      conn = get(conn, ~p"/api/billers/transactions/biller/bwb_postpaid?limit=2&offset=1")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => transactions,
        "count" => 2
      } = response
      
      assert length(transactions) == 2
    end
  end

  describe "GET /api/billers/transactions/status/:status" do
    test "retrieves transactions by status", %{conn: conn} do
      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT001",
        transaction_type: "account_details",
        status: "pending"
      })

      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT002",
        transaction_type: "account_details",
        status: "completed"
      })

      conn = get(conn, ~p"/api/billers/transactions/status/pending")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => transactions,
        "count" => 1
      } = response
      
      assert length(transactions) == 1
      assert hd(transactions)["status"] == "pending"
    end
  end

  describe "GET /api/billers/transactions/account/:account_number" do
    test "retrieves transactions by account number", %{conn: conn} do
      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT001",
        transaction_type: "account_details",
        status: "completed"
      })

      BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT002",
        transaction_type: "account_details",
        status: "completed"
      })

      conn = get(conn, ~p"/api/billers/transactions/account/********")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => transactions,
        "count" => 1
      } = response
      
      assert length(transactions) == 1
      assert hd(transactions)["account_number"] == "********"
    end
  end

  describe "POST /api/billers/transaction/:id/retry" do
    test "retries a failed transaction", %{conn: conn} do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "failed",
        error_message: "Temporary failure"
      })

      conn = post(conn, ~p"/api/billers/transaction/#{transaction.id}/retry")
      
      response = json_response(conn, 200)
      
      assert %{
        "data" => %{
          "result" => _result
        },
        "status" => true,
        "message" => "Transaction retry initiated successfully"
      } = response
    end

    test "returns error for non-failed transaction", %{conn: conn} do
      {:ok, transaction} = BillersService.create_transaction(%{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        account_number: "********",
        our_transaction_id: "TT202501010001",
        transaction_type: "account_details",
        status: "completed"
      })

      conn = post(conn, ~p"/api/billers/transaction/#{transaction.id}/retry")
      
      assert %{
        "error" => %{
          "message" => "Only failed transactions can be retried"
        },
        "status" => false
      } = json_response(conn, 400)
    end

    test "returns error for non-existent transaction", %{conn: conn} do
      conn = post(conn, ~p"/api/billers/transaction/999999/retry")
      
      assert %{
        "error" => %{
          "message" => "Transaction not found"
        },
        "status" => false
      } = json_response(conn, 400)
    end
  end

  describe "error handling" do
    test "handles service errors gracefully", %{conn: conn} do
      # This would trigger a service error - invalid biller type
      conn = post(conn, ~p"/api/billers/account-details/invalid_biller/********")
      
      response = json_response(conn, 400)
      
      assert %{
        "error" => %{
          "message" => "Validation failed"
        },
        "status" => false
      } = response
    end

    test "handles malformed JSON gracefully", %{conn: conn} do
      conn = conn
      |> put_req_header("content-type", "application/json")
      |> post(~p"/api/billers/payment/bwb_postpaid", "{invalid json}")
      
      # Phoenix should handle this and return a 400
      assert conn.status == 400
    end
  end

  describe "JSON responses format" do
    test "all successful responses follow consistent format", %{conn: conn} do
      conn = get(conn, ~p"/api/billers/types")
      
      response = json_response(conn, 200)
      
      # Check required top-level keys
      assert Map.has_key?(response, "data")
      assert Map.has_key?(response, "status")
      assert Map.has_key?(response, "message")
      
      # Check types
      assert is_boolean(response["status"])
      assert is_binary(response["message"])
      assert response["status"] == true
    end

    test "all error responses follow consistent format", %{conn: conn} do
      conn = get(conn, ~p"/api/billers/transaction/999999")
      
      response = json_response(conn, 404)
      
      # Check required top-level keys
      assert Map.has_key?(response, "error")
      assert Map.has_key?(response, "status")
      assert Map.has_key?(response, "message")
      
      # Check types and values
      assert is_boolean(response["status"])
      assert is_binary(response["message"])
      assert response["status"] == false
      assert Map.has_key?(response["error"], "message")
    end
  end
end