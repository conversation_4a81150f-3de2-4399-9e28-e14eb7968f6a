defmodule ServiceManagerWeb.TransfersJSONTest do
  use ExUnit.Case, async: true

  alias ServiceManagerWeb.TransfersJSON

  describe "error/1" do
    test "formats map error with nested error key as clean string" do
      error_data = %{error: "To account not found"}
      
      result = TransfersJSON.error(%{error: error_data})
      
      assert result == %{
        status: false,
        message: "Transaction Failed.",
        errors: %{error: "To account not found"}
      }
    end

    test "formats binary error as is" do
      error_data = "Account not found"
      
      result = TransfersJSON.error(%{error: error_data})
      
      assert result == %{
        status: false,
        message: "Transaction Failed.",
        errors: %{error: "Account not found"}
      }
    end

    test "formats atom error as readable string" do
      error_data = :account_not_found
      
      result = TransfersJSON.error(%{error: error_data})
      
      assert result == %{
        status: false,
        message: "Transaction Failed.",
        errors: %{error: "Account not found"}
      }
    end

    test "formats timeout error with user-friendly message" do
      error_data = {:error, :timeout}
      
      result = TransfersJSON.error(%{error: error_data})
      
      assert result == %{
        status: false,
        message: "Transaction Failed.",
        errors: %{error: "Request timeout. Please try again."}
      }
    end

    test "formats connection error with user-friendly message" do
      error_data = {:error, :econnrefused}
      
      result = TransfersJSON.error(%{error: error_data})
      
      assert result == %{
        status: false,
        message: "Transaction Failed.",
        errors: %{error: "Connection refused. Service may be unavailable."}
      }
    end

    test "handles nested error tuples" do
      error_data = {:error, "From account not found"}
      
      result = TransfersJSON.error(%{error: error_data})
      
      assert result == %{
        status: false,
        message: "Transaction Failed.",
        errors: %{error: "From account not found"}
      }
    end

    test "handles complex nested map errors" do
      error_data = %{error: %{error: "Insufficient funds"}}
      
      result = TransfersJSON.error(%{error: error_data})
      
      assert result == %{
        status: false,
        message: "Transaction Failed.",
        errors: %{error: "Insufficient funds"}
      }
    end
  end
end
