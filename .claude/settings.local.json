{"permissions": {"allow": ["Bash(sudo fallocate:*)", "Bash(sudo chmod:*)", "<PERSON><PERSON>(sudo mkswap:*)", "<PERSON><PERSON>(sudo:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(mix compile)", "<PERSON><PERSON>(mix test:*)", "Bash(mix compile:*)", "Bash(iex:*)", "<PERSON><PERSON>(mix run:*)", "Bash(rg:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./simple_test.sh:*)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(./test_plugin_builder_api.sh:*)", "Bash(npm run deploy:*)", "Bash(mix ecto.gen.migration:*)"], "deny": []}}