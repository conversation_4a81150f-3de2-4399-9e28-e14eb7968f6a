# Dynamic Forms MCP Server

A comprehensive Model Context Protocol (MCP) server for managing dynamic API routes, forms, and plugins in the banking platform.

## Overview

This MCP server provides AI assistants with standardized interfaces to:
- Manage dynamic routes, forms, and plugins
- Create and modify entity relationships  
- Perform CRUD operations with validation
- Generate AI-assisted workflows
- Access system analytics and insights

## Features

### 🔌 Resources
- **Dynamic Routes**: Complete route configurations with metadata
- **Dynamic Forms**: Form schemas with validation rules
- **Dynamic Plugins**: Business logic processors with code
- **Entity Connections**: Relationships between routes, forms, and plugins
- **System Statistics**: Usage analytics and performance metrics

### 🛠️ Tools
- **Route Management**: Create, update, delete, enable/disable routes
- **Form Management**: Create, update, delete, validate form data
- **Plugin Management**: Create, update, delete, test plugins
- **Entity Linking**: Link/unlink routes with forms and plugins
- **Connection Discovery**: Query entity relationships

### 💡 Prompts
- **Route Designer**: AI-assisted route creation with best practices
- **Form Builder**: Generate comprehensive form schemas
- **Plugin Generator**: Create business logic with error handling
- **Workflow Creator**: End-to-end API workflow design
- **Troubleshooter**: Diagnose and resolve system issues

## Quick Start

### 1. Start the MCP Server

#### Option A: Using Mix Task
```bash
mix mcp start
```

#### Option B: Using Standalone Script
```bash
elixir scripts/start_mcp_server.exs
```

#### Option C: As Part of Phoenix Application
The server starts automatically when the Phoenix app runs (if enabled in config).

### 2. Check Server Status
```bash
mix mcp status
```

### 3. List Available Resources
```bash
mix mcp resources
```

## Configuration

### Application Configuration
Add to `config/config.exs`:
```elixir
config :service_manager, :mcp_server,
  enabled: true,
  port: 8080,
  host: "localhost",
  auth_required: false,
  max_connections: 100
```

### MCP Client Configuration
For Claude Code or other MCP clients:
```json
{
  "mcpServers": {
    "dynamic-forms": {
      "command": "elixir",
      "args": ["scripts/start_mcp_server.exs"],
      "cwd": "/path/to/service_manager",
      "env": {
        "MIX_ENV": "dev"
      }
    }
  }
}
```

## API Reference

### Resources

#### Routes Resource
- **URI**: `mcp://dynamic-forms/routes`
- **Filters**: `method`, `category`, `enabled`
- **Description**: All dynamic routes with metadata

**Example with filters**:
```
mcp://dynamic-forms/routes/method/POST/enabled/true
```

#### Forms Resource
- **URI**: `mcp://dynamic-forms/forms`  
- **Filters**: `method`, `required`
- **Description**: All forms with validation schemas

#### Plugins Resource
- **URI**: `mcp://dynamic-forms/plugins`
- **Filters**: `category`, `type`
- **Description**: All plugins/processes with code and metadata

### Tools

#### Route Tools

**create_route**
```json
{
  "name": "User Registration",
  "method": "POST",
  "path": "/api/v1/users/register",
  "category": "Authentication",
  "group_name": "User Management",
  "description": "Register a new user account",
  "priority": 5,
  "tags": ["auth", "registration"],
  "enabled": true
}
```

**update_route**
```json
{
  "id": 123,
  "enabled": false,
  "description": "Updated description"
}
```

#### Form Tools

**create_form**
```json
{
  "name": "Registration Form",
  "http_method": "POST",
  "form_schema": {
    "fields": [
      {
        "name": "email",
        "type": "email",
        "label": "Email Address",
        "required": true
      }
    ]
  },
  "validation_schema": {
    "type": "object",
    "properties": {
      "email": {"type": "string", "format": "email"}
    },
    "required": ["email"]
  }
}
```

#### Plugin Tools

**create_plugin**
```json
{
  "name": "User Validator",
  "description": "Validates user registration data",
  "code": "def process(params) do\n  # validation logic\n  {:ok, validated_params}\nend",
  "category": "Validation",
  "plugin_type": "public"
}
```

#### Linking Tools

**link_route_form**
```json
{
  "route_id": 123,
  "form_id": 456
}
```

### Prompts

#### Route Designer
```json
{
  "purpose": "Create an API endpoint for money transfers",
  "data_type": "financial_transaction",
  "http_method": "POST",
  "existing_routes": true
}
```

#### Form Builder
```json
{
  "api_purpose": "User registration for mobile banking",
  "data_fields": "email, phone, full_name, id_number",
  "validation_level": "strict",
  "form_type": "create"
}
```

## Architecture

```
MCP Server
├── Server (Main coordinator)
├── Resources (Data discovery)
│   ├── Routes Resource
│   ├── Forms Resource
│   ├── Plugins Resource
│   └── Connections Resource
├── Tools (CRUD operations)
│   ├── Route Tools
│   ├── Form Tools
│   ├── Plugin Tools
│   └── Linking Tools
├── Prompts (AI assistance)
│   ├── Designer Prompts
│   ├── Builder Prompts
│   └── Helper Prompts
└── Schemas (Validation)
    ├── Parameter Validation
    ├── Schema Validation
    └── Code Validation
```

## Banking-Specific Features

### Security
- Input sanitization and validation
- Banking-compliant data handling
- Audit trail generation
- Authentication/authorization support

### Compliance
- Regulatory requirement validation
- KYC/AML considerations
- Data protection compliance
- Transaction integrity checks

### Integration
- T24 core banking system integration
- Multi-currency support
- Real-time transaction processing
- Comprehensive error handling

## Examples

### Creating a Complete Workflow

1. **Design Route**:
```bash
# Use route designer prompt
{
  "purpose": "Create account balance inquiry endpoint",
  "data_type": "account_balance",
  "http_method": "GET"
}
```

2. **Create Form**:
```bash
# Use form builder prompt
{
  "api_purpose": "Account balance inquiry",
  "data_fields": "account_number, pin",
  "validation_level": "strict"
}
```

3. **Generate Plugin**:
```bash
# Use plugin generator prompt
{
  "functionality": "Retrieve account balance from T24",
  "input_data": "account_number, pin",
  "external_apis": "T24 core banking"
}
```

4. **Link Components**:
```bash
# Link route to form
link_route_form(route_id: 1, form_id: 1)

# Link route to plugin
link_route_plugin(route_id: 1, plugin_id: 1)
```

### Monitoring and Analytics

```bash
# Get system statistics
mcp://dynamic-forms/stats

# Check entity connections
get_entity_connections(entity_type: "route", entity_id: 123)

# Validate existing configurations
route_validator(route_id: 123, check_type: "security")
```

## Troubleshooting

### Common Issues

1. **Server Won't Start**
   - Check if port 8080 is available
   - Verify database connection
   - Ensure all dependencies are installed

2. **Connection Refused**
   - Verify server is running: `mix mcp status`
   - Check firewall settings
   - Confirm host/port configuration

3. **Tool Failures**
   - Validate input parameters
   - Check database constraints
   - Review error logs

### Debug Commands
```bash
# Check server status
mix mcp status

# List available resources
mix mcp resources  

# Test database connection
iex -S mix
ServiceManager.Repo.query("SELECT 1")
```

## Development

### Adding New Tools
1. Define tool schema in `Tools.list_all/0`
2. Implement handler in `Tools.handle_call/2`
3. Add validation in `Schemas` module
4. Write tests

### Adding New Prompts  
1. Define prompt metadata in `Prompts.list_all/0`
2. Implement prompt generator in `Prompts.handle_call/2`
3. Add context gathering helpers
4. Test with real scenarios

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Update documentation
5. Submit pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section
- Review server logs
- Open GitHub issue with reproduction steps
- Include server status and configuration details