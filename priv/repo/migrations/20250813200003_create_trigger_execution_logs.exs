defmodule ServiceManager.Repo.Migrations.CreateTriggerExecutionLogs do
  use Ecto.Migration

  def change do
    create table(:trigger_execution_logs, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :trigger_id, references(:triggers, type: :binary_id, on_delete: :delete_all), null: false
      add :wizard_step_id, :binary_id
      add :user_id, :binary_id
      add :execution_type, :string, null: false
      add :request_payload, :map
      add :response_payload, :map
      add :execution_time_ms, :integer
      add :status, :string, null: false
      add :error_message, :text
      add :error_details, :map
      add :executed_at, :utc_datetime, null: false

      timestamps()
    end

    create index(:trigger_execution_logs, [:trigger_id])
    create index(:trigger_execution_logs, [:executed_at])
    create index(:trigger_execution_logs, [:status])
    create index(:trigger_execution_logs, [:wizard_step_id])
    create index(:trigger_execution_logs, [:user_id])
    create index(:trigger_execution_logs, [:execution_type])
    
    # Add check constraints
    execute """
    ALTER TABLE trigger_execution_logs ADD CONSTRAINT trigger_execution_logs_execution_type_check 
    CHECK (execution_type IN ('sync', 'async'))
    """, ""
    
    execute """
    ALTER TABLE trigger_execution_logs ADD CONSTRAINT trigger_execution_logs_status_check 
    CHECK (status IN ('success', 'error', 'timeout'))
    """, ""
    
    # Create index for performance monitoring queries
    create index(:trigger_execution_logs, [:trigger_id, :status])
    create index(:trigger_execution_logs, [:executed_at, :status])
  end
end