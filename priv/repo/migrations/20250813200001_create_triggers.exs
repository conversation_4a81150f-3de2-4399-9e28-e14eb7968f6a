defmodule ServiceManager.Repo.Migrations.CreateTriggers do
  use Ecto.Migration

  def change do
    create table(:triggers, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :module_name, :string, null: false
      add :function_name, :string, null: false
      add :mount_point, :string, null: false
      add :input_schema, :map
      add :execution_type, :string, null: false
      add :return_data, :boolean, default: false
      add :logging_enabled, :boolean, default: false
      add :rate_limiting_enabled, :boolean, default: false
      add :audit_logging_enabled, :boolean, default: false
      add :sandbox_execution, :boolean, default: true
      add :description, :text
      add :enabled, :boolean, default: true

      timestamps()
    end

    create unique_index(:triggers, [:mount_point])
    create index(:triggers, [:enabled])
    create index(:triggers, [:execution_type])
    create index(:triggers, [:module_name])
    create index(:triggers, [:function_name])
    
    # Add check constraints
    execute """
    ALTER TABLE triggers ADD CONSTRAINT triggers_execution_type_check 
    CHECK (execution_type IN ('sync', 'async'))
    """, ""
  end
end