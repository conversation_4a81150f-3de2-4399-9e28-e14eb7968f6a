defmodule ServiceManager.Repo.Migrations.CreateWizardStepTriggers do
  use Ecto.Migration

  def change do
    create table(:wizard_step_triggers, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :wizard_step_id, :binary_id, null: false
      add :trigger_id, references(:triggers, type: :binary_id, on_delete: :delete_all), null: false
      add :execution_timing, :string, null: false
      add :execution_conditions, :map
      add :input_mapping, :map, null: false
      add :execution_order, :integer, default: 0
      add :enabled, :boolean, default: true

      timestamps()
    end

    create index(:wizard_step_triggers, [:wizard_step_id])
    create index(:wizard_step_triggers, [:trigger_id])
    create index(:wizard_step_triggers, [:enabled])
    create index(:wizard_step_triggers, [:execution_timing])
    create index(:wizard_step_triggers, [:execution_order])
    
    # Add check constraint for execution timing
    execute """
    ALTER TABLE wizard_step_triggers ADD CONSTRAINT wizard_step_triggers_execution_timing_check 
    CHECK (execution_timing IN ('on_enter', 'on_exit', 'conditional'))
    """, ""
    
    # Create unique constraint to prevent duplicate triggers on same step with same order
    create unique_index(:wizard_step_triggers, [:wizard_step_id, :trigger_id], 
                       name: :wizard_step_triggers_unique_step_trigger)
  end
end