{"version": 3, "sources": ["fa-solid.scss", "fa-solid.css"], "names": [], "mappings": "AAAA;;;ECGE;ADGF;EACE,iCAAiC;EACjC,kBAAkB;EAClB,gBAAgB;EAChB,wCAA6C;EAC7C,8SAIkE,EAAA;;AAGpE;;EAEE,iCAAiC;EACjC,gBAAgB,EAAA", "file": "fa-solid.css", "sourcesContent": ["/*!\r\n * Font Awesome Pro 5.0.7 by @fontawesome - https://fontawesome.com\r\n * License - https://fontawesome.com/license (Commercial License)\r\n */\r\n@import 'variables';\r\n\r\n@font-face {\r\n  font-family: 'Font Awesome 5 Pro';\r\n  font-style: normal;\r\n  font-weight: 900;\r\n  src: url('#{$fa-font-path}/fa-solid-900.eot');\r\n  src: url('#{$fa-font-path}/fa-solid-900.eot?#iefix') format('embedded-opentype'),\r\n  url('#{$fa-font-path}/fa-solid-900.woff2') format('woff2'),\r\n  url('#{$fa-font-path}/fa-solid-900.woff') format('woff'),\r\n  url('#{$fa-font-path}/fa-solid-900.ttf') format('truetype'),\r\n  url('#{$fa-font-path}/fa-solid-900.svg#fontawesome') format('svg');\r\n}\r\n\r\n.fa,\r\n.fas {\r\n  font-family: 'Font Awesome 5 Pro';\r\n  font-weight: 900;\r\n}\r\n", "/*!\r\n * Font Awesome Pro 5.0.7 by @fontawesome - https://fontawesome.com\r\n * License - https://fontawesome.com/license (Commercial License)\r\n */\n@font-face {\n  font-family: 'Font Awesome 5 Pro';\n  font-style: normal;\n  font-weight: 900;\n  src: url(\"../webfonts/fa-solid-900.eot\");\n  src: url(\"../webfonts/fa-solid-900.eot?#iefix\") format(\"embedded-opentype\"), url(\"../webfonts/fa-solid-900.woff2\") format(\"woff2\"), url(\"../webfonts/fa-solid-900.woff\") format(\"woff\"), url(\"../webfonts/fa-solid-900.ttf\") format(\"truetype\"), url(\"../webfonts/fa-solid-900.svg#fontawesome\") format(\"svg\"); }\n\n.fa,\n.fas {\n  font-family: 'Font Awesome 5 Pro';\n  font-weight: 900; }\n"]}