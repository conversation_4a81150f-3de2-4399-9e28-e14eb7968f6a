@charset "UTF-8";
/* #GLOBAL IMPORTS
========================================================================== */
/* #BOOTSTRAP AND MIXINS - Base Unmodified Bootstrap file with theme mixins
========================================================================== */
/*---------------------------------------------------
    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) 
  -------------------------------- -------------------
    LESS ELEMENTS made by <PERSON> (http://fadeyev.net)
    SASS port by <PERSON> (http://samuelbeek.com) 
  ---------------------------------------------------*/
/*------------------------
    Usage

    h1 {
      font-size: rem(32);
    }

    OR:

    h1 {
      font-size: rem(32px);
    }
------------------------*/
/*------------------------
  FADE IN
  e.g. @include fadeIn( 2s );
------------------------*/
/*------------------------
mixin that calculates if text needs to be light or dark
depending on the background color passed.

From this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast

usage:
@include text-contrast($bgcolor)
      
Color brightness is determined by the following formula: 
((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000
------------------------*/
/*------------------------
 color factory 
  eg: @include paint($blue-grey-50, bg-blue-grey-50);
------------------------*/
/* backface visibility */
/* generate theme button */
/* #BASE - Base Variable file along with font library, and colors.
========================================================================== */
/*  THEME COLORs
========================================================================== */
/* Looks good on chrome default color profile */
/* looks good in sRGB but washed up on chrome default 
$color-primary:						#826bb0;
$color-success:						#31cb55;
$color-info:						#5e93ec;
$color-warning:						#eec559;
$color-danger:						#dc4b92;
$color-fusion:						darken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */
/*  Color Polarity
========================================================================== */
/*  PAINTBUCKET MIXER
========================================================================== */
/* the grays */
/* the sapphires */
/* the emeralds */
/* the amethyths */
/* the topaz */
/* the rubies */
/* the graphites */
/*  Define universal border difition (div outlines, etc)
========================================================================== */
/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)
========================================================================== */
/* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */
/* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/
/* global var used for spacing*/
/* Uniform Padding variable */
/* Heads up! This is a global scoped variable - changing may impact the whole template */
/*   BOOTSTRAP OVERRIDES (bootstrap variables)
========================================================================== */
/* usage: theme-colors("primary"); */
/* forms */
/*$input-height:							calc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */
/* links */
/* checkbox */
/*$custom-file-height-inner:				calc(2.25rem - 1px);*/
/* not part of bootstrap variable */
/* custom checkbox */
/* custom range */
/* select */
/* badge */
/* cards */
/*border radius*/
/* alert */
/* toast */
/* breadcrumb */
/* input button */
/* nav link */
/* nav, tabs, pills */
/* tables */
/* dropdowns */
/* dropdowns sizes */
/* popovers */
/* tooltips */
/* modal */
/* reference guide
http://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/
8px = 0.5rem
9px = 0.5625rem
10px = 0.625rem
11px = 0.6875rem
12px = 0.75rem
13px = 0.8125rem
14px = 0.875rem
15px = 0.9375rem
16px = 1rem (base)
17px = 1.0625rem
18px = 1.125rem
19px = 1.1875rem
20px = 1.25rem
21px = 1.3125rem
22px = 1.375rem
24px = 1.5rem
25px = 1.5625rem
26px = 1.625rem
28px = 1.75rem
30px = 1.875rem
32px = 2rem
34px = 2.125rem
36px = 2.25rem
38px = 2.375rem
40px = 2.5rem
*/
/* Fonts */
/* carousel */
/*  BASE VARS
========================================================================== */
/* font vars below will auto change to rem values using function rem($value)*/
/* 11px   */
/* 12px   */
/* 12.5px */
/* 14px   */
/* 15px   */
/* 16px   */
/* 28px   */
/*  Font Family
========================================================================== */
/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */
/*  ANIMATIONS
========================================================================== */
/* this addresses all animation related to nav hide to nav minify */
/*  Z-INDEX declearation
========================================================================== */
/* we adjust bootstrap z-index to be higher than our higest z-index*/
/*  CUSTOM ICON PREFIX 
========================================================================== */
/*  PRINT CSS (landscape or portrait)
========================================================================== */
/* landscape or portrait */
/* auto, letter */
/*  Common Element Variables
========================================================================== */
/* Z-index decleartion "birds eye view"
========================================================================== */
/*  Components
========================================================================== */
/*  PAGE HEADER STUFF
========================================================================== */
/* colors */
/* height */
/* logo */
/* try not to go beywond the width of $main_nav_width value */
/* you may need to change this depending on your logo design */
/* adjust this as you see fit : left, right, center */
/* icon font size (not button) */
/* search input box */
/* suggestion: #ccced0*/
/* btn */
/* dropdown: app list */
/* badge */
/* COMPONENTS & MODS */
/*  NAVIGATION STUFF

Guide:

aside.page-sidebar ($nav-width, $nav-background)
	.page-logo
	.primary-nav
		.info-card
		ul.nav-menu
			li
				a (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)
					icon 
					span
					collapse-sign 
					
				ul.nav-menu-sub-one  
					li
						a ($nav-level-1... $nav-sub-link-height)
							span
							collapse-sign

						ul.nav-menu-sub-two
							li
								a ($nav-level-2... $nav-sub-link-height)
									span

		p.nav-title ($nav-title-*...)


========================================================================== */
/* main navigation */
/* left panel */
/* nav parent level-0 */
/* nav icon sizes */
/* badge default */
/* all child */
/* nav title */
/* nav Minify */
/* when the menu pops on hover */
/* navigation Width */
/* partial visibility of the menu */
/* top navigation */
/* nav Info Card (appears below the logo) */
/* width is auto */
/* nav DL labels for all child */
/* will be pulled to left as a negative value */
/*   MISC Settings
========================================================================== */
/* List Table */
/*   PAGE SETTINGS
========================================================================== */
/*   PAGE BREADCRUMB 
========================================================================== */
/*   PAGE COMPONENT PANELS 
========================================================================== */
/*   PAGE COMPONENT PROGRESSBARS 
========================================================================== */
/*   PAGE COMPONENT MESSENGER 
========================================================================== */
/*   FOOTER
========================================================================== */
/*   GLOBALS
========================================================================== */
/* ACCESSIBILITIES */
@import url("https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900");
body {
  font-family: "Roboto", "Helvetica Neue", Helvetica, Arial;
  font-size: 0.8125rem;
  letter-spacing: 0.1px; }

.page-content {
  color: #666666; }

h1, h2, h3, h4, h5, h6 {
  line-height: 1.3;
  font-weight: 400; }

strong {
  font-weight: 500; }

h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small {
  font-weight: 300;
  display: block;
  font-size: 0.9375rem;
  line-height: 1.5;
  margin: 2px 0 1.5rem; }

h2 small,
h3 small,
.h2 small,
.h3 small {
  font-size: 0.9375rem; }

h4 small,
.h4 small {
  font-size: 0.875rem; }

h5 small,
h6 small,
.h5 small,
.h6 small {
  font-size: 0.8125rem; }

/* contrast text */
.text-contrast {
  color: #333333; }

/* text-gradient */
.text-gradient {
  background: -webkit-gradient(linear, left top, left bottom, color-stop(25%, #6e4e9e), color-stop(50%, #62468d), color-stop(75%, #0c7cd5), to(#0960a5));
  background: linear-gradient(180deg, #6e4e9e 25%, #62468d 50%, #0c7cd5 75%, #0960a5 100%);
  color: #886ab5;
  background-clip: text;
  text-fill-color: transparent;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none; }

/* looking for font size? Check _helpers.scss */
/* PLACEHOLDER 
============================================= 

EXAMPLE:

%bg-image {
		width: 100%;
		background-position: center center;
		background-size: cover;
		background-repeat: no-repeat;
}

.image-one {
		@extend %bg-image;
		background-image:url(/img/image-one.jpg");
}

RESULT:

.image-one, .image-two {
		width: 100%;
		background-position: center center;
		background-size: cover;
		background-repeat: no-repeat;
}

*/
.page-logo, .page-sidebar, .nav-footer, .bg-brand-gradient {
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(51, 148, 225, 0.18)), to(transparent));
  background-image: linear-gradient(270deg, rgba(51, 148, 225, 0.18), transparent);
  background-color: #000080; }

/*
%shadow-hover {
	box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);
	transition: all 0.2s ease-in-out;

	&:hover {
		box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);
	}
}
*/
.btn-default {
  background-color: #f5f5f5;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f5f5f5), to(#f1f1f1));
  background-image: linear-gradient(to top, #f5f5f5, #f1f1f1);
  color: #444;
  border: 1px solid rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: none;
          box-shadow: none; }
  .btn-default:hover {
    -webkit-box-shadow: none;
            box-shadow: none;
    border: 1px solid #c6c6c6;
    color: #333;
    z-index: 2; }
  .btn-default:focus {
    border-color: #b19dce !important;
    z-index: 3; }
  .active.btn-default {
    background: #a38cc6;
    color: #fff;
    -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important; }

.custom-scroll,
.custom-scrollbar {
  overflow: hidden;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch; }
  .custom-scroll::-webkit-scrollbar-track-piece,
  .custom-scrollbar::-webkit-scrollbar-track-piece {
    background-color: transparent; }
  .custom-scroll::-webkit-scrollbar-thumb:vertical,
  .custom-scrollbar::-webkit-scrollbar-thumb:vertical {
    background-color: #666; }
  .custom-scroll::-webkit-scrollbar,
  .custom-scrollbar::-webkit-scrollbar {
    height: 4px;
    width: 4px; }
  .custom-scroll::-webkit-scrollbar-corner,
  .custom-scrollbar::-webkit-scrollbar-corner {
    width: 40px; }
  .custom-scroll::-webkit-scrollbar-thumb:vertical,
  .custom-scrollbar::-webkit-scrollbar-thumb:vertical {
    background-color: #666; }

.page-logo, body:not(.header-function-fixed) .page-logo, .header-function-fixed:not(.nav-function-top) .page-header, #msgr_listfilter_input, .msgr-list, .msgr-list + .msgr:before {
  -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
  transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1); }

.breadcrumb > li.breadcrumb-item {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; }

.btn-switch, [class*="btn-outline-"], .panel-toolbar .btn-panel, .settings-panel .list, .settings-panel .list .onoffswitch,
.settings-panel .list:hover .onoffswitch, .color-disp-demo tr td, .icon-demo li {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out; }

.info-card img.cover {
  -webkit-transition: opacity 0.5s ease-in-out;
  transition: opacity 0.5s ease-in-out; }

.header-function-fixed .btn-switch[data-class="header-function-fixed"], .nav-function-fixed .btn-switch[data-class="nav-function-fixed"], .nav-function-minify .btn-switch[data-class="nav-function-minify"], .nav-function-hidden .btn-switch[data-class="nav-function-hidden"], .nav-function-top .btn-switch[data-class="nav-function-top"], .nav-mobile-push .btn-switch[data-class="nav-mobile-push"], .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"], .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"], .mod-main-boxed .btn-switch[data-class="mod-main-boxed"], .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"], .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"], .mod-pace-custom .btn-switch[data-class="mod-pace-custom"], .mod-bigger-font .btn-switch[data-class="mod-bigger-font"], .mod-high-contrast .btn-switch[data-class="mod-high-contrast"], .mod-color-blind .btn-switch[data-class="mod-color-blind"], .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"], .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"], .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"], .mod-disable-animation .btn-switch[data-class="mod-disable-animation"], .mod-nav-link .btn-switch[data-class="mod-nav-link"], .mod-app-rtl .btn-switch[data-class="mod-app-rtl"] {
  color: #fff;
  background: #886ab5 !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"]:before, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"]:before, .nav-function-minify .btn-switch[data-class="nav-function-minify"]:before, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"]:before, .nav-function-top .btn-switch[data-class="nav-function-top"]:before, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"]:before, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"]:before, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"]:before, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"]:before, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"]:before, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"]:before, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"]:before, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"]:before, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"]:before, .mod-color-blind .btn-switch[data-class="mod-color-blind"]:before, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"]:before, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"]:before, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"]:before, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"]:before, .mod-nav-link .btn-switch[data-class="mod-nav-link"]:before, .mod-app-rtl .btn-switch[data-class="mod-app-rtl"]:before {
    content: "ON" !important;
    left: 7px !important;
    right: auto !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"]:after, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"]:after, .nav-function-minify .btn-switch[data-class="nav-function-minify"]:after, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"]:after, .nav-function-top .btn-switch[data-class="nav-function-top"]:after, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"]:after, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"]:after, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"]:after, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"]:after, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"]:after, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"]:after, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"]:after, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"]:after, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"]:after, .mod-color-blind .btn-switch[data-class="mod-color-blind"]:after, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"]:after, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"]:after, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"]:after, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"]:after, .mod-nav-link .btn-switch[data-class="mod-nav-link"]:after, .mod-app-rtl .btn-switch[data-class="mod-app-rtl"]:after {
    content: " " !important;
    right: 0 !important;
    left: auto !important;
    background: #fff !important;
    color: #886ab5 !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"] + .onoffswitch-title, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"] + .onoffswitch-title, .nav-function-minify .btn-switch[data-class="nav-function-minify"] + .onoffswitch-title, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"] + .onoffswitch-title, .nav-function-top .btn-switch[data-class="nav-function-top"] + .onoffswitch-title, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"] + .onoffswitch-title, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"] + .onoffswitch-title, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"] + .onoffswitch-title, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"] + .onoffswitch-title, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"] + .onoffswitch-title, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"] + .onoffswitch-title, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"] + .onoffswitch-title, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"] + .onoffswitch-title, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"] + .onoffswitch-title, .mod-color-blind .btn-switch[data-class="mod-color-blind"] + .onoffswitch-title, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"] + .onoffswitch-title, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"] + .onoffswitch-title, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"] + .onoffswitch-title, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"] + .onoffswitch-title, .mod-nav-link .btn-switch[data-class="mod-nav-link"] + .onoffswitch-title, .mod-app-rtl .btn-switch[data-class="mod-app-rtl"] + .onoffswitch-title {
    font-weight: 500;
    color: #886ab5; }

.info-card img.cover {
  background-size: cover; }

.nav-mobile-slide-out #nmp,
.nav-mobile-slide-out #nmno, .nav-function-top #mnl,
.nav-function-minify #mnl,
.mod-hide-nav-icons #mnl, .nav-function-top #nfh, .nav-function-top #mhni,
.nav-function-minify #mhni {
  position: relative; }
  .nav-mobile-slide-out #nmp .onoffswitch-title, .nav-mobile-slide-out #nmno .onoffswitch-title, .nav-function-top #mnl .onoffswitch-title, .nav-function-minify #mnl .onoffswitch-title, .mod-hide-nav-icons #mnl .onoffswitch-title, .nav-function-top #nfh .onoffswitch-title, .nav-function-top #mhni .onoffswitch-title, .nav-function-minify #mhni .onoffswitch-title {
    color: #d58100 !important; }
  .nav-mobile-slide-out #nmp .onoffswitch-title-desc, .nav-mobile-slide-out #nmno .onoffswitch-title-desc, .nav-function-top #mnl .onoffswitch-title-desc, .nav-function-minify #mnl .onoffswitch-title-desc, .mod-hide-nav-icons #mnl .onoffswitch-title-desc, .nav-function-top #nfh .onoffswitch-title-desc, .nav-function-top #mhni .onoffswitch-title-desc, .nav-function-minify #mhni .onoffswitch-title-desc {
    color: #ec9f28 !important; }
  .nav-mobile-slide-out #nmp:after,
  .nav-mobile-slide-out #nmno:after, .nav-function-top #mnl:after,
  .nav-function-minify #mnl:after,
  .mod-hide-nav-icons #mnl:after, .nav-function-top #nfh:after, .nav-function-top #mhni:after,
  .nav-function-minify #mhni:after {
    content: "DISABLED"; }

/*%fixed-header-shadow {
	@include box-shadow(0 2px 2px -1px rgba(0,0,0,.1));
}*/
.dropdown-icon-menu > ul > li .btn, .header-btn {
  border-radius: 4px;
  border: 1px solid gainsboro;
  height: 2.25rem;
  width: 3.25rem;
  vertical-align: middle;
  line-height: 2.125rem;
  margin-right: 0.9375rem;
  font-size: 21px;
  padding: 0 11px;
  cursor: default;
  color: #a6a6a6;
  position: relative;
  /*
	&.active {
		@extend %header-btn-active;
	}*/ }
  .dropdown-icon-menu > ul > li .btn:hover, .header-btn:hover {
    -webkit-box-shadow: none;
            box-shadow: none;
    border-color: #886ab5;
    background: #a38cc6;
    color: #fff; }

.settings-panel .expanded {
  -webkit-box-shadow: inset 0 1px 5px rgba(0, 0, 0, 0.125);
          box-shadow: inset 0 1px 5px rgba(0, 0, 0, 0.125);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-width: 0 0 1px 0;
  background: #fff;
  padding: 16px 16px 10px; }

.nav-function-fixed:not(.nav-function-top) .page-header [data-class="nav-function-fixed"] {
  background: #505050;
  border-color: #363636 !important;
  -webkit-box-shadow: inset 0 0 3px 1px rgba(0, 0, 0, 0.37);
          box-shadow: inset 0 0 3px 1px rgba(0, 0, 0, 0.37);
  color: #fff !important; }

/*  %selected-dot {
		&:before {
			content: " ";
			display: block;
			border-radius: 50%;
			background: inherit;
			background-image: none;
			border: 2px solid rgba(0,0,0,0.2);
			position: absolute;
			top: 15px;
			left: 15px;
			height: 20px;
			width: 20px;
		}
		&:after {
			content: " ";
			height: inherit;
			width: inherit;
			border: 5px solid rgba(0,0,0,0.1);
			position: absolute;
			left: 0;
			top: 0;
			border-radius: 50%;
		} 
	}*/
.saving #saving {
  margin: 5px;
  height: 20px;
  width: 20px;
  -webkit-animation: spin 0.5s infinite linear;
          animation: spin 0.5s infinite linear;
  border: 2px solid #886ab5;
  border-right-color: transparent;
  border-radius: 50%; }

.nav-mobile-slide-out #nmp:after,
.nav-mobile-slide-out #nmno:after, .nav-function-top #mnl:after,
.nav-function-minify #mnl:after,
.mod-hide-nav-icons #mnl:after, .nav-function-top #nfh:after, .nav-function-top #mhni:after,
.nav-function-minify #mhni:after {
  display: block;
  position: absolute;
  background: #ffebc1;
  font-size: 10px;
  width: 65px;
  text-align: center;
  border: 1px solid #ffb20e;
  height: 22px;
  line-height: 20px;
  border-radius: 10px;
  right: 13px;
  top: 26%;
  color: #1d1d1d; }

/* patterns */
.mod-color-blind .page-sidebar .primary-nav .nav-menu > li.active > a,
.mod-color-blind [class*="bg-danger-"],
.mod-color-blind .btn-danger,
.mod-color-blind .btn-outline-danger,
.mod-color-blind .alert-danger,
.pattern-0 {
  background-size: 10px 10px;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.07) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.07) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%, transparent);
  -pie-background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%, transparent) 0 0/10px 10px transparent; }

.mod-color-blind .page-sidebar .primary-nav .nav-menu > li.active > a + ul > li.active > a,
.pattern-1 {
  background-size: 5px 5px;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.04) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.04) 50%, rgba(255, 255, 255, 0.04) 75%, transparent 75%, transparent);
  -pie-background: linear-gradient(45deg, rgba(255, 255, 255, 0.04) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.04) 50%, rgba(255, 255, 255, 0.04) 75%, transparent 75%, transparent) 0 0/5px 5px transparent; }

.mod-color-blind [class*="bg-primary-"],
.mod-color-blind .btn-primary,
.mod-color-blind .btn-outline-primary,
.mod-color-blind .alert-primary,
.pattern-2 {
  background-size: 15px 15px;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(50%, rgba(255, 255, 255, 0.2)), color-stop(50%, transparent), to(transparent));
  background-image: linear-gradient(rgba(255, 255, 255, 0.2) 50%, transparent 50%, transparent);
  -pie-background: linear-gradient(rgba(255, 255, 255, 0.2) 50%, transparent 50%, transparent) 0 0/15px transparent; }

.mod-color-blind [class*="bg-success-"],
.mod-color-blind .btn-success,
.mod-color-blind .btn-outline-success,
.mod-color-blind .alert-success,
.pattern-3 {
  background-size: 15px 15px;
  background-image: -webkit-gradient(linear, left top, right top, color-stop(50%, rgba(255, 255, 255, 0.2)), color-stop(50%, transparent), to(transparent));
  background-image: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 50%, transparent 50%, transparent);
  -pie-background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 50%, transparent 50%, transparent) 0 0/15px 15px transparent; }

.mod-color-blind [class*="bg-info-"],
.mod-color-blind .btn-info,
.mod-color-blind .btn-outline-info,
.mod-color-blind .alert-info,
.pattern-4 {
  background-size: 37px 37px;
  background-position: 0 0, 18.5px 18.5px;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2)), linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2));
  -pie-background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2)) 0 0/37px, linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2)) 37px 37px/74px, transparent; }

.mod-color-blind [class*="bg-warning-"],
.mod-color-blind .btn-warning,
.mod-color-blind .btn-outline-warning,
.mod-color-blind .alert-warning,
.pattern-5 {
  background-size: 37px 37px;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2)), linear-gradient(135deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2));
  -pie-background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2)) 0 0/60px, linear-gradient(135deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2)) 0 0/60px, #eee; }

/* #FRAMEWORK - Structure and layout files. (**DO NOT** change order)
                DOC: you can disable unused _modules
========================================================================== */
/* contains root variables to be used with css (see docs) */
:root {
  --theme-primary: #886ab5;
  --theme-secondary: #868e96;
  --theme-success: #1dc9b7;
  --theme-info: #2196F3;
  --theme-warning: #ffc241;
  --theme-danger: #fd3995;
  --theme-light: #fff;
  --theme-dark: #505050;
  --theme-primary-50: #ccbfdf;
  --theme-primary-100: #beaed7;
  --theme-primary-200: #b19dce;
  --theme-primary-300: #a38cc6;
  --theme-primary-400: #967bbd;
  --theme-primary-500: #886ab5;
  --theme-primary-600: #7a59ad;
  --theme-primary-700: #6e4e9e;
  --theme-primary-800: #62468d;
  --theme-primary-900: #563d7c;
  --theme-success-50: #7aece0;
  --theme-success-100: #63e9db;
  --theme-success-200: #4de5d5;
  --theme-success-300: #37e2d0;
  --theme-success-400: #21dfcb;
  --theme-success-500: #1dc9b7;
  --theme-success-600: #1ab3a3;
  --theme-success-700: #179c8e;
  --theme-success-800: #13867a;
  --theme-success-900: #107066;
  --theme-info-50: #9acffa;
  --theme-info-100: #82c4f8;
  --theme-info-200: #6ab8f7;
  --theme-info-300: #51adf6;
  --theme-info-400: #39a1f4;
  --theme-info-500: #2196F3;
  --theme-info-600: #0d8aee;
  --theme-info-700: #0c7cd5;
  --theme-info-800: #0a6ebd;
  --theme-info-900: #0960a5;
  --theme-warning-50: #ffebc1;
  --theme-warning-100: #ffe3a7;
  --theme-warning-200: #ffdb8e;
  --theme-warning-300: #ffd274;
  --theme-warning-400: #ffca5b;
  --theme-warning-500: #ffc241;
  --theme-warning-600: #ffba28;
  --theme-warning-700: #ffb20e;
  --theme-warning-800: #f4a500;
  --theme-warning-900: #da9400;
  --theme-danger-50: #feb7d9;
  --theme-danger-100: #fe9ecb;
  --theme-danger-200: #fe85be;
  --theme-danger-300: #fe6bb0;
  --theme-danger-400: #fd52a3;
  --theme-danger-500: #fd3995;
  --theme-danger-600: #fd2087;
  --theme-danger-700: #fc077a;
  --theme-danger-800: #e7026e;
  --theme-danger-900: #ce0262;
  --theme-fusion-50: #909090;
  --theme-fusion-100: #838383;
  --theme-fusion-200: #767676;
  --theme-fusion-300: dimgray;
  --theme-fusion-400: #5d5d5d;
  --theme-fusion-500: #505050;
  --theme-fusion-600: #434343;
  --theme-fusion-700: #363636;
  --theme-fusion-800: #2a2a2a;
  --theme-fusion-900: #1d1d1d;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1399px; }

/* resets DOM elements to its natural state */
/* for IE */
main {
  display: block; }

/* removes dotted lines for focus */
a, a:active, a:focus,
button, button:focus, button:active,
.btn, .btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn.focus:active, .btn.active.focus {
  outline: none;
  outline: 0; }

input::-moz-focus-inner {
  border: 0; }

/* html and body base styles */
html body {
  direction: ltr;
  text-rendering: optimizeLegibility;
  background-color: #fff; }

/* app header */
.header-icon {
  color: #666666;
  height: 4.125rem;
  display: block;
  line-height: 4.125rem;
  text-decoration: none;
  position: relative; }
  .header-icon:not(.btn) {
    min-width: 3.125rem;
    text-align: center;
    overflow: visible; }
    .header-icon:not(.btn) > [class*='fa-']:first-child,
    .header-icon:not(.btn) > .ni:first-child {
      color: #886ab5;
      vertical-align: middle; }
    .header-icon:not(.btn) > [class*='fa-']:first-child {
      font-size: 21px; }
    .header-icon:not(.btn) > .ni:first-child {
      font-size: 21px; }
    .header-icon:not(.btn):hover > [class*='fa-']:only-child,
    .header-icon:not(.btn):hover > .ni {
      color: #404040; }
    .header-icon:not(.btn)[data-toggle="dropdown"] {
      /* header dropdowns */
      /* note: important rules to override popper's inline classes */
      /* end header dropdowns */ }
      .header-icon:not(.btn)[data-toggle="dropdown"][data-toggle="dropdown"]:after {
        content: " ";
        width: 1.5rem;
        height: 1.5rem;
        position: absolute;
        background: #dae1e8;
        border-radius: 50%;
        top: 1.3125rem;
        z-index: -1;
        left: 0.9375rem;
        opacity: 0;
        -webkit-transition: all 100ms ease-in;
        transition: all 100ms ease-in; }
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] {
        color: #404040;
        /* new lines for arrow visibility */
        position: relative;
        z-index: 1001;
        font-weight: 500; }
        .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"]:after {
          content: " ";
          width: 2.5rem;
          height: 2.5rem;
          top: 0.8125rem;
          z-index: -1;
          left: 0.3125rem;
          opacity: 1; }
        .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > [class*='fa-']:first-child,
        .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > .ni:first-child {
          color: #404040 !important;
          -webkit-background-clip: initial;
          -webkit-text-fill-color: initial;
          background: none; }
      .header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu {
        position: absolute;
        border: 0px solid #ccc;
        right: 2rem;
        top: 4.0625rem !important;
        left: auto !important;
        padding: 0;
        margin: 0; }
    .header-icon:not(.btn) .profile-image {
      width: 2rem;
      height: auto; }
  .header-icon:hover {
    cursor: default;
    color: #404040; }

.page-header {
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 28px 0px rgba(86, 61, 124, 0.13);
          box-shadow: 0px 0px 28px 0px rgba(86, 61, 124, 0.13);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 4.125rem;
  position: relative;
  z-index: 1000;
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1; }
  .page-header .page-logo {
    display: none; }
  .page-header .badge-icon {
    left: 1.5625rem;
    top: 1.09375rem; }
    .page-header .badge-icon:only-child {
      position: relative;
      left: auto;
      right: auto;
      font-size: 14px;
      height: 26px;
      width: 26px;
      line-height: 21px;
      top: 20px;
      margin: 0 auto;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center; }

/* app logo */
.page-logo {
  height: 4.125rem;
  width: 16.875rem;
  -webkit-box-shadow: 0px 0px 28px 0px rgba(0, 0, 0, 0.13);
          box-shadow: 0px 0px 28px 0px rgba(0, 0, 0, 0.13);
  overflow: hidden;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-positive: 0;
  -webkit-box-flex: 0;
          flex-grow: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  min-height: 1px;
  padding: 0 2rem; }
  .page-logo img {
    width: auto;
    height: auto; }
  .page-logo .page-logo-link {
    -webkit-box-flex: 1;
        -ms-flex: 1 0 auto;
            flex: 1 0 auto; }

.page-logo-text {
  margin-left: 0.5rem;
  font-weight: 300;
  font-size: 1rem;
  color: #fff;
  display: block;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 auto;
          flex: 1 0 auto;
  text-align: left; }

/* app search */
.search {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1; }
  .search .app-forms {
    padding: 0; }
    .search .app-forms.has-length {
      position: relative; }
    .search .app-forms:before {
      content: none; }

#search-field {
  max-width: 21.875rem;
  height: 2.625rem;
  line-height: normal;
  border-radius: 4px;
  background: transparent;
  border: 1px solid transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 0.9375rem;
  padding: 0.38rem; }

/* icon menu with user options */
.dropdown-icon-menu > .btn {
  z-index: 1; }

.dropdown-icon-menu > ul {
  opacity: 0;
  list-style: none;
  margin: 0;
  position: absolute;
  background: #fff;
  height: 2.25rem;
  padding: 2.75rem 4px 5px;
  width: 3.75rem;
  margin-left: 1px;
  margin-top: -2.5rem;
  left: -5px;
  overflow: hidden;
  -webkit-box-shadow: 0 3px 3px rgba(0, 0, 0, 0.12), 0 0 3px rgba(0, 0, 0, 0.24);
          box-shadow: 0 3px 3px rgba(0, 0, 0, 0.12), 0 0 3px rgba(0, 0, 0, 0.24);
  border-radius: 0.25rem;
  -webkit-transition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);
  transition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1); }
  .dropdown-icon-menu > ul > li {
    margin-bottom: 4px;
    position: relative; }
    .dropdown-icon-menu > ul > li:last-child {
      margin-bottom: 0; }

.dropdown-icon-menu:hover > ul {
  display: block;
  opacity: 1;
  /*
			 * n = number of buttons minus 1 
			 *     eg. $header-btn-height * 2n
			 */
  height: 7.75rem; }
  .dropdown-icon-menu:hover > ul:hover {
    overflow: visible; }

/* dropdown notification in the app header */
.tab-notification {
  height: 363px; }
  .tab-notification .tab-pane {
    height: 100%; }

.notification {
  padding: 0;
  margin: 0;
  list-style: none;
  position: relative; }
  .notification li {
    position: relative;
    background: #fff; }
    .notification li.unread {
      background: #fffaee; }
      .notification li.unread .name {
        font-weight: 500; }
    .notification li > :first-child {
      padding: 0.75rem 1.5rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06); }
      .notification li > :first-child:hover {
        text-decoration: none;
        background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.03)), to(rgba(29, 33, 41, 0.04)));
        background-image: linear-gradient(rgba(29, 33, 41, 0.03), rgba(29, 33, 41, 0.04)); }
      .notification li > :first-child:focus {
        text-decoration: none; }
      .notification li > :first-child > span {
        position: relative; }
        .notification li > :first-child > span > span {
          /* IE fix */
          display: block; }
    .notification li:last-child > a {
      border: 0; }
  .notification .name {
    color: #222222;
    font-weight: 400;
    font-size: 0.8125rem; }
  .notification .msg-a,
  .notification .msg-b {
    color: #555555; }
  .notification.notification-layout-2 li {
    background: #f9f9f9; }
    .notification.notification-layout-2 li.unread {
      background: #fff; }
      .notification.notification-layout-2 li.unread .name {
        font-weight: bold; }
    .notification.notification-layout-2 li > :first-child {
      position: relative;
      border-bottom: 1px solid rgba(0, 0, 0, 0.04);
      z-index: 1; }
      .notification.notification-layout-2 li > :first-child:hover {
        background: transparent; }
        .notification.notification-layout-2 li > :first-child:hover:after {
          content: "";
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: -1;
          -webkit-box-shadow: inset 1px 0 0 #dadce0, inset -1px 0 0 #dadce0, 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
                  box-shadow: inset 1px 0 0 #dadce0, inset -1px 0 0 #dadce0, 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15); }
    .notification.notification-layout-2 li .name {
      font-size: 0.875rem; }
  .notification.notification-layout-2:hover {
    cursor: pointer; }
  .notification:not(.notification-loading):before {
    content: "No new messages";
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    padding: 1.5rem;
    width: 100%;
    display: block; }

/* icon menu with stacked icons located in the app header */
.app-list {
  margin: 0 auto;
  display: block;
  width: 21.875rem !important;
  height: 22.5rem !important;
  font-size: 0;
  padding: 0.5rem 1rem;
  text-align: center; }
  .app-list > li {
    display: inline-block;
    text-align: center;
    padding: 0; }

.app-list-item {
  height: 5.9375rem;
  width: 6.25rem;
  display: block;
  text-decoration: none;
  color: #666666;
  margin: 10px 2px;
  border: 1px solid transparent !important;
  outline: none;
  border-radius: 3px;
  padding-top: 8px;
  border-radius: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column; }
  .app-list-item .icon-stack {
    font-size: 3.125rem;
    margin-top: 4px; }
  .app-list-item:hover {
    border: 1px solid #e3e3e3;
    padding-top: 7px; }
    .app-list-item:hover > .icon-stack {
      font-size: 3.1875rem; }
  .app-list-item:active {
    border-color: #886ab5;
    padding-top: 8px; }
    .app-list-item:active > .icon-stack {
      font-size: 3.125rem; }

.app-list-name {
  text-align: center;
  font-size: 0.8125rem;
  text-overflow: ellipsis;
  display: block;
  white-space: nowrap;
  overflow: hidden; }

/* app header stays fixed */
.header-function-fixed:not(.nav-function-top) .page-header {
  left: 0;
  position: fixed !important;
  right: 0;
  top: 0; }

.header-function-fixed:not(.nav-function-top) .page-content {
  margin-top: 4.125rem; }

.header-function-fixed:not(.nav-function-top):not(.nav-function-fixed) {
  /* bug fix for nav hidden other than chrome...*/ }
  .header-function-fixed:not(.nav-function-top):not(.nav-function-fixed) .page-logo {
    width: 16.875rem;
    position: fixed;
    top: 0;
    z-index: 950; }
  .header-function-fixed:not(.nav-function-top):not(.nav-function-fixed) .page-sidebar .primary-nav {
    margin-top: 4.125rem; }
  .header-function-fixed:not(.nav-function-top):not(.nav-function-fixed).desktop.nav-function-hidden .page-logo {
    position: absolute;
    -webkit-transition: none !important;
    transition: none !important; }

@media (min-width: 992px) {
  .header-function-fixed:not(.nav-function-top) .page-header {
    margin-left: 16.875rem; }
  .header-function-fixed:not(.nav-function-top).nav-function-minify .page-sidebar .page-logo {
    width: 4.6875rem; }
  .header-function-fixed.nav-function-top {
    /*.page-wrapper {
				padding-top: $header-height-nav-top;
			}*/ }
    .header-function-fixed.nav-function-top .page-header {
      position: fixed !important;
      /*top: 0;
				right: 0;
				left: 0;*/
      -webkit-box-shadow: 0px 0px 28px 2px rgba(86, 61, 124, 0.13);
              box-shadow: 0px 0px 28px 2px rgba(86, 61, 124, 0.13);
      /*chrome flickering solution*/
      -webkit-transform: translateZ(0); } }

/* app far left panel */
.page-sidebar {
  position: relative;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 auto;
          flex: 1 0 auto;
  width: 16.875rem;
  max-width: 16.875rem;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  z-index: 1002;
  will-change: left, right; }

/* app navigation */
.primary-nav {
  overflow: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }
  .primary-nav .nav-menu:last-of-type {
    margin: 0; }
  .primary-nav .nav-menu:first-of-type {
    margin-top: 1rem;
    margin-bottom: 1rem; }

.nav-title {
  text-transform: uppercase;
  margin: 0;
  color: #8268a8;
  padding: 1rem 2rem;
  margin-top: 1.5rem;
  font-size: 0.7rem;
  letter-spacing: 1px;
  font-weight: 500; }

.nav-menu {
  padding: 0;
  list-style: none;
  margin: 0; }
  .nav-menu a,
  .nav-menu a > [class*='fa-'],
  .nav-menu a > .ni {
    -webkit-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out; }
  .nav-menu b.collapse-sign {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    color: #967bbd; }
    .nav-menu b.collapse-sign > [class*='fa-'],
    .nav-menu b.collapse-sign > .ni {
      font-size: inherit; }
  .nav-menu ul {
    padding-left: 0;
    list-style: none;
    display: none; }
  .nav-menu li {
    position: relative; }
    .nav-menu li.open > a {
      color: white; }
    .nav-menu li.active {
      /* arrow that appears next to active/selected items */ }
      .nav-menu li.active > a {
        color: white;
        background-color: rgba(255, 255, 255, 0.04);
        -webkit-box-shadow: inset 3px 0 0 #886ab5;
                box-shadow: inset 3px 0 0 #886ab5;
        font-weight: 400; }
        .nav-menu li.active > a:hover > [class*='fa-'],
        .nav-menu li.active > a:hover > .ni {
          color: #a8a6ac; }
      .nav-menu li.active > ul {
        display: block; }
      .nav-menu li.active:not(.open) > a:before {
        content: '\f413';
        font-family: 'nextgen-icons';
        position: absolute;
        top: calc(50% - 5px);
        right: 11px;
        font-size: 7px;
        height: 10px;
        width: auto;
        color: #24b3a4;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-line-pack: center;
            align-content: center;
        -webkit-box-align: center;
            -ms-flex-align: center;
                align-items: center; }
    .nav-menu li a {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      /*new*/
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      /*new*/
      outline: 0;
      padding: 0.8125rem 2rem;
      font-size: 0.875rem;
      color: #bdafd1;
      font-weight: 400;
      text-decoration: none;
      position: relative;
      /* needed for mod-nav-hiarchiy*/
      /*> .badge {

				@extend %ping-badge;

				& + [class*='fa-'],
				& + .ni {
					display: none;
				}

			}*/
      /*> [class*='fa-'] {
				font-size: $nav-font-icon-size - 2;
			}*/
      /*> img {
				background: $primary-500;
				padding: 0.125rem;
				width: 20px;
				height: 20px;
				margin-left: 5px;
				margin-right: $nav-icon-margin-right + 0.1875rem;
			}*/
      /*> .badge:not(.clear-badge):first-child  {
				position: static;
				display: inline-block;
				border-radius: 5px;
				margin-right: 10px;
				width: 28px;
				height: auto;
				padding: 4px 0;
				font-size: rem($fs-base);
			}*/ }
      .nav-menu li a .dl-ref {
        font-size: 0.625rem;
        text-align: center;
        min-width: 1.25rem;
        display: inline-block;
        border-radius: 4px;
        letter-spacing: 0.5px;
        margin-left: -2.1875rem;
        margin-right: 0.9375rem;
        font-weight: 500;
        overflow: hidden;
        padding: 0 4px;
        -webkit-font-smoothing: subpixel-antialiased; }
        .nav-menu li a .dl-ref.label {
          margin-left: 0;
          margin-right: 0;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.7); }
      .nav-menu li a > [class*='fa-'],
      .nav-menu li a > .ni {
        margin-right: 0.25rem;
        font-size: 1.125rem;
        width: 1.75rem;
        color: #876fab; }
      .nav-menu li a > .nav-link-text {
        -webkit-box-flex: 1;
            -ms-flex: 1;
                flex: 1;
        /*new*/
        display: -webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex;
        -webkit-box-align: center;
            -ms-flex-align: center;
                align-items: center;
        line-height: normal; }
      .nav-menu li a.collapsed .nav-menu-btn-sub-collapse {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg); }
      .nav-menu li a:hover {
        color: white;
        text-decoration: none;
        background-color: rgba(0, 0, 0, 0.1); }
        .nav-menu li a:hover .badge {
          color: #fff; }
        .nav-menu li a:hover > [class*='fa-'],
        .nav-menu li a:hover > .ni {
          color: #a8a6ac; }
        .nav-menu li a:hover > .badge {
          -webkit-box-shadow: 0 0 0 1px rgba(107, 83, 143, 0.8);
                  box-shadow: 0 0 0 1px rgba(107, 83, 143, 0.8);
          border: 1px solid rgba(107, 83, 143, 0.8); }
      .nav-menu li a:focus {
        color: white; }
        .nav-menu li a:focus .badge {
          color: #fff; }
    .nav-menu li > ul {
      background-color: rgba(0, 0, 0, 0.1);
      padding-top: 10px;
      padding-bottom: 10px; }
      .nav-menu li > ul li a {
        color: #af9fc7;
        padding: 0.8125rem 2rem 0.8125rem 4rem; }
        .nav-menu li > ul li a b.collapse-sign > [class*='fa-'],
        .nav-menu li > ul li a b.collapse-sign > .ni {
          font-size: inherit-2; }
        .nav-menu li > ul li a > [class*='fa-'],
        .nav-menu li > ul li a > .ni {
          margin-left: -2.1875rem;
          margin-right: 0.9375rem;
          color: #876fab;
          font-size: 0.875rem;
          width: 1.25rem;
          text-align: center; }
        .nav-menu li > ul li a > .badge {
          color: #fff;
          background-color: #fd3995;
          border: 1px solid #505050; }
        .nav-menu li > ul li a:hover {
          color: white;
          background-color: rgba(0, 0, 0, 0.1); }
          .nav-menu li > ul li a:hover > .nav-link-text > [class*='fa-'],
          .nav-menu li > ul li a:hover > .nav-link-text > .ni {
            color: #a8a6ac; }
      .nav-menu li > ul li.active > a {
        color: white;
        background-color: transparent;
        -webkit-box-shadow: none;
                box-shadow: none;
        font-weight: 400; }
        .nav-menu li > ul li.active > a > .nav-link-text > [class*='fa-'],
        .nav-menu li > ul li.active > a > .nav-link-text > .ni {
          color: white; }
        .nav-menu li > ul li.active > a:hover > .nav-link-text > [class*='fa-'],
        .nav-menu li > ul li.active > a:hover > .nav-link-text > .ni {
          color: #a8a6ac; }
      .nav-menu li > ul li:last-child > ul {
        padding-bottom: 0; }
      .nav-menu li > ul li > ul li.active > a {
        color: white; }
      .nav-menu li > ul li > ul li a {
        color: #aa99c4;
        padding: 0.8125rem 2rem 0.8125rem 4.75rem; }
        .nav-menu li > ul li > ul li a .dl-ref {
          margin-left: 0;
          margin-right: 0.20833rem; }
        .nav-menu li > ul li > ul li a > [class*='fa-'],
        .nav-menu li > ul li > ul li a > .ni {
          margin-left: 0;
          margin-right: 0.20833rem; }
        .nav-menu li > ul li > ul li a:hover {
          color: white; }
        .nav-menu li > ul li > ul li a > .badge {
          color: #fff;
          background-color: #fd3995;
          border: 1px solid #505050; }
    .nav-menu li:last-child {
      margin-bottom: 0; }
  .nav-menu:last-child {
    margin-bottom: 0; }

/* nav hover elements 
.nav-menu-hover {

	li > ul {
		background-color: rgba(0,0,0,0.17) !important;
	}

	li {
		a {
			color: rgba(255,255,255,0.90);
			span {
				color: rgba(255,255,255,0.90);
			}
			

			&:hover {
				background:rgba(255,255,255,0.09) !important;
				color: $white !important;
				span {
					color: $white;
				}
				
			}
		}
	}
}*/
/* nav clean elements */
.nav-menu-clean {
  background: #fff; }
  .nav-menu-clean ul {
    background: transparent !important;
    padding-bottom: 0 !important; }
  .nav-menu-clean li a {
    background: transparent !important;
    color: #505050 !important; }
    .nav-menu-clean li a span {
      color: #505050 !important; }
    .nav-menu-clean li a:hover {
      background-color: #f4f4f4 !important; }
  .nav-menu-clean li a {
    border-bottom: 1px solid transparent; }
  .nav-menu-clean li > ul li > ul > li:not(:last-child) a {
    border-bottom: none; }

/* nav bordered elements */
.nav-menu-bordered {
  border: 1px solid rgba(0, 0, 0, 0.08); }
  .nav-menu-bordered li a {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08); }
  .nav-menu-bordered li > ul li > ul > li:not(:last-child) a {
    border-bottom: none; }

/* nav compact elements */
.nav-menu-compact li a {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important; }

.nav-menu-compact li li a {
  padding-left: 2rem !important; }

.nav-menu-compact li li li a {
  padding-left: 2.5rem !important; }

.nav-menu.nav-menu-reset li a:not(:hover),
.nav-menu.nav-menu-reset .collapse-sign {
  color: rgba(255, 255, 255, 0.7) !important; }

/*body:not(.nav-function-top) {

	.primary-nav {

		.nav-menu {

			a,
			a:hover,
			a:focus {

				.badge-detached {
					
					display: inline-block;
					font-family: 'helvetica neue', helvetica, arial, sans-serif;
					font-size: rem($fs-nano);
					min-height: 13px;
					min-width: $nav-badge-height + 4;
					background-color: #fff;
					border: 1px solid #33383e;
					border-radius: 3px;
					color: #33383E;
					padding: 1px 5px;
					right: 15px;
					left: auto;
					top: 13px;
					width: auto;
					max-width: 0;
					height: auto;

					-webkit-font-smoothing: subpixel-antialiased;
				}
			}
		}
	}
}*/
@media (min-width: 1399px) {
  .page-sidebar .primary-nav .nav-menu > li > a {
    font-size: 0.875rem; } }

/* app navigation filter */
.nav-filter {
  margin: 0;
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
  height: 0px;
  position: relative;
  -webkit-transform: scale(0.3);
          transform: scale(0.3);
  -webkit-transition: all 400ms cubic-bezier(0.34, 1.25, 0.3, 1);
  transition: all 400ms cubic-bezier(0.34, 1.25, 0.3, 1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }
  .nav-filter input[type="text"] {
    padding: 8px 40px 8px 14px;
    width: 14.625rem;
    background: rgba(0, 0, 0, 0.4);
    color: #fff; }
    .nav-filter input[type="text"]:not(:focus) {
      border-color: rgba(0, 0, 0, 0.1); }
    .nav-filter input[type="text"]:focus {
      border-color: #775c9f; }

.list-filter-active {
  /* these classes are triggered by JS */ }
  .list-filter-active .info-card {
    height: calc(9.53rem - 9.53rem);
    -webkit-transition: all 400ms cubic-bezier(0.34, 1.25, 0.3, 1);
    transition: all 400ms cubic-bezier(0.34, 1.25, 0.3, 1); }
  .list-filter-active .nav-filter {
    opacity: 1;
    visibility: visible;
    height: 60px;
    -webkit-box-shadow: 0px 0px 28px 0px rgba(0, 0, 0, 0.13);
            box-shadow: 0px 0px 28px 0px rgba(0, 0, 0, 0.13);
    -webkit-transform: scale(1);
            transform: scale(1); }
  .list-filter-active .nav-title {
    display: none; }
  .list-filter-active .nav-menu {
    margin: 0; }
    .list-filter-active .nav-menu li > ul {
      padding: 0; }
  .list-filter-active .js-filter-hide {
    display: none; }
  .list-filter-active .js-filter-show {
    display: block; }

/* only show filter message if lister filter is active */
.page-sidebar:not(.list-filter-active) .filter-message {
  display: none; }

@media (min-width: 992px) {
  .nav-function-top .page-sidebar,
  .nav-function-minify .page-sidebar {
    /*.js-filter-hide,
			.js-filter-show {
				display:block;
			}*/ }
    .nav-function-top .page-sidebar .filter-message,
    .nav-function-top .page-sidebar .nav-filter,
    .nav-function-minify .page-sidebar .filter-message,
    .nav-function-minify .page-sidebar .nav-filter {
      display: none; } }

/* app info card inside navigation */
.info-card {
  position: relative;
  width: 16.875rem;
  height: 9.53rem;
  color: #fff;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 2rem;
  -webkit-transition: all 700ms cubic-bezier(0.34, 1.25, 0.3, 1);
  transition: all 700ms cubic-bezier(0.34, 1.25, 0.3, 1); }
  .info-card img.cover {
    opacity: 0.5;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    position: absolute;
    height: auto; }
  .info-card .profile-image {
    width: 3.125rem;
    height: auto;
    display: inline-block;
    z-index: 2;
    position: relative; }
  .info-card .info-card-text {
    margin-left: 1rem;
    color: inherit;
    text-shadow: #000 0 1px;
    z-index: 1;
    position: relative;
    line-height: normal; }
    .info-card .info-card-text > span {
      font-weight: 300; }

@media (min-width: 1399px) {
  .info-card:hover {
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
    will-change: opacity; }
  .info-card:hover img.cover {
    opacity: 0.7;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden; } }

.info-card-text {
  font-size: 0.875rem;
  display: inline-block;
  vertical-align: middle;
  font-weight: 500;
  line-height: 1.35; }
  .info-card-text > span {
    font-size: 0.875rem;
    display: block;
    font-weight: 300; }

/* app navigation made horizontal */
.nav-padel-left,
.nav-padel-right {
  display: none; }

@media (min-width: 992px) {
  .nav-function-top {
    /* digitally created elements */
    /* hide elements when nav-function-top */
    /* correct search field color */
    /* reorder */ }
    .nav-function-top .nav-menu-wrapper {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 100%;
              flex: 0 1 100%; }
    .nav-function-top .hidden-nav-function-top {
      display: none !important; }
    .nav-function-top #search-field {
      color: #fff; }
    .nav-function-top:not(.header-function-fixed) #nff {
      position: relative; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title {
        color: #d58100; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title-desc {
        color: #ec9f28; }
      .nav-function-top:not(.header-function-fixed) #nff:after {
        content: "DISABLED";
        display: block;
        position: absolute;
        background: #ffebc1;
        font-size: 10px;
        width: 65px;
        text-align: center;
        border: 1px solid #ffb20e;
        height: 22px;
        line-height: 20px;
        border-radius: 10px;
        right: 13px;
        top: 26%;
        color: #1d1d1d; }
    .nav-function-top .page-header {
      margin-top: 0;
      height: 4.125rem;
      background-image: -webkit-gradient(linear, right top, left top, from(rgba(51, 148, 225, 0.18)), to(transparent));
      background-image: linear-gradient(270deg, rgba(51, 148, 225, 0.18), transparent);
      background-color: #000080;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(86, 61, 124, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(86, 61, 124, 0.13); }
      .nav-function-top .page-header .dropdown-icon-menu {
        display: none; }
      .nav-function-top .page-header #search-field {
        margin: 0 !important; }
      .nav-function-top .page-header .page-logo {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        width: auto;
        width: initial;
        padding-left: 0;
        background: transparent;
        -webkit-box-shadow: none;
                box-shadow: none; }
      .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child,
      .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child {
        color: #a38cc6; }
        .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child:hover,
        .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child:hover {
          color: #beaed7; }
      .nav-function-top .page-header .badge.badge-icon {
        -webkit-box-shadow: 0 0 0 1px #7a59ad;
                box-shadow: 0 0 0 1px #7a59ad; }
      .nav-function-top .page-header .header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu {
        top: 4.125rem !important; }
    .nav-function-top .page-content-wrapper {
      margin-top: 7.625rem; }
    .nav-function-top .page-wrapper {
      padding-left: 0; }
      .nav-function-top .page-wrapper .page-footer {
        width: 100%; }
    .nav-function-top .page-sidebar {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      max-width: 100%;
      height: 3.5rem;
      z-index: 999;
      padding: 0 0.625rem;
      background: #fff;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(86, 61, 124, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(86, 61, 124, 0.13);
      position: absolute;
      top: 4.125rem;
      -webkit-box-ordinal-group: 3;
          -ms-flex-order: 2;
              order: 2; }
      .nav-function-top .page-sidebar .page-logo,
      .nav-function-top .page-sidebar .nav-filter,
      .nav-function-top .page-sidebar .info-card,
      .nav-function-top .page-sidebar .nav-title {
        display: none; }
      .nav-function-top .page-sidebar .primary-nav {
        -webkit-box-flex: 1;
            -ms-flex: 1;
                flex: 1;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: stretch;
            -ms-flex-align: stretch;
                align-items: stretch;
        font-size: 0;
        /* Make an auto-hiding scroller for the 3 people using a IE */
        -ms-overflow-style: -ms-autohiding-scrollbar;
        /* Remove the default scrollbar for WebKit implementations */ }
        .nav-function-top .page-sidebar .primary-nav::-webkit-scrollbar {
          display: none; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu {
          margin: 0;
          margin-left: 2.90rem;
          /* this will get overriden with JS script, but we add it here as a counter weight for the flickering effect */
          padding: 0;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-orient: horizontal;
          -webkit-box-direction: normal;
              -ms-flex-direction: row;
                  flex-direction: row;
          -webkit-box-align: stretch;
              -ms-flex-align: stretch;
                  align-items: stretch;
          -webkit-box-flex: 0;
              -ms-flex: 0 1 100%;
                  flex: 0 1 100%;
          -webkit-transition: margin 0.5s ease-out 0s;
          transition: margin 0.5s ease-out 0s; }
          .nav-function-top .page-sidebar .primary-nav .nav-menu > li {
            display: inline-block;
            position: static; }
            .nav-function-top .page-sidebar .primary-nav .nav-menu > li.nav-title {
              display: none; }
            .nav-function-top .page-sidebar .primary-nav .nav-menu > li.active > a {
              -webkit-box-shadow: none;
                      box-shadow: none; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li.active > a:before {
                content: '\f413';
                font-family: 'nextgen-icons';
                position: absolute;
                top: calc(50% + 15px);
                right: calc(50% - 5px);
                font-size: 7px;
                height: 10px;
                width: auto;
                color: #24b3a4; }
            .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a {
              padding: .75rem 1.5rem .75rem 1rem;
              text-align: center;
              height: 100%; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .ni,
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'] {
                width: inherit;
                margin: 0;
                margin-right: .5rem !important;
                display: -webkit-box !important;
                display: -ms-flexbox !important;
                display: flex !important;
                -webkit-box-align: center;
                    -ms-flex-align: center;
                        align-items: center;
                -webkit-box-pack: left;
                    -ms-flex-pack: left;
                        justify-content: left;
                color: inherit; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .nav-link-text {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                line-height: normal;
                vertical-align: text-top;
                font-weight: 400;
                display: inline-block;
                /*override inline-flex*/
                -webkit-box-flex: 0;
                    -ms-flex: 0 1 auto;
                        flex: 0 1 auto; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .badge,
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .badge.clear-badge {
                left: 53%; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .collapse-sign {
                margin-left: 0.5rem;
                color: #a492c0;
                position: absolute;
                right: 0.5rem;
                top: 0;
                bottom: 0; }
                .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .collapse-sign > em:before {
                  content: "\f107"; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .badge:first-child {
                max-width: none;
                width: 25px !important;
                height: 25px !important;
                line-height: 16px !important;
                font-size: 0.8125rem !important;
                display: block !important;
                margin: 0 auto 4px !important; }
            .nav-function-top .page-sidebar .primary-nav .nav-menu > li a {
              font-size: .9rem;
              color: #000080; }
            .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul {
              display: none !important;
              width: 13rem;
              height: auto !important;
              top: 3.5rem;
              position: absolute;
              background: #68518c;
              border-radius: 10px;
              -webkit-box-shadow: 0px 0px 40px 0px rgba(82, 63, 105, 0.15);
                      box-shadow: 0px 0px 40px 0px rgba(82, 63, 105, 0.15);
              padding: 1rem 0;
              margin-top: 1rem; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li {
                width: 100%;
                position: relative; }
                .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a {
                  padding: 0.65rem 1.25rem;
                  width: 100%;
                  color: #bdafd1;
                  max-height: none;
                  -webkit-box-shadow: none;
                          box-shadow: none; }
                  .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a .nav-link-text {
                    display: block; }
                    .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a .nav-link-text > .dl-ref {
                      margin-left: 0;
                      margin-right: 5px;
                      display: none; }
                    .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a .nav-link-text > .ni > [class*='fa-'] {
                      margin-left: 6px; }
                .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li ul {
                  background: #68518c;
                  padding: 0; }
                  .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li ul li a {
                    padding-left: 2rem; }
                .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li:hover > a {
                  background: rgba(0, 0, 0, 0.1);
                  color: #fff; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:after {
                content: "";
                display: block;
                width: calc(100% + 100px);
                height: calc(100% + 120px);
                position: absolute;
                z-index: -1;
                left: -50px;
                top: -1rem;
                background: transparent; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {
                content: "\f1c8";
                font-family: 'nextgen-icons';
                position: absolute;
                font-size: 5rem;
                color: #68518c;
                overflow: hidden;
                display: block;
                top: -1.7rem;
                left: 0; }
            .nav-function-top .page-sidebar .primary-nav .nav-menu > li:hover > a {
              color: #886ab5;
              background: transparent; }
              .nav-function-top .page-sidebar .primary-nav .nav-menu > li:hover > a + ul {
                display: block !important;
                animation: animateFadeInUp 0.5s;
                -webkit-animation: animateFadeInUp 0.5s; }
      .nav-function-top .page-sidebar .nav-footer {
        display: none; }
    .nav-function-top.nav-function-minify .page-sidebar .primary-nav .nav-menu > li > a > .nav-link-text {
      display: none; }
    .nav-function-top.nav-function-minify .page-sidebar .primary-nav .nav-menu > li > a > .badge {
      left: 24px; }
    .nav-function-top.nav-function-minify .page-sidebar .primary-nav .nav-menu > li > a > .ni,
    .nav-function-top.nav-function-minify .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'] {
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center; }
    .nav-function-top .page-header {
      -webkit-box-ordinal-group: 2;
          -ms-flex-order: 1;
              order: 1; }
    .nav-function-top .page-wrapper {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column; }
    .nav-function-top .page-sidebar {
      -webkit-box-ordinal-group: 3;
          -ms-flex-order: 2;
              order: 2; }
    .nav-function-top .page-content {
      -webkit-box-ordinal-group: 4;
          -ms-flex-order: 3;
              order: 3;
      -webkit-box-align: stretch;
          -ms-flex-align: stretch;
              align-items: stretch;
      -webkit-box-flex: 1;
          -ms-flex: 1 1 auto;
              flex: 1 1 auto; } }

@media (min-width: 1399px) {
  .nav-function-top .page-sidebar .primary-nav .nav-menu {
    /*li {
						ul {
							 li {
							 	a {
							 		font-size: rem($fs-base);
							 	}
							 }
						}
					}*/ }
    .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .badge:first-child {
      max-width: none;
      width: 27px !important;
      height: 27px !important;
      line-height: 18px !important;
      margin: 0 auto 2px !important; }
    .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .ni,
    .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'],
    .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > img {
      font-size: 22px;
      height: 22px; } }

/* app navgation stays hidden */
@media (min-width: 992px) {
  .nav-function-hidden:not(.nav-function-top) .page-wrapper {
    padding-left: 0.625rem; }
  .nav-function-hidden:not(.nav-function-top) .page-sidebar {
    left: -16.25rem;
    z-index: 1001;
    -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
    transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
    position: absolute;
    top: 0;
    bottom: 0;
    will-change: left, right;
    /* apply invisible hit area to reveal nav */ }
    .nav-function-hidden:not(.nav-function-top) .page-sidebar:after {
      content: "";
      background: transparent;
      height: 100%;
      display: block;
      position: fixed;
      z-index: 1;
      top: 0;
      bottom: 0;
      left: 16.875rem;
      width: 2.1rem; }
    .nav-function-hidden:not(.nav-function-top) .page-sidebar:hover {
      left: 0;
      -webkit-transition: 450ms cubic-bezier(0.9, 0.01, 0.09, 1);
      transition: 450ms cubic-bezier(0.9, 0.01, 0.09, 1); }
      .nav-function-hidden:not(.nav-function-top) .page-sidebar:hover:after {
        content: "";
        z-index: -1; }
  .nav-function-hidden:not(.nav-function-top) .page-header {
    margin-left: 0;
    /* active button state for "nav-function-hidden" */ }
    .nav-function-hidden:not(.nav-function-top) .page-header [data-class="nav-function-hidden"] {
      background: #505050;
      border-color: #363636 !important;
      -webkit-box-shadow: inset 0 0 3px 1px rgba(0, 0, 0, 0.37);
              box-shadow: inset 0 0 3px 1px rgba(0, 0, 0, 0.37);
      color: #fff !important; }
  .nav-function-hidden:not(.nav-function-top).nav-function-fixed .page-sidebar {
    /* apply invisible hit area to reveal nav */ }
    .nav-function-hidden:not(.nav-function-top).nav-function-fixed .page-sidebar:after {
      left: 0.625rem; }
  .nav-function-hidden.header-function-fixed:not(.nav-function-top) .page-header {
    margin-left: 0.625rem; } }

/* app navigation stays fixed */
.nav-function-fixed:not(.nav-function-top) .page-sidebar {
  position: fixed !important;
  top: 0;
  bottom: 0; }
  .nav-function-fixed:not(.nav-function-top) .page-sidebar .primary-nav {
    overflow: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    height: calc(100% - 6.9375rem); }
  .nav-function-fixed:not(.nav-function-top) .page-sidebar .slimScrollDiv {
    height: calc(100% - 6.9375rem) !important; }
    .nav-function-fixed:not(.nav-function-top) .page-sidebar .slimScrollDiv .primary-nav {
      padding-bottom: 0; }

@media (min-width: 992px) {
  .nav-function-fixed {
    /*
		 * top navigation fixed for larger screens with nav on LEFT
		 */
    /*
		 * top navigation fixed for larger screens with nav on TOP
		 */
    /*
		 * center for left nav fixed with boxed layout 
		 */ }
    .nav-function-fixed:not(.nav-function-top).mod-main-boxed .page-sidebar {
      position: fixed !important; }
    .nav-function-fixed:not(.nav-function-top):not(.nav-function-hidden):not(.nav-function-minify) .page-content-wrapper {
      padding-left: 16.875rem; }
    .nav-function-fixed.nav-function-top.header-function-fixed .page-sidebar {
      position: fixed !important;
      -webkit-box-shadow: 0px 0px 28px 2px rgba(86, 61, 124, 0.13);
              box-shadow: 0px 0px 28px 2px rgba(86, 61, 124, 0.13); }
    .nav-function-fixed.nav-function-top.mod-main-boxed .page-sidebar {
      right: 0;
      left: 0;
      margin-right: auto;
      margin-left: auto;
      max-width: 1397px; } }

@media (min-width: 1399px) {
  .nav-function-fixed {
    /*
		 * top navigation fixed for extra large screens with nav on LEFT
		 */ }
    .nav-function-fixed:not(.nav-function-top).mod-main-boxed .page-sidebar {
      position: absolute !important; } }

/* app navigation stays minified */
@media (min-width: 992px) {
  .nav-function-minify:not(.nav-function-top) {
    /* hide elements when nav-function-minify */ }
    .nav-function-minify:not(.nav-function-top) .hidden-nav-function-minify {
      display: none !important; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar {
      width: 4.6875rem;
      z-index: 1001;
      will-change: width;
      -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
      transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1); }
      .nav-function-minify:not(.nav-function-top) .page-sidebar .page-logo {
        width: 4.6875rem;
        padding: 0;
        -webkit-box-pack: center;
            -ms-flex-pack: center;
                justify-content: center; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .page-logo .page-logo-link {
          -webkit-box-flex: 0;
              -ms-flex: none;
                  flex: none; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .page-logo .page-logo-text {
          display: none; }
          .nav-function-minify:not(.nav-function-top) .page-sidebar .page-logo .page-logo-text + * {
            display: none !important; }
      .nav-function-minify:not(.nav-function-top) .page-sidebar .info-card {
        height: 5.9375rem;
        width: 100%;
        padding: 1.21324rem 0;
        text-align: center;
        overflow: hidden;
        -webkit-box-pack: center;
            -ms-flex-pack: center;
                justify-content: center; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .info-card .profile-image + div {
          position: absolute;
          top: 0;
          width: 14.0625rem;
          text-align: left;
          display: none; }
      .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav {
        overflow: hidden; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-title {
          display: none; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu {
          margin: 0; }
          .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu li.active.open > a:before {
            content: '\f413';
            font-family: 'nextgen-icons';
            position: absolute;
            top: calc(50% - 5px);
            right: 11px;
            font-size: 7px;
            height: 10px;
            width: auto;
            color: #24b3a4;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-line-pack: center;
                align-content: center;
            -webkit-box-align: center;
                -ms-flex-align: center;
                    align-items: center; }
          .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu li ul {
            /*.dl-ref {
								display:none !important;
							}*/ }
          .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a {
            text-align: center;
            -webkit-box-pack: center;
                -ms-flex-pack: center;
                    justify-content: center;
            -webkit-box-align: center;
                -ms-flex-align: center;
                    align-items: center;
            padding-left: 0;
            padding-right: 0; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'],
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > .ni {
              font-size: 1.2375rem;
              margin: 0; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > .badge {
              left: 2.34375rem; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a:not(.livicon) > .badge:not(.clear-badge):first-child {
              margin-right: 0; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > .nav-link-text {
              display: none;
              position: absolute;
              text-align: left;
              background: trasparent;
              padding-left: 1.375rem;
              color: #fff;
              top: 0;
              left: 5.6875rem;
              height: 100%;
              width: 13.75rem;
              font-weight: 500;
              margin-top: -1.563rem; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > b.collapse-sign {
              display: none; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul {
              position: absolute;
              width: 13.75rem;
              left: 5.6875rem;
              background-color: #000080;
              margin-top: -5rem;
              padding-top: 3.75rem;
              border-radius: 4px .5rem .5rem .5rem;
              padding-bottom: 1rem; }
              .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul:before {
                content: "\f1c8";
                font-family: 'nextgen-icons';
                position: absolute;
                font-size: 3.5rem;
                left: -0.4125rem;
                color: #000080;
                z-index: -1;
                -webkit-transform: rotate(270deg);
                        transform: rotate(270deg);
                overflow: hidden;
                display: block;
                top: 1rem; }
              .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul > li > a {
                padding-left: 1.375rem;
                padding-top: 0.6rem;
                padding-bottom: 0.6rem; }
                .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul > li > a > b.collapse-sign > [class*='fa-'],
                .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul > li > a > b.collapse-sign > .ni {
                  display: inline-block !important; }
                .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul > li > a + ul > li > a {
                  padding-left: 1.71875rem;
                  padding-top: 0.6rem;
                  padding-bottom: 0.6rem; }
              .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul > li .nav-link-text > [class*='fa-'],
              .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul > li .nav-link-text > .ni {
                display: none; }
          .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > ul {
            display: none !important; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover {
          overflow: visible; }
          .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a {
            background: #614b82;
            color: #fff;
            overflow: visible;
            z-index: 10; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text {
              display: -webkit-box;
              display: -ms-flexbox;
              display: flex;
              overflow: hidden;
              animation: animateFadeInLeft 0.5s;
              -webkit-animation: animateFadeInLeft 0.5s; }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child {
              top: 26px;
              -webkit-box-align: center;
                  -ms-flex-align: center;
                      align-items: center;
              background: #000080;
              overflow: visible;
              border-radius: 4px 10px 10px 4px; }
              .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child:before {
                content: "\f1c8";
                font-family: 'nextgen-icons';
                position: absolute;
                font-size: 3.5rem;
                left: -7px;
                color: #000080;
                z-index: -1;
                -webkit-transform: rotate(270deg);
                        transform: rotate(270deg);
                overflow: hidden;
                display: block;
                top: -9px; }
          .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > ul {
            display: block !important;
            z-index: 1;
            animation: animateFadeInLeft 0.5s;
            -webkit-animation: animateFadeInLeft 0.5s;
            -webkit-box-shadow: 0px 0px 40px 0px rgba(82, 63, 105, 0.15);
                    box-shadow: 0px 0px 40px 0px rgba(82, 63, 105, 0.15); }
            .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > ul:after {
              content: "";
              display: block;
              position: absolute;
              height: calc(100% + 180px);
              width: calc(100% + 80px);
              top: -4.125rem;
              z-index: -1;
              left: -1rem; }
    .nav-function-minify:not(.nav-function-top) .page-header [data-class="nav-function-minify"] {
      background: #505050;
      border-color: #363636 !important;
      -webkit-box-shadow: inset 0 0 3px 1px rgba(0, 0, 0, 0.37);
              box-shadow: inset 0 0 3px 1px rgba(0, 0, 0, 0.37);
      color: #fff !important; }
    .nav-function-minify:not(.nav-function-top).nav-function-hidden .page-wrapper {
      padding-left: 0.625rem; }
    .nav-function-minify:not(.nav-function-top).nav-function-hidden .page-sidebar {
      left: -4.0625rem;
      overflow: visible; }
      .nav-function-minify:not(.nav-function-top).nav-function-hidden .page-sidebar:hover {
        left: 0; }
    .nav-function-minify:not(.nav-function-top).nav-function-hidden .page-header {
      margin-left: 0; }
    .nav-function-minify:not(.nav-function-top).nav-function-fixed:not(.nav-function-hidden) .page-content-wrapper {
      padding-left: 4.6875rem; }
    .nav-function-minify:not(.nav-function-top).header-function-fixed .page-header {
      margin-left: 4.6875rem; }
    .nav-function-minify:not(.nav-function-top).header-function-fixed.nav-function-hidden .page-header {
      margin-left: 0.625rem; }
    .nav-function-minify:not(.nav-function-top).header-function-fixed.nav-function-fixed:not(.nav-function-hidden) .page-content-wrapper {
      padding-left: 4.6875rem; }
    .nav-function-minify:not(.nav-function-top).header-function-fixed.nav-function-fixed:not(.nav-function-hidden).mod-main-boxed .page-content-wrapper {
      padding-left: 0; } }

/* app navigation footer */
.nav-footer {
  /*background-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);
	background-image: linear-gradient(270deg, $nav-background-shade, transparent); 
	background-color: $nav-background;*/
  height: 2.8125rem;
  bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; }
  .nav-footer .nav-footer-buttons > li > a {
    display: block;
    color: #9782b7;
    height: 1.8125rem;
    line-height: 1.8125rem;
    margin-top: 1px;
    padding: 0 13px;
    overflow: visible;
    font-size: 1rem; }
  .nav-footer [data-class="nav-function-minify"] {
    display: none; }

.nav-function-fixed .nav-footer {
  background: #000080;
  border: 0; }
  .nav-function-fixed .nav-footer:before {
    content: ' ';
    height: 1px;
    position: inherit;
    width: inherit;
    background: rgba(112, 87, 149, 0.2);
    background: -webkit-gradient(linear, left top, right top, from(#000080), color-stop(50%, #7c62a4), color-stop(50%, #7c62a4), to(#000080));
    background: linear-gradient(to right, #000080 0%, #7c62a4 50%, #7c62a4 50%, #000080 100%);
    opacity: 0.5; }
  .nav-function-fixed .nav-footer:after {
    opacity: 0.1; }

@media (min-width: 992px) {
  .nav-function-minify .nav-footer {
    background-color: #53406f; }
    .nav-function-minify .nav-footer [data-class="nav-function-minify"] {
      display: block;
      width: 100%;
      height: 2.8125rem;
      line-height: 2.8125rem;
      font-size: 1.1875rem;
      vertical-align: middle;
      color: #876fab;
      text-align: center;
      text-decoration: none;
      position: relative;
      -webkit-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }
      .nav-function-minify .nav-footer [data-class="nav-function-minify"] > :first-child {
        margin-right: -4px; }
      .nav-function-minify .nav-footer [data-class="nav-function-minify"] > :only-child {
        margin: 0; }
    .nav-function-minify .nav-footer:hover {
      background-color: #5f497f; }
      .nav-function-minify .nav-footer:hover [data-class="nav-function-minify"] {
        color: #a8a6ac;
        margin-left: 7px; }
    .nav-function-minify .nav-footer .nav-footer-buttons {
      display: none; } }

/* app wrapper */
.page-wrapper {
  position: relative; }

.page-inner {
  min-height: 100vh; }

.page-wrapper, .page-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-flex: 1;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  width: 100%; }

.page-content-wrapper {
  background-color: #faf8fb;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-flex: 1;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  padding: 0;
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 0;
  min-width: 0;
  max-width: 100%;
  min-height: 1px; }

/* app content heading */
.subheader {
  margin-bottom: calc(1.5rem + 0.625rem);
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.subheader-icon {
  color: #a8a6ac;
  margin-right: 0.25rem; }

.subheader-title {
  font-size: 1.375rem;
  font-weight: 500;
  color: #505050;
  text-shadow: #fff 0 1px;
  margin: 0;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1; }
  .subheader-title sup.badge {
    text-shadow: none;
    position: absolute;
    margin-top: 0.4rem;
    margin-left: 0.25rem;
    font-size: 40%;
    padding: 2px 5px;
    line-height: normal; }
  .subheader-title small {
    font-weight: 400;
    color: #838383;
    margin-bottom: 0;
    font-size: 0.875rem; }

/* app content */
.page-content {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  -webkit-box-ordinal-group: 4;
      -ms-flex-order: 3;
          order: 3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: relative; }

/*@include media-breakpoint-up($mobile-breakpoint) {

	.nav-function-top {
		.page-content {
			min-height: calc(100vh - 10.4375rem)
		}
	}

}

*/
/* app footer */
.page-footer {
  height: 2.8125rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: #fff;
  color: #4d4d4d;
  font-size: 0.8125rem;
  padding: 0 2rem;
  -webkit-box-ordinal-group: 5;
      -ms-flex-order: 4;
          order: 4; }

/* app error page */
.alt {
  padding: 0 !important;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  min-height: 100vh; }
  .alt .page-footer {
    width: 100% !important; }

.page-error {
  font-size: 600% !important;
  font-weight: bold !important; }
  .page-error small {
    font-size: 40%;
    font-weight: 500; }

.h-alt-f {
  height: calc(100vh - 11.4375rem);
  width: 100%; }

.h-alt-hf {
  height: calc(100vh - 15.5625rem);
  width: 100%; }

.nav-function-top .h-alt-hf {
  height: calc(100vh - 19.0625rem); }

/* various app components (see docs for the full list) */
.accordion .card .card-header {
  cursor: pointer;
  margin: 0;
  padding: 0;
  border-bottom: 0;
  background-color: #f7f9fa; }
  .accordion .card .card-header .card-title {
    padding: 1rem 1rem;
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    color: #886ab5; }
    .accordion .card .card-header .card-title.collapsed {
      color: #838383; }

.accordion.accordion-clean:not(.accordion-outline):not(.accordion-hover) .card-title,
.accordion.accordion-clean:not(.accordion-outline):not(.accordion-hover) .card-body {
  padding-left: 0 !important;
  padding-right: 0 !important; }

.accordion.accordion-clean:not(.accordion-outline):not(.accordion-hover) .card-body {
  padding-top: 0; }

.accordion.accordion-clean .card-header {
  background: #fff; }

.accordion.accordion-clean .card {
  border-left: 0;
  border-right: 0; }
  .accordion.accordion-clean .card:first-child {
    border-top: 0; }
  .accordion.accordion-clean .card:last-child {
    border-bottom: 0; }

.accordion.accordion-clean.accordion-outline .card-title,
.accordion.accordion-clean.accordion-outline .card-body, .accordion.accordion-clean.accordion-hover .card-title,
.accordion.accordion-clean.accordion-hover .card-body {
  padding-left: 1rem !important;
  padding-right: 1rem !important; }

.accordion.accordion-outline .card {
  margin-bottom: 1rem;
  border: 2px solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 4px !important; }

.accordion.accordion-hover .card-title {
  -webkit-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease; }

.accordion.accordion-hover .card-header {
  background: #fff; }
  .accordion.accordion-hover .card-header:hover .card-title.collapsed {
    color: #fff;
    background-color: #a38cc6; }

.accordion.accordion-hover .card-title:not(.collapsed) {
  color: #fff;
  background-color: #886ab5; }

/* 	DEV NOTE: The reason why we had to add this layer for alert colors is because BS4 
	does not allow you to add your own alert colors via variable control rather 
	through a systemetic agent that changes the theme colors. 

	REF: https://github.com/twbs/bootstrap/issues/24341#issuecomment-337457218
*/
.alert-primary {
  color: dimgray;
  background-color: #f3f1f5;
  border-color: #d6d3da; }

.alert-success {
  color: #45a197;
  background-color: #f7fdfc;
  border-color: #a3ebe4; }

.alert-danger {
  color: #e7026e;
  background-color: #ffe5f1;
  border-color: #fe9ecb; }

.alert-warning {
  color: #c18300;
  background-color: #fff8e9;
  border-color: #ffcd65; }

.alert-info {
  color: #0a6ebd;
  background-color: #e3f2fd;
  border-color: #82c4f8; }

.alert-secondary {
  color: #505050;
  background-color: #fbfbfb;
  border-color: gainsboro; }

.alert-icon {
  width: 2.5rem; }
  .alert-icon > i {
    font-size: 1.75rem; }
  .alert-icon + div {
    padding-left: 0.25rem; }

.badge.badge-icon {
  position: absolute;
  display: inline-block;
  background-color: #fd3995;
  color: #fff;
  -webkit-box-shadow: 0 0 0 1px #fff;
          box-shadow: 0 0 0 1px #fff;
  cursor: default;
  border: 1px solid transparent;
  font-size: 0.625rem;
  min-width: 1rem;
  max-width: 1.6875rem;
  padding: 0 3px;
  border-radius: 1.25rem;
  font-weight: 500;
  line-height: normal;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  /* when self is relative */
  vertical-align: middle; }

/* parent position needs to be relative, and turn off waves function */
.btn-icon .badge {
  top: auto; }
  .btn-icon .badge.pos-top {
    margin-top: -4px; }
  .btn-icon .badge.pos-bottom {
    margin-bottom: -4px; }
  .btn-icon .badge.pos-left {
    margin-left: -4px; }
  .btn-icon .badge.pos-right {
    margin-right: -4px; }

.page-breadcrumb {
  padding: 0;
  background: transparent;
  margin: 0 0 1.5rem;
  position: relative;
  text-shadow: #fff 0 1px; }

.breadcrumb > li > a {
  text-decoration: none !important; }

.breadcrumb > li.breadcrumb-item {
  max-width: 130px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden; }
  .breadcrumb > li.breadcrumb-item:hover {
    max-width: 200px !important;
    cursor: default; }

[data-breadcrumb-seperator] + [data-breadcrumb-seperator]:before {
  content: attr(data-breadcrumb-seperator); }

.breadcrumb-lg > li {
  font-size: 1rem; }

.breadcrumb-sm > li {
  font-size: 0.6875rem; }

[class*='breadcrumb-seperator-'] .breadcrumb-item + .breadcrumb-item:before {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  font-weight: 400;
  line-height: 1;
  font-family: Font Awesome\ 5 Pro; }

.breadcrumb-seperator-1 .breadcrumb-item + .breadcrumb-item:before {
  content: "\f105"; }

.breadcrumb-seperator-2 .breadcrumb-item + .breadcrumb-item:before {
  content: "\f178"; }

.breadcrumb-seperator-3 .breadcrumb-item + .breadcrumb-item:before {
  content: "\f054"; }

.breadcrumb-arrow {
  padding: 0;
  background: transparent; }
  .breadcrumb-arrow li.active {
    font-weight: 500;
    opacity: 0.5; }
  .breadcrumb-arrow li a {
    color: white;
    display: inline-block;
    background: #838383;
    text-decoration: none;
    position: relative;
    height: 2.5em;
    line-height: 2.5em;
    padding: 0 10px 0 5px;
    text-align: center;
    margin-right: 22px; }
  .breadcrumb-arrow li:nth-child(even) a {
    background-color: #838383; }
    .breadcrumb-arrow li:nth-child(even) a:before {
      border-color: #838383;
      border-left-color: transparent; }
    .breadcrumb-arrow li:nth-child(even) a:after {
      border-left-color: #838383; }
  .breadcrumb-arrow li:first-child a {
    padding-left: 0.938em;
    border-radius: 4px 0 0 4px; }
    .breadcrumb-arrow li:first-child a:before {
      border: none; }
  .breadcrumb-arrow li:last-child a {
    padding-right: 0.938em;
    border-radius: 0 4px 4px 0; }
    .breadcrumb-arrow li:last-child a:after {
      border: none; }
  .breadcrumb-arrow li a:before, .breadcrumb-arrow li a:after {
    content: "";
    position: absolute;
    top: 0;
    border: 0 solid #838383;
    border-width: 1.250em 10px;
    width: 0;
    height: 0; }
  .breadcrumb-arrow li a:before {
    left: -20px;
    border-left-color: transparent; }
  .breadcrumb-arrow li a:after {
    left: 100%;
    border-color: transparent;
    border-left-color: #838383; }
  .breadcrumb-arrow li a:hover {
    background-color: #886ab5; }
    .breadcrumb-arrow li a:hover:before {
      border-color: #886ab5;
      border-left-color: transparent; }
    .breadcrumb-arrow li a:hover:after {
      border-left-color: #886ab5; }
  .breadcrumb-arrow li a:active {
    background-color: #838383; }
    .breadcrumb-arrow li a:active:before {
      border-color: #838383;
      border-left-color: transparent; }
    .breadcrumb-arrow li a:active:after {
      border-left-color: #838383; }

/* btn switch */
.btn-switch {
  background: dimgray;
  padding: 2px 8px 1px 22px;
  font-size: 10px;
  line-height: 15px;
  border-radius: 20px;
  text-transform: uppercase;
  color: white;
  font-weight: 500;
  min-width: 55px;
  height: 20px;
  margin-top: 5%;
  position: relative;
  overflow: hidden; }
  .btn-switch:hover {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
    color: white; }
  .btn-switch:before {
    content: "OFF";
    position: absolute;
    right: 7px; }
  .btn-switch:after {
    content: " ";
    text-align: center;
    color: white;
    width: 16px;
    height: 16px;
    position: absolute;
    background: #fff;
    padding: 1px;
    left: 0;
    top: 0;
    line-height: normal;
    margin: 1px;
    border-radius: 50%;
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23); }
  .btn-switch.active {
    color: #fff;
    background: #886ab5; }
    .btn-switch.active:before {
      content: "ON";
      left: 7px;
      right: auto;
      color: white; }
    .btn-switch.active:after {
      content: " ";
      right: 0;
      left: auto;
      background: #fff;
      color: #886ab5; }

/* button used to close filter and mobile search */
.btn-search-close {
  position: absolute !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0px 5px;
  border-radius: 3px;
  color: #fff;
  right: 8px;
  top: 0;
  left: 0;
  bottom: 0;
  margin: auto 0 auto auto;
  width: 20px;
  height: 20px; }

/* buttons used in the header section of the page */
.header-btn[data-class='mobile-nav-on'] {
  border-color: #fc077a;
  background-color: #fd2087;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fd2087), to(#e7026e));
  background-image: linear-gradient(to top, #fd2087, #e7026e);
  color: #fff;
  width: 3.875rem; }

/* btn widths */
.btn-w-m {
  min-width: 85px; }

.btn-w-l {
  min-width: 130px; }

.btn-m-s {
  margin: 3px 1px; }

.btn-m-l {
  margin: 3px 2.5px; }

/* dropdown btn */
/* used on info card pulldown filter */
.pull-trigger-btn {
  position: absolute !important;
  top: -5px;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  background: rgba(0, 0, 0, 0.4);
  padding: 0px 9px;
  border: 1px solid rgba(0, 0, 0, 0.4);
  border-radius: 0 0 20px 20px;
  text-decoration: none;
  font-size: 17px;
  height: 21px;
  width: 31px;
  color: #fff !important;
  line-height: 20px;
  text-align: center;
  -webkit-transition: all 200ms cubic-bezier(0.34, 1.25, 0.6, 1);
  transition: all 200ms cubic-bezier(0.34, 1.25, 0.6, 1);
  -webkit-box-shadow: 0px 0px 2px rgba(136, 106, 181, 0.3);
          box-shadow: 0px 0px 2px rgba(136, 106, 181, 0.3);
  opacity: 1; }
  .pull-trigger-btn:hover {
    font-size: 23px;
    height: 25px;
    width: 35px;
    line-height: 23px;
    background: #886ab5;
    border-color: #7a59ad;
    -webkit-box-shadow: 0px 0px 10px #5790b3;
            box-shadow: 0px 0px 10px #5790b3; }

/* buttons dropshadow */
/*[class*='btn-']:not(.btn-switch):not(.btn-group):not([class*='btn-w-']),
[class*='btn-']:not(.btn-switch):not(.btn-group):not([class*='btn-w-']):focus {*/
.btn-shadow,
.btn-shadow:focus {
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05); }
  .btn-shadow:active,
  .btn-shadow .active,
  .btn-shadow:focus:active,
  .btn-shadow:focus .active {
    -webkit-box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0.3) !important;
            box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0.3) !important; }
  .btn-shadow:not(.disabled):not([disabled]):hover,
  .btn-shadow:focus:not(.disabled):not([disabled]):hover {
    -webkit-box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);
            box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3); }

.btn:active {
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important; }

/*.btn-group {
	[class*='btn-'] {
		box-shadow: 0 0px 0px 0 rgba(0,0,0,.3) !important;
	}
}*/
.btn-light {
  border-color: rgba(0, 0, 0, 0.15); }
  .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active, .btn-light:hover {
    border-color: rgba(0, 0, 0, 0.25); }

.show > .btn-light.dropdown-toggle {
  border-color: rgba(0, 0, 0, 0.25); }

/* btn misc */
.btn-outline-default {
  background-color: transparent;
  color: #212529;
  border-color: #E5E5E5; }
  .btn-outline-default:hover, .btn-outline-default:not(:disabled):not(.disabled):active, .btn-outline-default:not(:disabled):not(.disabled).active,
  .show > .btn-outline-default.dropdown-toggle {
    color: #212529;
    background-color: #f9f9f9;
    border-color: #E5E5E5; }
  .btn-outline-default.disabled, .btn-outline-default:disabled {
    color: #212529;
    background-color: transparent; }

.btn-pills {
  border-radius: 15px; }

/* new btn size */
.btn-xs,
.btn-group-xs > .btn {
  padding: 1px 0.844rem;
  font-size: .7rem;
  line-height: 1.5;
  border-radius: .25rem; }

/* btn shadows */
.btn-primary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5);
          box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5); }

.btn-secondary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(134, 142, 150, 0.5);
          box-shadow: 0 2px 6px 0 rgba(134, 142, 150, 0.5); }

.btn-success {
  -webkit-box-shadow: 0 2px 6px 0 rgba(29, 201, 183, 0.5);
          box-shadow: 0 2px 6px 0 rgba(29, 201, 183, 0.5); }

.btn-info {
  -webkit-box-shadow: 0 2px 6px 0 rgba(33, 150, 243, 0.5);
          box-shadow: 0 2px 6px 0 rgba(33, 150, 243, 0.5); }

.btn-warning {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 194, 65, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 194, 65, 0.5); }

.btn-danger {
  -webkit-box-shadow: 0 2px 6px 0 rgba(253, 57, 149, 0.5);
          box-shadow: 0 2px 6px 0 rgba(253, 57, 149, 0.5); }

.btn-light {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5); }

.btn-dark {
  -webkit-box-shadow: 0 2px 6px 0 rgba(80, 80, 80, 0.5);
          box-shadow: 0 2px 6px 0 rgba(80, 80, 80, 0.5); }

/* btn icon */
.btn-icon {
  width: calc(2.1rem + 2px);
  padding: 0;
  line-height: 2.1rem; }
  .btn-icon:not([class*="-primary"]):not([class*="-secondary"]):not([class*="-default"]):not([class*="-success"]):not([class*="-info"]):not([class*="-warning"]):not([class*="-danger"]):not([class*="-dark"]):not([class*="-light"]):not(.nav-item):hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-color: transparent; }
  .btn-icon:not([class*="-primary"]):not([class*="-secondary"]):not([class*="-default"]):not([class*="-success"]):not([class*="-info"]):not([class*="-warning"]):not([class*="-danger"]):not([class*="-dark"]):not([class*="-light"]):not(.nav-item):not(.active):not(:active):not(:hover):not(:focus) {
    background: transparent;
    color: #434343; }
  .btn-icon:not([class*="-primary"]):not([class*="-secondary"]):not([class*="-default"]):not([class*="-success"]):not([class*="-info"]):not([class*="-warning"]):not([class*="-danger"]):not([class*="-dark"]):not([class*="-light"]):not(.nav-item):focus {
    border-color: rgba(80, 80, 80, 0.1) !important; }
  .btn-icon.btn-xs {
    width: calc(1.15rem + 2px);
    line-height: 1.15rem; }
  .btn-icon.btn-sm {
    width: calc(1.5rem + 2px);
    line-height: 1.5rem; }
  .btn-icon.btn-lg {
    width: calc(3rem + 2px);
    line-height: 3rem; }

.btn-icon-light {
  color: rgba(255, 255, 255, 0.7) !important;
  border-color: transparent !important; }
  .btn-icon-light:not(.active):not(:active):not(:hover):not(:focus) {
    color: rgba(255, 255, 255, 0.7) !important; }
  .btn-icon-light:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.2) !important; }

.card-header {
  color: inherit;
  background-color: #f7f9fa; }
  .card-header small {
    margin: 0;
    opacity: 0.8;
    font-weight: 400;
    font-size: 85%; }

/* remove extra margin in card child items */
.card,
.card-group {
  -webkit-box-shadow: 0px 0px 13px 0px rgba(74, 53, 107, 0.08);
          box-shadow: 0px 0px 13px 0px rgba(74, 53, 107, 0.08); }
  .card > :last-child,
  .card-group > :last-child {
    margin-bottom: 0px; }

.accordion > .card {
  -webkit-box-shadow: none;
          box-shadow: none; }

.card-group > .card {
  -webkit-box-shadow: none;
          box-shadow: none; }

/* remove wierd line height issue */
.card-header-pills,
.card-header-tabs {
  font-size: 0; }
  .card-header-pills .nav-link,
  .card-header-tabs .nav-link {
    font-size: 0.8125rem; }

/* card title */
.card-title {
  font-size: 0.9375rem; }

.card-header .card-title {
  display: inline-block;
  padding: 0;
  margin: 0 0.5rem 0 0; }

.carousel-indicators li {
  border-radius: 50%; }

.carousel-control-prev:hover {
  /*background: -moz-linear-gradient(left, rgba(0,0,0,0.25) 0%, rgba(0,0,0,0) 45%);
	background: -webkit-linear-gradient(left, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%); */
  background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

.carousel-control-next:hover {
  /*background: -moz-linear-gradient(right, rgba(0,0,0,0.25) 0%, rgba(0,0,0,0) 45%); 
	background: -webkit-linear-gradient(right, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%);*/
  background: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to left, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

.dropdown-header.bg-trans-gradient {
  padding: 1.25rem 1.5rem; }

/* dropdown-item hover menu*/
.dropdown-menu-animated {
  -webkit-transform: scale(0.8) !important;
  transform: scale(0.8) !important;
  -webkit-transition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);
  transition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);
  opacity: 0;
  visibility: hidden;
  display: block; }

/* various sizes */
.dropdown-menu {
  -webkit-box-shadow: 0 0 15px 1px rgba(90, 80, 105, 0.2);
          box-shadow: 0 0 15px 1px rgba(90, 80, 105, 0.2);
  -webkit-user-select: text; }
  .dropdown-menu .dropdown-item {
    font-weight: 400;
    cursor: pointer; }
  .dropdown-menu.dropdown-sm {
    width: 8rem;
    height: auto; }
  .dropdown-menu.dropdown-md {
    width: 14rem;
    height: auto; }
  .dropdown-menu.dropdown-lg {
    width: 17.5rem;
    height: auto; }
  .dropdown-menu.dropdown-xl {
    width: 21.875rem;
    height: auto; }
  .dropdown-menu .dropdown-item:first-child, .dropdown-menu .dropdown-item:last-child {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0; }

/* replace bootstrap's default arrow */
.dropdown-toggle:after,
.dropleft .dropdown-toggle:before {
  text-align: center;
  display: inline;
  border: 0 !important;
  font-family: 'Font Awesome 5 Pro';
  content: "\f107" !important;
  vertical-align: top !important;
  position: relative; }

.dropup .dropdown-toggle:after {
  content: "\f106" !important; }

.dropright .dropdown-toggle:after {
  content: "\f105" !important; }

.dropleft .dropdown-toggle:before {
  content: "\f104" !important; }

.nav-item .dropdown-toggle:after {
  font-size: 0.90em; }

/* remove arrow */
.dropdown-toggle.no-arrow:before, .dropdown-toggle.no-arrow:after {
  display: none !important; }

/* dropdown menu multi-level */
.dropdown-menu .dropdown-menu {
  margin: 0;
  padding: 0;
  border-radius: 0;
  position: absolute;
  top: -1px;
  left: 100%;
  background: #fff;
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  -webkit-transform: scale(0.8);
  transform: scale(0.8);
  -webkit-transition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);
  transition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);
  -webkit-transition-delay: 500ms;
          transition-delay: 500ms;
  opacity: 0;
  visibility: hidden;
  display: block; }
  .dropdown-menu .dropdown-menu .dropdown-item {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }

.dropdown-menu .dropdown-multilevel {
  position: relative;
  /* it is displayed on right by default */
  /* add arrow */ }
  .dropdown-menu .dropdown-multilevel.dropdown-multilevel-left > .dropdown-menu {
    right: 100%;
    left: auto; }
  .dropdown-menu .dropdown-multilevel > .dropdown-item:first-child:after {
    content: "\f2fb";
    font-family: 'nextgen-icons';
    font-size: inherit;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding-right: 1.5rem; }
  .dropdown-menu .dropdown-multilevel:hover > .dropdown-item:not(.disabled) {
    background: #f8f9fa;
    color: #6e4e9e; }
    .dropdown-menu .dropdown-multilevel:hover > .dropdown-item:not(.disabled) + .dropdown-menu {
      -webkit-transition-delay: 0ms;
              transition-delay: 0ms;
      -webkit-transform: scale(1);
      transform: scale(1);
      -webkit-transform-origin: 29px -50px;
      transform-origin: 29px -50px;
      opacity: 1;
      visibility: visible; }

.icon-stack {
  position: relative;
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1em;
  vertical-align: middle;
  text-align: center; }

.icon-stack-1x,
.icon-stack-2x,
.icon-stack-3x {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  line-height: inherit !important; }

.icon-stack-1x {
  font-size: 0.5em; }

.icon-stack-2x {
  font-size: 0.70em;
  /*padding-right: 0.025em;*/ }

.icon-stack-3x {
  font-size: 1em; }

.icon-stack-xl {
  font-size: 3.125rem; }

.icon-stack-lg {
  font-size: 2.5rem; }

.icon-stack-md {
  font-size: 2.125rem; }

.icon-stack-sm {
  font-size: 1.875rem; }

.filter-message {
  display: block;
  text-align: center;
  padding: 2px;
  font-size: 0.6875rem;
  text-transform: capitalize;
  font-style: italic;
  width: calc(100% - 60px);
  max-width: 180px;
  border-radius: 4px;
  margin: 1rem auto; }
  .filter-message:empty {
    display: none; }

.js-list-filter:not(.primary-nav) {
  /* these classes are triggered by JS */
  /*.js-filter-show {
		display:block !important;
	}*/ }
  .js-list-filter:not(.primary-nav) .js-filter-hide {
    display: none !important; }

.js-list-filter.nav-menu:not(.primary-nav) .js-filter-show {
  display: block !important; }

.loader {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  color: inherit;
  z-index: 10;
  /*
	The use of translate3d pushes CSS animations into hardware acceleration.
	Even if you're looking to do a basic 2d translation, use translate3d for more power!
	If your animation is still flickering after switching to the transform above,
	you can use a few little-known CSS properties to try to fix the problem:
	*/
  /*-webkit-transform: translate3d(0, 0, 0);
	-webkit-backface-visibility: hidden;
	-webkit-perspective: 1000;	*/ }

/*:not(.enable-loader) .loader {
	> * {
		animation: pause;
	}
}*/
.enable-loader:before {
  content: '';
  background-color: rgba(255, 255, 255, 0.7);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0; }

.enable-loader .loader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

#msgr_listfilter_input {
  width: calc(100% - 1rem);
  margin: 0 auto;
  margin-top: -41px;
  padding-left: 41px;
  margin-bottom: 10px;
  opacity: 0;
  background: transparent;
  -webkit-transition-delay: 100ms;
          transition-delay: 100ms; }

.msgr-list {
  width: 14.563rem;
  right: -11.438rem;
  z-index: 101;
  -webkit-transition-delay: 100ms;
          transition-delay: 100ms; }
  .msgr-list + .msgr {
    width: calc(100% - 3.125rem);
    height: 100%; }
    .msgr-list + .msgr:before {
      content: '';
      height: 100%;
      width: 100%;
      background: rgba(255, 255, 255, 0.4);
      position: absolute;
      z-index: 100;
      -webkit-transition-delay: 100ms;
              transition-delay: 100ms;
      opacity: 0;
      visibility: hidden; }
  .msgr-list:hover {
    right: 0;
    border-left-color: rgba(0, 0, 0, 0.1); }
    .msgr-list:hover #msgr_listfilter_input {
      opacity: 1; }
    .msgr-list:hover + .msgr:before {
      opacity: 1;
      visibility: visible; }

/*.msgr-chatinput {

	min-height: 110px;
	max-height: 160px;

	[contenteditable="true"] {
		min-height:50px;
		max-height:110px;
	}

}*/
/*.msgr-chatinput-icons {
	height: 40px;
}*/
/*.msgr-chatinput-container {
	> div {
		border-top: 1px solid rgba($black,0.07);
	}
}*/
/* IE HACK */
/*@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
	[contenteditable="true"] {
		height: 110px;
	}
}*/
.chat-segment-get {
  text-align: left;
  position: relative;
  margin: 0 2rem 0.5rem 0; }
  .chat-segment-get.chat-start .chat-message {
    border-bottom-left-radius: 3px; }
  .chat-segment-get.chat-start + :not(.chat-end) .chat-message {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px; }
  .chat-segment-get.chat-end .chat-message {
    border-top-left-radius: 3px; }
  .chat-segment-get .chat-message {
    background: #f1f0f0;
    color: rgba(0, 0, 0, 0.8);
    text-align: left; }

.chat-segment-sent {
  text-align: right;
  position: relative;
  margin: 0 0 .5rem 3rem; }
  .chat-segment-sent.chat-start .chat-message {
    border-bottom-right-radius: 3px; }
  .chat-segment-sent.chat-start + :not(.chat-end) .chat-message {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px; }
  .chat-segment-sent.chat-end .chat-message {
    border-top-right-radius: 3px; }
  .chat-segment-sent .chat-message {
    background: #1dc9b7;
    color: white;
    text-align: left; }

.chat-message {
  padding: 0.75rem 1rem;
  border-radius: 0.625rem;
  position: relative;
  display: inline-block; }
  .chat-message > p {
    padding: 0.75rem 0 0;
    margin: 0; }
  .chat-message > p:first-child {
    padding-top: 0; }

.chat-start {
  margin-bottom: 3px !important; }
  .chat-start .time-stamp {
    display: none; }
  .chat-start + .chat-segment:not(.chat-end) {
    margin-bottom: 3px !important; }
    .chat-start + .chat-segment:not(.chat-end) .time-stamp {
      display: none; }

/* modal shadow */
.modal-content {
  -webkit-box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2); }

/* adjustments */
.modal.show .modal-dialog {
  -webkit-transform: none;
          transform: none; }

/* alert modal */
.modal-alert {
  padding: 0 !important;
  /* overriding bootstrap generated style */
  /*.modal-body {
		padding: 0.5rem 0;
	}
*/ }
  .modal-alert .modal-dialog {
    max-width: 100% !important;
    /* overriding bootstrap css for all media queries */
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    min-height: calc(100% - 3.5rem);
    -webkit-transform: none !important;
            transform: none !important;
    /* overriding bootstrap css */ }
  .modal-alert .modal-content {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 0;
    padding: 1.5rem 1rem 1rem; }
  .modal-alert .modal-title {
    font-size: 1.5rem;
    font-weight: 300;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; }
  .modal-alert .modal-header,
  .modal-alert .modal-body,
  .modal-alert .modal-footer {
    /*left: 20%;
		width: 60%;*/
    width: 100%;
    max-width: 992px;
    margin: 0 auto;
    padding: 0;
    color: #fff;
    position: relative; }
  .modal-alert .close {
    color: #fff;
    text-shadow: 0 1px 0 #000; }

/* transparent modal */
.modal-transparent .modal-content {
  -webkit-box-shadow: 0 1px 15px 1px rgba(86, 61, 124, 0.3);
          box-shadow: 0 1px 15px 1px rgba(86, 61, 124, 0.3); }

.modal-transparent .modal-content {
  background: rgba(40, 36, 47, 0.85); }

/* transparent backdrop */
.modal-backdrop-transparent {
  background: transparent; }

/* fullscreen modal */
.modal-fullscreen {
  padding: 0 !important; }
  .modal-fullscreen .modal-content {
    border-radius: 0;
    border-width: 0; }
  .modal-fullscreen .modal-dialog {
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 80px); }
    .modal-fullscreen .modal-dialog .modal-content {
      height: calc(100vh - 80px); }

/* top */
.modal-dialog-top,
.modal-dialog-bottom {
  position: absolute;
  top: 0;
  margin: 0;
  min-width: 100%; }
  .modal-dialog-top .modal-dialog,
  .modal-dialog-bottom .modal-dialog {
    width: 100%;
    max-width: 100%;
    margin: 0; }
  .modal-dialog-top .modal-content,
  .modal-dialog-bottom .modal-content {
    border-radius: 0px;
    border: 0; }

.modal-dialog-bottom {
  top: auto;
  bottom: 0; }
  .modal.fade .modal-dialog-bottom {
    -webkit-transform: translate(0, 25%);
            transform: translate(0, 25%); }

/* left */
.modal-dialog-left {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0 !important; }
  .modal-dialog-left .modal-content {
    min-height: 100%;
    border-width: 0;
    border-radius: 0; }
  .modal.fade .modal-dialog-left {
    -webkit-transform: translate(-25%, 0);
            transform: translate(-25%, 0); }

/* right */
.modal-dialog-right {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  margin: 0 !important;
  border-width: 0px; }
  .modal-dialog-right .modal-content {
    min-height: 100%;
    border-width: 0;
    border-radius: 0; }
  .modal.fade .modal-dialog-right {
    -webkit-transform: translate(25%, 0);
            transform: translate(25%, 0); }

.modal.show .modal-dialog {
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0); }

/* modal size */
.modal-md {
  max-width: 350px; }

.pagination .page-item:not(:first-child) {
  margin-left: 0.4rem; }

.pagination .page-item:first-child:not(.active) .page-link,
.pagination .page-item:last-child:not(.active) .page-link,
.pagination .page-item.disabled .page-link {
  background: #e7e1f0; }

.pagination .page-link {
  border-radius: 4px;
  border-width: 0px; }
  .pagination .page-link:hover {
    background-color: #886ab5 !important;
    color: #fff; }

.pagination.pagination-xs .page-link {
  padding: 0.2rem 0.5rem;
  font-size: 0.75rem; }

.panel-fullscreen {
  overflow: hidden;
  max-width: 100%; }

.panel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: relative;
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 13px 0px rgba(62, 44, 90, 0.08);
          box-shadow: 0px 0px 13px 0px rgba(62, 44, 90, 0.08);
  margin-bottom: 1.5rem;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  border-bottom: 1px solid #e0e0e0;
  border-radius: 4px;
  -webkit-transition: border 500ms ease-out;
  transition: border 500ms ease-out;
  /* panel container */
  /* panel refresh */
  /* panel fullscreen */
  /* panel collapse */
  /*&.panel-collapsed:not(.panel-fullscreen) {

		.panel-container {
			display:none;
		}

	}*/
  /* panel locked */ }
  .panel .panel-container {
    position: relative;
    border-radius: 0 0 4px 4px; }
    .panel .panel-container .panel-content {
      padding: 1rem 1rem; }
      .panel .panel-container .panel-content:only-child, .panel .panel-container .panel-content:last-child {
        border-radius: 0 0 4px 4px; }
  .panel.panel-refresh .js-panel-refresh {
    opacity: 0.5;
    cursor: wait; }
  .panel.panel-fullscreen {
    position: fixed !important;
    /* there is a bug with jquery ui, so we have to add !important rule here */
    z-index: 2055;
    top: 0;
    left: 0;
    right: 0;
    height: 100vh !important;
    max-height: 100vh !important;
    width: 100vw !important;
    max-width: 100vw !important;
    border-radius: 0;
    border: 0;
    /*display: flex;
		flex-direction: column;*/
    /* make panel header bigger */ }
    .panel.panel-fullscreen [data-action="panel-collapse"],
    .panel.panel-fullscreen .js-panel-collapse,
    .panel.panel-fullscreen [data-action="panel-close"],
    .panel.panel-fullscreen .js-panel-close {
      display: none; }
    .panel.panel-fullscreen .panel-hdr {
      height: 4.125rem;
      border-radius: 0;
      -webkit-box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(74, 53, 107, 0.1);
              box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(74, 53, 107, 0.1);
      /* make panel header bigger */ }
      .panel.panel-fullscreen .panel-hdr h2 {
        font-size: 1.125rem;
        font-weight: 400; }
    .panel.panel-fullscreen .js-panel-locked {
      display: none; }
    .panel.panel-fullscreen .btn-panel[data-action="panel-fullscreen"],
    .panel.panel-fullscreen .js-panel-fullscreen {
      width: 1.5rem;
      height: 1.5rem; }
    .panel.panel-fullscreen .panel-container {
      -webkit-box-flex: 1;
          -ms-flex: 1;
              flex: 1;
      overflow-y: auto;
      border-radius: 0;
      display: block !important; }
  .panel.panel-locked:not(.panel-fullscreen) .js-panel-locked {
    font-weight: bold; }
  .panel.panel-locked:not(.panel-fullscreen) .panel-hdr h2:before {
    font-family: 'nextgen-icons';
    content: "\f2ae";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 5px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    font-size: 1.1rem;
    color: #fd3995; }
  .panel.ui-sortable-helper {
    -webkit-box-shadow: 0 5px 16px 0 rgba(0, 0, 0, 0.05), 0 5px 20px 0 rgba(0, 0, 0, 0.09);
            box-shadow: 0 5px 16px 0 rgba(0, 0, 0, 0.05), 0 5px 20px 0 rgba(0, 0, 0, 0.09);
    /*.panel-toolbar {
			-webkit-filter: grayscale(100%);  Safari 6.0 - 9.0 
			filter: grayscale(100%);
		}*/ }

/* panel tag can be used globally */
.panel-tag {
  padding: 1rem 1rem;
  margin-bottom: 2rem;
  border-left: 3px solid #1dc9b7;
  background: #eef7fd;
  opacity: 0.8;
  font-weight: 400;
  font-size: 0.875rem;
  border-radius: 0px 8px 8px 0px; }
  .panel-tag > *:last-child,
  .panel-tag > *:only-child {
    margin-bottom: 0; }
  .panel-tag:only-child {
    margin-bottom: 0; }

/* panel header */
.panel-hdr {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: #fff;
  min-height: 3rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  border-radius: 4px 4px 0 0;
  -webkit-transition: background-color 0.4s ease-out;
  transition: background-color 0.4s ease-out;
  /* add padding to first and last child */
  /* adjusts title */ }
  .panel-collapsed .panel-hdr {
    border-radius: 4px; }
  .panel-hdr > :first-child {
    padding-left: 1rem; }
  .panel-hdr > :last-child {
    padding-right: 1rem; }
  .panel-hdr h2 {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    font-size: 0.875rem;
    margin: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    line-height: 3rem;
    color: inherit;
    color: #333;
    position: relative;
    font-weight: 500;
    /* panel header icon */ }
    .panel-hdr h2:not(:only-child) {
      margin-right: 0.66667rem; }
    .panel-hdr h2 > [class*='fw-'] {
      margin-left: 4px; }
    .panel-hdr h2 small {
      display: inline-block;
      margin: 0;
      opacity: 0.8;
      font-weight: 400;
      font-size: 0.75rem;
      margin-left: 0.5rem; }
    .panel-hdr h2 .panel-icon {
      margin-right: 0.5rem; }

.panel-hdr[class^="bg-"] h2,
.panel-hdr[class*=" bg-"] h2 {
  color: inherit; }

/* panel tap highlight */
.panel-sortable:not(.panel-locked).ui-sortable-helper {
  /*.panel-hdr {
		&:active {
			border-top-color: rgba($primary-300, 0.7);
			border-left-color: rgba($primary-500, 0.7);
			border-right-color: rgba($primary-500, 0.7);

			& + .panel-container {

				border-color: transparent rgba($primary-500, 0.7) rgba($primary-600, 0.7);
			}
		}
	}*/ }
  .panel-sortable:not(.panel-locked).ui-sortable-helper:active {
    border-color: rgba(0, 0, 0, 0.15); }

/*.panel-sortable .panel-hdr:active,
.panel-sortable .panel-hdr:active + .panel-container {
	@include transition-border(0.4s, ease-out);
}*/
.panel-sortable.panel-locked {
  /*.panel-hdr {
		&:active {
			border-top-color: $danger-300;
			border-left-color: $danger;
			border-right-color: $danger;

			& + .panel-container {
				border-color: transparent $danger $danger;
			}
		}
	}*/ }

/* panel toolbar (sits inside panel header) */
.panel-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  /* we curve the last button to make it seamless with panel's border radius */ }
  .panel-toolbar .btn-panel {
    margin-left: 0.3rem;
    padding: 0;
    width: 1rem;
    height: 1rem;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    border-radius: 50%;
    opacity: 0.8;
    /* add default colors for action buttons */
    /*&:after {
			content: "";
			position: absolute;
			border: 1px solid rgba($white, 0.6);
			border-radius: 50%;
			width: calc(100% + 2px);
			height: calc(100% + 2px);
			right: -1px;
			top: -1px;
		}*/ }
    .panel-toolbar .btn-panel:hover {
      opacity: 1; }
    .panel-toolbar .btn-panel[data-action="panel-collapse"], .panel-toolbar .btn-panel.js-panel-collapse {
      background: #1dc9b7; }
    .panel-toolbar .btn-panel[data-action="panel-fullscreen"], .panel-toolbar .btn-panel.js-panel-fullscreen {
      background: #ffc241; }
    .panel-toolbar .btn-panel[data-action="panel-close"], .panel-toolbar .btn-panel.js-panel-close {
      background: #fd3995; }
  .panel-toolbar .btn-toolbar-master {
    height: 3rem;
    width: 1.826875rem;
    /* stop flickering bug due to cpu latency */
    border-radius: 0;
    margin-right: -1rem;
    border-top-right-radius: 3px;
    margin-left: 0.5rem;
    padding: 0 13px;
    background: transparent;
    border: 0;
    font-size: 1.5625rem;
    color: inherit;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center; }
    .panel-toolbar .btn-toolbar-master[aria-expanded="true"] {
      -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset; }
    .panel-toolbar .btn-toolbar-master + .dropdown-menu {
      right: 0;
      top: 3rem;
      left: auto !important;
      margin: 0;
      border-radius: 0; }
  .panel-toolbar .btn-panel-flat:last-child {
    border-top-right-radius: 3px; }

.panel-sortable:not(.panel-fullscreen):not(.panel-locked) .ui-sortable-handle {
  cursor: move; }

/* placeholder */
.panel-placeholder {
  background-color: #e8e6ec;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  margin-bottom: 1.5rem;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  /* placeholder border animation */ }
  .panel-placeholder:before, .panel-placeholder:after {
    content: " ";
    background-image: url(data:image/gif;base64,R0lGODlhCAAIAJAAAAAAAP///yH/C05FVFNDQVBFMi4wAwEAAAAh+QQECgD/ACwAAAAACAAIAAACD4SDYZB6udpiaMJYsXuoAAAh+QQECgD/ACwAAAAACAAIAAACDYQRGadrzVRMB9FZ5SwAIfkEBAoA/wAsAAAAAAgACAAAAg8MDqGYaudeW9ChyOyltQAAIfkEBAoA/wAsAAAAAAgACAAAAg9MgGCXm+rQYtC0WGl9oQAAIfkEBAoA/wAsAAAAAAgACAAAAg+MgWCRernaYmjCWLF7qAAAIfkEBAoA/wAsAAAAAAgACAAAAg2MAwmna81UTAfRWeUsACH5BAQKAP8ALAAAAAAIAAgAAAIPRB6gmGrnXlvQocjspbUAACH5BAQKAP8ALAAAAAAIAAgAAAIPBIJhl5vq0GLQtFhpfaAAADs=);
    border-radius: 5px;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0.3; }
  .panel-placeholder:before {
    background: #e8e6ec;
    margin: 1px;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    opacity: 1;
    z-index: 1; }

.mod-panel-clean {
  /*.panel-container {
		.panel-content:first-child {
			padding-top: $p-1;
		}
	}*/ }
  .mod-panel-clean .panel-hdr {
    background: #fff;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f7f7f7), to(#fff));
    background-image: linear-gradient(to bottom, #f7f7f7, #fff);
    -webkit-box-shadow: none;
            box-shadow: none; }
    .mod-panel-clean .panel-hdr h2 {
      color: #333;
      font-weight: 500; }

@media only screen and (max-width: 420px) {
  /* making mobile spacing a little narrow */
  .panel .panel-hdr {
    font-size: 0.875rem;
    min-height: -1rem; }
    .panel .panel-hdr > :first-child {
      padding-left: 10px; }
    .panel .panel-hdr > :last-child {
      padding-right: 10px; }
    .panel .panel-hdr .panel-toolbar .btn-toolbar-master {
      margin-right: -10px; }
  .panel .panel-container .panel-content:first-child {
    padding: 10px; } }

/* print only selected panel when on fullscreen */
@media print {
  .panel-fullscreen .subheader,
  .panel-fullscreen .page-breadcrumb,
  .panel-fullscreen .page-content .panel:not(.panel-fullscreen) {
    display: none; }
  .panel-fullscreen .panel-hdr,
  .panel-fullscreen .panel-container,
  .panel-fullscreen .panel-content,
  .panel-fullscreen h2 {
    border: none;
    padding: 0 !important; }
  .panel-fullscreen .panel {
    margin: 0; } }

.popover {
  -webkit-box-shadow: 0 0 15px 1px rgba(90, 80, 105, 0.2);
          box-shadow: 0 0 15px 1px rgba(90, 80, 105, 0.2);
  margin: 12px; }
  .popover .arrow {
    border-color: inherit; }
  .popover .popover-header {
    font-weight: 500;
    font-size: 0.875rem;
    border-radius: 0.5rem 0.5rem 0 0;
    border-bottom-width: 0px; }
  .popover .popover-body {
    padding: 0; }

.popover-body:not(:empty) {
  padding: 0 1rem 1rem; }

.popover-header:empty + .popover-body {
  padding-top: 1rem; }

.progress-xs {
  height: 5px; }

.progress-sm {
  height: 8px; }

.progress-md {
  height: 14px; }

.progress-lg {
  height: 20px; }

.progress-xl {
  height: 30px; }

.menu-item,
label.menu-open-button {
  background: #886ab5;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  position: absolute !important;
  padding: 0;
  right: 0;
  bottom: 0;
  color: #fff !important;
  text-align: center;
  line-height: 45px;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 200ms;
  transition: -webkit-transform ease-out 200ms;
  transition: transform ease-out 200ms;
  transition: transform ease-out 200ms, -webkit-transform ease-out 200ms;
  -webkit-box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
          box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1); }
  .menu-item:hover,
  label.menu-open-button:hover {
    background: #6e4e9e; }

.shortcut-menu {
  position: fixed;
  right: 1.5rem;
  bottom: 4.3125rem;
  z-index: 931; }

.menu-open {
  display: none; }

.menu-item,
label.menu-open-button {
  font-size: 16px; }

label.menu-open-button {
  z-index: 932;
  -webkit-transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
          transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -webkit-transition-duration: 400ms;
          transition-duration: 400ms;
  cursor: pointer;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

.app-shortcut-icon {
  width: 5px;
  height: 5px;
  background: #ecf0f1;
  color: #ecf0f1;
  -webkit-transition: .3s;
  transition: .3s;
  -webkit-box-shadow: -8px -8px, 0 -8px, 8px -8px, -8px 0, 8px 0, -8px 8px, 0 8px, 8px 8px;
          box-shadow: -8px -8px, 0 -8px, 8px -8px, -8px 0, 8px 0, -8px 8px, 0 8px, 8px 8px; }

.menu-open:checked + .menu-open-button {
  -webkit-transition-timing-function: linear;
          transition-timing-function: linear;
  -webkit-transition-duration: 200ms;
          transition-duration: 200ms;
  -webkit-transform: scale(0.9, 0.9) translate3d(0, 0, 0);
          transform: scale(0.9, 0.9) translate3d(0, 0, 0);
  background: #505050; }
  .menu-open:checked + .menu-open-button .app-shortcut-icon {
    -webkit-box-shadow: 0 -5px, 0 -8px, 5px 0, -5px 0, 8px 0, -8px 0, 0 8px, 0 5px !important;
            box-shadow: 0 -5px, 0 -8px, 5px 0, -5px 0, 8px 0, -8px 0, 0 8px, 0 5px !important;
    -webkit-transform: rotate3d(0, 0, 1, -45deg) scale3d(0.8, 0.8, 0.8);
    transform: rotate3d(0, 0, 1, -45deg) scale3d(0.8, 0.8, 0.8); }

.menu-open:checked ~ .menu-item {
  -webkit-transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
          transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1); }
  .menu-open:checked ~ .menu-item:nth-child(3) {
    -webkit-transition-duration: 150ms;
            transition-duration: 150ms;
    -webkit-transform: translate3d(0, -48px, 0);
            transform: translate3d(0, -48px, 0); }
  .menu-open:checked ~ .menu-item:nth-child(4) {
    -webkit-transition-duration: 250ms;
            transition-duration: 250ms;
    -webkit-transform: translate3d(0, -96px, 0);
            transform: translate3d(0, -96px, 0); }
  .menu-open:checked ~ .menu-item:nth-child(5) {
    -webkit-transition-duration: 350ms;
            transition-duration: 350ms;
    -webkit-transform: translate3d(0, -144px, 0);
            transform: translate3d(0, -144px, 0); }
  .menu-open:checked ~ .menu-item:nth-child(6) {
    -webkit-transition-duration: 450ms;
            transition-duration: 450ms;
    -webkit-transform: translate3d(0, -192px, 0);
            transform: translate3d(0, -192px, 0); }
  .menu-open:checked ~ .menu-item:nth-child(7) {
    -webkit-transition-duration: 550ms;
            transition-duration: 550ms;
    -webkit-transform: translate3d(0, -240px, 0);
            transform: translate3d(0, -240px, 0); }

/* set base height for slider */
.slide-on-mobile {
  width: 15rem; }

@media only screen and (max-width: 992px) {
  /* SIDE PANELS */
  .slide-on-mobile {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    z-index: 950;
    position: absolute !important;
    top: 0;
    bottom: 0;
    background-color: #f8f9fa;
    -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
    transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1); }
  .slide-backdrop {
    background: transparent;
    -webkit-transition: background 300ms;
    transition: background 300ms; }
  .slide-on-mobile-left {
    border-right: 1px solid rgba(0, 0, 0, 0.09);
    /* new solution */
    left: -15rem; }
  .slide-on-mobile-left-show {
    left: 0; }
  .slide-on-mobile-right {
    border-left: 1px solid rgba(0, 0, 0, 0.09);
    right: -15rem; }
  .slide-on-mobile-right-show {
    right: 0; }
  /* place the backdrop right after these classes */
  .slide-on-mobile-right-show + .slide-backdrop,
  .slide-on-mobile-left-show + .slide-backdrop {
    background: rgba(0, 0, 0, 0.09);
    position: absolute;
    z-index: 948;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0; } }

/* bootstrap override table stripe */
.table.table-striped:not(.table-bordered) th,
.table.table-striped:not(.table-bordered) tbody th,
.table.table-striped:not(.table-bordered) tbody td {
  border: 0; }

.table thead[class^="bg-"] tr > th,
.table thead[class*=" bg-"] tr > th {
  border-top: 0;
  border-bottom: 0; }

.table tr[class^="bg-"] > td,
.table tr[class^="bg-"] > th,
.table tr[class*=" bg-"] > td,
.table tr[class*=" bg-"] > th {
  border-top: 0 !important; }

.thead-themed {
  background-color: #f2f2f2;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f2f2f2), to(#fafafa));
  background-image: linear-gradient(to top, #f2f2f2, #fafafa); }

.table-dark .thead-themed {
  background-color: #363636;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#363636), to(#2a2a2a));
  background-image: linear-gradient(to top, #363636, #2a2a2a); }

.table-bordered[class*=" bg-"],
.table-bordered[class*=" bg-"] td,
.table-bordered[class*=" bg-"] th, .table-bordered[class^="bg-"],
.table-bordered[class^="bg-"] td,
.table-bordered[class^="bg-"] th {
  border: 1px solid rgba(255, 255, 255, 0.1); }

.table-bordered [class*=" bg-"] td,
.table-bordered [class*=" bg-"] th,
.table-bordered [class^="bg-"] td,
.table-bordered [class^="bg-"] th {
  border: 1px solid rgba(0, 0, 0, 0.1); }

/* table hover */
.table-hover tbody tr:hover {
  -webkit-box-shadow: inset 1px 0 0 #dadce0, inset -1px 0 0 #dadce0, 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
          box-shadow: inset 1px 0 0 #dadce0, inset -1px 0 0 #dadce0, 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
  z-index: 1; }

/* reset table (global) */
th {
  font-weight: 500; }

.table-clean {
  background: transparent;
  border: none; }
  .table-clean tr,
  .table-clean td,
  .table-clean th {
    border: none;
    background: none; }

.table-scale-border-top {
  border-top: 2px solid #505050 !important; }

.table-scale-border-bottom {
  border-bottom: 2px solid #505050 !important; }

/* nav tabs panel */
.nav-tabs-clean {
  height: 45px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); }
  .nav-tabs-clean .nav-item .nav-link {
    border-radius: 0;
    border: 0;
    height: 45px;
    /*font-size: rem($fs-md);
			font-weight: 500;*/
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    border-bottom: 1px solid transparent; }
    .nav-tabs-clean .nav-item .nav-link.active {
      border-bottom: 1px solid #886ab5;
      color: #886ab5; }
    .nav-tabs-clean .nav-item .nav-link:hover {
      color: #886ab5; }

/* hack for waves effect breaking tabs */
/*.nav-tabs .nav-item > .nav-link.waves-effect {
	height: 100% !important;
	display: block;
}*/
/* fontsize for tabs */
.nav-tabs .nav-item .nav-link:not(:hover) {
  color: inherit; }

.nav-tabs .nav-item .nav-link.active:not(:hover) {
  color: #333; }

.nav-tabs .nav-item .nav-link:hover:not(.active) {
  color: inherit; }

.nav .nav-link:not([class^="btn-"]):not([class*=" btn-"]) {
  font-weight: 500;
  font-size: 0.8125rem; }

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #886ab5; }

.tooltip-inner {
  font-family: "Roboto", "Helvetica Neue", Helvetica, Arial;
  font-weight: 500;
  -webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }

/*.tooltip .arrow {
	display: none;
}*/
/* #MISC - misc styles, helpers, effects and hacks
========================================================================== */
/* height & width auto */
.h-auto {
  height: auto !important; }

.w-auto {
  width: auto !important; }

.min-height-reset {
  min-height: initial !important; }

.max-width-reset {
  max-width: none !important; }

.max-height-reset {
  max-height: none !important; }

/* width preset */
.min-width-0 {
  min-width: 0; }

.width-0 {
  width: 0; }

.width-1 {
  width: 1.5rem; }

.width-2 {
  width: 2rem; }

.width-3 {
  width: 2.5rem; }

.width-4 {
  width: 2.75rem; }

.width-5 {
  width: 3rem; }

.width-6 {
  width: 3.25rem; }

.width-7 {
  width: 3.5rem; }

.width-8 {
  width: 3.75rem; }

.width-9 {
  width: 4rem; }

.width-10 {
  width: 4.25rem; }

.width-xs {
  min-width: 5rem; }

.width-sm {
  min-width: 10rem; }

.width-lg {
  min-width: 15rem; }

.width-xl {
  min-width: 20rem; }

.height-0 {
  height: 0; }

.height-1 {
  height: 1.5rem; }

.height-2 {
  height: 2rem; }

.height-3 {
  height: 2.5rem; }

.height-4 {
  height: 2.75rem; }

.height-5 {
  height: 3rem; }

.height-6 {
  height: 3.25rem; }

.height-7 {
  height: 3.5rem; }

.height-8 {
  height: 3.75rem; }

.height-9 {
  height: 4rem; }

.height-10 {
  height: 4.25rem; }

.height-xs {
  min-height: 5rem; }

.height-sm {
  min-height: 10rem; }

.height-lg {
  min-height: 15rem; }

.height-xl {
  min-height: 20rem; }

/* line-heights */
.l-h-n {
  line-height: normal; }

/* no bg image */
.bg-img-none {
  background-image: none !important; }

/* flex */
.flex-1 {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1; }

/* margins */
.m-g {
  margin: 1.5rem; }

.mb-g {
  margin-bottom: 1.5rem !important; }

.mb-gb {
  margin-bottom: 3rem; }

/* paddings */
.p-g {
  padding: 1.5rem; }

/* text turncaters */
.text-truncate-header {
  max-width: 6.0625rem; }

.text-truncate-xs {
  max-width: 4.5625rem; }

.text-truncate-sm {
  max-width: 117px; }

.text-truncate-md {
  max-width: 160px; }

.text-truncate-default {
  max-width: 180px; }

.text-truncate-lg {
  max-width: 200px; }

/* blur text */
.text-blur {
  color: transparent !important;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5); }

a.text-blur {
  text-shadow: 0 0 5px rgba(136, 106, 181, 0.5); }

/* positions usage: 
   .position-absolute|relative|static|fixed .pos-top|left|right|bottom */
.pos-top {
  top: 0; }

.pos-left {
  left: 0; }

.pos-right {
  right: 0; }

.pos-bottom {
  bottom: 0; }

/* font weights */
.fw-300 {
  font-weight: 300 !important; }

.fw-400 {
  font-weight: 400 !important; }

.fw-500 {
  font-weight: 500 !important; }

.fw-700 {
  font-weight: 700 !important; }

.fw-900 {
  font-weight: 900 !important; }

.fw-n {
  font-weight: normal !important; }

/* font sizes */
.fs-nano {
  font-size: 0.6875rem !important; }

/* 10px */
.fs-xs {
  font-size: 0.75rem !important; }

/* 12px */
.fs-sm {
  font-size: 0.78125rem !important; }

/* 12.5px */
.fs-b {
  font-size: 0.8125rem !important; }

/* 13px */
.fs-md {
  font-size: 0.875rem !important; }

/* 14px */
.fs-lg {
  font-size: 0.9375rem !important; }

/* 15px */
.fs-xl {
  font-size: 1rem !important; }

/* 16px */
.fs-xxl {
  font-size: 1.75rem !important; }

/* page header */
/* alphas */
.opacity-5 {
  opacity: 0.05; }

.opacity-10 {
  opacity: 0.1; }

.opacity-15 {
  opacity: 0.15; }

.opacity-20 {
  opacity: 0.2; }

.opacity-25 {
  opacity: 0.25; }

.opacity-30 {
  opacity: 0.3; }

.opacity-35 {
  opacity: 0.35; }

.opacity-40 {
  opacity: 0.4; }

.opacity-45 {
  opacity: 0.45; }

.opacity-50 {
  opacity: 0.5; }

.opacity-55 {
  opacity: 0.55; }

.opacity-60 {
  opacity: 0.6; }

.opacity-65 {
  opacity: 0.65; }

.opacity-70 {
  opacity: 0.7; }

.opacity-75 {
  opacity: 0.75; }

.opacity-80 {
  opacity: 0.8; }

.opacity-85 {
  opacity: 0.85; }

.opacity-90 {
  opacity: 0.9; }

.opacity-95 {
  opacity: 0.95; }

.opacity-100 {
  opacity: 1; }

/* backgrounds */
.bg-white {
  background-color: #fff;
  color: #666666; }

.bg-faded {
  background-color: #f7f9fa; }

.bg-offwhite-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(#f8f8f8));
  background-image: linear-gradient(to top, #fff, #f8f8f8); }

.bg-subtlelight {
  background-color: white; }

.bg-subtlelight-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(#f5fcff));
  background-image: linear-gradient(to top, #fff, #f5fcff); }

.bg-highlight {
  background-color: #fffaee; }

.bg-gray-50 {
  background-color: #f9f9f9; }

.bg-gray-100 {
  background-color: #f8f9fa; }

.bg-gray-200 {
  background-color: #f3f3f3; }

.bg-gray-300 {
  background-color: #dee2e6; }

.bg-gray-400 {
  background-color: #ced4da; }

.bg-gray-500 {
  background-color: #adb5bd; }

.bg-gray-600 {
  background-color: #868e96; }

.bg-gray-700 {
  background-color: #495057; }

.bg-gray-800 {
  background-color: #343a40; }

.bg-gray-900 {
  background-color: #212529; }

/* borders */
.border-faded {
  border: 1px solid rgba(29, 29, 29, 0.07); }

.border-transparent {
  border: 1px solid transparent !important; }

/* border radius */
.border-top-left-radius-0 {
  border-top-left-radius: 0 !important; }

.border-bottom-left-radius-0 {
  border-bottom-left-radius: 0 !important; }

.border-top-right-radius-0 {
  border-top-right-radius: 0 !important; }

.border-bottom-right-radius-0 {
  border-bottom-right-radius: 0 !important; }

.rounded-plus {
  border-radius: 10px; }

.rounded-bottom {
  border-radius: 0 0 4px; }

.rounded-top {
  border-radius: 4px 4px 0 0; }

/* progressbars */
/*.progress-xs { height: 5px }
.progress-sm { height: 8px }
.progress-md { height: 14px }
.progress-lg { height: 20px }
.progress-xl { height: 30px }*/
/* rotate */
.rotate-90 {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg); }

.rotate-180 {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg); }

.rotate-270 {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg); }

/* shadows */
.shadow-0 {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }

.shadow-1, .shadow-hover-1:hover {
  -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.02), 0 1px 2px rgba(0, 0, 0, 0.1);
          box-shadow: 0 2px 3px rgba(0, 0, 0, 0.02), 0 1px 2px rgba(0, 0, 0, 0.1); }

.shadow-2, .shadow-hover-2:hover {
  -webkit-box-shadow: 0 0.1rem 0.15rem rgba(0, 0, 0, 0.1);
          box-shadow: 0 0.1rem 0.15rem rgba(0, 0, 0, 0.1); }

.shadow-3, .shadow-hover-3:hover {
  -webkit-box-shadow: 0 0.1rem 0.15rem rgba(0, 0, 0, 0.125);
          box-shadow: 0 0.1rem 0.15rem rgba(0, 0, 0, 0.125); }

.shadow-4, .shadow-hover-4:hover {
  -webkit-box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.125);
          box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.125); }

.shadow-5, .shadow-hover-5:hover {
  -webkit-box-shadow: 0 0.125rem 0.325rem rgba(0, 0, 0, 0.175);
          box-shadow: 0 0.125rem 0.325rem rgba(0, 0, 0, 0.175); }

.shadow-inset-1, .shadow-hover-inset-1:hover {
  -webkit-box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025);
          box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025); }

.shadow-inset-2, .shadow-hover-inset-2:hover {
  -webkit-box-shadow: inset 0 0.2rem 0.325rem rgba(0, 0, 0, 0.04);
          box-shadow: inset 0 0.2rem 0.325rem rgba(0, 0, 0, 0.04); }

.shadow-inset-3, .shadow-hover-inset-3:hover {
  -webkit-box-shadow: inset 0 0.2rem 0.325rem rgba(0, 0, 0, 0.05);
          box-shadow: inset 0 0.2rem 0.325rem rgba(0, 0, 0, 0.05); }

.shadow-inset-4, .shadow-hover-inset-4:hover {
  -webkit-box-shadow: inset 0 0.25rem 0.5rem rgba(0, 0, 0, 0.06);
          box-shadow: inset 0 0.25rem 0.5rem rgba(0, 0, 0, 0.06); }

.shadow-inset-5, .shadow-hover-inset-5:hover {
  -webkit-box-shadow: inset 0 0.35rem 0.5rem rgba(0, 0, 0, 0.07);
          box-shadow: inset 0 0.35rem 0.5rem rgba(0, 0, 0, 0.07); }

.shadow-sm-hover:hover {
  -webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }

.shadow-hover:hover {
  -webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
          box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }

.shadow-lg-hover:hover {
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }

/*.shadow-unique-hover {
   @extend %shadow-hover;
}*/
/* hover any bg */
/* inherits the parent background on hover */
.hover-bg {
  background: #fff;
  -webkit-transition: all .1s ease-in;
  transition: all .1s ease-in;
  color: inherit; }
  .hover-bg:hover {
    background: inherit;
    color: inherit; }

/* hover alpha effect */
/* example found in the buttons page */
/* example of use could be found inside panel buttons top right */
.hover-effect-dot {
  position: relative; }
  .hover-effect-dot:before {
    content: "";
    background: rgba(0, 0, 0, 0.2);
    width: 0%;
    height: 0%;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 100%;
    -webkit-transition: all 100ms ease-in;
    transition: all 100ms ease-in; }
  .hover-effect-dot:hover:before {
    width: 75%;
    height: 75%;
    top: 12%;
    left: 12%; }

/* hover light bg effect */
.hover-highlight:hover {
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.03)), to(rgba(29, 33, 41, 0.04)));
  background-image: linear-gradient(rgba(29, 33, 41, 0.03), rgba(29, 33, 41, 0.04)); }

.hover-highlight:active {
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.05)), to(rgba(29, 33, 41, 0.06)));
  background-image: linear-gradient(rgba(29, 33, 41, 0.05), rgba(29, 33, 41, 0.06)); }

/* hover and hide items on show */
.show-child-on-hover .show-on-hover-parent {
  display: none; }

.show-child-on-hover:hover .show-on-hover-parent {
  display: block; }

.hide-child-on-hover .hide-on-hover-parent {
  display: block; }

.hide-child-on-hover:hover .hide-on-hover-parent {
  display: none; }

/* z-indexes */
.z-index-space {
  z-index: 1000; }

.z-index-cloud {
  z-index: 950; }

.z-index-ground {
  z-index: 0; }

.z-index-water {
  z-index: -99; }

/* cursor 
	usage: cursor-default;
*/
.cursor-auto {
  cursor: auto !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-crosshair {
  cursor: crosshair !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-default {
  cursor: default !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-e-resize {
  cursor: e-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-help {
  cursor: help !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-move {
  cursor: move !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-n-resize {
  cursor: n-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-ne-resize {
  cursor: ne-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-nw-resize {
  cursor: nw-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-pointer {
  cursor: pointer !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-progress {
  cursor: progress !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-s-resize {
  cursor: s-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-se-resize {
  cursor: se-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-sw-resize {
  cursor: sw-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-text {
  cursor: text !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-w-resize {
  cursor: w-resize !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-wait {
  cursor: wait !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

.cursor-inherit {
  cursor: inherit !important;
  /* had to insert important since bootstrap has some peculiar classes */ }

/* states */
.state-selected {
  background: #e7f4fe !important; }

/* collapse toggle to reveal and hide elements */
[aria-expanded="false"] ~ .collapsed-reveal {
  display: none; }

[aria-expanded="false"] .collapsed-reveal {
  display: none; }

[aria-expanded="false"] ~ .collapsed-hidden {
  display: block; }

[aria-expanded="false"] .collapsed-hidden {
  display: block; }

[aria-expanded="true"] ~ .collapsed-reveal {
  display: block; }

[aria-expanded="true"] .collapsed-reveal {
  display: block; }

[aria-expanded="true"] ~ .collapsed-hidden {
  display: none; }

[aria-expanded="true"] .collapsed-hidden {
  display: none; }

/* demo window */
.demo-window {
  position: relative;
  z-index: 1;
  overflow: hidden;
  padding-top: 23px;
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none; }
  .demo-window:before, .demo-window:after,
  .demo-window .demo-window-content:before,
  .demo-window .demo-window-content:after {
    content: "";
    position: absolute;
    display: block; }
  .demo-window:before {
    top: 0;
    right: 0;
    left: 0;
    z-index: 3;
    height: 23px;
    background: #e5e5e5; }
  .demo-window:after,
  .demo-window .demo-window-content:before,
  .demo-window .demo-window-content:after {
    left: 10px;
    background: #ccc;
    top: 6px;
    z-index: 4;
    width: 11px;
    height: 11px;
    border-radius: 50%; }
  .demo-window .demo-window-content {
    width: 100%; }
    .demo-window .demo-window-content img {
      display: block;
      width: 100%; }
    .demo-window .demo-window-content:before {
      left: 26px; }
    .demo-window .demo-window-content:after {
      left: 43px; }

/* layout composed */
.layout-composed .page-content {
  padding: 0 !important; }
  .layout-composed .page-content .page-breadcrumb,
  .layout-composed .page-content .subheader {
    display: none; }

/* responsive helpers */
@media only screen and (max-width: 992px) {
  /* layout composed mobile only */
  .layout-composed-mobile .page-content {
    padding: 0 !important; }
    .layout-composed-mobile .page-content .page-breadcrumb,
    .layout-composed-mobile .page-content .subheader {
      display: none; }
  /* positions on mobile view */
  .position-on-mobile-absolute {
    position: absolute !important; }
  .position-on-mobile-relative {
    position: relative !important; }
  .position-on-mobile-static {
    position: static !important; }
  /* RESET HEIGHTS */
  .height-mobile-auto {
    height: auto;
    min-height: auto;
    max-height: auto; }
  .width-mobile-auto {
    width: auto;
    min-width: auto;
    max-width: auto; }
  /* FULL HEIGHT ON MOBILE */
  .expand-full-height-on-mobile {
    height: calc(100vh - 4.125rem) !important; }
  .expand-full-width-on-mobile {
    width: 100vw !important;
    max-width: 100vw !important; } }

/* row grid */
.row-grid {
  overflow: hidden; }
  .row-grid > .col,
  .row-grid > [class^="col-"],
  .row-grid > [class*=" col-"],
  .row-grid > [class^="col "],
  .row-grid > [class*=" col "],
  .row-grid > [class$=" col"],
  .row-grid > [class="col"] {
    position: relative; }
  .row-grid > .col:after,
  .row-grid > [class^="col-"]:after,
  .row-grid > [class*=" col-"]:after,
  .row-grid > [class^="col "]:after,
  .row-grid > [class*=" col "]:after,
  .row-grid > [class$=" col"]:after,
  .row-grid > [class="col"]:after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: -1px;
    display: block;
    width: 0;
    border-left: 1px solid rgba(29, 29, 29, 0.07); }
  .row-grid > .col:before,
  .row-grid > [class^="col-"]:before,
  .row-grid > [class*=" col-"]:before,
  .row-grid > [class^="col "]:before,
  .row-grid > [class*=" col "]:before,
  .row-grid > [class$=" col"]:before,
  .row-grid > [class="col"]:before {
    content: "";
    position: absolute;
    right: 0;
    bottom: -1px;
    left: 0;
    display: block;
    height: 0;
    border-top: 1px solid rgba(29, 29, 29, 0.07); }

/* List table */
.list-table {
  height: auto;
  display: table;
  margin: 0;
  padding: 0; }
  .list-table > li {
    display: table-cell;
    vertical-align: middle;
    position: relative;
    padding: 0; }
    .list-table > li.search {
      position: static; }

/* mostly used for nav items */
.disabled:not(.btn),
.disabled:not(.btn) > * {
  -webkit-filter: grayscale(80%);
          filter: grayscale(80%);
  opacity: 0.80;
  cursor: not-allowed; }
  .disabled:not(.btn) ul,
  .disabled:not(.btn) .collapse-sign,
  .disabled:not(.btn) > * ul,
  .disabled:not(.btn) > * .collapse-sign {
    display: none; }

ul.list-verticle {
  margin: 0;
  padding: 0;
  list-style: none; }

.show > .dropdown-menu-animated {
  -webkit-transform: scale(1) !important;
  transform: scale(1) !important;
  -webkit-transform-origin: 29px -50px;
  transform-origin: 29px -50px;
  opacity: 1;
  visibility: visible; }

hr {
  border: none;
  border-bottom: 1px dashed #eee; }
  hr.hr-xl {
    margin: 3rem 0; }

.bg-trans-gradient {
  background: linear-gradient(250deg, #3e93d6, #8a75aa); }

/* custom scroll */
/* table no border */
.table-border-0 th,
.table-border-0 td {
  border: 0 !important; }

/* table calendar */
.table-calendar {
  table-layout: fixed; }
  .table-calendar th {
    border: 0px !important;
    font-weight: 500; }
  .table-calendar tr td:first-child {
    border-left: 0; }
  .table-calendar tr td:last-child {
    border-right: 0;
    padding-right: 10px; }
  .table-calendar td, .table-calendar th {
    text-align: right;
    vertical-align: top;
    padding: 5px 8px;
    position: relative; }

/* list spaced */
.list-spaced li {
  margin-top: 7px;
  margin-bottom: 7px; }
  .list-spaced li:first-child {
    margin-top: 0; }
  .list-spaced li:last-child {
    margin-bottom: 0; }

.list-spaced > li {
  padding: 0 0 0.2rem; }

/* profile images */
.profile-image {
  width: 3.125rem;
  height: 3.125rem; }

.profile-image-md {
  width: 2rem;
  height: 2rem; }

.profile-image-sm {
  width: 1.5625rem;
  height: 1.5625rem; }

/* image share */
.img-share {
  width: auto;
  height: 2.8125rem; }

span.img-share {
  width: 4.12594rem;
  height: 2.8125rem; }

.notes {
  padding: 5px;
  background: #f9f4b5; }

/*
.shadow-2 {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}
.shadow-3 {
  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}
.shadow-4 {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}
.shadow-5 {
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
}*/
/* disclaimer class */
.disclaimer {
  padding-left: 10px;
  font-size: 0.5rem;
  color: #a2a2a2;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-style: italic; }

/* horizontal scrolling */
.scrolling-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; }
  .scrolling-wrapper .card {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto; }
  .scrolling-wrapper::-webkit-scrollbar {
    display: none; }

/* online status */
.status {
  position: relative; }
  .status:before {
    content: " ";
    position: absolute;
    width: 15px;
    height: 15px;
    display: block;
    top: -2px;
    right: -2px;
    background: #505050;
    border-radius: 50%;
    border: 2px solid #fff; }
  .status.status-sm:before {
    width: 10px;
    height: 10px;
    border-width: 1px;
    top: 0;
    right: 0; }
  .status.status-success:before {
    background: #1dc9b7; }
  .status.status-danger:before {
    background: #fd3995; }
  .status.status-warning:before {
    background: #ffc241; }

/* containers */
.container.container-sm {
  max-width: 576px; }

.container.container-md {
  max-width: 768px; }

.container.container-lg {
  max-width: 992px; }

/* responsive visibility */
/* https://getbootstrap.com/docs/3.4/css/#responsive-utilities */
.hidden-xs-up {
  display: none !important; }

@media (max-width: 575.98px) {
  .hidden-xs-down {
    display: none !important; } }

@media (min-width: 576px) {
  .hidden-sm-up {
    display: none !important; } }

@media (max-width: 767.98px) {
  .hidden-sm-down {
    display: none !important; } }

@media (min-width: 768px) {
  .hidden-md-up {
    display: none !important; } }

@media (max-width: 991.98px) {
  .hidden-md-down {
    display: none !important; } }

@media (min-width: 992px) {
  .hidden-lg-up {
    display: none !important; } }

@media (max-width: 1398.98px) {
  .hidden-lg-down {
    display: none !important; } }

@media (min-width: 1399px) {
  .hidden-xl-up {
    display: none !important; } }

.hidden-xl-down {
  display: none !important; }

/* display frame */
.frame-heading {
  font-size: 0.8125rem;
  margin-bottom: 1rem;
  color: #a1a1a1;
  font-weight: 500; }
  .frame-heading small {
    font-size: 0.8125rem;
    margin-bottom: 0.5rem; }

.frame-wrap {
  background: white;
  padding: 0;
  margin-bottom: 3rem; }

* > .frame-wrap:last-child {
  margin-bottom: 0 !important; }

/* time stamp */
.time-stamp {
  font-size: 0.78125rem;
  margin: 0.25rem 0 0 0;
  color: #767676;
  font-weight: 300; }

/* data-hasmore */
[data-hasmore] {
  position: relative;
  color: #fff; }
  [data-hasmore]:before {
    content: attr(data-hasmore);
    border-radius: inherit;
    background: rgba(0, 0, 0, 0.4);
    height: inherit;
    width: 100%;
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    font-weight: 500;
    font-size: inherit; }

/* code */
code {
  background: #f8f8f8;
  padding: 4px 7px;
  border-radius: 4px; }

/* star checkbox */
.star {
  visibility: hidden;
  font-size: 1.5em;
  cursor: pointer; }

.star:before {
  content: "☆";
  position: absolute;
  visibility: visible; }

.star:checked:before {
  content: "★";
  position: absolute; }

.shadow-top:after,
.shadow-bottom:after,
.mobile-view-activated.header-function-fixed .page-header:after,
.mobile-view-activated.header-function-fixed .page-footer:after {
  content: "";
  height: 6px;
  position: absolute;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPYAAAAICAMAAAD9VPKTAAAATlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADEoqZZAAAAGnRSTlMACRVXPCMeNMEsdZ98SZBDbFDIrZhkXreCiM2g9MAAAAD1SURBVDjLzZHbDoIwEAVdEC94raLi//+otD1Lu4tgCWqYhPC0J5np4jeQ+2gKYWZukCLrYTWKvpVOl/9AAeVUMNuWtWA5AnkZFgtGlaHA901jTeEYa50cB4/x5A6Tp2H478GU342ziBJRBsqmNIAqaVWIwpMVW7l7w81y9pSS/QdKCUbcoF1GEMTgDm0ETqALpPraA6nLskKVRWEZzOq6fjYcQQV2CVSAb+1OMxeaoANHEAk4gNQn6A+/sBDu+kayrApPKMLuCh6ezQhwwgvogRSowAmiAG/ttXyPs35lLW0MpNOstfJlEK2e5g1xY7S4fnUPzF+TRjAMoku43AAAAABJRU5ErkJggg==);
  background-size: cover;
  top: -5px;
  left: 0;
  right: 0;
  opacity: 0.06; }

.shadow-bottom:after,
.mobile-view-activated.header-function-fixed .page-header:after {
  top: auto;
  bottom: -5px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPYAAAAICAMAAAD9VPKTAAAANlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC3dmhyAAAAEnRSTlMACRUgPnjDV51MNCxhkIVsrbfUWo5iAAAA/UlEQVQ4y8zR7WqEMBCF4Xy4HeNos73/m605M+HQlIB2oeyjP5XwngQTXTLZqOp2WqCAiOxQa32Yz+Zo1u5pvpqPP8CPT7N2R4PDHqbWuoOIFFhgO6lqNslEFwaoHsKV4cXCpZl3H+z2cLidjGjozfNqaX5F6xANYSKO8aDjtXMAqJwAK8Dh1hXvNf41oJKdUL2VsZNeBp8Pii+Inp9cdsoBTHECe1fN4yXV7J1AYSpjUesSeyHcF8EXcLlT2HwFKj+IKXJNYRwt4LZGITO1i8Dc10VKjjNwCa5Byw0beR8bmUmRwv+IgzSRb0kTcRDezfcYwRhEUAKgxtAEAACGmyM6KW9inQAAAABJRU5ErkJggg==); }

.shadow-bottom-line,
.shadow-top-line {
  position: relative; }
  .shadow-bottom-line:after,
  .shadow-top-line:after {
    content: "";
    width: 100%;
    height: 5px;
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    background-color: rgba(80, 80, 80, 0.09);
    background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(80, 80, 80, 0.09)), to(transparent));
    background-image: linear-gradient(to top, rgba(80, 80, 80, 0.09), transparent);
    /*background-image: -webkit-linear-gradient(top, rgba($fusion-500, 0.09), transparent);
		background-image: linear-gradient(top, rgba($fusion-500, 0.09), transparent);*/ }

.shadow-top-line:after {
  content: "";
  top: -5px;
  background-color: transparent;
  background-image: -webkit-gradient(linear, left bottom, left top, from(transparent), to(rgba(80, 80, 80, 0.09)));
  background-image: linear-gradient(to top, transparent, rgba(80, 80, 80, 0.09));
  /*background-image: -webkit-linear-gradient(top, transparent, rgba($fusion-500, 0.09));
		background-image: linear-gradient(top, transparent, rgba($fusion-500, 0.09));*/ }

/* press animation */
.press-scale-down {
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease; }
  .press-scale-down:active {
    -webkit-transform: scale(0.95);
    transform: scale(0.95); }

.hover-white:hover {
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.03)), to(rgba(29, 33, 41, 0.04)));
  background-image: linear-gradient(rgba(29, 33, 41, 0.03), rgba(29, 33, 41, 0.04)); }

.hover-white:active {
  background: #e7e7e7; }

/*

	DOC: In Bootstrap there is a small snippet added by the team for IE10 in windows 8 the 
	following comments by the author states:

		IE10 in Windows (Phone) 8

		Support for responsive views via media queries is kind of borked in IE10, for
		Surface/desktop in split view and for Windows Phone 8. This particular fix
		must be accompanied by a snippet of JavaScript to sniff the user agent and
		apply some conditional CSS to *only* the Surface/desktop Windows 8. Look at
		our Getting Started page for more information on this bug.

		For more information, see the following:

		Issue: https://github.com/twbs/bootstrap/issues/10497
		Docs: http://getbootstrap.com/getting-started/#support-ie10-width
		Source: http://timkadlec.com/2013/01/windows-phone-8-and-device-width/
		Source: http://timkadlec.com/2012/10/ie10-snap-mode-and-responsive-design/

		@-ms-viewport {
		width: device-width;
		}

	Solution: 
	http://msdn.microsoft.com/en-us/library/ie/hh771902(v=vs.85).aspx
	We add the following instead:

*/
body.desktop-detected {
  -ms-overflow-style: scrollbar; }

/* Reset elms pos when js-waves-off is used */
/*.js-waves-off {
	position: relative;
    overflow: hidden;
    user-select: none;
    z-index: 0;
}*/
/*.btn {
	box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 
				0 1px 2px rgba(0, 0, 0, 0.1);

	&:hover,
	&:active {
		box-shadow: none;
	}
}*/
/* change the white to any color ;) */
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  -webkit-text-fill-color: inherit !important; }

/* select background */
::-moz-selection {
  background: #505050;
  color: #fff; }
::selection {
  background: #505050;
  color: #fff; }

::-moz-selection {
  background: #505050;
  color: #fff; }

/* remove dotted line from focus */
input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none; }

/* IE input clear field "X" input remove */
::-ms-clear {
  width: 0;
  height: 0; }

/* links */
a {
  text-decoration: none !important; }

/* touch action */
a, area, button, [role="button"], input, label, select, summary, textarea {
  -ms-touch-action: manipulation;
      touch-action: manipulation; }

a[target]:not(.btn) {
  font-weight: 500;
  -webkit-text-decoration-skip: ink;
          text-decoration-skip-ink: auto;
  text-decoration: underline !important; }

/* btn active */
/*.btn.active, 
.btn:active {
    background-image: none;
    outline: 0;
    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
    box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
}*/
/* dot bullet */
.dot {
  /*width: 4px;
	height: 4px;
	display: inline-block;
	line-height: 0;
	border-radius: 100%;*/
  font-size: 4px !important;
  margin-right: 0.5rem !important;
  margin-left: -8px !important; }

/* forms */
/* fix alignment for custom controls */
/*.custom-control {
	display: flex;
    align-items: center;
}*/
select.custom-select {
  -webkit-appearance: none;
  -moz-appearance: none;
  text-indent: 1px;
  text-overflow: ''; }

select.custom-select::-ms-expand {
  display: none; }

/* bootstrap modal remove padding */
/* you need to disable this if you do not plan on using _addon-custom-scrollbar.scss */
body:not(.mod-main-boxed):not(.mobile-view-activated).chrome.modal-open {
  padding-right: 8px !important; }

body:not(.mobile-view-activated).mod-main-boxed.modal-open {
  padding-right: 0px !important; }

/* hover adjustment for close buttons */
.close:not(:disabled):not(.disabled):hover,
.close:not(:disabled):not(.disabled):focus {
  color: inherit; }

/* add borders to button groups */
.btn-group .btn:not([class*="btn-outline-"]):not(.btn-icon):not(.btn-light) {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  border-left: 1px solid rgba(0, 0, 0, 0.1); }

.input-group-prepend .btn:not([class*="btn-outline-"]):not(.btn-icon):not(:first-child) {
  border-left: 1px solid rgba(0, 0, 0, 0.1); }

.input-group-append .btn:not([class*="btn-outline-"]):not(.btn-icon):not(:first-child) {
  border-left: 1px solid rgba(0, 0, 0, 0.1); }

.btn-group-vertical .btn:not([class*="btn-outline-"]):not(:first-child),
.btn-group-vertical .btn-group {
  border-top: 1px solid rgba(0, 0, 0, 0.1); }

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /* IE10+ CSS styles go here */
  .text-gradient {
    background: transparent; }
  .nav-function-minify:not(.nav-function-top) .primary-nav .nav-menu > li > a + ul:before {
    left: -0.25rem !important; }
  .ie-only {
    display: inline-block !important; }
  /* table hover */
  .table-hover tbody tr:hover {
    -webkit-box-shadow: none;
            box-shadow: none;
    background-color: #fffaee; }
  /*  Counters IE bug
		DOC: If you've ever used CSS transitions on structural elements on your page, 
		you may have noticed a case where a transition occur immideately after page load.
		As a result the user may experience the illusion of "broken page elements"

		Below solution insures that pace's indication of "everything loaded" before applying the CSS transitions
	*/
  .pace-running .page-sidebar,
  .pace-running .page-sidebar *,
  .pace-running .page-content-wrapper {
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -ms-transition: none !important;
    -o-transition: none !important; } }

/* #MOBILE - mobile media related styles
========================================================================== */
/* contains most of the responsive styles for the app */
/********************************************************
					RESPONSIVE REFERENCES

.col-xs-	.col-sm-	.col-md-	.col-lg-	.col-xl-
<544px		≥544px		≥768px		≥992px		≥1200px

$grid-breakpoints: (
  // Extra small screen / phone
  xs: 0,
  // Small screen / phone
  sm: 544px,
  // Medium screen / tablet
  md: 768px,
  // Large screen / desktop
  lg: 992px,
  // Extra large screen / wide desktop
  xl: 1200px
) !default;

*********************************************************/
@media (min-width: 992px) {
  .page-header {
    padding: 0 2rem; }
  .page-content {
    padding: 1.5rem 2rem; }
    .page-content .panel {
      margin-bottom: 1.5rem; } }

/*@include media-breakpoint-up(xl) {
	.page-header {
		padding: 0 $header-inner-padding-x;
	}
	.page-content {
		padding: 1.5rem 2rem;

		.card.panel {
			margin-bottom: 1.5rem;
		}
	}
}*/
@media only screen and (max-width: 992px) {
  /*html {
		font-size: 18px;
	}*/
  .dropdown-menu .dropdown-menu {
    position: static;
    padding: .5rem 0 0 .75rem;
    width: 100%;
    border: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    -webkit-transition: none;
    transition: none; }
  .show .dropdown-menu .dropdown-menu {
    visibility: visible; }
  .dropdown-menu .dropdown-multilevel > .dropdown-item:first-child:after {
    display: none; }
  body {
    overflow-x: hidden; }
  .page-logo-text {
    font-size: 1rem; }
  .page-content-overlay {
    background: transparent;
    -webkit-transition: background 300ms;
    transition: background 300ms;
    position: fixed;
    z-index: 1001; }
  .page-wrapper {
    padding-left: 0;
    background: #fff; }
    .page-wrapper .page-header {
      padding: 0 1.5rem;
      width: 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.09); }
      .page-wrapper .page-header [data-toggle="dropdown"] + .dropdown-menu {
        right: 1.5rem; }
    .page-wrapper .page-sidebar {
      z-index: 2000;
      -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
      transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
      -webkit-transform: translate3d(-16.875rem, 0, 0);
      transform: translate3d(-16.875rem, 0, 0);
      position: fixed !important;
      top: 0;
      bottom: 0; }
      .page-wrapper .page-sidebar .primary-nav {
        overflow: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        height: calc(100% - 6.9375rem); }
        .page-wrapper .page-sidebar .primary-nav .nav-menu .dl-ref {
          vertical-align: text-top; }
    .page-wrapper .page-content {
      padding: 1.5rem 1.5rem;
      color: #222;
      font-size: 14px;
      min-height: calc(100vh - 6.9375rem); }
      .page-wrapper .page-content .breadcrumb > .breadcrumb-item {
        max-width: 80px; }
      .page-wrapper .page-content .subheader {
        margin-bottom: 1.5rem; }
        .page-wrapper .page-content .subheader .subheader-title {
          line-height: 32px;
          font-weight: 300;
          color: #22282d; }
          .page-wrapper .page-content .subheader .subheader-title small {
            font-size: 68%;
            letter-spacing: normal;
            margin-top: 0px;
            color: #181c21;
            overflow: hidden;
            width: calc(100% - 30px);
            font-weight: 300; }
      .page-wrapper .page-content .p-g {
        padding: 1.5rem; }
    .page-wrapper .page-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.09); }
  .header-function-fixed {
    /* this was conflicting with the new DOM change where we swtiched header with nav */
    /*&:not(.nav-function-fixed) {

			.page-sidebar {
				.page-logo {
					position: absolute !important;
					top:0px !important;
				}
			}

		}*/ }
    .header-function-fixed .page-header {
      margin-left: 0;
      left: 0;
      position: fixed;
      right: 0;
      top: 0;
      -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
      transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1); }
    .header-function-fixed .page-header,
    .header-function-fixed .page-logo {
      -webkit-box-shadow: none !important;
              box-shadow: none !important; }
    .header-function-fixed .page-content {
      margin-top: 4.125rem; }
  /* Push content */
  .nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-sidebar {
    -webkit-transform: translate3d(-16.875rem, 0, 0);
    transform: translate3d(-16.875rem, 0, 0); }
  .nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-header,
  .nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-content,
  .nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-footer,
  .nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-footer-push {
    -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
    transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1); }
  /* Off canvas */
  .nav-mobile-slide-out {
    min-height: 100vh !important;
    /*new*/ }
    .nav-mobile-slide-out .page-wrapper .page-sidebar {
      z-index: 0;
      -webkit-transition: none;
      transition: none;
      -webkit-transform: translate3d(0px, 0, 0);
      transform: translate3d(0px, 0, 0); }
    .nav-mobile-slide-out .page-wrapper .page-header,
    .nav-mobile-slide-out .page-wrapper .page-content,
    .nav-mobile-slide-out .page-wrapper .page-footer,
    .nav-mobile-slide-out .page-wrapper .page-footer-push {
      -webkit-transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
      transition: all 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0); }
    .nav-mobile-slide-out .page-wrapper .page-content {
      /* min-height: calc(100vh - 6.9375rem);  no longer needed here */
      background: #faf8fb; }
  /* mobile nav show & hide button */
  /* general */
  .mobile-nav-on {
    -ms-touch-action: none;
        touch-action: none;
    overflow: hidden;
    height: 100vh;
    /*.page-content:before {
			content:" ";
			position:fixed;
			z-index: $space;
			background:rgba(0,0,0,0);
			display: block;
			height: 100vh;
			width: 100vw;
			left: 0;
			top: 0;
		}*/
    /* Push content */
    /* Off canvas turned ON*/
    /* 'not' is ON by default */ }
    .mobile-nav-on .page-sidebar {
      border-right: 1px solid rgba(0, 0, 0, 0.03);
      -webkit-box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52);
              box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52); }
    .mobile-nav-on .page-content-overlay {
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.09); }
    .mobile-nav-on:not(.nav-mobile-push) .page-sidebar {
      -webkit-transform: translate3d(0px, 0, 0) !important;
      transform: translate3d(0px, 0, 0) !important; }
    .mobile-nav-on:not(.nav-mobile-push).nav-function-fixed:not(.nav-function-top) .page-sidebar {
      -webkit-transform: translate3d(0px, 0, 0) !important;
      transform: translate3d(0px, 0, 0) !important; }
    .mobile-nav-on.nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-sidebar {
      left: 0;
      -webkit-transform: translate3d(0px, 0, 0);
      transform: translate3d(0px, 0, 0); }
    .mobile-nav-on.nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-header,
    .mobile-nav-on.nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-content,
    .mobile-nav-on.nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-footer,
    .mobile-nav-on.nav-mobile-push:not(.nav-mobile-slide-out) .page-wrapper .page-footer-push {
      -webkit-transform: translate3d(16.875rem, 0, 0);
      transform: translate3d(16.875rem, 0, 0); }
    .mobile-nav-on.nav-mobile-slide-out .page-wrapper {
      overflow: hidden;
      /*.page-content:before {
					background:transparent !important;
				}*/ }
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-header,
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-content,
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-footer,
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-footer-push {
        -webkit-transform: translate3d(16.875rem, 0, 0);
        transform: translate3d(16.875rem, 0, 0); }
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-content-overlay {
        background: transparent !important;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        left: 16.875rem; }
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-header,
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-content,
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-footer,
      .mobile-nav-on.nav-mobile-slide-out .page-wrapper .page-footer-push {
        -webkit-box-shadow: 0 9px 0px 0px #faf8fb, 0 -9px 0px 0px #faf8fb, 12px 0 15px -4px rgba(0, 0, 0, 0.32), -12px 0 15px -4px rgba(0, 0, 0, 0.32);
                box-shadow: 0 9px 0px 0px #faf8fb, 0 -9px 0px 0px #faf8fb, 12px 0 15px -4px rgba(0, 0, 0, 0.32), -12px 0 15px -4px rgba(0, 0, 0, 0.32); }
    .mobile-nav-on.nav-mobile-no-overlay .page-wrapper {
      /*.page-content:before {
					background:rgba(0,0,0,0.3);
				}*/ }
      .mobile-nav-on.nav-mobile-no-overlay .page-wrapper .page-content-overlay {
        background: transparent;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0; } }

@media only screen and (max-width: 576px) {
  /* here we turn on mobile font for smaller screens */
  /*body {
		font-family: $mobile-page-font !important;
	}*/
  /* mobile nav search */
  .mobile-search-on:not(.mobile-nav-on) .page-header > * {
    display: none !important; }
  .mobile-search-on:not(.mobile-nav-on) .search {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; }
    .mobile-search-on:not(.mobile-nav-on) .search .app-forms {
      display: block !important;
      position: relative !important;
      width: 100%; }
      .mobile-search-on:not(.mobile-nav-on) .search .app-forms .btn-search-close {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        right: 10px;
        width: 26px;
        height: 26px;
        font-size: 1rem; }
      .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field {
        border: 1px solid #886ab5;
        padding-left: 1rem;
        padding-right: 3rem;
        width: 100%;
        max-width: none;
        background: #fff; }
        .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field:focus {
          border-color: #886ab5; }
  .mobile-search-on:not(.mobile-nav-on) [data-class="mobile-nav-on"] {
    display: none !important; }
  .page-header [data-toggle="dropdown"] + .dropdown-menu {
    width: calc(100% - 2rem) !important;
    right: 1rem !important; }
  .page-header,
  .page-content {
    padding-left: 1rem !important;
    padding-right: 1rem !important; }
  .primary-nav .nav-menu li a > .badge {
    font-size: 10px !important; }
  .card .card-header,
  .card .card-body {
    padding: 1rem; }
  .alert,
  .panel .panel-tag,
  .accordion .card .card-header .card-title {
    padding: 1rem; } }

/* changes content colors based on ambience light source of the user (experimental) */
/* DOCS : https://developer.mozilla.org/en-US/docs/Web/CSS/%40media/light-level */
/* The device is used in a environment with a light level in the ideal range for the screen, 
 * and which does not necessitate any particular adjustment.. */
/* The device is used in a dim environment, where excessive contrast and brightness would be 
 * distracting or uncomfortable to the reader. For example: night time, or a dimly 
 * illuminated indoor environment. */
/* The device is used in an exceptionally bright environment, causing the screen to be washed 
 * out and difficult to read. For example: bright daylight. */
/* #FORMS (customized bootstrap form elems)
========================================================================== */
/* text area */
.form-content-editable[contenteditable="true"] {
  overflow: auto;
  -webkit-user-modify: read-write-plaintext-only;
  line-height: normal; }
  .form-content-editable[contenteditable="true"]:focus {
    outline: 0; }
  .form-content-editable[contenteditable="true"]:empty:not(:focus):before {
    content: attr(data-placeholder);
    color: #909090; }
  .form-content-editable[contenteditable="true"]::-moz-selection {
    background: rgba(0, 132, 255, 0.2);
    color: #000; }
  .form-content-editable[contenteditable="true"]::selection {
    background: rgba(0, 132, 255, 0.2);
    color: #000; }
  .form-content-editable[contenteditable="true"]::-moz-selection {
    background: rgba(0, 132, 255, 0.2);
    color: #000; }

/*.form-control:not(.form-control-sm),
.custom-select:not(.custom-select-sm),
.input-group:not(.input-group-sm) {
	min-height: calc(2.25rem + 2px);
}*/
.form-label {
  font-weight: 500; }

/* select arrow */
/*select:not(.custom-select):not([multiple]) {
  background-image:
    linear-gradient(45deg, transparent 50%, red 60%),
    linear-gradient(135deg, red 40%, transparent 50%) !important;
  background-position:
    calc(100% - 30px) 14px,
    calc(100% - 20px) 14px,
    100% 0;
  background-size:
    10px 10px,
    10px 10px;
  background-repeat: no-repeat;
  -webkit-appearance: none;
  -moz-appearance: none;
}*/
/* fix */
.custom-range {
  -webkit-appearance: none;
  -moz-appearance: none; }

.custom-range::-moz-range-thumb {
  -moz-appearance: none; }

.custom-range::-webkit-slider-thumb {
  -webkit-appearance: none; }

/* add background to focused inpur prepend and append */
.form-control:focus ~ .input-group-prepend {
  background: #886ab5; }

.has-length .input-group-text {
  border-color: #886ab5; }
  .has-length .input-group-text + .input-group-text {
    border-left: 1px solid rgba(0, 0, 0, 0.1); }

.has-length .input-group-text:not([class^="bg-"]):not([class*=" bg-"]) {
  background: #886ab5;
  color: #fff !important; }

.input-group-text {
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out; }

/* input group animation for multiple inputs */
.input-group.input-group-multi-transition input[type="text"] {
  -webkit-transition: width 470ms cubic-bezier(0.34, 1.25, 0.3, 1);
  transition: width 470ms cubic-bezier(0.34, 1.25, 0.3, 1); }
  .input-group.input-group-multi-transition input[type="text"]:focus {
    width: 50%; }

/* BS form hack for checkbox tick mark */
.custom-checkbox .custom-control-label::after {
  background-size: 50% 50%; }

/* circle checkbox */
.custom-checkbox-circle .custom-control-label:before {
  border-radius: 50%; }

/* rounded radio */
.custom-radio-rounded .custom-control-label:before {
  border-radius: 4px; }

/* not sure if we need this? */
/*.custom-control {
	min-height: $custom-control-indicator-size;
}*/
/* make checked label bold */
input[type="radio"]:checked + .custom-control-label,
input[type="checkbox"]:checked + .custom-control-label {
  font-weight: 500; }

/* help block and validation feedback texts*/
.help-block {
  color: #909090; }

/* on feedback error */
.help-block, .invalid-feedback, .valid-feedback {
  font-size: 0.6875rem;
  margin-top: 0.325rem; }

/* when form group is last child show now margin */
.form-group:last-child,
.form-group:only-child {
  margin-bottom: 0; }

/* fix alignment for generic checkbox and radio */
.form-check-input {
  margin-top: 0; }

.form-check-label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.form-check {
  margin: 5px 0; }

/* #COMPONENTS (can be removed but may or may not impact other components)
========================================================================== */
/*@import '_modules/_form-switches';*/
@media (min-width: 1399px) {
  .page-sidebar {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  .header-function-fixed .page-wrapper,
  .header-function-fixed .page-sidebar,
  .nav-function-fixed .page-wrapper,
  .nav-function-fixed .page-sidebar,
  .header-function-fixed.nav-function-fixed .page-wrapper,
  .header-function-fixed.nav-function-fixed .page-sidebar {
    transform: none;
    -webkit-transform: none;
    -ms-transform: none; } }

/* #DEMO ELEMS - elements mostly used for demo (can be removed)
========================================================================== */
@media only screen and (max-width: 992px) {
  .mobile-view-activated #nff,
  .mobile-view-activated #nfm,
  .mobile-view-activated #nfh,
  .mobile-view-activated #nft,
  .mobile-view-activated #mmb {
    position: relative; }
    .mobile-view-activated #nff .onoffswitch-title,
    .mobile-view-activated #nfm .onoffswitch-title,
    .mobile-view-activated #nfh .onoffswitch-title,
    .mobile-view-activated #nft .onoffswitch-title,
    .mobile-view-activated #mmb .onoffswitch-title {
      color: #d58100 !important; }
    .mobile-view-activated #nff .onoffswitch-title-desc,
    .mobile-view-activated #nfm .onoffswitch-title-desc,
    .mobile-view-activated #nfh .onoffswitch-title-desc,
    .mobile-view-activated #nft .onoffswitch-title-desc,
    .mobile-view-activated #mmb .onoffswitch-title-desc {
      color: #ec9f28 !important; }
    .mobile-view-activated #nff:after,
    .mobile-view-activated #nfm:after,
    .mobile-view-activated #nfh:after,
    .mobile-view-activated #nft:after,
    .mobile-view-activated #mmb:after {
      content: "DISABLED";
      font-size: 10px;
      position: absolute;
      background: #ffebc1;
      width: 65px;
      text-align: center;
      border: 1px solid #ffb20e;
      height: 22px;
      line-height: 20px;
      border-radius: 10px;
      display: block;
      right: 13px;
      top: 26%;
      color: #1d1d1d; } }

.settings-panel .expanded.theme-colors {
  display: block;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  background: transparent;
  /*@include theme-button-color ($theme-1-fusion, $theme-1-primary, $theme-1-info, $theme-1-success, $theme-1-warning, $theme-1-danger)*/ }
  .settings-panel .expanded.theme-colors > ul {
    height: auto; }
    .settings-panel .expanded.theme-colors > ul > li [data-action] {
      width: 36px;
      height: 36px;
      margin-right: 4px;
      margin-bottom: 4px;
      border-radius: 4px; }
      .settings-panel .expanded.theme-colors > ul > li [data-action]:hover {
        opacity: 1; }
    .settings-panel .expanded.theme-colors > ul:last-child {
      margin-right: 0; }
  .settings-panel .expanded.theme-colors #myapp-0 {
    background: #886ab5;
    -webkit-box-shadow: inset 0 0 0 3px #2ba1ff;
            box-shadow: inset 0 0 0 3px #2ba1ff; }
  .settings-panel .expanded.theme-colors #myapp-1 {
    background: #b56a9f; }
  .settings-panel .expanded.theme-colors #myapp-2 {
    background: #9fcb3d; }
  .settings-panel .expanded.theme-colors #myapp-3 {
    background: #4679cc; }
  .settings-panel .expanded.theme-colors #myapp-4 {
    background: #2198F3; }
  .settings-panel .expanded.theme-colors #myapp-5 {
    background: #6ab5b4; }
  .settings-panel .expanded.theme-colors #myapp-6 {
    background: #dd5293; }
  .settings-panel .expanded.theme-colors #myapp-7 {
    background: #868e96; }
  .settings-panel .expanded.theme-colors #myapp-8 {
    background: #7c91df; }
  .settings-panel .expanded.theme-colors #myapp-9 {
    background: #e59c6c; }
  .settings-panel .expanded.theme-colors #myapp-10 {
    background: #778c85; }
  .settings-panel .expanded.theme-colors #myapp-11 {
    background: #a2b077; }
  .settings-panel .expanded.theme-colors #myapp-12 {
    background: #7976b3; }
  .settings-panel .expanded.theme-colors #myapp-13 {
    background: #55ce5f; }
  .settings-panel .expanded.theme-colors #myapp-14 {
    background: #5c4581; }
  .settings-panel .expanded.theme-colors #myapp-15 {
    background: #5c4581; }

.settings-panel:first-child h5 {
  margin-top: 0;
  padding-top: 5px; }

.settings-panel h5 {
  margin: 0;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 1rem 1rem 5px;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  display: block;
  overflow: hidden;
  text-decoration: none;
  margin-top: 5px;
  color: #505050;
  text-align: left; }
  .settings-panel h5 small {
    display: inline; }

.settings-panel .list {
  font-weight: 400;
  min-height: 45px;
  padding: 0.25rem 1rem 0.25rem 2rem;
  color: #666666;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }
  .settings-panel .list:hover {
    color: #333333;
    background: rgba(255, 255, 255, 0.7); }
    .settings-panel .list:hover .onoffswitch {
      -webkit-transform: scale(1.13);
      transform: scale(1.13); }
  .settings-panel .list .btn-switch {
    position: absolute;
    right: 1rem;
    margin: 0;
    top: 30%; }
  .settings-panel .list .onoffswitch-title {
    margin-top: 0.5px;
    font-size: 0.8125rem;
    display: block; }
  .settings-panel .list .onoffswitch-title-desc {
    display: block;
    font-size: 0.75rem;
    color: #989da5;
    text-transform: lowercase; }

.settings-panel .expanded {
  position: relative;
  display: none; }
  .settings-panel .expanded:before {
    border-bottom-color: #5d5d5d;
    bottom: 1px; }
  .settings-panel .expanded > ul {
    padding: 0;
    margin: 0;
    margin: 0 0 0 1rem;
    height: 50px; }
    .settings-panel .expanded > ul > li {
      display: inline-block;
      margin: 0;
      padding: 0; }
      .settings-panel .expanded > ul > li [data-action] {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-right: 2px;
        display: block;
        cursor: pointer;
        position: relative;
        -webkit-box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
                box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1); }
  .settings-panel .expanded .list {
    padding: 0;
    min-height: auto;
    margin: 0.5rem 0 0 1rem; }
    .settings-panel .expanded .list .btn-switch {
      margin-top: 2px;
      top: -2px;
      right: 0; }
    .settings-panel .expanded .list:hover {
      background: transparent; }

#saving {
  position: absolute;
  z-index: 1;
  top: 0;
  left: -40px;
  display: none; }

/* localstorage success fade animation */
.saving #saving {
  display: block; }

.saving [data-action="app-reset"] {
  opacity: 0.5;
  cursor: not-allowed !important; }

.color-disp-demo tr:first-child td {
  height: 100px;
  -webkit-box-shadow: inset 0 -5px 0 rgba(255, 255, 255, 0.8);
          box-shadow: inset 0 -5px 0 rgba(255, 255, 255, 0.8); }

.color-disp-demo tr td {
  border: none;
  padding-top: 7px;
  padding-bottom: 7px; }
  .color-disp-demo tr td:hover {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
    font-weight: 500; }

.cr-c {
  width: 21px;
  height: 21px;
  display: block;
  border-radius: 50%; }

.icon-demo {
  list-style: none;
  padding: 0; }
  .icon-demo li {
    border: 1px solid #7f8995;
    padding: 10px;
    display: inline-block;
    font-size: 20px;
    width: 60px;
    height: 60px;
    overflow: hidden;
    margin: 0 6px 0 0;
    text-align: center;
    background: #fff; }
    .icon-demo li:hover {
      -webkit-transform: scale(1.4);
      transform: scale(1.4); }

code[class*="language-"],
pre[class*="language-"] {
  display: none !important; }

.show-codes code[class*="language-"],
.show-codes pre[class*="language-"] {
  display: block !important; }

.container-demo {
  max-width: 100%;
  width: 100%; }

.bd-example {
  position: relative;
  padding: 1rem 0; }

.bd-example-row-flex-cols .row {
  min-height: 10rem;
  background-color: rgba(255, 0, 0, 0.1); }

.bd-example-row .row > .col,
.bd-example-row .row > [class^=col-] {
  padding-top: .75rem;
  padding-bottom: .75rem;
  background-color: rgba(86, 61, 124, 0.15);
  border: 1px solid rgba(86, 61, 124, 0.2); }

.bd-example-row .row + .row {
  margin-top: 1rem; }

.bd-highlight {
  background-color: rgba(86, 61, 124, 0.2);
  border: 1px solid rgba(86, 61, 124, 0.2); }

.demo {
  margin: 0; }
  .demo > * {
    margin: 0 .375rem 1rem 0 !important; }
  .demo.demo-no-mb > * {
    margin-bottom: 0 !important; }

.demo-v-spacing-sm > * + *,
.demo-v-spacing > * + *,
.demo-v-spacing-lg > * + * {
  margin-top: 0.875rem !important;
  margin-bottom: 0 !important; }

.demo-v-spacing-sm > *,
.demo-v-spacing > *,
.demo-v-spacing-lg > * {
  margin-bottom: 0 !important; }

.demo-v-spacing > * + * {
  margin-top: 1rem !important; }

.demo-v-spacing-lg > * + * {
  margin-top: 1.5rem !important; }

.demo-h-spacing > *:not(last-child):not(only-child) {
  margin-right: 1rem !important; }

/*.demo-vh-spacing > *:not(last-child):not(only-child) {
	margin: 0 .375rem 1rem 0 !important;
}*/
/* #_extensions - Components imported in alphabetical order (remove extensions from directory if not needed)
========================================================================== */
body:not(.mod-pace-custom) .pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
      user-select: none; }
  body:not(.mod-pace-custom) .pace .pace-progress {
    background: #886ab5;
    position: fixed;
    z-index: 2000;
    top: 0;
    right: 100%;
    width: 100%;
    height: 3px; }

body:not(.mod-pace-custom) .pace-inactive {
  display: none; }

body.mod-pace-custom {
  /*&.pace-done {
		.page-content {
			&:before{
				z-index:-2;
				display: none;
			}
		}
	}*/ }
  body.mod-pace-custom .pace {
    -webkit-pointer-events: none;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
        user-select: none;
    z-index: 2000;
    position: fixed;
    margin: auto;
    top: 45vh;
    left: 0;
    right: 0;
    height: 13px;
    border: 2px solid #fff;
    width: 15.18902rem;
    background: #fff;
    overflow: hidden; }
    body.mod-pace-custom .pace .pace-progress {
      -webkit-box-sizing: border-box;
              box-sizing: border-box;
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
      max-width: 15.18902rem;
      z-index: 2000;
      display: block;
      position: absolute;
      top: 0;
      right: 100%;
      height: 100%;
      width: 100%;
      background-color: #886ab5;
      background-image: linear-gradient(135deg, #886ab5 0%, #886ab5 25%, #6e4e9e 25%, #6e4e9e 50%, #886ab5 50%, #886ab5 75%, #6e4e9e 75%, #6e4e9e 100%);
      background-repeat: repeat;
      background-position: 0 0;
      background-size: 13px 13px;
      background-clip: content-box;
      animation: loading 0.5s linear infinite;
      -o-animation: loading 0.5s linear infinite;
      -moz-animation: loading 0.5s linear infinite;
      -webkit-animation: loading 0.5s linear infinite; }
  body.mod-pace-custom .pace-inactive {
    display: none; }
  body.mod-pace-custom.pace-running .page-content:before {
    content: '';
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #faf8fb;
    z-index: 1; }

@keyframes loading {
  from {
    background-position: 0 0; }
  to {
    background-position: -13px 0; } }

@-webkit-keyframes loading {
  from {
    background-position: 0 0; }
  to {
    background-position: -13px 0; } }

.slimScrollBar {
  border-radius: 3px !important; }

body:not(.no-slimscroll) .custom-scroll {
  overflow: hidden; }

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent; }
  .waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    opacity: 0;
    background: rgba(255, 255, 255, 0.6);
    background: radial-gradient(rgba(255, 255, 255, 0.3) 0, rgba(255, 255, 255, 0.4) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0) 70%);
    -webkit-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;
    -webkit-transition-property: -webkit-transform, opacity;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
    -webkit-transform: scale(0) translate(0, 0);
    transform: scale(0) translate(0, 0);
    pointer-events: none; }

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important; }

.waves-themed.btn-warning .waves-ripple, .waves-themed.btn-default .waves-ripple, .waves-themed.btn-outline-default .waves-ripple {
  background: rgba(0, 0, 0, 0.6);
  background: radial-gradient(rgba(0, 0, 0, 0.1) 0, rgba(0, 0, 0, 0.2) 40%, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0.4) 60%, rgba(0, 0, 0, 0) 70%); }

.waves-themed.btn-primary .waves-ripple, .waves-themed.btn-outline-primary .waves-ripple, .waves-themed.btn-info .waves-ripple, .waves-themed.btn-outline-info .waves-ripple, .waves-themed.btn-danger .waves-ripple, .waves-themed.btn-outline-danger .waves-ripple, .waves-themed.btn-success .waves-ripple, .waves-themed.btn-outline-success .waves-ripple, .waves-themed.btn-dark .waves-ripple, .waves-themed.btn-outline-dark .waves-ripple {
  background: rgba(255, 255, 255, 0.6);
  background: radial-gradient(rgba(255, 255, 255, 0.3) 0, rgba(255, 255, 255, 0.4) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0) 70%); }

.page-sidebar .primary-nav .nav-menu li a.waves-themed .waves-ripple {
  background: rgba(39, 28, 55, 0.6);
  background: radial-gradient(rgba(39, 28, 55, 0.2) 0, rgba(39, 28, 55, 0.3) 40%, rgba(39, 28, 55, 0.4) 50%, rgba(39, 28, 55, 0.5) 60%, rgba(39, 28, 55, 0) 70%); }

.panel-hdr:not([class^="bg-"]):not([class*=" bg-"]) .waves-themed.btn-toolbar-master .waves-ripple,
.waves-themed.nav-link .waves-ripple {
  background: rgba(136, 106, 181, 0.6);
  background: radial-gradient(rgba(136, 106, 181, 0.2) 0, rgba(136, 106, 181, 0.3) 40%, rgba(136, 106, 181, 0.4) 50%, rgba(136, 106, 181, 0.5) 60%, rgba(136, 106, 181, 0) 70%); }

/* #_plugins - Components imported in alphabetical order (remove plugins from directory if not needed)
========================================================================== */
/*@import '_plugins/_plugin-*.scss';*/
/* #ANIMATION - CSS animations and keyframes
========================================================================== */
@-webkit-keyframes seconds {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }

@keyframes seconds {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }

@-webkit-keyframes delayed {
  99% {
    visibility: hidden; }
  100% {
    visibility: visible; } }

@keyframes delayed {
  99% {
    visibility: hidden; }
  100% {
    visibility: visible; } }

@keyframes subtle {
  0% {
    opacity: 1; }
  100% {
    opacity: 0.2; } }

@-webkit-keyframes subtle {
  0% {
    opacity: 1; }
  100% {
    opacity: 0.2; } }

@-webkit-keyframes highlight {
  from {
    background: #ffebc1; }
  to {
    background: transparent; } }

@keyframes highlight {
  from {
    background: #ffebc1; }
  to {
    background: transparent; } }

.highlight {
  -webkit-animation: highlight 1.5s;
  animation: highlight 1.5s; }

@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg); }
  to {
    -webkit-transform: rotate(360deg); } }

@keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

/*.spinner {
  margin: 5px;
  height: 20px;
  width: 20px;
  animation: rotate 0.7s infinite linear;
  border: 2px solid $color-primary;
  border-right-color: transparent;
  border-radius: 50%;
}*/
.fa-spin-4x {
  -webkit-animation: spin 0.5s infinite linear;
          animation: spin 0.5s infinite linear; }

.fa-spin-2x {
  -webkit-animation: spin 1s infinite linear;
          animation: spin 1s infinite linear; }

/*=== Animations start here  ===*/
/*=== FADE IN DOWN ===*/
@-webkit-keyframes animateFadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

@keyframes animateFadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

/*==== FADE IN UP ===*/
@-webkit-keyframes animateFadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

@keyframes animateFadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

/*=== FADE IN LEFT ===*/
@-webkit-keyframes animateFadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

@keyframes animateFadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

/*==== FADE IN RIGHT ===*/
@-webkit-keyframes animateFadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

@keyframes animateFadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0); }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none; } }

/* remove transition delay */
.no-transition-delay {
  -webkit-transition-delay: 0ms !important;
          transition-delay: 0ms !important; }

/* fade transitions for page elements */
.page-content > .alert {
  animation: animateFadeInUp 0.3s;
  -webkit-animation: animateFadeInUp 0.3s; }

.page-content > .card,
.page-content > .row {
  animation: animateFadeInUp 0.7s;
  -webkit-animation: animateFadeInUp 0.7s; }

.tab-content > .active:not(.fade) {
  animation: animateFadeInUp 0.5s;
  -webkit-animation: animateFadeInUp 0.5s; }

/* repeated transitions */
.fadeinup {
  animation: animateFadeInUp 0.5s;
  -webkit-animation: animateFadeInUp 0.5s; }

.fadeindown {
  animation: animateFadeInDown 0.5s;
  -webkit-animation: animateFadeInDown 0.5s; }

.fadeinleft {
  animation: animateFadeInLeft 0.5s;
  -webkit-animation: animateFadeInLeft 0.5s; }

.fadeinright {
  animation: animateFadeInRight 0.5s;
  -webkit-animation: animateFadeInRight 0.5s; }

/* #MODS - Layout manipulation
========================================================================== */
@media (min-width: 1399px) {
  .mod-main-boxed.mod-bg-1 [data-class="mod-bg-1"]:before,
  .mod-main-boxed.mod-bg-2 [data-class="mod-bg-2"]:before,
  .mod-main-boxed.mod-bg-3 [data-class="mod-bg-3"]:before,
  .mod-main-boxed.mod-bg-4 [data-class="mod-bg-4"]:before {
    content: " ";
    display: block;
    border-radius: 50%;
    background: inherit;
    background-image: none;
    border: 2px solid rgba(0, 0, 0, 0.2);
    position: absolute;
    top: 15px;
    left: 15px;
    height: 20px;
    width: 20px; }
  .mod-main-boxed.mod-bg-1 [data-class="mod-bg-1"]:after,
  .mod-main-boxed.mod-bg-2 [data-class="mod-bg-2"]:after,
  .mod-main-boxed.mod-bg-3 [data-class="mod-bg-3"]:after,
  .mod-main-boxed.mod-bg-4 [data-class="mod-bg-4"]:after {
    content: " ";
    height: inherit;
    width: inherit;
    border: 5px solid rgba(0, 0, 0, 0.1);
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 50%; }
  .mod-main-boxed .settings-panel .expanded {
    display: block; }
    .mod-main-boxed .settings-panel .expanded > ul > li {
      display: inline-block;
      margin: 0;
      padding: 0; }
      .mod-main-boxed .settings-panel .expanded > ul > li [data-action][data-class="mod-bg-1"] {
        background-image: url("../img/backgrounds/prev-bg-1.png"); }
      .mod-main-boxed .settings-panel .expanded > ul > li [data-action][data-class="mod-bg-2"] {
        background-image: url("../img/backgrounds/prev-bg-2.png"); }
      .mod-main-boxed .settings-panel .expanded > ul > li [data-action][data-class="mod-bg-3"] {
        background-image: url("../img/backgrounds/prev-bg-3.png"); }
      .mod-main-boxed .settings-panel .expanded > ul > li [data-action][data-class="mod-bg-4"] {
        background-image: url("../img/backgrounds/prev-bg-4.png"); }
      .mod-main-boxed .settings-panel .expanded > ul > li:last-child [data-action="toggle"] {
        margin-right: 0; }
  .mod-main-boxed.mod-bg-1 {
    background-image: url("../img/backgrounds/bg-1.png"); }
  .mod-main-boxed.mod-bg-2 {
    background-image: url("../img/backgrounds/bg-2.png"); }
  .mod-main-boxed.mod-bg-3 {
    background-image: url("../img/backgrounds/bg-3.png"); }
  .mod-main-boxed.mod-bg-4 {
    background-image: url("../img/backgrounds/bg-4.png"); }
  .mod-main-boxed.mod-fixed-bg {
    background-attachment: fixed; } }

.mod-clean-page-bg .page-content-wrapper {
  background: #fff !important; }

.mod-clean-page-bg .page-header {
  border-bottom-color: rgba(150, 123, 189, 0.13); }

.mod-color-blind .page-wrapper {
  -webkit-filter: grayscale(65%);
  filter: grayscale(55%); }

.mod-color-blind [class*="btn-"].active {
  background-image: none !important; }

.mod-disable-animation *,
.mod-disable-animation *:before,
.mod-disable-animation *:after {
  -webkit-transition: none !important;
  transition: none !important;
  -webkit-animation: none !important;
  animation: none !important; }

.mod-hide-info-card {
  /*.page-logo {
		border-bottom: 1px solid lighten($header-logo-border-bottom, 13%);
	}*/ }
  .mod-hide-info-card .page-sidebar .info-card {
    display: none; }

.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu a > [class*='fa-'],
.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu a > .ni,
.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu a > img {
  display: none; }

.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu a > .badge {
  right: 40px;
  left: auto;
  top: 30%; }

.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu span > [class*='fa-'],
.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu span > .ni,
.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu span > img {
  display: none; }

.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu .dl-ref {
  display: none; }

.mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu li > ul > li > a {
  padding-left: 2.9375rem; }
  .mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) .page-sidebar .primary-nav .nav-menu li > ul > li > a + ul > li > a {
    padding-left: 4.1875rem; }

.mod-high-contrast .nav-menu li a,
.mod-high-contrast .nav-title,
.mod-high-contrast .nav-menu li a [class*='fa-'],
.mod-high-contrast .nav-menu li a .ni,
.mod-high-contrast .dl-ref,
.mod-high-contrast .btn {
  text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;
  color: #fff !important;
  font-weight: 500 !important; }

.mod-high-contrast .subheader-title,
.mod-high-contrast h1,
.mod-high-contrast h2,
.mod-high-contrast h3,
.mod-high-contrast h4,
.mod-high-contrast h5,
.mod-high-contrast .settings-panel-title a,
.mod-high-contrast .panel-header,
.mod-high-contrast .badge-detached,
.mod-high-contrast .btn-secondary,
.mod-high-contrast .btn-default,
.mod-high-contrast .page-header .btn,
.mod-high-contrast [class*="btn-outline-"] {
  text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
  color: #000 !important;
  font-weight: 500; }

.mod-high-contrast .subheader-title small,
.mod-high-contrast .breadcrumb > li > a,
.mod-high-contrast .page-content,
.mod-high-contrast h1 small,
.mod-high-contrast h2 small,
.mod-high-contrast h3 small,
.mod-high-contrast h4 small,
.mod-high-contrast h5,
.mod-high-contrast h6,
.mod-high-contrast p,
.mod-high-contrast .btn-switch + .onoffswitch-title,
.mod-high-contrast .onoffswitch-title + .onoffswitch-title-desc,
.mod-high-contrast .panel-container,
.mod-high-contrast .panel-header .btn {
  text-shadow: none;
  color: #000 !important;
  font-weight: normal !important; }

.mod-lean-subheader .subheader {
  margin: 0; }
  .mod-lean-subheader .subheader .subheader-title {
    margin-top: 0;
    font-size: 1rem;
    display: inline-block;
    font-weight: 400;
    text-transform: capitalize;
    margin-bottom: 1.5rem; }
    .mod-lean-subheader .subheader .subheader-title:not(:only-child) {
      margin-top: 23px; }
    .mod-lean-subheader .subheader .subheader-title small {
      font-size: 0.875rem;
      display: inline-block;
      text-transform: capitalize; }
      .mod-lean-subheader .subheader .subheader-title small:before {
        content: " - "; }
  .mod-lean-subheader .subheader .breadcrumb {
    position: absolute;
    top: 0; }

@media (min-width: 1399px) {
  .mod-main-boxed {
    /*
		why did we add this again?
		this was buggy when open modal with mod main boxed then click on logo for the dropdown, it won't close
		&.nav-function-fixed:not(.nav-function-top):not(.header-function-fixed) {
			.page-sidebar {
				transform: translateX(0) !important;
				box-shadow: none;
			}

			.page-wrapper {
				transform: translateX(0) !important;
			}

		}*/ }
    .mod-main-boxed .page-wrapper {
      max-width: 1399px;
      margin: 0 auto; }
    .mod-main-boxed:not(.nav-function-top) #nff {
      position: relative; }
      .mod-main-boxed:not(.nav-function-top) #nff .onoffswitch-title {
        color: #d58100; }
      .mod-main-boxed:not(.nav-function-top) #nff .onoffswitch-title-desc {
        color: #ec9f28; }
      .mod-main-boxed:not(.nav-function-top) #nff:after {
        content: "DISABLED";
        display: block;
        position: absolute;
        background: #ffebc1;
        font-size: 0.625rem;
        width: 65px;
        text-align: center;
        border: 1px solid #ffb20e;
        height: 22px;
        line-height: 20px;
        border-radius: 10px;
        right: 13px;
        top: 26%;
        color: #1d1d1d; }
    .mod-main-boxed.header-function-fixed .page-wrapper .page-header {
      width: 100%;
      max-width: 1397px;
      margin: 0 auto !important; }
    .mod-main-boxed.header-function-fixed:not(.nav-function-top):not(.nav-function-fixed) .page-wrapper .page-sidebar {
      position: absolute !important;
      top: 0;
      bottom: 0; }
    .mod-main-boxed.header-function-fixed:not(.nav-function-top):not(.nav-function-hidden):not(.nav-function-minify) .page-wrapper .page-header {
      padding-left: 18.875rem; }
    .mod-main-boxed.header-function-fixed:not(.nav-function-top):not(.nav-function-hidden):not(.nav-function-minify):not(.nav-function-fixed) .page-content {
      margin-left: 16.875rem; }
    .mod-main-boxed.header-function-fixed.nav-function-minify:not(.nav-function-top):not(.nav-function-hidden) .page-wrapper .page-header {
      padding-left: 6.6875rem; }
    .mod-main-boxed.header-function-fixed.nav-function-minify:not(.nav-function-top):not(.nav-function-hidden) .page-wrapper .page-content-wrapper {
      margin-left: 4.6875rem; }
    .mod-main-boxed.nav-function-hidden:not(.nav-function-top) .page-sidebar:after {
      position: absolute;
      left: 16.875rem; }
    .mod-main-boxed.nav-function-hidden.nav-function-minify .page-sidebar:after {
      position: absolute;
      left: 4.6875rem; }
    .mod-main-boxed.nav-function-fixed:not(.nav-function-top) .page-wrapper .page-sidebar {
      position: absolute; }
      .mod-main-boxed.nav-function-fixed:not(.nav-function-top) .page-wrapper .page-sidebar .page-logo {
        position: fixed;
        top: 0;
        z-index: 950;
        -webkit-box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
                box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1); }
      .mod-main-boxed.nav-function-fixed:not(.nav-function-top) .page-wrapper .page-sidebar .primary-nav {
        margin-top: 4.125rem; }
    .mod-main-boxed.nav-function-fixed:not(.nav-function-top).nav-function-hidden .page-logo {
      position: absolute !important;
      -webkit-transition: none;
      transition: none; }
    .mod-main-boxed .page-wrapper {
      border-left: 1px solid rgba(0, 0, 0, 0.15);
      border-right: 1px solid rgba(0, 0, 0, 0.15);
      -webkit-box-shadow: 5px 0 20px 0px rgba(0, 0, 0, 0.1), -5px 0 20px 0px rgba(0, 0, 0, 0.1);
              box-shadow: 5px 0 20px 0px rgba(0, 0, 0, 0.1), -5px 0 20px 0px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative; }
    .mod-main-boxed:not(.header-function-fixed):not(.nav-function-top):not(.modal-open):not(.panel-fullscreen) .page-wrapper {
      -webkit-transform: translateX(0) !important;
              transform: translateX(0) !important; } }

/* Hierarchical Navigation */
.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li a > .dl-ref:first-child {
  margin-left: 0 !important; }

.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul {
  /* addressing all second, third children */ }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before {
    content: "";
    display: block;
    position: absolute;
    z-index: 1;
    left: 2.5625rem;
    top: 44px;
    bottom: 0;
    border-left: 1px solid #614b82; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul > li a:after {
    content: "";
    display: block;
    position: absolute;
    width: 0.4rem;
    height: 0.4rem;
    background-color: #876fab;
    left: 2.4rem;
    top: calc(50% - 0.3rem);
    border: 1px solid #333;
    border-radius: 50%;
    z-index: 1; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul > li a:hover:after {
    border-color: transparent; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul > li li > a:after {
    content: "";
    display: none; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul li a i {
    margin-left: 0 !important; }

html:not(.root-text-sm):not(.root-text-lg):not(.root-text-xl) [data-class="root-text"] {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }

.root-text-sm {
  font-size: 15px; }
  .root-text-sm [data-class="root-text-sm"] {
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important;
            box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important; }

.root-text-lg {
  font-size: 17px; }
  .root-text-lg [data-class="root-text-lg"] {
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important;
            box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important; }

.root-text-xl {
  font-size: 18px; }
  .root-text-xl [data-class="root-text-xl"] {
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important;
            box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important; }

.mod-bigger-font {
  font-size: 18px; }

/* #COLORS - we place this here so it can override other colors as needed
========================================================================== */
.bg-primary-50 {
  background-color: #ccbfdf;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-100 {
  background-color: #beaed7;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-200 {
  background-color: #b19dce;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-300 {
  background-color: #a38cc6;
  color: white; }
  .bg-primary-300:hover {
    color: white; }

.bg-primary-400 {
  background-color: #967bbd;
  color: white; }
  .bg-primary-400:hover {
    color: white; }

.bg-primary-500 {
  background-color: #886ab5;
  color: white; }
  .bg-primary-500:hover {
    color: white; }

.bg-primary-600 {
  background-color: #7a59ad;
  color: white; }
  .bg-primary-600:hover {
    color: white; }

.bg-primary-700 {
  background-color: #6e4e9e;
  color: white; }
  .bg-primary-700:hover {
    color: white; }

.bg-primary-800 {
  background-color: #62468d;
  color: white; }
  .bg-primary-800:hover {
    color: white; }

.bg-primary-900 {
  background-color: #563d7c;
  color: white; }
  .bg-primary-900:hover {
    color: white; }

.color-primary-50 {
  color: #ccbfdf; }

.color-primary-100 {
  color: #beaed7; }

.color-primary-200 {
  color: #b19dce; }

.color-primary-300 {
  color: #a38cc6; }

.color-primary-400 {
  color: #967bbd; }

.color-primary-500 {
  color: #886ab5; }

.color-primary-600 {
  color: #7a59ad; }

.color-primary-700 {
  color: #6e4e9e; }

.color-primary-800 {
  color: #62468d; }

.color-primary-900 {
  color: #563d7c; }

.bg-success-50 {
  background-color: #7aece0;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-100 {
  background-color: #63e9db;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-200 {
  background-color: #4de5d5;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-300 {
  background-color: #37e2d0;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-400 {
  background-color: #21dfcb;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-500 {
  background-color: #1dc9b7;
  color: white; }
  .bg-success-500:hover {
    color: white; }

.bg-success-600 {
  background-color: #1ab3a3;
  color: white; }
  .bg-success-600:hover {
    color: white; }

.bg-success-700 {
  background-color: #179c8e;
  color: white; }
  .bg-success-700:hover {
    color: white; }

.bg-success-800 {
  background-color: #13867a;
  color: white; }
  .bg-success-800:hover {
    color: white; }

.bg-success-900 {
  background-color: #107066;
  color: white; }
  .bg-success-900:hover {
    color: white; }

.color-success-50 {
  color: #7aece0; }

.color-success-100 {
  color: #63e9db; }

.color-success-200 {
  color: #4de5d5; }

.color-success-300 {
  color: #37e2d0; }

.color-success-400 {
  color: #21dfcb; }

.color-success-500 {
  color: #1dc9b7; }

.color-success-600 {
  color: #1ab3a3; }

.color-success-700 {
  color: #179c8e; }

.color-success-800 {
  color: #13867a; }

.color-success-900 {
  color: #107066; }

.bg-info-50 {
  background-color: #9acffa;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-100 {
  background-color: #82c4f8;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-200 {
  background-color: #6ab8f7;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-300 {
  background-color: #51adf6;
  color: white; }
  .bg-info-300:hover {
    color: white; }

.bg-info-400 {
  background-color: #39a1f4;
  color: white; }
  .bg-info-400:hover {
    color: white; }

.bg-info-500 {
  background-color: #2196F3;
  color: white; }
  .bg-info-500:hover {
    color: white; }

.bg-info-600 {
  background-color: #0d8aee;
  color: white; }
  .bg-info-600:hover {
    color: white; }

.bg-info-700 {
  background-color: #0c7cd5;
  color: white; }
  .bg-info-700:hover {
    color: white; }

.bg-info-800 {
  background-color: #0a6ebd;
  color: white; }
  .bg-info-800:hover {
    color: white; }

.bg-info-900 {
  background-color: #0960a5;
  color: white; }
  .bg-info-900:hover {
    color: white; }

.color-info-50 {
  color: #9acffa; }

.color-info-100 {
  color: #82c4f8; }

.color-info-200 {
  color: #6ab8f7; }

.color-info-300 {
  color: #51adf6; }

.color-info-400 {
  color: #39a1f4; }

.color-info-500 {
  color: #2196F3; }

.color-info-600 {
  color: #0d8aee; }

.color-info-700 {
  color: #0c7cd5; }

.color-info-800 {
  color: #0a6ebd; }

.color-info-900 {
  color: #0960a5; }

.bg-warning-50 {
  background-color: #ffebc1;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-100 {
  background-color: #ffe3a7;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-200 {
  background-color: #ffdb8e;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-300 {
  background-color: #ffd274;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-400 {
  background-color: #ffca5b;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-500 {
  background-color: #ffc241;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-600 {
  background-color: #ffba28;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-700 {
  background-color: #ffb20e;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-700:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-800 {
  background-color: #f4a500;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-800:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-900 {
  background-color: #da9400;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-900:hover {
    color: rgba(0, 0, 0, 0.8); }

.color-warning-50 {
  color: #ffebc1; }

.color-warning-100 {
  color: #ffe3a7; }

.color-warning-200 {
  color: #ffdb8e; }

.color-warning-300 {
  color: #ffd274; }

.color-warning-400 {
  color: #ffca5b; }

.color-warning-500 {
  color: #ffc241; }

.color-warning-600 {
  color: #ffba28; }

.color-warning-700 {
  color: #ffb20e; }

.color-warning-800 {
  color: #f4a500; }

.color-warning-900 {
  color: #da9400; }

.bg-danger-50 {
  background-color: #feb7d9;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-100 {
  background-color: #fe9ecb;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-200 {
  background-color: #fe85be;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-300 {
  background-color: #fe6bb0;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-400 {
  background-color: #fd52a3;
  color: white; }
  .bg-danger-400:hover {
    color: white; }

.bg-danger-500 {
  background-color: #fd3995;
  color: white; }
  .bg-danger-500:hover {
    color: white; }

.bg-danger-600 {
  background-color: #fd2087;
  color: white; }
  .bg-danger-600:hover {
    color: white; }

.bg-danger-700 {
  background-color: #fc077a;
  color: white; }
  .bg-danger-700:hover {
    color: white; }

.bg-danger-800 {
  background-color: #e7026e;
  color: white; }
  .bg-danger-800:hover {
    color: white; }

.bg-danger-900 {
  background-color: #ce0262;
  color: white; }
  .bg-danger-900:hover {
    color: white; }

.color-danger-50 {
  color: #feb7d9; }

.color-danger-100 {
  color: #fe9ecb; }

.color-danger-200 {
  color: #fe85be; }

.color-danger-300 {
  color: #fe6bb0; }

.color-danger-400 {
  color: #fd52a3; }

.color-danger-500 {
  color: #fd3995; }

.color-danger-600 {
  color: #fd2087; }

.color-danger-700 {
  color: #fc077a; }

.color-danger-800 {
  color: #e7026e; }

.color-danger-900 {
  color: #ce0262; }

.bg-fusion-50 {
  background-color: #909090;
  color: white; }
  .bg-fusion-50:hover {
    color: white; }

.bg-fusion-100 {
  background-color: #838383;
  color: white; }
  .bg-fusion-100:hover {
    color: white; }

.bg-fusion-200 {
  background-color: #767676;
  color: white; }
  .bg-fusion-200:hover {
    color: white; }

.bg-fusion-300 {
  background-color: dimgray;
  color: white; }
  .bg-fusion-300:hover {
    color: white; }

.bg-fusion-400 {
  background-color: #5d5d5d;
  color: white; }
  .bg-fusion-400:hover {
    color: white; }

.bg-fusion-500 {
  background-color: #505050;
  color: white; }
  .bg-fusion-500:hover {
    color: white; }

.bg-fusion-600 {
  background-color: #434343;
  color: white; }
  .bg-fusion-600:hover {
    color: white; }

.bg-fusion-700 {
  background-color: #363636;
  color: white; }
  .bg-fusion-700:hover {
    color: white; }

.bg-fusion-800 {
  background-color: #2a2a2a;
  color: white; }
  .bg-fusion-800:hover {
    color: white; }

.bg-fusion-900 {
  background-color: #1d1d1d;
  color: white; }
  .bg-fusion-900:hover {
    color: white; }

.color-fusion-50 {
  color: #909090; }

.color-fusion-100 {
  color: #838383; }

.color-fusion-200 {
  color: #767676; }

.color-fusion-300 {
  color: dimgray; }

.color-fusion-400 {
  color: #5d5d5d; }

.color-fusion-500 {
  color: #505050; }

.color-fusion-600 {
  color: #434343; }

.color-fusion-700 {
  color: #363636; }

.color-fusion-800 {
  color: #2a2a2a; }

.color-fusion-900 {
  color: #1d1d1d; }

.color-white {
  color: #fff; }

.color-black {
  color: #222222; }

.bg-primary-gradient {
  background-image: linear-gradient(250deg, rgba(86, 61, 124, 0.7), transparent); }

.bg-danger-gradient {
  background-image: linear-gradient(250deg, rgba(206, 2, 98, 0.7), transparent); }

.bg-info-gradient {
  background-image: linear-gradient(250deg, rgba(9, 96, 165, 0.7), transparent); }

.bg-warning-gradient {
  background-image: linear-gradient(250deg, rgba(218, 148, 0, 0.7), transparent); }

.bg-success-gradient {
  background-image: linear-gradient(250deg, rgba(16, 112, 102, 0.7), transparent); }

.bg-fusion-gradient {
  background-image: linear-gradient(250deg, rgba(29, 29, 29, 0.7), transparent); }

/* #APP related modules (print, fullscreen, etc)
========================================================================== */
/* Custom Webkit Scrollbar */
/* http://css-tricks.com/custom-scrollbars-in-webkit/ */
body:not(.mobile-detected)::-webkit-scrollbar,
body:not(.mobile-detected).modal-open .modal::-webkit-scrollbar {
  height: 8px;
  width: 8px; }

body:not(.mobile-detected)::-webkit-scrollbar:hover,
body:not(.mobile-detected).modal-open .modal::-webkit-scrollbar:hover {
  background-color: rgba(0, 0, 0, 0.01); }

body:not(.mobile-detected)::-webkit-scrollbar-track-piece,
body:not(.mobile-detected).modal-open .modal::-webkit-scrollbar-track-piece {
  background-color: #efefef; }

body:not(.mobile-detected)::-webkit-scrollbar-track-piece:hover,
body:not(.mobile-detected).modal-open .modal::-webkit-scrollbar-track-piece:hover {
  background-color: #d0d0d0; }

body:not(.mobile-detected)::-webkit-scrollbar-thumb:vertical,
body:not(.mobile-detected).modal-open .modal::-webkit-scrollbar-thumb:vertical {
  background-color: #838383; }

body:not(.mobile-detected)::-webkit-scrollbar-thumb:vertical:hover,
body:not(.mobile-detected).modal-open .modal::-webkit-scrollbar-thumb:vertical:hover {
  background-color: dimgray; }

/* 
 * Left Panel custom scroll 
 */
.page-sidebar .primary-nav::-webkit-scrollbar-track-piece {
  background-color: #efefef; }

.page-sidebar .primary-nav::-webkit-scrollbar-thumb:vertical {
  background-color: #666; }

.page-sidebar .primary-nav::-webkit-scrollbar {
  height: 4px;
  width: 4px; }

.page-sidebar .primary-nav:hover::-webkit-scrollbar-corner {
  width: 40px; }

.page-sidebar .primary-nav::-webkit-scrollbar-track-piece {
  background-color: #efefef; }

.page-sidebar .primary-nav::-webkit-scrollbar-thumb:vertical {
  background-color: #666; }

:-webkit-full-screen [data-action="app-fullscreen"] {
  color: #cccccc; }

:-moz-full-screen [data-action="app-fullscreen"] {
  color: #cccccc; }

@media print {
  @page {
    size: letter portrait;
    margin: 1cm;
    padding: 0;
    border: none;
    border-collapse: collapse; }
  *:not(.keep-print-font) {
    color: #333 !important;
    background: transparent !important;
    font-family: Arial, Helvetica, sans-serif !important;
    letter-spacing: normal !important;
    font-size: 10pt !important;
    line-height: 1.7 !important;
    text-transform: none !important;
    -webkit-transition: none !important;
    transition: none !important; }
  table {
    font-size: 80%; }
  .card,
  .card-body,
  .container {
    display: inline;
    padding: 0;
    margin: 0;
    border: 0; }
  a:link {
    font-weight: bold;
    text-decoration: underline;
    color: #06c; }
  .subheader-title {
    font-size: 14pt !important; }
    .subheader-title small {
      font-size: 12pt !important; }
  h1, h2, h3, h4, h5, h6 {
    font-weight: bold !important; }
  .page-sidebar,
  .btn,
  .page-header,
  .page-footer {
    display: none !important; }
  .page-wrapper,
  .page-content,
  .container.card {
    padding: 0;
    display: block;
    margin: 0;
    border: 0 !important;
    width: auto;
    float: none; }
  .panel-header > * {
    font-weight: bold !important; }
  .card.panel {
    border-color: #333 !important; }
  .clearfix:after {
    content: '';
    clear: both;
    display: table; } }

/* #OVERRIDE - You can override any of the variables through this file
========================================================================== */

/*# sourceMappingURL=app.bundle.css.map */
