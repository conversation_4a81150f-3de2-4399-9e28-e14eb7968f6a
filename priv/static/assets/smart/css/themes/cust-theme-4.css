/* #THEME COLOR (variable overrides)
========================================================================== */
/* #GLOBAL IMPORTS
========================================================================== */
/* #IMPORTS ~~
========================================================================== */
/*---------------------------------------------------
    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) 
  -------------------------------- -------------------
    LESS ELEMENTS made by Dmitry Fadeyev (http://fadeyev.net)
    SASS port by Samuel Beek (http://samuelbeek.com) 
  ---------------------------------------------------*/
/*------------------------
    Usage

    h1 {
      font-size: rem(32);
    }

    OR:

    h1 {
      font-size: rem(32px);
    }
------------------------*/
/*------------------------
  FADE IN
  e.g. @include fadeIn( 2s );
------------------------*/
/*------------------------
mixin that calculates if text needs to be light or dark
depending on the background color passed.

From this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast

usage:
@include text-contrast($bgcolor)
      
Color brightness is determined by the following formula: 
((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000
------------------------*/
/*------------------------
 color factory 
  eg: @include paint($blue-grey-50, bg-blue-grey-50);
------------------------*/
/* backface visibility */
/* generate theme button */
/*  THEME COLORs
========================================================================== */
/* Looks good on chrome default color profile */
/* looks good in sRGB but washed up on chrome default 
$color-primary:						#826bb0;
$color-success:						#31cb55;
$color-info:						#5e93ec;
$color-warning:						#eec559;
$color-danger:						#dc4b92;
$color-fusion:						darken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */
/*  Color Polarity
========================================================================== */
/*  PAINTBUCKET MIXER
========================================================================== */
/* the grays */
/* the sapphires */
/* the emeralds */
/* the amethyths */
/* the topaz */
/* the rubies */
/* the graphites */
/*  Define universal border difition (div outlines, etc)
========================================================================== */
/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)
========================================================================== */
/* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */
/* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/
/* global var used for spacing*/
/* Uniform Padding variable */
/* Heads up! This is a global scoped variable - changing may impact the whole template */
/*   BOOTSTRAP OVERRIDES (bootstrap variables)
========================================================================== */
/* usage: theme-colors("primary"); */
/* forms */
/*$input-height:							calc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */
/* links */
/* checkbox */
/*$custom-file-height-inner:				calc(2.25rem - 1px);*/
/* not part of bootstrap variable */
/* custom checkbox */
/* custom range */
/* select */
/* badge */
/* cards */
/*border radius*/
/* alert */
/* toast */
/* breadcrumb */
/* input button */
/* nav link */
/* nav, tabs, pills */
/* tables */
/* dropdowns */
/* dropdowns sizes */
/* popovers */
/* tooltips */
/* modal */
/* reference guide
http://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/
8px = 0.5rem
9px = 0.5625rem
10px = 0.625rem
11px = 0.6875rem
12px = 0.75rem
13px = 0.8125rem
14px = 0.875rem
15px = 0.9375rem
16px = 1rem (base)
17px = 1.0625rem
18px = 1.125rem
19px = 1.1875rem
20px = 1.25rem
21px = 1.3125rem
22px = 1.375rem
24px = 1.5rem
25px = 1.5625rem
26px = 1.625rem
28px = 1.75rem
30px = 1.875rem
32px = 2rem
34px = 2.125rem
36px = 2.25rem
38px = 2.375rem
40px = 2.5rem
*/
/* Fonts */
/* carousel */
/*  BASE VARS
========================================================================== */
/* font vars below will auto change to rem values using function rem($value)*/
/* 11px   */
/* 12px   */
/* 12.5px */
/* 14px   */
/* 15px   */
/* 16px   */
/* 28px   */
/*  Font Family
========================================================================== */
/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */
/*  ANIMATIONS
========================================================================== */
/* this addresses all animation related to nav hide to nav minify */
/*  Z-INDEX declearation
========================================================================== */
/* we adjust bootstrap z-index to be higher than our higest z-index*/
/*  CUSTOM ICON PREFIX 
========================================================================== */
/*  PRINT CSS (landscape or portrait)
========================================================================== */
/* landscape or portrait */
/* auto, letter */
/*  Common Element Variables
========================================================================== */
/* Z-index decleartion "birds eye view"
========================================================================== */
/*  Components
========================================================================== */
/*  PAGE HEADER STUFF
========================================================================== */
/* colors */
/* height */
/* logo */
/* try not to go beywond the width of $main_nav_width value */
/* you may need to change this depending on your logo design */
/* adjust this as you see fit : left, right, center */
/* icon font size (not button) */
/* search input box */
/* suggestion: #ccced0*/
/* btn */
/* dropdown: app list */
/* badge */
/* COMPONENTS & MODS */
/*  NAVIGATION STUFF

Guide:

aside.page-sidebar ($nav-width, $nav-background)
	.page-logo
	.primary-nav
		.info-card
		ul.nav-menu
			li
				a (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)
					icon 
					span
					collapse-sign 
					
				ul.nav-menu-sub-one  
					li
						a ($nav-level-1... $nav-sub-link-height)
							span
							collapse-sign

						ul.nav-menu-sub-two
							li
								a ($nav-level-2... $nav-sub-link-height)
									span

		p.nav-title ($nav-title-*...)


========================================================================== */
/* main navigation */
/* left panel */
/* nav parent level-0 */
/* nav icon sizes */
/* badge default */
/* all child */
/* nav title */
/* nav Minify */
/* when the menu pops on hover */
/* navigation Width */
/* partial visibility of the menu */
/* top navigation */
/* nav Info Card (appears below the logo) */
/* width is auto */
/* nav DL labels for all child */
/* will be pulled to left as a negative value */
/*   MISC Settings
========================================================================== */
/* List Table */
/*   PAGE SETTINGS
========================================================================== */
/*   PAGE BREADCRUMB 
========================================================================== */
/*   PAGE COMPONENT PANELS 
========================================================================== */
/*   PAGE COMPONENT PROGRESSBARS 
========================================================================== */
/*   PAGE COMPONENT MESSENGER 
========================================================================== */
/*   FOOTER
========================================================================== */
/*   GLOBALS
========================================================================== */
/* ACCESSIBILITIES */
/* PLACEHOLDER 
============================================= 

EXAMPLE:

%bg-image {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

.image-one {
    @extend %bg-image;
    background-image:url(/img/image-one.jpg");
}

RESULT:

.image-one, .image-two {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

*/
.page-logo, .page-sidebar, .nav-footer, .bg-brand-gradient {
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(178, 45, 226, 0.18)), to(transparent));
  background-image: linear-gradient(270deg, rgba(178, 45, 226, 0.18), transparent);
  background-color: #0f619f; }

/*
%shadow-hover {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);
  }
}
*/
.btn-default {
  background-color: #f5f5f5;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f5f5f5), to(#f1f1f1));
  background-image: linear-gradient(to top, #f5f5f5, #f1f1f1);
  color: #444; }
  .btn-default:hover {
    border: 1px solid #c6c6c6; }
  .btn-default:focus {
    border-color: #6abaf7 !important; }

.header-function-fixed .btn-switch[data-class="header-function-fixed"], .nav-function-fixed .btn-switch[data-class="nav-function-fixed"], .nav-function-minify .btn-switch[data-class="nav-function-minify"], .nav-function-hidden .btn-switch[data-class="nav-function-hidden"], .nav-function-top .btn-switch[data-class="nav-function-top"], .nav-mobile-push .btn-switch[data-class="nav-mobile-push"], .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"], .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"], .mod-main-boxed .btn-switch[data-class="mod-main-boxed"], .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"], .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"], .mod-pace-custom .btn-switch[data-class="mod-pace-custom"], .mod-bigger-font .btn-switch[data-class="mod-bigger-font"], .mod-high-contrast .btn-switch[data-class="mod-high-contrast"], .mod-color-blind .btn-switch[data-class="mod-color-blind"], .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"], .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"], .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"], .mod-disable-animation .btn-switch[data-class="mod-disable-animation"], .mod-nav-link .btn-switch[data-class="mod-nav-link"], .mod-app-rtl .btn-switch[data-class="mod-app-rtl"] {
  color: #fff;
  background: #2198F3 !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"]:after, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"]:after, .nav-function-minify .btn-switch[data-class="nav-function-minify"]:after, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"]:after, .nav-function-top .btn-switch[data-class="nav-function-top"]:after, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"]:after, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"]:after, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"]:after, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"]:after, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"]:after, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"]:after, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"]:after, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"]:after, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"]:after, .mod-color-blind .btn-switch[data-class="mod-color-blind"]:after, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"]:after, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"]:after, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"]:after, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"]:after, .mod-nav-link .btn-switch[data-class="mod-nav-link"]:after, .mod-app-rtl .btn-switch[data-class="mod-app-rtl"]:after {
    background: #fff !important;
    color: #2198F3 !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"] + .onoffswitch-title, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"] + .onoffswitch-title, .nav-function-minify .btn-switch[data-class="nav-function-minify"] + .onoffswitch-title, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"] + .onoffswitch-title, .nav-function-top .btn-switch[data-class="nav-function-top"] + .onoffswitch-title, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"] + .onoffswitch-title, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"] + .onoffswitch-title, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"] + .onoffswitch-title, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"] + .onoffswitch-title, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"] + .onoffswitch-title, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"] + .onoffswitch-title, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"] + .onoffswitch-title, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"] + .onoffswitch-title, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"] + .onoffswitch-title, .mod-color-blind .btn-switch[data-class="mod-color-blind"] + .onoffswitch-title, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"] + .onoffswitch-title, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"] + .onoffswitch-title, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"] + .onoffswitch-title, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"] + .onoffswitch-title, .mod-nav-link .btn-switch[data-class="mod-nav-link"] + .onoffswitch-title, .mod-app-rtl .btn-switch[data-class="mod-app-rtl"] + .onoffswitch-title {
    color: #2198F3; }

.nav-mobile-slide-out #nmp .onoffswitch-title, .nav-mobile-slide-out #nmno .onoffswitch-title, .nav-function-top #mnl .onoffswitch-title, .nav-function-minify #mnl .onoffswitch-title, .mod-hide-nav-icons #mnl .onoffswitch-title, .nav-function-top #nfh .onoffswitch-title {
  color: #d58100 !important; }

.nav-mobile-slide-out #nmp .onoffswitch-title-desc, .nav-mobile-slide-out #nmno .onoffswitch-title-desc, .nav-function-top #mnl .onoffswitch-title-desc, .nav-function-minify #mnl .onoffswitch-title-desc, .mod-hide-nav-icons #mnl .onoffswitch-title-desc, .nav-function-top #nfh .onoffswitch-title-desc {
  color: #ec9f28 !important; }

.header-btn {
  border: 1px solid #d3d6da;
  color: #a6a6a6; }
  .header-btn:hover {
    border-color: #2198F3;
    background: #51aef6;
    color: #fff; }

.nav-mobile-slide-out #nmp:after,
.nav-mobile-slide-out #nmno:after, .nav-function-top #mnl:after,
.nav-function-minify #mnl:after,
.mod-hide-nav-icons #mnl:after, .nav-function-top #nfh:after {
  background: #ffd193;
  border: 1px solid #df8000;
  color: #15171a; }

/* #GLOBAL IMPORTS
========================================================================== */
/*@import '_imports/_global-import';*/
/* #FRAMEWORK - Structure and layout files. (**DO NOT** change order)
                DOC: you can disable unused _modules
========================================================================== */
/* contains root variables to be used with css (see docs) */
/* html and body base styles */
html body {
  background-color: #fff; }

.header-icon {
  color: #666666; }
  .header-icon:not(.btn) > [class*='fa-']:first-child,
  .header-icon:not(.btn) > .ni:first-child {
    color: #2198F3; }
  .header-icon:not(.btn):hover > [class*='fa-']:only-child,
  .header-icon:not(.btn):hover > .ni {
    color: #404040; }
  .header-icon:not(.btn)[data-toggle="dropdown"] {
    /* header dropdowns */
    /* note: important rules to override popper's inline classes */
    /* end header dropdowns */ }
    .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] {
      color: #404040; }
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > [class*='fa-']:first-child,
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > .ni:first-child {
        color: #404040 !important; }
    .header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu {
      border-color: #ccc; }
  .header-icon:hover {
    color: #404040; }

.page-header {
  background-color: #fff; }

#search-field {
  background: transparent;
  border: 1px solid transparent; }

.dropdown-icon-menu > ul {
  background: #fff; }

.notification li.unread {
  background: #ffe4c0; }

.notification li > :first-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06); }
  .notification li > :first-child:hover {
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.03)), to(rgba(29, 33, 41, 0.04)));
    background-image: linear-gradient(rgba(29, 33, 41, 0.03), rgba(29, 33, 41, 0.04)); }

.notification .name {
  color: #222222; }

.notification .msg-a,
.notification .msg-b {
  color: #555555; }

.notification.notification-layout-2 li {
  background: #f9f9f9; }
  .notification.notification-layout-2 li.unread {
    background: #fff; }
  .notification.notification-layout-2 li > :first-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04); }

.notification.notification-layout-2:hover {
  cursor: pointer; }

.app-list-item {
  color: #666666; }
  .app-list-item:hover {
    border: 1px solid #e3e3e3; }
  .app-list-item:active {
    border-color: #2198F3; }

@media (min-width: 992px) {
  .header-function-fixed.nav-function-top .page-header {
    -webkit-box-shadow: 0px 0px 28px 2px rgba(9, 97, 165, 0.13);
            box-shadow: 0px 0px 28px 2px rgba(9, 97, 165, 0.13); } }

.nav-title {
  color: #1b90e9; }

.nav-menu li.open > a {
  color: white; }

.nav-menu li.active {
  /* arrow that appears next to active/selected items */ }
  .nav-menu li.active > a {
    color: white;
    background-color: rgba(255, 255, 255, 0.04);
    -webkit-box-shadow: inset 3px 0 0 #2198F3;
            box-shadow: inset 3px 0 0 #2198F3; }
    .nav-menu li.active > a:hover > [class*='fa-'],
    .nav-menu li.active > a:hover > .ni {
      color: #6dabda; }
  .nav-menu li.active > ul {
    display: block; }
  .nav-menu li.active:not(.open) > a:before {
    color: #24b3a4; }

.nav-menu li a {
  color: #82c2f3; }
  .nav-menu li a .dl-ref.label {
    color: rgba(255, 255, 255, 0.7); }
  .nav-menu li a > [class*='fa-'],
  .nav-menu li a > .ni {
    color: #2595ea; }
  .nav-menu li a.collapsed .nav-menu-btn-sub-collapse {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg); }
  .nav-menu li a:hover {
    color: white;
    background-color: rgba(0, 0, 0, 0.1); }
    .nav-menu li a:hover .badge {
      color: #fff; }
    .nav-menu li a:hover > [class*='fa-'],
    .nav-menu li a:hover > .ni {
      color: #6dabda; }
    .nav-menu li a:hover > .badge {
      -webkit-box-shadow: 0 0 0 1px rgba(19, 119, 196, 0.8);
              box-shadow: 0 0 0 1px rgba(19, 119, 196, 0.8);
      border: 1px solid rgba(19, 119, 196, 0.8); }
  .nav-menu li a:focus {
    color: white; }
    .nav-menu li a:focus .badge {
      color: #fff; }

.nav-menu li b.collapse-sign {
  color: #39a3f4; }

.nav-menu li > ul {
  background-color: rgba(0, 0, 0, 0.1); }
  .nav-menu li > ul li a {
    color: #6ab7f1; }
    .nav-menu li > ul li a > [class*='fa-'],
    .nav-menu li > ul li a > .ni {
      color: #2595ea; }
    .nav-menu li > ul li a > .badge {
      color: #fff;
      background-color: #FC1349; }
    .nav-menu li > ul li a:hover {
      color: white;
      background-color: rgba(0, 0, 0, 0.1); }
      .nav-menu li > ul li a:hover > .nav-link-text > [class*='fa-'],
      .nav-menu li > ul li a:hover > .nav-link-text > .ni {
        color: #6dabda; }
  .nav-menu li > ul li.active > a {
    color: white;
    background-color: transparent; }
    .nav-menu li > ul li.active > a > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a > .nav-link-text > .ni {
      color: white; }
    .nav-menu li > ul li.active > a:hover > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a:hover > .nav-link-text > .ni {
      color: #6dabda; }
  .nav-menu li > ul li > ul li.active > a {
    color: white; }
  .nav-menu li > ul li > ul li a {
    color: #61b2f0; }
    .nav-menu li > ul li > ul li a:hover {
      color: white; }
    .nav-menu li > ul li > ul li a > .badge {
      color: #fff;
      background-color: #FC1349;
      border: 1px solid #434a51; }

/* nav clean elements */
.nav-menu-clean {
  background: #fff; }
  .nav-menu-clean li a {
    color: #434a51 !important; }
    .nav-menu-clean li a span {
      color: #434a51 !important; }
    .nav-menu-clean li a:hover {
      background-color: #f4f4f4 !important; }

/* nav bordered elements */
.nav-menu-bordered {
  border: 1px solid rgba(0, 0, 0, 0.08); }
  .nav-menu-bordered li a {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08); }

.nav-filter input[type="text"] {
  background: rgba(0, 0, 0, 0.4);
  color: #fff; }
  .nav-filter input[type="text"]:not(:focus) {
    border-color: rgba(0, 0, 0, 0.1); }
  .nav-filter input[type="text"]:focus {
    border-color: #1585dc; }

.info-card {
  color: #fff; }
  .info-card .info-card-text {
    text-shadow: #000 0 1px; }

@media (min-width: 992px) {
  .nav-function-top {
    /* correct search field color */ }
    .nav-function-top #search-field {
      color: #fff; }
    .nav-function-top:not(.header-function-fixed) #nff {
      position: relative; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title {
        color: #d58100; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title-desc {
        color: #ec9f28; }
      .nav-function-top:not(.header-function-fixed) #nff:after {
        background: #ffd193;
        border: 1px solid #df8000;
        color: #15171a; }
    .nav-function-top .page-header {
      background-image: -webkit-gradient(linear, right top, left top, from(rgba(178, 45, 226, 0.18)), to(transparent));
      background-image: linear-gradient(270deg, rgba(178, 45, 226, 0.18), transparent);
      background-color: #0f619f;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(9, 97, 165, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(9, 97, 165, 0.13); }
      .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child,
      .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child {
        color: #51aef6; }
        .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child:hover,
        .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child:hover {
          color: #82c5f8; }
      .nav-function-top .page-header .badge.badge-icon {
        -webkit-box-shadow: 0 0 0 1px #0d8cee;
                box-shadow: 0 0 0 1px #0d8cee; }
    .nav-function-top .page-sidebar {
      background: #fff;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(9, 97, 165, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(9, 97, 165, 0.13); }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li.active > a:before {
        color: #24b3a4; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .ni,
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'] {
        color: inherit; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .collapse-sign {
        color: #58aeef; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li a {
        color: #0f619f; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul {
        background: #1274c0; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a {
          color: #82c2f3; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li ul {
          background: #1274c0; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li:hover > a {
          background: rgba(0, 0, 0, 0.1);
          color: #fff; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:after {
          background: transparent; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {
          color: #1274c0; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li:hover > a {
        color: #2198F3;
        background: transparent; } }

@media (min-width: 992px) {
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu li.active.open > a:before {
    color: #24b3a4; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > .nav-link-text {
    background: trasparent; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul {
    background-color: #0f619f; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul:before {
      color: #0f619f; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover {
    overflow: visible; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a {
      background: #116cb2;
      color: #fff; }
      .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child {
        background: #0f619f; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child:before {
          color: #0f619f; }
  .nav-function-minify:not(.nav-function-top) .page-header [data-class="nav-function-minify"] {
    background: #434a51;
    border-color: #2c3136 !important;
    color: #fff !important; } }

.nav-footer .nav-footer-buttons > li > a {
  color: #40a2ed; }

.nav-function-fixed .nav-footer {
  background: #0f619f; }
  .nav-function-fixed .nav-footer:before {
    background: rgba(19, 125, 206, 0.2);
    background: -webkit-gradient(linear, left top, right top, from(#0f619f), color-stop(50%, #168be5), color-stop(50%, #168be5), to(#0f619f));
    background: linear-gradient(to right, #0f619f 0%, #168be5 50%, #168be5 50%, #0f619f 100%); }

@media (min-width: 992px) {
  .nav-function-minify .nav-footer {
    background-color: #0e5b96; }
    .nav-function-minify .nav-footer [data-class="nav-function-minify"] {
      color: #2595ea; }
    .nav-function-minify .nav-footer:hover {
      background-color: #1069ad; }
      .nav-function-minify .nav-footer:hover [data-class="nav-function-minify"] {
        color: #6dabda; } }

.page-content-wrapper {
  background-color: #ebf6fd; }

.subheader-icon {
  color: #6dabda; }

.subheader-title {
  color: #434a51;
  text-shadow: #fff 0 1px; }
  .subheader-title small {
    color: #717d89; }

.page-footer {
  background: #fff;
  color: #4d4d4d; }

.accordion .card .card-header {
  background-color: #f7f9fa; }
  .accordion .card .card-header .card-title {
    color: #2198F3; }
    .accordion .card .card-header .card-title.collapsed {
      color: #717d89; }

.accordion.accordion-clean .card-header {
  background: #fff; }

.accordion.accordion-hover .card-header {
  background: #fff; }
  .accordion.accordion-hover .card-header:hover .card-title.collapsed {
    color: #fff;
    background-color: #51aef6; }

.accordion.accordion-hover .card-title:not(.collapsed) {
  color: #fff;
  background-color: #2198F3; }

/* 	DEV NOTE: The reason why we had to add this layer for alert colors is because BS4 
	does not allow you to add your own alert colors via variable control rather 
	through a systemetic agent that changes the theme colors. 

	REF: https://github.com/twbs/bootstrap/issues/24341#issuecomment-337457218
*/
.alert-primary {
  color: #376a90;
  background-color: #e1effa;
  border-color: #b4d5ef; }

.alert-success {
  color: #74d13d;
  background-color: white;
  border-color: #d3fabc; }

.alert-danger {
  color: #c0022e;
  background-color: #febecd;
  border-color: #fd7897; }

.alert-warning {
  color: #935400;
  background-color: #ffe2bb;
  border-color: #ffa937; }

.alert-info {
  color: #8b09ba;
  background-color: #f5defd;
  border-color: #d87cf9; }

.alert-secondary {
  color: #434a51;
  background-color: #f4f5f6;
  border-color: #d3d6da; }

.badge.badge-icon {
  background-color: #FC1349;
  color: #fff;
  -webkit-box-shadow: 0 0 0 1px #fff;
          box-shadow: 0 0 0 1px #fff; }

/* btn switch */
.btn-switch {
  background: #5a636d;
  color: white; }
  .btn-switch:hover {
    color: white; }
  .btn-switch:after {
    color: white; }
  .btn-switch.active {
    color: #fff;
    background: #2198F3; }
    .btn-switch.active:before {
      color: white; }
    .btn-switch.active:after {
      background: #fff;
      color: #2198F3; }

/* button used to close filter and mobile search */
.btn-search-close {
  color: #fff; }

/* buttons used in the header section of the page */
.header-btn[data-class='mobile-nav-on'] {
  border-color: #d90334;
  background-color: #f2033b;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f2033b), to(#c0022e));
  background-image: linear-gradient(to top, #f2033b, #c0022e);
  color: #fff; }

/* dropdown btn */
/* used on info card pulldown filter */
.pull-trigger-btn {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 0, 0, 0.4);
  color: #fff !important;
  -webkit-box-shadow: 0px 0px 2px rgba(33, 152, 243, 0.3);
          box-shadow: 0px 0px 2px rgba(33, 152, 243, 0.3); }
  .pull-trigger-btn:hover {
    background: #2198F3;
    border-color: #0d8cee; }

/* btn misc */
.btn-outline-default {
  color: #212529;
  border-color: #E5E5E5; }
  .btn-outline-default:hover, .btn-outline-default:not(:disabled):not(.disabled):active, .btn-outline-default:not(:disabled):not(.disabled).active,
  .show > .btn-outline-default.dropdown-toggle {
    color: #212529;
    background-color: #f9f9f9;
    border-color: #E5E5E5; }
  .btn-outline-default.disabled, .btn-outline-default:disabled {
    color: #212529; }

/* btn shadows */
.btn-primary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(33, 152, 243, 0.5);
          box-shadow: 0 2px 6px 0 rgba(33, 152, 243, 0.5); }

.btn-secondary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5);
          box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5); }

.btn-success {
  -webkit-box-shadow: 0 2px 6px 0 rgba(105, 251, 19, 0.5);
          box-shadow: 0 2px 6px 0 rgba(105, 251, 19, 0.5); }

.btn-info {
  -webkit-box-shadow: 0 2px 6px 0 rgba(187, 27, 244, 0.5);
          box-shadow: 0 2px 6px 0 rgba(187, 27, 244, 0.5); }

.btn-warning {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 154, 19, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 154, 19, 0.5); }

.btn-danger {
  -webkit-box-shadow: 0 2px 6px 0 rgba(252, 19, 73, 0.5);
          box-shadow: 0 2px 6px 0 rgba(252, 19, 73, 0.5); }

.btn-light {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5); }

.btn-dark {
  -webkit-box-shadow: 0 2px 6px 0 rgba(67, 74, 81, 0.5);
          box-shadow: 0 2px 6px 0 rgba(67, 74, 81, 0.5); }

.btn-icon-light {
  color: rgba(255, 255, 255, 0.7) !important;
  border-color: transparent !important; }
  .btn-icon-light:not(.active):not(:active):not(:hover):not(:focus) {
    color: rgba(255, 255, 255, 0.7) !important; }
  .btn-icon-light:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.2) !important; }

.card-header {
  background-color: #f7f9fa; }

.carousel-control-prev:hover {
  background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

.carousel-control-next:hover {
  background: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to left, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

/* dropdown menu multi-level */
.dropdown-menu .dropdown-menu {
  background: #fff; }

.dropdown-menu .dropdown-multilevel:hover > .dropdown-item:not(.disabled) {
  background: #f8f9fa;
  color: #0c7ed5; }

.chat-segment-get .chat-message {
  background: #f1f0f0; }

.chat-segment-sent .chat-message {
  background: #69FB13; }

/* transparent modal */
.modal-transparent .modal-content {
  -webkit-box-shadow: 0 1px 15px 1px rgba(9, 97, 165, 0.3);
          box-shadow: 0 1px 15px 1px rgba(9, 97, 165, 0.3); }

.modal-transparent .modal-content {
  background: rgba(11, 39, 61, 0.85); }

.panel {
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  /* panel fullscreen */
  /* panel locked */ }
  .panel.panel-fullscreen {
    /* make panel header bigger */ }
    .panel.panel-fullscreen .panel-hdr {
      -webkit-box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(8, 83, 141, 0.1);
              box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(8, 83, 141, 0.1); }
  .panel.panel-locked:not(.panel-fullscreen) .panel-hdr:active h2:before {
    color: #FC1349; }

/* panel tag can be used globally */
.panel-tag {
  background: #eef7fd; }

/* panel header */
.panel-hdr {
  background: #fff; }

/* panel tap highlight */
.panel-sortable:not(.panel-locked) .panel-hdr:active {
  border-top-color: rgba(81, 174, 246, 0.7);
  border-left-color: rgba(33, 152, 243, 0.7);
  border-right-color: rgba(33, 152, 243, 0.7); }
  .panel-sortable:not(.panel-locked) .panel-hdr:active + .panel-container {
    border-color: transparent rgba(33, 152, 243, 0.7) rgba(13, 140, 238, 0.7); }

/*.panel-sortable .panel-hdr:active,
.panel-sortable .panel-hdr:active + .panel-container {
	@include transition-border(0.4s, ease-out);
}*/
.panel-sortable.panel-locked .panel-hdr:active {
  border-top-color: #fd4570;
  border-left-color: #dc3545;
  border-right-color: #dc3545; }
  .panel-sortable.panel-locked .panel-hdr:active + .panel-container {
    border-color: transparent #dc3545 #dc3545; }

/* panel toolbar (sits inside panel header) */
.panel-toolbar .btn-panel {
  /* add default colors for action buttons */ }
  .panel-toolbar .btn-panel[data-action="panel-collapse"], .panel-toolbar .btn-panel.js-panel-collapse {
    background: #69FB13; }
  .panel-toolbar .btn-panel[data-action="panel-fullscreen"], .panel-toolbar .btn-panel.js-panel-fullscreen {
    background: #FF9A13; }
  .panel-toolbar .btn-panel[data-action="panel-close"], .panel-toolbar .btn-panel.js-panel-close {
    background: #FC1349; }

/* placeholder */
.panel-placeholder {
  background-color: #d0e6f7; }
  .panel-placeholder:before {
    background: #d0e6f7; }

.mod-panel-clean .panel-hdr {
  background: #fff;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f7f7f7), to(#fff));
  background-image: linear-gradient(to bottom, #f7f7f7, #fff); }

@media only screen and (max-width: 420px) {
  /* making mobile spacing a little narrow */
  .panel .panel-hdr {
    color: #060606; } }

.popover .arrow {
  border-color: inherit; }

.menu-item,
label.menu-open-button {
  background: #2198F3;
  color: #fff !important; }
  .menu-item:hover,
  label.menu-open-button:hover {
    background: #0c7ed5; }

.app-shortcut-icon {
  background: #ecf0f1;
  color: #ecf0f1; }

.menu-open:checked + .menu-open-button {
  background: #434a51; }

/* backgrounds */
.bg-white {
  background-color: #fff;
  color: #666666; }

.bg-faded {
  background-color: #f7f9fa; }

.bg-offwhite-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(#f1f3f4));
  background-image: linear-gradient(to top, #fff, #f1f3f4); }

.bg-subtlelight {
  background-color: #f6fbff; }

.bg-subtlelight-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(#f6fbff));
  background-image: linear-gradient(to top, #fff, #f6fbff); }

.bg-highlight {
  background-color: #ffe4c0; }

.bg-gray-50 {
  background-color: #f9f9f9; }

.bg-gray-100 {
  background-color: #f8f9fa; }

.bg-gray-200 {
  background-color: #e9ecef; }

.bg-gray-300 {
  background-color: #dee2e6; }

.bg-gray-400 {
  background-color: #ced4da; }

.bg-gray-500 {
  background-color: #adb5bd; }

.bg-gray-600 {
  background-color: #6c757d; }

.bg-gray-700 {
  background-color: #495057; }

.bg-gray-800 {
  background-color: #343a40; }

.bg-gray-900 {
  background-color: #212529; }

/* borders */
.border-faded {
  border: 1px solid rgba(21, 23, 26, 0.07); }

/* hover any bg */
/* inherits the parent background on hover */
.hover-bg {
  background: #fff; }

/* states */
.state-selected {
  background: #f6e2fe !important; }

/* demo window */
.demo-window {
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12); }
  .demo-window:before {
    background: #e5e5e5; }
  .demo-window:after,
  .demo-window .demo-window-content:before,
  .demo-window .demo-window-content:after {
    background: #ccc; }

.bg-trans-gradient {
  background: linear-gradient(250deg, #ad39d6, #2d96e7); }

.notes {
  background: #f9f4b5; }

/* disclaimer class */
.disclaimer {
  color: #a2a2a2; }

/* online status */
.status {
  position: relative; }
  .status:before {
    background: #434a51;
    border: 2px solid #fff; }
  .status.status-success:before {
    background: #69FB13; }
  .status.status-danger:before {
    background: #FC1349; }
  .status.status-warning:before {
    background: #FF9A13; }

/* display frame */
.frame-heading {
  color: #929ca6; }

.frame-wrap {
  background: white; }

/* time stamp */
.time-stamp {
  color: #66707b; }

/* data-hasmore */
[data-hasmore] {
  color: #fff; }
  [data-hasmore]:before {
    background: rgba(0, 0, 0, 0.4); }

/* code */
code {
  background: #eff3f6; }

/* select background */
::-moz-selection {
  background: #434a51;
  color: #fff; }
::selection {
  background: #434a51;
  color: #fff; }

::-moz-selection {
  background: #434a51;
  color: #fff; }

@media only screen and (max-width: 992px) {
  .page-wrapper {
    background: #fff; }
    .page-wrapper .page-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.09); }
    .page-wrapper .page-content {
      color: #222; }
      .page-wrapper .page-content .subheader .subheader-title {
        color: #22282d; }
        .page-wrapper .page-content .subheader .subheader-title small {
          color: #181c21; }
      .page-wrapper .page-content .p-g {
        padding: 1.5rem; }
    .page-wrapper .page-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.09); }
  /* Off canvas */
  .nav-mobile-slide-out .page-wrapper .page-content {
    background: #ebf6fd; }
  /* mobile nav show & hide button */
  /* general */
  .mobile-nav-on .page-sidebar {
    border-right: 1px solid rgba(0, 0, 0, 0.03);
    -webkit-box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52);
            box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52); }
  .mobile-nav-on .page-content-overlay {
    background: rgba(0, 0, 0, 0.09); } }

@media only screen and (max-width: 576px) {
  /* here we turn on mobile font for smaller screens */
  /*body {
		font-family: $mobile-page-font !important;
	}*/
  /* mobile nav search */
  .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field {
    background: #fff; }
    .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field:focus {
      border-color: #2198F3; } }

/* text area */
[contenteditable="true"]:empty:not(:focus):before {
  content: attr(data-placeholder);
  color: #7f8a95; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

/* add background to focused inpur prepend and append */
.form-control:focus ~ .input-group-prepend {
  background: #2198F3; }

.has-length .input-group-text {
  border-color: #2198F3; }
  .has-length .input-group-text + .input-group-text {
    border-left: 1px solid rgba(0, 0, 0, 0.1); }

.has-length .input-group-text:not([class^="bg-"]):not([class*=" bg-"]) {
  background: #2198F3;
  color: #fff !important; }

/* help block and validation feedback texts*/
.help-block {
  color: #7f8a95; }

.settings-panel h5 {
  color: #434a51; }

.settings-panel .list {
  color: #666666; }
  .settings-panel .list:hover {
    color: #333333;
    background: rgba(255, 255, 255, 0.7); }

.settings-panel .expanded:before {
  border-bottom-color: #4f575f; }

@media only screen and (max-width: 992px) {
  .mobile-view-activated #nff,
  .mobile-view-activated #nfm,
  .mobile-view-activated #nfh,
  .mobile-view-activated #nft,
  .mobile-view-activated #mmb {
    position: relative; }
    .mobile-view-activated #nff .onoffswitch-title,
    .mobile-view-activated #nfm .onoffswitch-title,
    .mobile-view-activated #nfh .onoffswitch-title,
    .mobile-view-activated #nft .onoffswitch-title,
    .mobile-view-activated #mmb .onoffswitch-title {
      color: #d58100 !important; }
    .mobile-view-activated #nff .onoffswitch-title-desc,
    .mobile-view-activated #nfm .onoffswitch-title-desc,
    .mobile-view-activated #nfh .onoffswitch-title-desc,
    .mobile-view-activated #nft .onoffswitch-title-desc,
    .mobile-view-activated #mmb .onoffswitch-title-desc {
      color: #ec9f28 !important; }
    .mobile-view-activated #nff:after,
    .mobile-view-activated #nfm:after,
    .mobile-view-activated #nfh:after,
    .mobile-view-activated #nft:after,
    .mobile-view-activated #mmb:after {
      background: #ffd193;
      border: 1px solid #df8000;
      color: #15171a; } }

/* Hierarchical Navigation */
.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul {
  /* addressing all second, third children */ }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before {
    border-left: 1px solid #116cb2; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul > li a:after {
    background-color: #2595ea; }

.bg-primary-50 {
  background-color: #9ad0fa;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-100 {
  background-color: #82c5f8;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-200 {
  background-color: #6abaf7;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-300 {
  background-color: #51aef6;
  color: white; }
  .bg-primary-300:hover {
    color: white; }

.bg-primary-400 {
  background-color: #39a3f4;
  color: white; }
  .bg-primary-400:hover {
    color: white; }

.bg-primary-500 {
  background-color: #2198F3;
  color: white; }
  .bg-primary-500:hover {
    color: white; }

.bg-primary-600 {
  background-color: #0d8cee;
  color: white; }
  .bg-primary-600:hover {
    color: white; }

.bg-primary-700 {
  background-color: #0c7ed5;
  color: white; }
  .bg-primary-700:hover {
    color: white; }

.bg-primary-800 {
  background-color: #0a70bd;
  color: white; }
  .bg-primary-800:hover {
    color: white; }

.bg-primary-900 {
  background-color: #0961a5;
  color: white; }
  .bg-primary-900:hover {
    color: white; }

.color-primary-50 {
  color: #9ad0fa; }

.color-primary-100 {
  color: #82c5f8; }

.color-primary-200 {
  color: #6abaf7; }

.color-primary-300 {
  color: #51aef6; }

.color-primary-400 {
  color: #39a3f4; }

.color-primary-500 {
  color: #2198F3; }

.color-primary-600 {
  color: #0d8cee; }

.color-primary-700 {
  color: #0c7ed5; }

.color-primary-800 {
  color: #0a70bd; }

.color-primary-900 {
  color: #0961a5; }

.bg-success-50 {
  background-color: #b9fd90;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-100 {
  background-color: #a9fd77;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-200 {
  background-color: #99fc5e;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-300 {
  background-color: #89fc45;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-400 {
  background-color: #79fb2c;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-500 {
  background-color: #69FB13;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-600 {
  background-color: #5cf004;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-700 {
  background-color: #52d704;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-700:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-800 {
  background-color: #49be03;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-800:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-900 {
  background-color: #3fa503;
  color: white; }
  .bg-success-900:hover {
    color: white; }

.color-success-50 {
  color: #b9fd90; }

.color-success-100 {
  color: #a9fd77; }

.color-success-200 {
  color: #99fc5e; }

.color-success-300 {
  color: #89fc45; }

.color-success-400 {
  color: #79fb2c; }

.color-success-500 {
  color: #69FB13; }

.color-success-600 {
  color: #5cf004; }

.color-success-700 {
  color: #52d704; }

.color-success-800 {
  color: #49be03; }

.color-success-900 {
  color: #3fa503; }

.bg-info-50 {
  background-color: #df95fa;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-100 {
  background-color: #d87cf9;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-200 {
  background-color: #d164f8;
  color: white; }
  .bg-info-200:hover {
    color: white; }

.bg-info-300 {
  background-color: #ca4cf6;
  color: white; }
  .bg-info-300:hover {
    color: white; }

.bg-info-400 {
  background-color: #c233f5;
  color: white; }
  .bg-info-400:hover {
    color: white; }

.bg-info-500 {
  background-color: #BB1BF4;
  color: white; }
  .bg-info-500:hover {
    color: white; }

.bg-info-600 {
  background-color: #b00bea;
  color: white; }
  .bg-info-600:hover {
    color: white; }

.bg-info-700 {
  background-color: #9d0ad2;
  color: white; }
  .bg-info-700:hover {
    color: white; }

.bg-info-800 {
  background-color: #8b09ba;
  color: white; }
  .bg-info-800:hover {
    color: white; }

.bg-info-900 {
  background-color: #7908a1;
  color: white; }
  .bg-info-900:hover {
    color: white; }

.color-info-50 {
  color: #df95fa; }

.color-info-100 {
  color: #d87cf9; }

.color-info-200 {
  color: #d164f8; }

.color-info-300 {
  color: #ca4cf6; }

.color-info-400 {
  color: #c233f5; }

.color-info-500 {
  color: #BB1BF4; }

.color-info-600 {
  color: #b00bea; }

.color-info-700 {
  color: #9d0ad2; }

.color-info-800 {
  color: #8b09ba; }

.color-info-900 {
  color: #7908a1; }

.bg-warning-50 {
  background-color: #ffd193;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-100 {
  background-color: #ffc679;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-200 {
  background-color: #ffbb60;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-300 {
  background-color: #ffb046;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-400 {
  background-color: #ffa52d;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-500 {
  background-color: #FF9A13;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-600 {
  background-color: #f98e00;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-700 {
  background-color: #df8000;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-700:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-800 {
  background-color: #c67100;
  color: white; }
  .bg-warning-800:hover {
    color: white; }

.bg-warning-900 {
  background-color: #ac6200;
  color: white; }
  .bg-warning-900:hover {
    color: white; }

.color-warning-50 {
  color: #ffd193; }

.color-warning-100 {
  color: #ffc679; }

.color-warning-200 {
  color: #ffbb60; }

.color-warning-300 {
  color: #ffb046; }

.color-warning-400 {
  color: #ffa52d; }

.color-warning-500 {
  color: #FF9A13; }

.color-warning-600 {
  color: #f98e00; }

.color-warning-700 {
  color: #df8000; }

.color-warning-800 {
  color: #c67100; }

.color-warning-900 {
  color: #ac6200; }

.bg-danger-50 {
  background-color: #fe91aa;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-100 {
  background-color: #fd7897;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-200 {
  background-color: #fd5f83;
  color: white; }
  .bg-danger-200:hover {
    color: white; }

.bg-danger-300 {
  background-color: #fd4570;
  color: white; }
  .bg-danger-300:hover {
    color: white; }

.bg-danger-400 {
  background-color: #fc2c5c;
  color: white; }
  .bg-danger-400:hover {
    color: white; }

.bg-danger-500 {
  background-color: #FC1349;
  color: white; }
  .bg-danger-500:hover {
    color: white; }

.bg-danger-600 {
  background-color: #f2033b;
  color: white; }
  .bg-danger-600:hover {
    color: white; }

.bg-danger-700 {
  background-color: #d90334;
  color: white; }
  .bg-danger-700:hover {
    color: white; }

.bg-danger-800 {
  background-color: #c0022e;
  color: white; }
  .bg-danger-800:hover {
    color: white; }

.bg-danger-900 {
  background-color: #a70228;
  color: white; }
  .bg-danger-900:hover {
    color: white; }

.color-danger-50 {
  color: #fe91aa; }

.color-danger-100 {
  color: #fd7897; }

.color-danger-200 {
  color: #fd5f83; }

.color-danger-300 {
  color: #fd4570; }

.color-danger-400 {
  color: #fc2c5c; }

.color-danger-500 {
  color: #FC1349; }

.color-danger-600 {
  color: #f2033b; }

.color-danger-700 {
  color: #d90334; }

.color-danger-800 {
  color: #c0022e; }

.color-danger-900 {
  color: #a70228; }

.bg-fusion-50 {
  background-color: #7f8a95;
  color: white; }
  .bg-fusion-50:hover {
    color: white; }

.bg-fusion-100 {
  background-color: #717d89;
  color: white; }
  .bg-fusion-100:hover {
    color: white; }

.bg-fusion-200 {
  background-color: #66707b;
  color: white; }
  .bg-fusion-200:hover {
    color: white; }

.bg-fusion-300 {
  background-color: #5a636d;
  color: white; }
  .bg-fusion-300:hover {
    color: white; }

.bg-fusion-400 {
  background-color: #4f575f;
  color: white; }
  .bg-fusion-400:hover {
    color: white; }

.bg-fusion-500 {
  background-color: #434a51;
  color: white; }
  .bg-fusion-500:hover {
    color: white; }

.bg-fusion-600 {
  background-color: #383d43;
  color: white; }
  .bg-fusion-600:hover {
    color: white; }

.bg-fusion-700 {
  background-color: #2c3136;
  color: white; }
  .bg-fusion-700:hover {
    color: white; }

.bg-fusion-800 {
  background-color: #202428;
  color: white; }
  .bg-fusion-800:hover {
    color: white; }

.bg-fusion-900 {
  background-color: #15171a;
  color: white; }
  .bg-fusion-900:hover {
    color: white; }

.color-fusion-50 {
  color: #7f8a95; }

.color-fusion-100 {
  color: #717d89; }

.color-fusion-200 {
  color: #66707b; }

.color-fusion-300 {
  color: #5a636d; }

.color-fusion-400 {
  color: #4f575f; }

.color-fusion-500 {
  color: #434a51; }

.color-fusion-600 {
  color: #383d43; }

.color-fusion-700 {
  color: #2c3136; }

.color-fusion-800 {
  color: #202428; }

.color-fusion-900 {
  color: #15171a; }

.color-white {
  color: #fff; }

.color-black {
  color: #222222; }

.bg-primary-gradient {
  background-image: linear-gradient(250deg, rgba(9, 97, 165, 0.7), transparent); }

.bg-danger-gradient {
  background-image: linear-gradient(250deg, rgba(167, 2, 40, 0.7), transparent); }

.bg-info-gradient {
  background-image: linear-gradient(250deg, rgba(121, 8, 161, 0.7), transparent); }

.bg-warning-gradient {
  background-image: linear-gradient(250deg, rgba(172, 98, 0, 0.7), transparent); }

.bg-success-gradient {
  background-image: linear-gradient(250deg, rgba(63, 165, 3, 0.7), transparent); }

.bg-fusion-gradient {
  background-image: linear-gradient(250deg, rgba(21, 23, 26, 0.7), transparent); }

.btn-primary {
  color: #fff;
  background-color: #2198F3;
  border-color: #2198F3;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-primary:hover {
    color: #fff;
    background-color: #0c85e2;
    border-color: #0c7ed5; }
  .btn-primary:focus, .btn-primary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(66, 167, 245, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(66, 167, 245, 0.5); }
  .btn-primary.disabled, .btn-primary:disabled {
    color: #fff;
    background-color: #2198F3;
    border-color: #2198F3; }
  .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
  .show > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #0c7ed5;
    border-color: #0b77c9; }
    .btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(66, 167, 245, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(66, 167, 245, 0.5); }

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62; }
  .btn-secondary:focus, .btn-secondary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }
  .btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b; }
    .btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }

.btn-success {
  color: #212529;
  background-color: #69FB13;
  border-color: #69FB13;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-success:hover {
    color: #212529;
    background-color: #57e404;
    border-color: #52d704; }
  .btn-success:focus, .btn-success.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(94, 219, 22, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(94, 219, 22, 0.5); }
  .btn-success.disabled, .btn-success:disabled {
    color: #212529;
    background-color: #69FB13;
    border-color: #69FB13; }
  .btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active,
  .show > .btn-success.dropdown-toggle {
    color: #212529;
    background-color: #52d704;
    border-color: #4dcb03; }
    .btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(94, 219, 22, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(94, 219, 22, 0.5); }

.btn-info {
  color: #fff;
  background-color: #BB1BF4;
  border-color: #BB1BF4;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-info:hover {
    color: #fff;
    background-color: #a70bde;
    border-color: #9d0ad2; }
  .btn-info:focus, .btn-info.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(197, 61, 246, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(197, 61, 246, 0.5); }
  .btn-info.disabled, .btn-info:disabled {
    color: #fff;
    background-color: #BB1BF4;
    border-color: #BB1BF4; }
  .btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active,
  .show > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #9d0ad2;
    border-color: #940ac6; }
    .btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(197, 61, 246, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(197, 61, 246, 0.5); }

.btn-warning {
  color: #212529;
  background-color: #FF9A13;
  border-color: #FF9A13;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-warning:hover {
    color: #fff;
    background-color: #ec8700;
    border-color: #df8000; }
  .btn-warning:focus, .btn-warning.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 136, 22, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(222, 136, 22, 0.5); }
  .btn-warning.disabled, .btn-warning:disabled {
    color: #212529;
    background-color: #FF9A13;
    border-color: #FF9A13; }
  .btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active,
  .show > .btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #df8000;
    border-color: #d27800; }
    .btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 136, 22, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(222, 136, 22, 0.5); }

.btn-danger {
  color: #fff;
  background-color: #FC1349;
  border-color: #FC1349;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-danger:hover {
    color: #fff;
    background-color: #e60338;
    border-color: #d90334; }
  .btn-danger:focus, .btn-danger.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(252, 54, 100, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(252, 54, 100, 0.5); }
  .btn-danger.disabled, .btn-danger:disabled {
    color: #fff;
    background-color: #FC1349;
    border-color: #FC1349; }
  .btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active,
  .show > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #d90334;
    border-color: #cd0331; }
    .btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(252, 54, 100, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(252, 54, 100, 0.5); }

.btn-light {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-light:hover {
    color: #212529;
    background-color: #ececec;
    border-color: #e6e6e6; }
  .btn-light:focus, .btn-light.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }
  .btn-light.disabled, .btn-light:disabled {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active,
  .show > .btn-light.dropdown-toggle {
    color: #212529;
    background-color: #e6e6e6;
    border-color: #dfdfdf; }
    .btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }

.btn-dark {
  color: #fff;
  background-color: #434a51;
  border-color: #434a51;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-dark:hover {
    color: #fff;
    background-color: #32373c;
    border-color: #2c3136; }
  .btn-dark:focus, .btn-dark.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(95, 101, 108, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(95, 101, 108, 0.5); }
  .btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #434a51;
    border-color: #434a51; }
  .btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active,
  .show > .btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #2c3136;
    border-color: #262a2f; }
    .btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(95, 101, 108, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(95, 101, 108, 0.5); }

.btn-outline-primary {
  color: #2198F3;
  border-color: #2198F3; }
  .btn-outline-primary:hover {
    color: #fff;
    background-color: #2198F3;
    border-color: #2198F3; }
  .btn-outline-primary:focus, .btn-outline-primary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(33, 152, 243, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(33, 152, 243, 0.5); }
  .btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #2198F3;
    background-color: transparent; }
  .btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #2198F3;
    border-color: #2198F3; }
    .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(33, 152, 243, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(33, 152, 243, 0.5); }

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d; }
  .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-outline-secondary:focus, .btn-outline-secondary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }
  .btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent; }
  .btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }

.btn-outline-success {
  color: #69FB13;
  border-color: #69FB13; }
  .btn-outline-success:hover {
    color: #212529;
    background-color: #69FB13;
    border-color: #69FB13; }
  .btn-outline-success:focus, .btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(105, 251, 19, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(105, 251, 19, 0.5); }
  .btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #69FB13;
    background-color: transparent; }
  .btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
  .show > .btn-outline-success.dropdown-toggle {
    color: #212529;
    background-color: #69FB13;
    border-color: #69FB13; }
    .btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(105, 251, 19, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(105, 251, 19, 0.5); }

.btn-outline-info {
  color: #BB1BF4;
  border-color: #BB1BF4; }
  .btn-outline-info:hover {
    color: #fff;
    background-color: #BB1BF4;
    border-color: #BB1BF4; }
  .btn-outline-info:focus, .btn-outline-info.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(187, 27, 244, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(187, 27, 244, 0.5); }
  .btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #BB1BF4;
    background-color: transparent; }
  .btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
  .show > .btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #BB1BF4;
    border-color: #BB1BF4; }
    .btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(187, 27, 244, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(187, 27, 244, 0.5); }

.btn-outline-warning {
  color: #FF9A13;
  border-color: #FF9A13; }
  .btn-outline-warning:hover {
    color: #212529;
    background-color: #FF9A13;
    border-color: #FF9A13; }
  .btn-outline-warning:focus, .btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 154, 19, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 154, 19, 0.5); }
  .btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #FF9A13;
    background-color: transparent; }
  .btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
  .show > .btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #FF9A13;
    border-color: #FF9A13; }
    .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 154, 19, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 154, 19, 0.5); }

.btn-outline-danger {
  color: #FC1349;
  border-color: #FC1349; }
  .btn-outline-danger:hover {
    color: #fff;
    background-color: #FC1349;
    border-color: #FC1349; }
  .btn-outline-danger:focus, .btn-outline-danger.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(252, 19, 73, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(252, 19, 73, 0.5); }
  .btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #FC1349;
    background-color: transparent; }
  .btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
  .show > .btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #FC1349;
    border-color: #FC1349; }
    .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(252, 19, 73, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(252, 19, 73, 0.5); }

.btn-outline-light {
  color: #fff;
  border-color: #fff; }
  .btn-outline-light:hover {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-outline-light:focus, .btn-outline-light.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }
  .btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #fff;
    background-color: transparent; }
  .btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
  .show > .btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
    .btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }

.btn-outline-dark {
  color: #434a51;
  border-color: #434a51; }
  .btn-outline-dark:hover {
    color: #fff;
    background-color: #434a51;
    border-color: #434a51; }
  .btn-outline-dark:focus, .btn-outline-dark.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(67, 74, 81, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(67, 74, 81, 0.5); }
  .btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #434a51;
    background-color: transparent; }
  .btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
  .show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #434a51;
    border-color: #434a51; }
    .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(67, 74, 81, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(67, 74, 81, 0.5); }

.border-primary {
  border-color: #2198F3 !important; }

.border-secondary {
  border-color: #6c757d !important; }

.border-success {
  border-color: #69FB13 !important; }

.border-info {
  border-color: #BB1BF4 !important; }

.border-warning {
  border-color: #FF9A13 !important; }

.border-danger {
  border-color: #FC1349 !important; }

.border-light {
  border-color: #fff !important; }

.border-dark {
  border-color: #434a51 !important; }

.text-primary {
  color: #2198F3 !important; }

a.text-primary:hover, a.text-primary:focus {
  color: #0a70bd !important; }

.text-secondary {
  color: #6c757d !important; }

a.text-secondary:hover, a.text-secondary:focus {
  color: #494f54 !important; }

.text-success {
  color: #69FB13 !important; }

a.text-success:hover, a.text-success:focus {
  color: #49be03 !important; }

.text-info {
  color: #BB1BF4 !important; }

a.text-info:hover, a.text-info:focus {
  color: #8b09ba !important; }

.text-warning {
  color: #FF9A13 !important; }

a.text-warning:hover, a.text-warning:focus {
  color: #c67100 !important; }

.text-danger {
  color: #FC1349 !important; }

a.text-danger:hover, a.text-danger:focus {
  color: #c0022e !important; }

.text-light {
  color: #fff !important; }

a.text-light:hover, a.text-light:focus {
  color: #d9d9d9 !important; }

.text-dark {
  color: #434a51 !important; }

a.text-dark:hover, a.text-dark:focus {
  color: #202428 !important; }

/* #Reset userselect
========================================================================== */
#myapp-0 {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }

#myapp-4 {
  -webkit-box-shadow: 0 0 0 3px #000000;
          box-shadow: 0 0 0 3px #000000; }

/*# sourceMappingURL=cust-theme-4.css.map */
