{"version": 3, "sources": ["fullcalendar.bundle.css", "../../../scss/_modules/_fonts.scss", "../../../scss/_imports/_global-import.scss", "../../../scss/_mixins/mixins.scss", "../../../scss/_modules/variables.scss", "../../../scss/_modules/_placeholders.scss"], "names": [], "mappings": "AAAA;;;;CAIC;ACJD,iFAAY;ADKZ;EACE,cAAc;EACd,gBAAgB,EAAA;;AAElB;EACE,iBAAiB,EAAA;;AAEnB;EACE,sCAAA;EACA,cAAc,EAAA;;AAEhB;mGAEmG;AAAnG;EACE,iCAAA;EACA,mBAAmB;EACnB,WAAW,EAAA;;AAEb;EACE,uCAAA;EACA,mBAAmB;EACnB,WAAW,EAAA;;AAEb;EACE,8CAAA;EACA,sCAAA;EACA,mBAAmB,EAAA;;AAErB;mGAEmG;AAAnG;EACE,kBAAkB;EAClB,iDAAyC;UAAzC,yCAAyC,EAAA;;AAE3C;EACE,kDAAA;EACA,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,8BAAmB;EAAnB,6BAAmB;MAAnB,uBAAmB;UAAnB,mBAAmB;EACnB,yBAA8B;MAA9B,sBAA8B;UAA9B,8BAA8B;EAC9B,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,gBAAgB,EAAA;;AAElB;EACE,8BAA2B;EAA3B,8BAA2B;MAA3B,+BAA2B;UAA3B,2BAA2B,EAAA;;AAE7B;EACE,aAAa,EAAA;;AAEf;EACE,eAAe;EACf,aAAa;EACb,gBAAgB,EAAA;;AAElB;mGAEmG;AAAnG;EACE,mBAAmB;EACnB,iBAAiB,EAAA;;AAEnB;EACE,SAAS;EACT,SAAS;EACT,gBAAgB;EAChB,yDAAA;EACA,mBAAmB,EAAA;;AAErB;;;;EAIE,gEAAA;EACA,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ,EAAA;;AAEV;EACE,SAAS;EACT,6BAAA,EAA8B;;AAEhC;EACE,YAAY;EACZ,6BAAA,EAA8B;;AAEhC;mGAEmG;AAAnG;EACE,WAAW;EACX,8BAAsB;UAAtB,sBAAsB;EACtB,mCAAA;EACA,mBAAmB;EACnB,yBAAyB;EACzB,iBAAiB;EACjB,cAAc;EACd,4BAAA,EAA6B;;AAE/B;EACE,kBAAkB,EAAA;;AAEpB;;EAEE,mBAAmB;EACnB,iBAAiB;EACjB,UAAU;EACV,mBAAmB,EAAA;;AAErB;EACE,oBAAoB;EACpB,iCAAA,EAAkC;;AAEpC;mGAEmG;AAAnG;EACE,eAAe,EAAA;;AAEjB;EACE,0BAA0B,EAAA;;AAE5B;mGAEmG;AAAnG;EACE,6DAAA;EACA,+FAAA;EACA,mBAAmB;EACnB,eAAe,EAAA;;AAEjB;EACE;4CAE0C;EAA1C,iCAAiC;EACjC,kCAAkC;EAClC,8BAAA;EACA,mCAAmC,EAAA;;AAErC;EACE,gCAAgC;EAChC,+BAAA,EAAgC;;AAElC;mGAEmG;AAAnG;EACE,kBAAkB,EAAA;;AAEpB;EACE,UAAU,EAAA;;AAEZ,mDAAA;AACA;;EAEE,SAAS;EACT,sCAAA,EAAuC;;AAEzC;;EAEE,YAAY;EACZ,sCAAA,EAAuC;;AAEzC;;EAEE,yBAAyB,EAAA;;AAE3B;EACE,UAAU,EAAA;;AAEZ;EACE,UAAU,EAAA;;AAEZ;;;CAIC;AAAD;EACE,kBAAkB;EAClB,UAAU;EACV,mBAAmB;EACnB,uCAAA,EAAwC;;AAE1C;EACE,UAAU,EAAA;;AAEZ;;;EAGE,wCAAA;EACA,2DAAA;EACA,gBAAgB;EAChB,sCAAA;EACA,yBAAyB,EAAA;;AAE3B;;EAEE,4DAAA;EACA,gBAAgB,EAAA;;AAElB;;EAEE,2CAAA;EACA,aAAa,EAAA;;AAEf;mGAEmG;AAAnG;EACE,iCAAiC,EAAA;;AAEnC,mCAAA;AACA;;EAEE,kBAAkB;EAClB,2BAAA;EACA,WAAW;EACX,gFAAA,EAAiF;;AAEnF;mGAEmG;AAAnG;EACE,kBAAkB;EAClB,kDAAA;EACA,cAAc;EACd,2BAAA;EACA,gBAAgB;EAChB,gBAAgB;EAChB,kBAAkB;EAClB,yBAAyB,EAAA;;AAE3B;;EAEE,yBAAyB;EACzB,6BAAA,EAA8B;;AAEhC;;EAEE,WAAW;EACX,uBAAA;EACA,qBAAqB;EACrB,uBAAA,EAAwB;;AAE1B;;EAEE,eAAe;EACf,qEAAA,EAAsE;;AAExE;;EAEE,yCAAA;EACA,mBAAmB,EAAA;;AAErB;EACE,kBAAkB;EAClB,UAAU,EAAA;;AAEZ,uCAAA;AACA;EACE,kBAAkB;EAClB,UAAU,EAAA;;AAEZ,4BAAA;AAJA;EAME,aAAa,EAAA;;AAEf;;EAEE,qDAAA;EACA,cAAc,EAAA;;AAEhB,aAAA;AACA;EACE,qBAAA;EACA,WAAW;EACX,kBAAkB;EAClB,aAAa;EACb,uDAAA;EACA,QAAQ;EACR,SAAS;EACT,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,iBAAiB,EAAA;;AAEnB;mGAEmG;AAAnG;EACE,wBAAwB;EACxB,6BAAA;EACA,gDAAwC;UAAxC,wCAAwC,EAAA;;AAE1C;EACE,WAAW;EACX,kBAAkB;EAClB,UAAU;EACV,uCAAA;EACA,yBAAA;EACA,SAAS;EACT,WAAW;EACX,YAAY;EACZ,UAAU;EACV,qBAAA;EACA,gBAAgB;EAChB,YAAY,EAAA;;AAEd;mGAEmG;AAAnG;EACE,gDAAwC;UAAxC,wCAAwC,EAAA;;AAE1C;EACE,YAAY,EAAA;;AAEd;mGAEmG;AAAnG,oCAAA;AACA;EACE,WAAW;EACX,kBAAkB;EAClB,UAAU;EACV,mBAAA;EACA,UAAU;EACV,aAAa;EACb,OAAO;EACP,QAAQ,EAAA;;AAEV,mGAAA;AACA;;EAEE,cAAc;EACd,oBAAoB;EACpB,iBAAiB;EACjB,oCAAA;EACA,yBAAyB;EACzB,4BAA4B,EAAA;;AAE9B;;EAEE,eAAe;EACf,qBAAqB;EACrB,kBAAkB;EAClB,oCAAA;EACA,0BAA0B;EAC1B,6BAA6B,EAAA;;AAE/B,uCAAA;AACA,kBAAA;AACA;;EAEE,gBAAgB;EAChB,UAAU;EACV,oBAAA,EAAqB;;AAEvB,kBAAA;AACA;;EAEE,gBAAgB;EAChB,WAAW;EACX,oBAAA,EAAqB;;AAEvB,4BAAA;AACA;EACE,UAAU;EACV,SAAS;EACT,wBAAA;EACA,YAAY;EACZ,2BAAA,EAA4B;;AAE9B,4BAAA;AACA;EACE,mBAAA;EACA,kBAAkB;EAClB,iBAAiB;EACjB,UAAU;EACV,WAAW;EACX,mBAAmB;EACnB,qBAAqB;EACrB,gBAAgB;EAChB,sBAAA;EACA,QAAQ;EACR,gBAAgB,EAAA;;AAElB,kBAAA;AACA;;EAEE,iBAAiB;EACjB,yCAAA,EAA0C;;AAE5C,kBAAA;AACA;;EAEE,kBAAkB;EAClB,0CAAA,EAA2C;;AAE7C;;;;CAKC;AAAD;EACE,iBAAiB;EACjB,qCAAA;EACA,cAAc,EAAA;;AAEhB;EACE,eAAe;EACf,mDAAA,EAAoD;;AAEtD;EACE,aAAa;EACb,+BAAA,EAAgC;;AAElC;EACE,qCAAA;EACA,mBAAmB;EACnB,gBAAgB,EAAA;;AAElB;EACE,iBAAiB,EAAA;;AAEnB,6BAAA;AACA,kBAAA;AACA;;EAEE,iBAAiB;EACjB,2BAAA,EAA4B;;AAE9B,kBAAA;AACA;;EAEE,kBAAkB;EAClB,2BAAA,EAA4B;;AAE9B;mGAEmG;AAAnG,8CAAA;AACA;EACE,eAAe;EACf,gBAAgB;EAChB,eAAe;EACf,qBAAqB,EAAA;;AAEvB;EACE,0BAA0B,EAAA;;AAE5B;EACE,4DAAA;EACA,aAAa,EAAA;;AAEf,qDAAA;AACA;EACE,UAAU;EACV,iDAAA,EAAkD;;AAEpD;EACE,UAAU;EACV,YAAY,EAAA;;AAEd;EACE,aAAa,EAAA;;AAEf;mGAEmG;AAAnG;EACE,kBAAkB;EAClB,mBAAmB,EAAA;;AAErB;mGAEmG;AAAnG;EACE,yBAAyB;EAEzB,sBAAsB;EACtB,qBAAqB;EACrB,iBAAiB;EACjB,2BAA2B;EAC3B,6CAA6C,EAAA;;AAE/C;;CAGC;AAAD;mGAEmG;AAAnG;;;;;;;;;;EAUE,kBAAkB,EAAA;;AAEpB;EACE,sBAAsB,EAAA;;AAExB;;;EAGE,gBAAgB,EAAA;;AAElB;EACE,mBAAmB,EAAA;;AAErB;EACE,mBAAmB;EACnB,WAAW,EAAA;;AAEb;;;CAIC;AAAD;EACE,sBAAsB;EACtB,4mGAA4mG;EAC5mG,mBAAmB;EACnB,kBAAkB,EAAA;;AACpB;EACE,+EAAA;EACA,iCAAiC;EACjC,WAAW;EACX,kBAAkB;EAClB,mBAAmB;EACnB,oBAAoB;EACpB,oBAAoB;EACpB,cAAc;EACd,sCAAA;EACA,mCAAmC;EACnC,kCAAkC,EAAA;;AAEpC;EACE,gBAAgB,EAAA;;AAElB;EACE,gBAAgB,EAAA;;AAElB;EACE,gBAAgB,EAAA;;AAElB;EACE,gBAAgB,EAAA;;AAElB;EACE,gBAAgB,EAAA;;AAElB;EACE,gBAAgB,EAAA;;AAElB;EACE,gBAAgB,EAAA;;AAhClB;EAmCE,qBAAqB;EACrB,UAAU;EACV,WAAW;EACX,kBAAkB,EAAA;;AAEpB;;;CAKC;AADD,UAAA;AACA;EACE,gBAAgB;EAChB,iBAAiB;EACjB,oBAAoB;EACpB,SAAS;EACT,oBAAoB;EACpB,kBAAkB;EAClB,oBAAoB,EAAA;;AAEtB;EACE,mBAAmB;EACnB,0CAA0C,EAAA;;AAX5C;EAcE,0BAA0B,EAAA;;AAE5B;EACE,eAAe,EAAA;;AAEjB;EACE,UAAU;EACV,kBAAkB,EAAA;;AAEpB,UAAA;AAvBA;EAyBE,qBAAqB;EACrB,gBAAgB;EAChB,cAAc;EACd,kBAAkB;EAClB,sBAAsB;EACtB,yBAAyB;EACzB,sBAAsB;EACtB,qBAAqB;EACrB,iBAAiB;EACjB,6BAA6B;EAC7B,6BAA6B;EAC7B,qBAAqB;EACrB,cAAc;EACd,gBAAgB;EAChB,qBAAqB,EAAA;;AAEvB;EACE,cAAc;EACd,qBAAqB,EAAA;;AAlCvB;EAqCE,UAAU;EACV,uDAAuD;EACvD,+CAA+C,EAAA;;AAEjD;EACE,aAAa,EAAA;;AAEf,uBAAA;AACA;EACE,WAAW;EACX,yBAAyB;EACzB,qBAAqB,EAAA;;AAEvB;EACE,WAAW;EACX,yBAAyB;EACzB,qBAAqB,EAAA;;AAEvB;EACE,uDAAuD;EACvD,+CAA+C,EAAA;;AAEjD;EACE,WAAW;EACX,yBAAyB;EACzB,qBAAqB,EAAA;;AAEvB;;EAEE,WAAW;EACX,yBAAyB;EACzB,qBAAqB,EAAA;;AAEvB;;EAEE,uDAAuD;EACvD,+CAA+C,EAAA;;AAEjD,yBAAA;AACA;EACE,sBAAsB;EACtB,gBAAgB,EAAA;;AAElB;mGAGmG;AADnG;EACE,kBAAkB;EAClB,2BAA2B;EAC3B,2BAA2B;EAC3B,oBAAoB;EACpB,sBAAsB,EAAA;;AAExB;EACE,kBAAkB;EAClB,mBAAmB;EACnB,kBAAkB;EAClB,cAAc,EAAA;;AAEhB;EACE,UAAU,EAAA;;AAEZ;;;EAGE,UAAU,EAAA;;AAEZ;EACE,iBAAiB,EAAA;;AAEnB;EACE,0BAA0B;EAC1B,6BAA6B,EAAA;;AAL/B;EAQE,yBAAyB;EACzB,4BAA4B,EAAA;;AAE9B;mGAGmG;AAnMnG;EAmME,iBAAiB;EACjB,mBAAmB,EAAA;;AAErB;mGAGmG;AADnG;EACE,yBAAyB,EAAA;;AAE3B;mGAGmG;AADnG;EACE,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAA8B;MAA9B,sBAA8B;UAA9B,8BAA8B;EAC9B,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB,EAAA;;AAErB;EACE,oBAAoB,EAAA;;AAEtB;EACE,iBAAiB,EAAA;;AAEnB,kBAAA;AACA;EACE,kBAAkB,EAAA;;AAEpB;EACE,iBAAiB;EACjB,SAAS,EAAA;;AAEX;mGAGmG;AADnG;EACE,kBAAkB,EAAA;;AAEpB,iFAAA;AACA,iGAAA;AACA;;;EAGE,+BAA+B;EAE/B,uBAAuB,EAAA;;AAEzB;;EAEE,6DAAA;EACA,kBAAkB;EAClB,UAAU,EAAA;;AAEZ;EA7tBA;IA+tBI,0BAA0B,EAAA;EAE5B;qGAEmG;EA7gBrG;IA8gBI,2BAA2B;IAC3B,sBAAsB;IACtB,wBAAwB,EAAA;EA3e5B;IA8eI,aAAa,EAAA;EAEf;qGAAmG;EAEnG;;;;;;IAME,6BAA6B;IAC7B,2BAA2B,EAAA;EAE7B,wDAAA;EACA,cAAA;EACA;;;;;;;;IAQE,aAAa,EAAA;EAEf,mDAAA;EACA;IACE,uBAAuB;IACvB,gDAAA;IACA,wBAAwB;IACxB,6DAAA,EAA8D;EAEhE;IACE,gBAAgB;IAChB,mBAAA;IACA,4BAA4B;IAC5B,kDAAA,EAAmD;EAErD;IACE,iCAAA;IACA,mBAAmB;IACnB,yEAAA,EAA0E;EAE5E;IACE;sFALkF;IAOlF,WAAW,EAAA;EAEb;qGANmG;EAQnG;;IAEE,wBAAwB,EAAA;EAE1B;IACE,6BAA6B,EAAA;EAE/B;IACE,8BAA8B,EAAA;EArwBlC;IAwwBI,aAAa;IACb,qDAAA,EAAsD;EAExD;qGAVmG;EAYnG,uEAAA;EACA;IACE,wBAAwB,EAAA;EAE1B,kEAAA;EACA;IACE,aAAa,EAAA;EAEf,uCAAA;EACA;;IAEE,oEAAA;IACA,wBAAwB;IACxB,2CAAA,EAA4C;EAE9C,2FAAA;EACA;IACE,gBAAgB,EAAA;EAElB,2DAAA;EACA;IACE,WAAW,EAAA;EAEb,8FAAA;EACA;IACE,oBAAoB,EAAA;EAEtB;qGAhBmG;EAkBnG,wDAAA;EACA;IACE,2BAA2B;IAC3B,0BAA0B,EAAA;EAE5B,0EAAA;EACA;IACE,mCAAmC,EAAA;EAErC,gDAAA;EACA;IACE,cAAc,EAAA;EAEhB,mFAAA;EACA;IACE,gCAAgC,EAAA;EAElC,wDAAA;EACA;IACE,cAAc,EAAA;EAEhB,SAAA;EACA,4EAAA;EACA;IACE,8BAA8B,EAAA;EAEhC,oDAAA;EACA;IACE,aAAa,EAAA;EAEf,2FAAA;EACA;IACE,wBAAwB,EAAA;EAE1B;qGAxBmG;EA0BnG,iDAAA;EACA;;;IAGE,IAAA;IACA,4BAA4B;IAC5B,uBAAuB,EAAA;EAEzB,yEAAA;EAxuBF;IA0uBI,oBAAoB;IACpB,oBAAoB,EAAA;EAEtB;qGA1BmG;EA4BnG;;IAEE,aAAa;IACb,8CAAA,EAA+C,EAAE;;AAErD;;;;CAvBC;AA4BD;mGA1BmG;AA4BnG,sBAAA;AACA;;EAEE,gEAAA;EACA,mBAAmB;EACnB,iEAAA,EAAkE;;AAEpE;EACE,eAAe;EACf,gDAAA,EAAiD;;AAEnD,gGAAA;AACA;EACE,gBAAgB,EAAA;;AAElB;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ,EAAA;;AAEV,gCAAA;AACA;EACE,YAAY,EAAA;;AAEd;;EAEE,YAAY,EAAA;;AAEd;;EAEE,cAAc;EACd,8CAAA,EAA+C;;AAEjD;EACE,YAAY,EAAA;;AAEd;EACE,WAAW,EAAA;;AAEb;EACE,WAAW;EACX,wBAAwB,EAAA;;AAE1B;EACE,YAAY;EACZ,wBAAwB,EAAA;;AAE1B;EACE,gBAAgB;EAChB,kBAAkB;EAClB,yBAAyB;EACzB,cAAc,EAAA;;AAEhB,yCAAA;AACA;EACE,kBAAkB,EAAA;;AAEpB;EACE,yEAAA;EACA,qBAAqB;EACrB,iBAAiB,EAAA;;AAEnB;;;;CAvBC;AA4BD;mGA1BmG;AA4BnG,sBAAA;AACA;EACE,qBAAqB;EACrB,WAAW;EACX,YAAY;EACZ,kBAAkB,EAAA;;AAEpB,iBAAA;AACA;EACE,cAAc;EACd,4CAAA,EAA6C;;AAE/C;EACE,iBAAiB;EACjB,mBAAmB,EAAA;;AAErB,iBAAA;AACA;EACE,kBAAkB;EAClB,oCAAA,EAAqC;;AAEvC;EACE,qBAAqB;EACrB,iBAAiB,EAAA;;AAEnB;EACE,mBAAmB,EAAA;;AAErB,+BAAA;AACA;EACE,wBAAwB,EAAA;;AAE1B;EACE,iBAAiB,EAAA;;AAEnB;EACE,WAAW,EAAA;;AAEb;EACE,YAAY,EAAA;;AAEd;EACE,YAAY,EAAA;;AAEd;EACE,WAAW,EAAA;;AAEb,qBAAA;AACA;EACE,eAAe;EACf,gCAAA,EAAiC;;AAEnC;;EAEE,mBAAmB;EACnB,UAAU,EAAA;;AAEZ,2CAAA;AACA;EACE,gBAAgB,EAAA;;AAElB;EACE,eAAe,EAAA;;AAEjB;EACE,0CAAA;EACA,qBAAqB;EACrB,cAAc,EAAA;;AAEhB;EACE,2CAAA;EACA,0BAA0B,EAAA;;AAE5B,2BAAA;AACA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS,EAAA;;AAEX;EACE,WAAW;EACX,YAAY;EACZ,cAAc,EAAA;;AAEhB;EACE,mBAAmB;EACnB,sBAAsB;EACtB,kBAAkB,EAAA;;AAEpB;EACE,sCAAA;EACA,sBAAsB,EAAA;;AAExB;;;;CAvBC;AA4BD;mGA1BmG;AA4BnG;EACE,kBAAkB;EAClB,UAAU;EACV,uDAAA,EAAwD;;AAE1D;EACE,eAAe;EACf,qDAAA,EAAsD;;AAExD;EACE,mBAAmB;EACnB,6DAAA,EAA8D;;AAEhE;mGA1BmG;AA4BnG;EACE,wCAAA;EACA,sBAAsB;EACtB,cAAc;EACd,mBAAmB,EAAA;;AAErB;EACE,iBAAiB,EAAA;;AAEnB;EACE,gBAAgB,EAAA;;AAElB;mGA1BmG;AA4BnG;;EAEE,6DAAA;EACA,kBAAkB;EAClB,UAAU,EAAA;;AAxSV;EA2SA,gBAAgB;EAChB,oEAAA,EAAqE;;AAEvE;EACE,oDAAA;EACA,4BAA4B,EAAA;;AAE9B;EACE,UAAU,EAAA;;AAEZ;;EAEE,qEAAA;EACA,kBAAkB;EAClB,UAAU,EAAA;;AAEZ;EACE,kBAAkB;EAClB,gDAAA,EAAiD;;AA9SjD;EAiTA,kBAAkB;EAClB,UAAU;EACV,MAAM;EACN,OAAO;EACP,QAAQ,EAAA;;AAEV,sDAAA;AACA;EACE,kBAAkB;EAClB,UAAU,EAAA;;AAEZ;EACE,kBAAkB;EAClB,UAAU,EAAA;;AAEZ;EACE,kBAAkB;EAClB,UAAU,EAAA;;AA1TV;EA6TA,kBAAkB;EAClB,UAAU,EAAA;;AAEZ;EACE,UAAU,EAAA;;AAEZ;EACE,+BAAA;EACA,kBAAkB;EAClB,UAAU,EAAA;;AAEZ;mGA1BmG;AA4BnG;EACE,aAAa;EACb,gBAAgB;EAChB,gDAAA,EAAiD;;AAEnD;EACE,wBAAwB,EAAA;;AAE1B;mGA1BmG;AAAnG;EA6BE,yDAAA;EACA,kBAAkB;EAClB,kEAAA,EAAmE;;AAErE;EACE,kBAAkB;EAClB,OAAO;EACP,QAAQ;EACR,oCAAA,EAAqC;;AAEvC;mGA1BmG;AA4BnG;EACE,mDAAA;EACA,oBAAoB,EAAA;;AAEtB;EACE,yCAAA;EACA,oBAAoB,EAAA;;AAEtB;;EAEE,kBAAkB;EAClB,UAAU;EACV,0BAAA,EAA2B;;AAE7B;EACE,6CAAA;EACA,OAAO;EACP,QAAQ,EAAA;;AAEV;;;;CAvBC;AA4BD;EACE,kBAAkB,EAAA;;AAEpB;EACE,wCAAwC;EACxC,gCAAgC,EAAA;;AAElC;EACE,gDAAA;EACA,sDAAA;EACA,mBAAmB;EACnB,gBAAgB;EAChB,+BAAA;EACA,yBAAyB;EACzB,0BAA0B,EAAA;;AAE5B;EACE,sDAAA;EACA,sBAAsB;EACtB,mBAAmB;EACnB,kCAAA;EACA,4BAA4B;EAC5B,6BAA6B,EAAA;;AAE/B;EACE,gBAAgB;EAChB,gBAAgB,EAAA;;AAElB;;EAEE,cAAc,EAAA;;AAEhB;EACE,gBAAgB;EAChB,mBAAmB,EAAA;;AAErB,0DAAA;AACA;EACE,iEAAA;EACA,mBAAmB,EAAA;;AAErB;;EAEE,4CAAA;EACA,qBAAqB;EACrB,mBAAmB,EAAA;;AAErB;EACE,aAAa;EACb,wCAAA,EAAyC;;AAE3C;EACE,yBAAyB;EACzB,4CAAA,EAA6C;;AAE/C;EACE,wBAAwB;EACxB,4CAAA,EAA6C;;AAE/C;EACE,gBAAgB;EAChB,kDAAA;EACA,UAAU;EACV,4BAAA,EAA6B;;AAE/B,4BAAA;AACA;EACE,OAAO;EACP,QAAQ;EACR,SAAS;EACT,WAAW;EACX,gBAAgB;EAChB,gBAAgB;EAChB,eAAe;EACf,sBAAsB;EACtB,kBAAkB;EAClB,gBAAgB,EAAA;;AAElB;EACE,YAAY,EAAA;;AAEd,2BAAA;AACA;EACE,cAAA;EACA,kBAAkB;EAClB,iBAAiB;EACjB,UAAU;EACV,WAAW;EACX,mBAAmB;EACnB,qBAAqB;EACrB,gBAAgB;EAChB,wBAAA;EACA,SAAS;EACT,iBAAiB;EACjB,8BAAA;EACA,YAAY,EAAA;;AAEd;mGA1BmG;AAhInG;EA6JE,qBAAqB;EACrB,OAAO;EACP,QAAQ,EAAA;;AAEV,kBAAA;AACA;EACE,gBAAgB;EAChB,wCAAA,EAAyC;;AAE3C;EACE,OAAO;EACP,+BAAA;EACA,2BAA2B;EAC3B,6BAA6B;EAC7B,gCAAgC,EAAA;;AAElC;EACE,QAAQ;EACR,8BAAA;EACA,2BAA2B;EAC3B,6BAA6B;EAC7B,gCAAgC,EAAA;;AAElC;;;;CAvBC;AA4BD;EACE,qBAAqB,EAAA;;AAEvB;EACE,0BAA0B,EAAA;;AAE5B;EACE,qBAAqB,EAAA;;AAEvB;EACE,gBAAgB,EAAA;;AAElB;EACE,WAAW,EAAA;;AAEb;EACE,kBAAkB,EAAA;;AAEpB;mGA1BmG;AA4BnG;EACE,UAAU,EAAA;;AAEZ;mGA1BmG;AA4BnG;EACE,4DAAA;EACA,gBAAgB,EAAA;;AE51ClB;4EFo0C4E;AGp0C5E;;;;;sDH00CsD;AGzmCtD;;;;;;;;;;;;yBHsnCyB;AG1lCzB;;;yBH8lCyB;AG1jCzB;;;;;;;;;;;yBHskCyB;AG5iCzB;;;yBHgjCyB;AGtgCzB,wBAAA;AAQA,0BAAA;ADtWA;4EFw2C4E;AI/2C5E;4EJi3C4E;AI/2C5E,+CAAA;AAQA;;;;;;kFJ+2CkF;AIv2ClF;4EJy2C4E;AIn2C5E;4EJq2C4E;AIn2C5E,cAAA;AAYA,kBAAA;AAYA,iBAAA;AAYA,kBAAA;AAYA,cAAA;AAYA,eAAA;AAYA,kBAAA;AA6EA;4EJutC4E;AIntC5E;4EJqtC4E;AItsCR,kGAAA;AACG,2EAAA;AAavE,+BAAA;AAgBA,6BAAA;AACA,wFAAA;AAQA;4EJsqC4E;AI7oC5E,oCAAA;AAYA,UAAA;AACA,wIAAA;AASA,UAAA;AAIA,aAAA;AAMA,qDAAA;AAGA,mCAAA;AAGA,oBAAA;AAKA,iBAAA;AASA,WAAA;AAEA,UAAA;AAIA,UAAA;AAOA,gBAAA;AAMA,UAAA;AAKA,UAAA;AAKA,eAAA;AAIA,iBAAA;AAUA,aAAA;AAIA,qBAAA;AAKA,WAAA;AASA,cAAA;AASA,oBAAA;AAOA,aAAA;AAcA,aAAA;AAYA,UAAA;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;CJ6hCC;AIhgCD,UAAA;AAuBA,aAAA;AAIA;4EJy+B4E;AIj+B5E,6EAAA;AAEiC,WAAA;AACD,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACC,WAAA;AAEjC;4EJi+B4E;AI/9BlE,mFAAA;AAOV;4EJ29B4E;AIz9BG,mEAAA;AAE/E;4EJ09B4E;AIp9B5E,oEAAA;AAUA;4EJ68B4E;AIz8B5E;4EJ28B4E;AIz8B5B,0BAAA;AACH,iBAAA;AAG7C;4EJy8B4E;AIp8B5E;4EJs8B4E;AIh8B5E;4EJk8B4E;AI97B5E;4EJg8B4E;AI77B5E,WAAA;AAOA,WAAA;AAMA,SAAA;AAEoD,6DAAA;AACC,8DAAA;AACC,qDAAA;AAEtD,gCAAA;AAGA,qBAAA;AAC4D,uBAAA;AAO5D,QAAA;AAYA,uBAAA;AASA,UAAA;AAKA,sBAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4EJ66B4E;AI94B5E,oBAAA;AACA,eAAA;AAMA,uBAAA;AAOA,mBAAA;AAOA,kBAAA;AAIA,cAAA;AAIA,cAAA;AAKA,eAAA;AAIA,gCAAA;AAGA,qBAAA;AACA,mCAAA;AAGA,mBAAA;AAQA,2CAAA;AAK6C,kBAAA;AAE7C,gCAAA;AAKyE,+CAAA;AAEzE;4EJ61B4E;AI31B5E,eAAA;AAIA;4EJ01B4E;AIn1B5E;4EJq1B4E;AIj1B5E;4EJm1B4E;AIt0B5E;4EJw0B4E;AIj0B5E;4EJm0B4E;AI3zB5E;4EJ6zB4E;AIrzB5E;4EJuzB4E;AIlzB5E,oBAAA;AH/vBA;EACC,yDGkf8D;EHjf9D,oBEsPkC;EFrPlC,qBAAqB,EAAA;;AAGtB;EACC,cG4dwC,EAAA;;AHzdzC;EACC,gBAAgB;EAChB,gBAAgB,EAAA;;AAIjB;EACC,gBAAgB,EAAA;;AAGjB;;;;;;;;;;;;EAYI,gBAAgB;EAChB,cAAc;EACjB,oBEsNkC;EFrN/B,gBAAgB;EAEhB,oBAA0C,EAAA;;AAG9C;;;;EAIC,oBE4MkC,EAAA;;AFzMnC;;EAEC,mBEuMkC,EAAA;;AFpMnC;;;;EAIC,oBEgMkC,EAAA;;AF7LnC,kBAAA;AACA;EACC,cAA2B,EAAA;;AAG5B,kBAAA;AACA;EAEC,sJAAsG;EAAtG,wFAAsG;EACtG,cGnE2B;EHoExB,qBAAqB;EACrB,4BAA4B;EAC5B,6BAA6B;EAC7B,oCAAoC;EACpC,iBAAiB,EAAA;;AAGrB,+CAAA;AI9EA;;;;;;;;;;;;;;;;;;;;;;;;;;CLgpDC;AK9mDD;;;;;;;;;CLwnDC;AKx7CD;;EL27CE;AKx4CF;;;;;;;;;;;;;;;;;;;;;;;;GLi6CG;AK52CH,aAAA;ALuhCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAyXC;AA1zCD;EAs+BC,cAAc,EAAA;;AA3+Bf;EA++BC,uBAAuB,EAAA;;AAn2CxB;EAu2CC,uBAAuB,EAAA;;AAr8BxB;EAy8BC,eAAe,EAAA;;AAh2ChB;EAo2CC,iBAAiB,EAAA;;AA5sBlB;EAgtBC,eAAe;EACf,gBAAgB;EAChB,yBAAyB,EAAA;;AAvrC1B;;EA4rCC,mBIt3C6C,EAAA;;AJgL9C;EA0sCC,yBIx3C4C,EAAA;;AJq3B7C;EAwgBC,UAAU,EAAA;;AAGX;EACI,oLAA8J;EAC9J,yBAAyB;EACzB,0BAA0B,EAAA;;AAG9B;;;CA8UC;AAxUD;EACI,sGAAgE;EAAhE,kEAAgE,EAAA;;AAGpE;EACC;;;;;;;;GAgVE;EA92CH;IAyiCE,eAAe,EAAA;EAGhB;IAEE,4BAAsB;IAAtB,6BAAsB;QAAtB,0BAAsB;YAAtB,sBAAsB,EAAA;IAFxB;MAKG,mBAAmB;MACnB,gBAAgB,EAAA;IANnB;MAWG,4BAAQ;UAAR,iBAAQ;cAAR,QAAQ,EAAA;IAXX;MAcG,4BAAQ;UAAR,iBAAQ;cAAR,QAAQ,EAAA;IAdX;MAiBG,4BAAQ;UAAR,iBAAQ;cAAR,QAAQ,EAAA;IAjBX;MAqBG,kBAAkB,EAAA,EAClB", "file": "fullcalendar.bundle.css", "sourcesContent": ["/*!\nFullCalendar Core Package v4.2.0\nDocs & License: https://fullcalendar.io/\n(c) 2019 <PERSON>\n*/\n.fc {\n  direction: ltr;\n  text-align: left; }\n\n.fc-rtl {\n  text-align: right; }\n\nbody .fc {\n  /* extra precedence to overcome jqui */\n  font-size: 1em; }\n\n/* Colors\n--------------------------------------------------------------------------------------------------*/\n.fc-highlight {\n  /* when user is selecting cells */\n  background: #bce8f1;\n  opacity: .3; }\n\n.fc-bgevent {\n  /* default look for background events */\n  background: #8fdf82;\n  opacity: .3; }\n\n.fc-nonbusiness {\n  /* default look for non-business-hours areas */\n  /* will inherit .fc-bgevent's styles */\n  background: #d7d7d7; }\n\n/* Popover\n--------------------------------------------------------------------------------------------------*/\n.fc-popover {\n  position: absolute;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); }\n\n.fc-popover .fc-header {\n  /* TODO: be more consistent with fc-head/fc-body */\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  padding: 2px 4px; }\n\n.fc-rtl .fc-popover .fc-header {\n  flex-direction: row-reverse; }\n\n.fc-popover .fc-header .fc-title {\n  margin: 0 2px; }\n\n.fc-popover .fc-header .fc-close {\n  cursor: pointer;\n  opacity: 0.65;\n  font-size: 1.1em; }\n\n/* Misc Reusable Components\n--------------------------------------------------------------------------------------------------*/\n.fc-divider {\n  border-style: solid;\n  border-width: 1px; }\n\nhr.fc-divider {\n  height: 0;\n  margin: 0;\n  padding: 0 0 2px;\n  /* height is unreliable across browsers, so use padding */\n  border-width: 1px 0; }\n\n.fc-bg,\n.fc-bgevent-skeleton,\n.fc-highlight-skeleton,\n.fc-mirror-skeleton {\n  /* these element should always cling to top-left/right corners */\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0; }\n\n.fc-bg {\n  bottom: 0;\n  /* strech bg to bottom edge */ }\n\n.fc-bg table {\n  height: 100%;\n  /* strech bg to bottom edge */ }\n\n/* Tables\n--------------------------------------------------------------------------------------------------*/\n.fc table {\n  width: 100%;\n  box-sizing: border-box;\n  /* fix scrollbar issue in firefox */\n  table-layout: fixed;\n  border-collapse: collapse;\n  border-spacing: 0;\n  font-size: 1em;\n  /* normalize cross-browser */ }\n\n.fc th {\n  text-align: center; }\n\n.fc th,\n.fc td {\n  border-style: solid;\n  border-width: 1px;\n  padding: 0;\n  vertical-align: top; }\n\n.fc td.fc-today {\n  border-style: double;\n  /* overcome neighboring borders */ }\n\n/* Internal Nav Links\n--------------------------------------------------------------------------------------------------*/\na[data-goto] {\n  cursor: pointer; }\n\na[data-goto]:hover {\n  text-decoration: underline; }\n\n/* Fake Table Rows\n--------------------------------------------------------------------------------------------------*/\n.fc .fc-row {\n  /* extra precedence to overcome themes forcing a 1px border */\n  /* no visible border by default. but make available if need be (scrollbar width compensation) */\n  border-style: solid;\n  border-width: 0; }\n\n.fc-row table {\n  /* don't put left/right border on anything within a fake row.\n     the outer tbody will worry about this */\n  border-left: 0 hidden transparent;\n  border-right: 0 hidden transparent;\n  /* no bottom borders on rows */\n  border-bottom: 0 hidden transparent; }\n\n.fc-row:first-child table {\n  border-top: 0 hidden transparent;\n  /* no top border on first row */ }\n\n/* Day Row (used within the header and the DayGrid)\n--------------------------------------------------------------------------------------------------*/\n.fc-row {\n  position: relative; }\n\n.fc-row .fc-bg {\n  z-index: 1; }\n\n/* highlighting cells & background event skeleton */\n.fc-row .fc-bgevent-skeleton,\n.fc-row .fc-highlight-skeleton {\n  bottom: 0;\n  /* stretch skeleton to bottom of row */ }\n\n.fc-row .fc-bgevent-skeleton table,\n.fc-row .fc-highlight-skeleton table {\n  height: 100%;\n  /* stretch skeleton to bottom of row */ }\n\n.fc-row .fc-highlight-skeleton td,\n.fc-row .fc-bgevent-skeleton td {\n  border-color: transparent; }\n\n.fc-row .fc-bgevent-skeleton {\n  z-index: 2; }\n\n.fc-row .fc-highlight-skeleton {\n  z-index: 3; }\n\n/*\nrow content (which contains day/week numbers and events) as well as \"mirror\" (which contains\ntemporary rendered events).\n*/\n.fc-row .fc-content-skeleton {\n  position: relative;\n  z-index: 4;\n  padding-bottom: 2px;\n  /* matches the space above the events */ }\n\n.fc-row .fc-mirror-skeleton {\n  z-index: 5; }\n\n.fc .fc-row .fc-content-skeleton table,\n.fc .fc-row .fc-content-skeleton td,\n.fc .fc-row .fc-mirror-skeleton td {\n  /* see-through to the background below */\n  /* extra precedence to prevent theme-provided backgrounds */\n  background: none;\n  /* in case <td>s are globally styled */\n  border-color: transparent; }\n\n.fc-row .fc-content-skeleton td,\n.fc-row .fc-mirror-skeleton td {\n  /* don't put a border between events and/or the day number */\n  border-bottom: 0; }\n\n.fc-row .fc-content-skeleton tbody td,\n.fc-row .fc-mirror-skeleton tbody td {\n  /* don't put a border between event cells */\n  border-top: 0; }\n\n/* Scrolling Container\n--------------------------------------------------------------------------------------------------*/\n.fc-scroller {\n  -webkit-overflow-scrolling: touch; }\n\n/* TODO: move to timegrid/daygrid */\n.fc-scroller > .fc-day-grid,\n.fc-scroller > .fc-time-grid {\n  position: relative;\n  /* re-scope all positions */\n  width: 100%;\n  /* hack to force re-sizing this inner element when scrollbars appear/disappear */ }\n\n/* Global Event Styles\n--------------------------------------------------------------------------------------------------*/\n.fc-event {\n  position: relative;\n  /* for resize handle and other inner positioning */\n  display: block;\n  /* make the <a> tag block */\n  font-size: .85em;\n  line-height: 1.4;\n  border-radius: 3px;\n  border: 1px solid #3788d8; }\n\n.fc-event,\n.fc-event-dot {\n  background-color: #3788d8;\n  /* default BACKGROUND color */ }\n\n.fc-event,\n.fc-event:hover {\n  color: #fff;\n  /* default TEXT color */\n  text-decoration: none;\n  /* if <a> has an href */ }\n\n.fc-event[href],\n.fc-event.fc-draggable {\n  cursor: pointer;\n  /* give events with links and draggable events a hand mouse pointer */ }\n\n.fc-not-allowed,\n.fc-not-allowed .fc-event {\n  /* to override an event's custom cursor */\n  cursor: not-allowed; }\n\n.fc-event .fc-content {\n  position: relative;\n  z-index: 2; }\n\n/* resizer (cursor AND touch devices) */\n.fc-event .fc-resizer {\n  position: absolute;\n  z-index: 4; }\n\n/* resizer (touch devices) */\n.fc-event .fc-resizer {\n  display: none; }\n\n.fc-event.fc-allow-mouse-resize .fc-resizer,\n.fc-event.fc-selected .fc-resizer {\n  /* only show when hovering or selected (with touch) */\n  display: block; }\n\n/* hit area */\n.fc-event.fc-selected .fc-resizer:before {\n  /* 40x40 touch area */\n  content: \"\";\n  position: absolute;\n  z-index: 9999;\n  /* user of this util can scope within a lower z-index */\n  top: 50%;\n  left: 50%;\n  width: 40px;\n  height: 40px;\n  margin-left: -20px;\n  margin-top: -20px; }\n\n/* Event Selection (only for touch devices)\n--------------------------------------------------------------------------------------------------*/\n.fc-event.fc-selected {\n  z-index: 9999 !important;\n  /* overcomes inline z-index */\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); }\n\n.fc-event.fc-selected:after {\n  content: \"\";\n  position: absolute;\n  z-index: 1;\n  /* same z-index as fc-bg, behind text */\n  /* overcome the borders */\n  top: -1px;\n  right: -1px;\n  bottom: -1px;\n  left: -1px;\n  /* darkening effect */\n  background: #000;\n  opacity: .25; }\n\n/* Event Dragging\n--------------------------------------------------------------------------------------------------*/\n.fc-event.fc-dragging.fc-selected {\n  box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3); }\n\n.fc-event.fc-dragging:not(.fc-selected) {\n  opacity: .75; }\n\n/* Horizontal Events\n--------------------------------------------------------------------------------------------------*/\n/* bigger touch area when selected */\n.fc-h-event.fc-selected:before {\n  content: \"\";\n  position: absolute;\n  z-index: 3;\n  /* below resizers */\n  top: -10px;\n  bottom: -10px;\n  left: 0;\n  right: 0; }\n\n/* events that are continuing to/from another week. kill rounded corners and butt up against edge */\n.fc-ltr .fc-h-event.fc-not-start,\n.fc-rtl .fc-h-event.fc-not-end {\n  margin-left: 0;\n  border-left-width: 0;\n  padding-left: 1px;\n  /* replace the border with padding */\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.fc-ltr .fc-h-event.fc-not-end,\n.fc-rtl .fc-h-event.fc-not-start {\n  margin-right: 0;\n  border-right-width: 0;\n  padding-right: 1px;\n  /* replace the border with padding */\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n/* resizer (cursor AND touch devices) */\n/* left resizer  */\n.fc-ltr .fc-h-event .fc-start-resizer,\n.fc-rtl .fc-h-event .fc-end-resizer {\n  cursor: w-resize;\n  left: -1px;\n  /* overcome border */ }\n\n/* right resizer */\n.fc-ltr .fc-h-event .fc-end-resizer,\n.fc-rtl .fc-h-event .fc-start-resizer {\n  cursor: e-resize;\n  right: -1px;\n  /* overcome border */ }\n\n/* resizer (mouse devices) */\n.fc-h-event.fc-allow-mouse-resize .fc-resizer {\n  width: 7px;\n  top: -1px;\n  /* overcome top border */\n  bottom: -1px;\n  /* overcome bottom border */ }\n\n/* resizer (touch devices) */\n.fc-h-event.fc-selected .fc-resizer {\n  /* 8x8 little dot */\n  border-radius: 4px;\n  border-width: 1px;\n  width: 6px;\n  height: 6px;\n  border-style: solid;\n  border-color: inherit;\n  background: #fff;\n  /* vertically center */\n  top: 50%;\n  margin-top: -4px; }\n\n/* left resizer  */\n.fc-ltr .fc-h-event.fc-selected .fc-start-resizer,\n.fc-rtl .fc-h-event.fc-selected .fc-end-resizer {\n  margin-left: -4px;\n  /* centers the 8x8 dot on the left edge */ }\n\n/* right resizer */\n.fc-ltr .fc-h-event.fc-selected .fc-end-resizer,\n.fc-rtl .fc-h-event.fc-selected .fc-start-resizer {\n  margin-right: -4px;\n  /* centers the 8x8 dot on the right edge */ }\n\n/* DayGrid events\n----------------------------------------------------------------------------------------------------\nWe use the full \"fc-day-grid-event\" class instead of using descendants because the event won't\nbe a descendant of the grid when it is being dragged.\n*/\n.fc-day-grid-event {\n  margin: 1px 2px 0;\n  /* spacing between events and edges */\n  padding: 0 1px; }\n\ntr:first-child > td > .fc-day-grid-event {\n  margin-top: 2px;\n  /* a little bit more space before the first event */ }\n\n.fc-mirror-skeleton tr:first-child > td > .fc-day-grid-event {\n  margin-top: 0;\n  /* except for mirror skeleton */ }\n\n.fc-day-grid-event .fc-content {\n  /* force events to be one-line tall */\n  white-space: nowrap;\n  overflow: hidden; }\n\n.fc-day-grid-event .fc-time {\n  font-weight: bold; }\n\n/* resizer (cursor devices) */\n/* left resizer  */\n.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer,\n.fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer {\n  margin-left: -2px;\n  /* to the day cell's edge */ }\n\n/* right resizer */\n.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer,\n.fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer {\n  margin-right: -2px;\n  /* to the day cell's edge */ }\n\n/* Event Limiting\n--------------------------------------------------------------------------------------------------*/\n/* \"more\" link that represents hidden events */\na.fc-more {\n  margin: 1px 3px;\n  font-size: .85em;\n  cursor: pointer;\n  text-decoration: none; }\n\na.fc-more:hover {\n  text-decoration: underline; }\n\n.fc-limited {\n  /* rows and cells that are hidden because of a \"more\" link */\n  display: none; }\n\n/* popover that appears when \"more\" link is clicked */\n.fc-day-grid .fc-row {\n  z-index: 1;\n  /* make the \"more\" popover one higher than this */ }\n\n.fc-more-popover {\n  z-index: 2;\n  width: 220px; }\n\n.fc-more-popover .fc-event-container {\n  padding: 10px; }\n\n/* Now Indicator\n--------------------------------------------------------------------------------------------------*/\n.fc-now-indicator {\n  position: absolute;\n  border: 0 solid red; }\n\n/* Utilities\n--------------------------------------------------------------------------------------------------*/\n.fc-unselectable {\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-touch-callout: none;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }\n\n/*\nTODO: more distinction between this file and common.css\n*/\n/* Colors\n--------------------------------------------------------------------------------------------------*/\n.fc-unthemed th,\n.fc-unthemed td,\n.fc-unthemed thead,\n.fc-unthemed tbody,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-row,\n.fc-unthemed .fc-content,\n.fc-unthemed .fc-popover,\n.fc-unthemed .fc-list-view,\n.fc-unthemed .fc-list-heading td {\n  border-color: #ddd; }\n\n.fc-unthemed .fc-popover {\n  background-color: #fff; }\n\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-popover .fc-header,\n.fc-unthemed .fc-list-heading td {\n  background: #eee; }\n\n.fc-unthemed td.fc-today {\n  background: #fcf8e3; }\n\n.fc-unthemed .fc-disabled-day {\n  background: #d7d7d7;\n  opacity: .3; }\n\n/* Icons\n--------------------------------------------------------------------------------------------------\nfrom https://feathericons.com/ and built with IcoMoon\n*/\n@font-face {\n  font-family: 'fcicons';\n  src: url(\"data:application/x-font-ttf;charset=utf-8;base64,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\") format(\"truetype\");\n  font-weight: normal;\n  font-style: normal; }\n.fc-icon {\n  /* use !important to prevent issues with browser extensions that change fonts */\n  font-family: 'fcicons' !important;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale; }\n\n.fc-icon-chevron-left:before {\n  content: \"\\e900\"; }\n\n.fc-icon-chevron-right:before {\n  content: \"\\e901\"; }\n\n.fc-icon-chevrons-left:before {\n  content: \"\\e902\"; }\n\n.fc-icon-chevrons-right:before {\n  content: \"\\e903\"; }\n\n.fc-icon-minus-square:before {\n  content: \"\\e904\"; }\n\n.fc-icon-plus-square:before {\n  content: \"\\e905\"; }\n\n.fc-icon-x:before {\n  content: \"\\e906\"; }\n\n.fc-icon {\n  display: inline-block;\n  width: 1em;\n  height: 1em;\n  text-align: center; }\n\n/* Buttons\n--------------------------------------------------------------------------------------------------\nLots taken from Flatly (MIT): https://bootswatch.com/4/flatly/bootstrap.css\n*/\n/* reset */\n.fc-button {\n  border-radius: 0;\n  overflow: visible;\n  text-transform: none;\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit; }\n\n.fc-button:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color; }\n\n.fc-button {\n  -webkit-appearance: button; }\n\n.fc-button:not(:disabled) {\n  cursor: pointer; }\n\n.fc-button::-moz-focus-inner {\n  padding: 0;\n  border-style: none; }\n\n/* theme */\n.fc-button {\n  display: inline-block;\n  font-weight: 400;\n  color: #212529;\n  text-align: center;\n  vertical-align: middle;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  background-color: transparent;\n  border: 1px solid transparent;\n  padding: 0.4em 0.65em;\n  font-size: 1em;\n  line-height: 1.5;\n  border-radius: 0.25em; }\n\n.fc-button:hover {\n  color: #212529;\n  text-decoration: none; }\n\n.fc-button:focus {\n  outline: 0;\n  -webkit-box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);\n  box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25); }\n\n.fc-button:disabled {\n  opacity: 0.65; }\n\n/* \"primary\" coloring */\n.fc-button-primary {\n  color: #fff;\n  background-color: #2C3E50;\n  border-color: #2C3E50; }\n\n.fc-button-primary:hover {\n  color: #fff;\n  background-color: #1e2b37;\n  border-color: #1a252f; }\n\n.fc-button-primary:focus {\n  -webkit-box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5);\n  box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5); }\n\n.fc-button-primary:disabled {\n  color: #fff;\n  background-color: #2C3E50;\n  border-color: #2C3E50; }\n\n.fc-button-primary:not(:disabled):active,\n.fc-button-primary:not(:disabled).fc-button-active {\n  color: #fff;\n  background-color: #1a252f;\n  border-color: #151e27; }\n\n.fc-button-primary:not(:disabled):active:focus,\n.fc-button-primary:not(:disabled).fc-button-active:focus {\n  -webkit-box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5);\n  box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5); }\n\n/* icons within buttons */\n.fc-button .fc-icon {\n  vertical-align: middle;\n  font-size: 1.5em; }\n\n/* Buttons Groups\n--------------------------------------------------------------------------------------------------*/\n.fc-button-group {\n  position: relative;\n  display: -webkit-inline-box;\n  display: -ms-inline-flexbox;\n  display: inline-flex;\n  vertical-align: middle; }\n\n.fc-button-group > .fc-button {\n  position: relative;\n  -webkit-box-flex: 1;\n  -ms-flex: 1 1 auto;\n  flex: 1 1 auto; }\n\n.fc-button-group > .fc-button:hover {\n  z-index: 1; }\n\n.fc-button-group > .fc-button:focus,\n.fc-button-group > .fc-button:active,\n.fc-button-group > .fc-button.fc-button-active {\n  z-index: 1; }\n\n.fc-button-group > .fc-button:not(:first-child) {\n  margin-left: -1px; }\n\n.fc-button-group > .fc-button:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.fc-button-group > .fc-button:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n/* Popover\n--------------------------------------------------------------------------------------------------*/\n.fc-unthemed .fc-popover {\n  border-width: 1px;\n  border-style: solid; }\n\n/* List View\n--------------------------------------------------------------------------------------------------*/\n.fc-unthemed .fc-list-item:hover td {\n  background-color: #f5f5f5; }\n\n/* Toolbar\n--------------------------------------------------------------------------------------------------*/\n.fc-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center; }\n\n.fc-toolbar.fc-header-toolbar {\n  margin-bottom: 1.5em; }\n\n.fc-toolbar.fc-footer-toolbar {\n  margin-top: 1.5em; }\n\n/* inner content */\n.fc-toolbar > * > :not(:first-child) {\n  margin-left: .75em; }\n\n.fc-toolbar h2 {\n  font-size: 1.75em;\n  margin: 0; }\n\n/* View Structure\n--------------------------------------------------------------------------------------------------*/\n.fc-view-container {\n  position: relative; }\n\n/* undo twitter bootstrap's box-sizing rules. normalizes positioning techniques */\n/* don't do this for the toolbar because we'll want bootstrap to style those buttons as some pt */\n.fc-view-container *,\n.fc-view-container *:before,\n.fc-view-container *:after {\n  -webkit-box-sizing: content-box;\n  -moz-box-sizing: content-box;\n  box-sizing: content-box; }\n\n.fc-view,\n.fc-view > table {\n  /* so dragged elements can be above the view's main element */\n  position: relative;\n  z-index: 1; }\n\n@media print {\n  .fc {\n    max-width: 100% !important; }\n\n  /* Global Event Restyling\n  --------------------------------------------------------------------------------------------------*/\n  .fc-event {\n    background: #fff !important;\n    color: #000 !important;\n    page-break-inside: avoid; }\n\n  .fc-event .fc-resizer {\n    display: none; }\n\n  /* Table & Day-Row Restyling\n  --------------------------------------------------------------------------------------------------*/\n  .fc th,\n  .fc td,\n  .fc hr,\n  .fc thead,\n  .fc tbody,\n  .fc-row {\n    border-color: #ccc !important;\n    background: #fff !important; }\n\n  /* kill the overlaid, absolutely-positioned components */\n  /* common... */\n  .fc-bg,\n  .fc-bgevent-skeleton,\n  .fc-highlight-skeleton,\n  .fc-mirror-skeleton,\n  .fc-bgevent-container,\n  .fc-business-container,\n  .fc-highlight-container,\n  .fc-mirror-container {\n    display: none; }\n\n  /* don't force a min-height on rows (for DayGrid) */\n  .fc tbody .fc-row {\n    height: auto !important;\n    /* undo height that JS set in distributeHeight */\n    min-height: 0 !important;\n    /* undo the min-height from each view's specific stylesheet */ }\n\n  .fc tbody .fc-row .fc-content-skeleton {\n    position: static;\n    /* undo .fc-rigid */\n    padding-bottom: 0 !important;\n    /* use a more border-friendly method for this... */ }\n\n  .fc tbody .fc-row .fc-content-skeleton tbody tr:last-child td {\n    /* only works in newer browsers */\n    padding-bottom: 1em;\n    /* ...gives space within the skeleton. also ensures min height in a way */ }\n\n  .fc tbody .fc-row .fc-content-skeleton table {\n    /* provides a min-height for the row, but only effective for IE, which exaggerates this value,\n       making it look more like 3em. for other browers, it will already be this tall */\n    height: 1em; }\n\n  /* Undo month-view event limiting. Display all events and hide the \"more\" links\n  --------------------------------------------------------------------------------------------------*/\n  .fc-more-cell,\n  .fc-more {\n    display: none !important; }\n\n  .fc tr.fc-limited {\n    display: table-row !important; }\n\n  .fc td.fc-limited {\n    display: table-cell !important; }\n\n  .fc-popover {\n    display: none;\n    /* never display the \"more..\" popover in print mode */ }\n\n  /* TimeGrid Restyling\n  --------------------------------------------------------------------------------------------------*/\n  /* undo the min-height 100% trick used to fill the container's height */\n  .fc-time-grid {\n    min-height: 0 !important; }\n\n  /* don't display the side axis at all (\"all-day\" and time cells) */\n  .fc-timeGrid-view .fc-axis {\n    display: none; }\n\n  /* don't display the horizontal lines */\n  .fc-slats,\n  .fc-time-grid hr {\n    /* this hr is used when height is underused and needs to be filled */\n    display: none !important;\n    /* important overrides inline declaration */ }\n\n  /* let the container that holds the events be naturally positioned and create real height */\n  .fc-time-grid .fc-content-skeleton {\n    position: static; }\n\n  /* in case there are no events, we still want some height */\n  .fc-time-grid .fc-content-skeleton table {\n    height: 4em; }\n\n  /* kill the horizontal spacing made by the event container. event margins will be done below */\n  .fc-time-grid .fc-event-container {\n    margin: 0 !important; }\n\n  /* TimeGrid *Event* Restyling\n  --------------------------------------------------------------------------------------------------*/\n  /* naturally position events, vertically stacking them */\n  .fc-time-grid .fc-event {\n    position: static !important;\n    margin: 3px 2px !important; }\n\n  /* for events that continue to a future day, give the bottom border back */\n  .fc-time-grid .fc-event.fc-not-end {\n    border-bottom-width: 1px !important; }\n\n  /* indicate the event continues via \"...\" text */\n  .fc-time-grid .fc-event.fc-not-end:after {\n    content: \"...\"; }\n\n  /* for events that are continuations from previous days, give the top border back */\n  .fc-time-grid .fc-event.fc-not-start {\n    border-top-width: 1px !important; }\n\n  /* indicate the event is a continuation via \"...\" text */\n  .fc-time-grid .fc-event.fc-not-start:before {\n    content: \"...\"; }\n\n  /* time */\n  /* undo a previous declaration and let the time text span to a second line */\n  .fc-time-grid .fc-event .fc-time {\n    white-space: normal !important; }\n\n  /* hide the the time that is normally displayed... */\n  .fc-time-grid .fc-event .fc-time span {\n    display: none; }\n\n  /* ...replace it with a more verbose version (includes AM/PM) stored in an html attribute */\n  .fc-time-grid .fc-event .fc-time:after {\n    content: attr(data-full); }\n\n  /* Vertical Scroller & Containers\n  --------------------------------------------------------------------------------------------------*/\n  /* kill the scrollbars and allow natural height */\n  .fc-scroller,\n  .fc-day-grid-container,\n  .fc-time-grid-container {\n    /* */\n    overflow: visible !important;\n    height: auto !important; }\n\n  /* kill the horizontal border/padding used to compensate for scrollbars */\n  .fc-row {\n    border: 0 !important;\n    margin: 0 !important; }\n\n  /* Button Controls\n  --------------------------------------------------------------------------------------------------*/\n  .fc-button-group,\n  .fc button {\n    display: none;\n    /* don't display any button-related controls */ } }\n\n/*!\nFullCalendar Day Grid Plugin v4.2.0\nDocs & License: https://fullcalendar.io/\n(c) 2019 Adam Shaw\n*/\n/* DayGridView\n--------------------------------------------------------------------------------------------------*/\n/* day row structure */\n.fc-dayGridWeek-view .fc-content-skeleton,\n.fc-dayGridDay-view .fc-content-skeleton {\n  /* there may be week numbers in these views, so no padding-top */\n  padding-bottom: 1em;\n  /* ensure a space at bottom of cell for user selecting/clicking */ }\n\n.fc-dayGrid-view .fc-body .fc-row {\n  min-height: 4em;\n  /* ensure that all rows are at least this tall */ }\n\n/* a \"rigid\" row will take up a constant amount of height because content-skeleton is absolute */\n.fc-row.fc-rigid {\n  overflow: hidden; }\n\n.fc-row.fc-rigid .fc-content-skeleton {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0; }\n\n/* week and day number styling */\n.fc-day-top.fc-other-month {\n  opacity: 0.3; }\n\n.fc-dayGrid-view .fc-week-number,\n.fc-dayGrid-view .fc-day-number {\n  padding: 2px; }\n\n.fc-dayGrid-view th.fc-week-number,\n.fc-dayGrid-view th.fc-day-number {\n  padding: 0 2px;\n  /* column headers can't have as much v space */ }\n\n.fc-ltr .fc-dayGrid-view .fc-day-top .fc-day-number {\n  float: right; }\n\n.fc-rtl .fc-dayGrid-view .fc-day-top .fc-day-number {\n  float: left; }\n\n.fc-ltr .fc-dayGrid-view .fc-day-top .fc-week-number {\n  float: left;\n  border-radius: 0 0 3px 0; }\n\n.fc-rtl .fc-dayGrid-view .fc-day-top .fc-week-number {\n  float: right;\n  border-radius: 0 0 0 3px; }\n\n.fc-dayGrid-view .fc-day-top .fc-week-number {\n  min-width: 1.5em;\n  text-align: center;\n  background-color: #f2f2f2;\n  color: #808080; }\n\n/* when week/day number have own column */\n.fc-dayGrid-view td.fc-week-number {\n  text-align: center; }\n\n.fc-dayGrid-view td.fc-week-number > * {\n  /* work around the way we do column resizing and ensure a minimum width */\n  display: inline-block;\n  min-width: 1.25em; }\n\n/*!\nFullCalendar List View Plugin v4.2.0\nDocs & License: https://fullcalendar.io/\n(c) 2019 Adam Shaw\n*/\n/* List View\n--------------------------------------------------------------------------------------------------*/\n/* possibly reusable */\n.fc-event-dot {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  border-radius: 5px; }\n\n/* view wrapper */\n.fc-rtl .fc-list-view {\n  direction: rtl;\n  /* unlike core views, leverage browser RTL */ }\n\n.fc-list-view {\n  border-width: 1px;\n  border-style: solid; }\n\n/* table resets */\n.fc .fc-list-table {\n  table-layout: auto;\n  /* for shrinkwrapping cell content */ }\n\n.fc-list-table td {\n  border-width: 1px 0 0;\n  padding: 8px 14px; }\n\n.fc-list-table tr:first-child td {\n  border-top-width: 0; }\n\n/* day headings with the list */\n.fc-list-heading {\n  border-bottom-width: 1px; }\n\n.fc-list-heading td {\n  font-weight: bold; }\n\n.fc-ltr .fc-list-heading-main {\n  float: left; }\n\n.fc-ltr .fc-list-heading-alt {\n  float: right; }\n\n.fc-rtl .fc-list-heading-main {\n  float: right; }\n\n.fc-rtl .fc-list-heading-alt {\n  float: left; }\n\n/* event list items */\n.fc-list-item.fc-has-url {\n  cursor: pointer;\n  /* whole row will be clickable */ }\n\n.fc-list-item-marker,\n.fc-list-item-time {\n  white-space: nowrap;\n  width: 1px; }\n\n/* make the dot closer to the event title */\n.fc-ltr .fc-list-item-marker {\n  padding-right: 0; }\n\n.fc-rtl .fc-list-item-marker {\n  padding-left: 0; }\n\n.fc-list-item-title a {\n  /* every event title cell has an <a> tag */\n  text-decoration: none;\n  color: inherit; }\n\n.fc-list-item-title a[href]:hover {\n  /* hover effect only on titles with hrefs */\n  text-decoration: underline; }\n\n/* message when no events */\n.fc-list-empty-wrap2 {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0; }\n\n.fc-list-empty-wrap1 {\n  width: 100%;\n  height: 100%;\n  display: table; }\n\n.fc-list-empty {\n  display: table-cell;\n  vertical-align: middle;\n  text-align: center; }\n\n.fc-unthemed .fc-list-empty {\n  /* theme will provide own background */\n  background-color: #eee; }\n\n/*!\nFullCalendar Time Grid Plugin v4.2.0\nDocs & License: https://fullcalendar.io/\n(c) 2019 Adam Shaw\n*/\n/* TimeGridView all-day area\n--------------------------------------------------------------------------------------------------*/\n.fc-timeGrid-view .fc-day-grid {\n  position: relative;\n  z-index: 2;\n  /* so the \"more..\" popover will be over the time grid */ }\n\n.fc-timeGrid-view .fc-day-grid .fc-row {\n  min-height: 3em;\n  /* all-day section will never get shorter than this */ }\n\n.fc-timeGrid-view .fc-day-grid .fc-row .fc-content-skeleton {\n  padding-bottom: 1em;\n  /* give space underneath events for clicking/selecting days */ }\n\n/* TimeGrid axis running down the side (for both the all-day area and the slot area)\n--------------------------------------------------------------------------------------------------*/\n.fc .fc-axis {\n  /* .fc to overcome default cell styles */\n  vertical-align: middle;\n  padding: 0 4px;\n  white-space: nowrap; }\n\n.fc-ltr .fc-axis {\n  text-align: right; }\n\n.fc-rtl .fc-axis {\n  text-align: left; }\n\n/* TimeGrid Structure\n--------------------------------------------------------------------------------------------------*/\n.fc-time-grid-container,\n.fc-time-grid {\n  /* so slats/bg/content/etc positions get scoped within here */\n  position: relative;\n  z-index: 1; }\n\n.fc-time-grid {\n  min-height: 100%;\n  /* so if height setting is 'auto', .fc-bg stretches to fill height */ }\n\n.fc-time-grid table {\n  /* don't put outer borders on slats/bg/content/etc */\n  border: 0 hidden transparent; }\n\n.fc-time-grid > .fc-bg {\n  z-index: 1; }\n\n.fc-time-grid .fc-slats,\n.fc-time-grid > hr {\n  /* the <hr> TimeGridView injects when grid is shorter than scroller */\n  position: relative;\n  z-index: 2; }\n\n.fc-time-grid .fc-content-col {\n  position: relative;\n  /* because now-indicator lives directly inside */ }\n\n.fc-time-grid .fc-content-skeleton {\n  position: absolute;\n  z-index: 3;\n  top: 0;\n  left: 0;\n  right: 0; }\n\n/* divs within a cell within the fc-content-skeleton */\n.fc-time-grid .fc-business-container {\n  position: relative;\n  z-index: 1; }\n\n.fc-time-grid .fc-bgevent-container {\n  position: relative;\n  z-index: 2; }\n\n.fc-time-grid .fc-highlight-container {\n  position: relative;\n  z-index: 3; }\n\n.fc-time-grid .fc-event-container {\n  position: relative;\n  z-index: 4; }\n\n.fc-time-grid .fc-now-indicator-line {\n  z-index: 5; }\n\n.fc-time-grid .fc-mirror-container {\n  /* also is fc-event-container */\n  position: relative;\n  z-index: 6; }\n\n/* TimeGrid Slats (lines that run horizontally)\n--------------------------------------------------------------------------------------------------*/\n.fc-time-grid .fc-slats td {\n  height: 1.5em;\n  border-bottom: 0;\n  /* each cell is responsible for its top border */ }\n\n.fc-time-grid .fc-slats .fc-minor td {\n  border-top-style: dotted; }\n\n/* TimeGrid Highlighting Slots\n--------------------------------------------------------------------------------------------------*/\n.fc-time-grid .fc-highlight-container {\n  /* a div within a cell within the fc-highlight-skeleton */\n  position: relative;\n  /* scopes the left/right of the fc-highlight to be in the column */ }\n\n.fc-time-grid .fc-highlight {\n  position: absolute;\n  left: 0;\n  right: 0;\n  /* top and bottom will be in by JS */ }\n\n/* TimeGrid Event Containment\n--------------------------------------------------------------------------------------------------*/\n.fc-ltr .fc-time-grid .fc-event-container {\n  /* space on the sides of events for LTR (default) */\n  margin: 0 2.5% 0 2px; }\n\n.fc-rtl .fc-time-grid .fc-event-container {\n  /* space on the sides of events for RTL */\n  margin: 0 2px 0 2.5%; }\n\n.fc-time-grid .fc-event,\n.fc-time-grid .fc-bgevent {\n  position: absolute;\n  z-index: 1;\n  /* scope inner z-index's */ }\n\n.fc-time-grid .fc-bgevent {\n  /* background events always span full width */\n  left: 0;\n  right: 0; }\n\n/* TimeGrid Event Styling\n----------------------------------------------------------------------------------------------------\nWe use the full \"fc-time-grid-event\" class instead of using descendants because the event won't\nbe a descendant of the grid when it is being dragged.\n*/\n.fc-time-grid-event {\n  margin-bottom: 1px; }\n\n.fc-time-grid-event-inset {\n  -webkit-box-shadow: 0px 0px 0px 1px #fff;\n  box-shadow: 0px 0px 0px 1px #fff; }\n\n.fc-time-grid-event.fc-not-start {\n  /* events that are continuing from another day */\n  /* replace space made by the top border with padding */\n  border-top-width: 0;\n  padding-top: 1px;\n  /* remove top rounded corners */\n  border-top-left-radius: 0;\n  border-top-right-radius: 0; }\n\n.fc-time-grid-event.fc-not-end {\n  /* replace space made by the top border with padding */\n  border-bottom-width: 0;\n  padding-bottom: 1px;\n  /* remove bottom rounded corners */\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.fc-time-grid-event .fc-content {\n  overflow: hidden;\n  max-height: 100%; }\n\n.fc-time-grid-event .fc-time,\n.fc-time-grid-event .fc-title {\n  padding: 0 1px; }\n\n.fc-time-grid-event .fc-time {\n  font-size: .85em;\n  white-space: nowrap; }\n\n/* short mode, where time and title are on the same line */\n.fc-time-grid-event.fc-short .fc-content {\n  /* don't wrap to second line (now that contents will be inline) */\n  white-space: nowrap; }\n\n.fc-time-grid-event.fc-short .fc-time,\n.fc-time-grid-event.fc-short .fc-title {\n  /* put the time and title on the same line */\n  display: inline-block;\n  vertical-align: top; }\n\n.fc-time-grid-event.fc-short .fc-time span {\n  display: none;\n  /* don't display the full time text... */ }\n\n.fc-time-grid-event.fc-short .fc-time:before {\n  content: attr(data-start);\n  /* ...instead, display only the start time */ }\n\n.fc-time-grid-event.fc-short .fc-time:after {\n  content: \"\\000A0-\\000A0\";\n  /* seperate with a dash, wrapped in nbsp's */ }\n\n.fc-time-grid-event.fc-short .fc-title {\n  font-size: .85em;\n  /* make the title text the same size as the time */\n  padding: 0;\n  /* undo padding from above */ }\n\n/* resizer (cursor device) */\n.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer {\n  left: 0;\n  right: 0;\n  bottom: 0;\n  height: 8px;\n  overflow: hidden;\n  line-height: 8px;\n  font-size: 11px;\n  font-family: monospace;\n  text-align: center;\n  cursor: s-resize; }\n\n.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer:after {\n  content: \"=\"; }\n\n/* resizer (touch device) */\n.fc-time-grid-event.fc-selected .fc-resizer {\n  /* 10x10 dot */\n  border-radius: 5px;\n  border-width: 1px;\n  width: 8px;\n  height: 8px;\n  border-style: solid;\n  border-color: inherit;\n  background: #fff;\n  /* horizontally center */\n  left: 50%;\n  margin-left: -5px;\n  /* center on the bottom edge */\n  bottom: -5px; }\n\n/* Now Indicator\n--------------------------------------------------------------------------------------------------*/\n.fc-time-grid .fc-now-indicator-line {\n  border-top-width: 1px;\n  left: 0;\n  right: 0; }\n\n/* arrow on axis */\n.fc-time-grid .fc-now-indicator-arrow {\n  margin-top: -5px;\n  /* vertically center on top coordinate */ }\n\n.fc-ltr .fc-time-grid .fc-now-indicator-arrow {\n  left: 0;\n  /* triangle pointing right... */\n  border-width: 5px 0 5px 6px;\n  border-top-color: transparent;\n  border-bottom-color: transparent; }\n\n.fc-rtl .fc-time-grid .fc-now-indicator-arrow {\n  right: 0;\n  /* triangle pointing left... */\n  border-width: 5px 6px 5px 0;\n  border-top-color: transparent;\n  border-bottom-color: transparent; }\n\n/*!\nFullCalendar Bootstrap Plugin v4.2.0\nDocs & License: https://fullcalendar.io/\n(c) 2019 Adam Shaw\n*/\n.fc.fc-bootstrap a {\n  text-decoration: none; }\n\n.fc.fc-bootstrap a[data-goto]:hover {\n  text-decoration: underline; }\n\n.fc-bootstrap hr.fc-divider {\n  border-color: inherit; }\n\n.fc-bootstrap .fc-today.alert {\n  border-radius: 0; }\n\n.fc-bootstrap a.fc-event:not([href]):not([tabindex]) {\n  color: #fff; }\n\n.fc-bootstrap .fc-popover.card {\n  position: absolute; }\n\n/* Popover\n--------------------------------------------------------------------------------------------------*/\n.fc-bootstrap .fc-popover .card-body {\n  padding: 0; }\n\n/* TimeGrid Slats (lines that run horizontally)\n--------------------------------------------------------------------------------------------------*/\n.fc-bootstrap .fc-time-grid .fc-slats table {\n  /* some themes have background color. see through to slats */\n  background: none; }\n\n@import './src/scss/_imports/_global-import';\r\n\r\n\r\n/*.panel {\r\n\t.panel-content.p-0 {\r\n\t\t.fc-bootstrap {\r\n\t\t\ttable {\r\n\t\t\t\tborder-left: none;\r\n\t\t\t\tborder-right: none;\r\n\r\n\t\t\t\ttd:first-child,\r\n\t\t\t\tth:first-child {\r\n\t\t\t\t\tborder-left: none;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttd:last-child,\r\n\t\t\t\tth:last-child {\r\n\t\t\t\t\tborder-right: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.fc-toolbar.fc-header-toolbar,\r\n\t\t.fc-toolbar.fc-footer-toolbar {\r\n\t\t\tpadding: 1rem;\r\n\t\t\tmargin: 0;\r\n\t\t}\r\n\r\n\t\t.fc-row.fc-rigid:last-child {\r\n\t\t\ttd,\r\n\t\t\tth {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n}\r\n*/\r\n\r\n.fc-day-grid-event .fc-time {\r\n\tdisplay: block;\r\n}\r\n\r\n.fc-day-grid-event .fc-content {\r\n\tpadding: 0.25rem 0.5rem;\r\n}\r\n\r\n.fc-popover .fc-header {\r\n\tpadding: 0.5rem 0.75rem;\r\n}\r\n\r\n.fc-more-popover .fc-event-container {\r\n\tpadding: 0.5rem;\t\r\n}\r\n\r\n.fc-popover .fc-header .fc-title {\r\n\tfont-weight: bold;\r\n}\r\n\r\n.fc-toolbar h2 {\r\n\tfont-size: 1rem;\r\n\tfont-weight: 500;\r\n\ttext-transform: uppercase;\r\n}\r\n   \r\n.fc-event, \r\n.fc-event-dot {\r\n\tbackground: $primary-400;\r\n}\r\n\r\n.fc-event {\r\n\tborder: 1px solid $primary-600;\r\n}\r\n\r\n\r\n.fc-day-top.fc-other-month {\r\n\topacity: 1;\r\n}\r\n\r\n.fc-other-month {\r\n    background-image: linear-gradient(135deg,rgba(0,0,0,.02)25%,transparent 25%,transparent 50%,rgba(0,0,0,.02)50%,rgba(0,0,0,.02)75%,transparent 75%,transparent);\r\n    background-color: #FAFCFD;\r\n    background-size: 1rem 1rem;\r\n}\r\n\r\n/*.fc-day.fc-other-month.fc-future {\r\n\tbackground-color: lighten($success-50, 25%);\r\n}\r\n*/\r\n\r\n\r\n.fc-head-container thead tr {\r\n    background-image: linear-gradient(to top,#f2f2f2 0,#fafafa 100%);\r\n}\r\n\r\n@media only screen and ( max-width: map-get($grid-breakpoints, md) ){\r\n\t/*.panel {\r\n\t\t.panel-content.p-0 {\r\n\t\t\t.fc-toolbar.fc-header-toolbar,\r\n\t\t\t.fc-toolbar.fc-footer-toolbar {\r\n\t\t\t\tpadding: $panel-spacer-y/2 10px;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}*/\r\n\r\n\t.fc-day-grid-event .fc-time {\r\n\t\tdisplay: inline;\r\n\t}\t\r\n\r\n\t.fc-bootstrap:not(.fc-reset-order) {\r\n\t\t.fc-toolbar {\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\th2 {\r\n\t\t\t\tfont-size: 1.125rem;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\r\n\t\t\t\r\n\t\t\t.fc-left {\r\n\t\t\t\torder: 1;\r\n\t\t\t}\r\n\t\t\t.fc-right {\r\n\t\t\t\torder: 2;\r\n\t\t\t}\r\n\t\t\t.fc-center {\r\n\t\t\t\torder: 3;\r\n\t\t\t}\r\n\r\n\t\t\t> div:not(:empty):not(:first-child) {\r\n\t\t\t\tmargin-top: 0.5rem;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "@import url($font-import);\r\n\r\nbody {\r\n\tfont-family: $page-font;\r\n\tfont-size: rem($fs-base);\r\n\tletter-spacing: 0.1px;\r\n}\r\n\r\n.page-content {\r\n\tcolor: $base-text-color;\r\n}\r\n\r\nh1, h2, h3, h4, h5, h6 {\r\n\tline-height: 1.3;\r\n\tfont-weight: 400;\r\n\t//color:$fusion-500;\r\n}\r\n\r\nstrong {\r\n\tfont-weight: 500;\r\n}\r\n\r\nh1 small, \r\nh2 small, \r\nh3 small, \r\nh4 small, \r\nh5 small, \r\nh6 small, \r\n.h1 small, \r\n.h2 small, \r\n.h3 small, \r\n.h4 small, \r\n.h5 small, \r\n.h6 small {\r\n    font-weight: 300;\r\n    display: block;\r\n\tfont-size: rem($fs-lg);\r\n    line-height: 1.5;\r\n    //letter-spacing: -0.2px;\r\n    margin:2px 0 ($grid-gutter-width-base / 2);\r\n}\r\n\r\nh2 small, \r\nh3 small, \r\n.h2 small, \r\n.h3 small, {\r\n\tfont-size: rem($fs-lg);\r\n}\r\n\r\nh4 small, \r\n.h4 small {\r\n\tfont-size: rem($fs-md);\r\n}\r\n\r\nh5 small, \r\nh6 small, \r\n.h5 small, \r\n.h6 small {\r\n\tfont-size: rem($fs-base);\t\r\n}\r\n\r\n/* contrast text */\r\n.text-contrast {\r\n\tcolor: lighten($black, 20%);\r\n}\r\n\r\n/* text-gradient */\r\n.text-gradient {\r\n\tbackground: -webkit-linear-gradient(180deg, $primary-700 25%, $primary-800 50%, $info-700 75%, $info-900 100%);\r\n\tbackground: linear-gradient(180deg, $primary-700 25%, $primary-800 50%, $info-700 75%, $info-900 100%);\r\n\tcolor: $primary-500;\r\n    background-clip: text;\r\n    text-fill-color: transparent;\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    text-shadow: none;\r\n}\r\n\r\n/* looking for font size? Check _helpers.scss */", "/* #BOOTSTRAP AND MIXINS - Base Unmodified Bootstrap file with theme mixins\r\n========================================================================== */\r\n@import './node_modules/bootstrap/scss/functions';\r\n@import './node_modules/bootstrap/scss/variables'; \r\n@import './node_modules/bootstrap/scss/mixins';\r\n@import './src/scss/_mixins/mixins';\r\n\r\n/* #BASE - Base Variable file along with font library, and colors.\r\n========================================================================== */\r\n@import './src/scss/_modules/variables';\r\n@import './src/scss/_modules/_fonts';\r\n@import './src/scss/_modules/_placeholders';\r\n@import './src/scss/_modules/_custom';", "/*---------------------------------------------------\r\n    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) \r\n  -------------------------------- -------------------\r\n    LESS ELEMENTS made by <PERSON> (http://fadeyev.net)\r\n    SASS port by <PERSON> (http://samuelbeek.com) \r\n  ---------------------------------------------------*/\r\n \r\n@mixin gradient-img($start: #EEE,$stop: #FFF) {\r\n  background-color: $start;\r\n  background-image: -webkit-linear-gradient(top,$start,$stop);\r\n  background-image: linear-gradient(to top,$start,$stop);\r\n}\r\n\r\n@mixin gradient($color: #F5F5F5,$start: #EEE,$stop: #FFF) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,$start),color-stop(1,$stop));\r\n    background:-ms-linear-gradient(bottom,$start,$stop);\r\n    background:-moz-linear-gradient(center bottom,$start 0%,$stop 100%);\r\n    background:-o-linear-gradient($stop,$start);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=$start,endColorstr=$stop)\r\n}\r\n\r\n@mixin bw-gradient($color: #F5F5F5,$start: 0,$stop: 255) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,#000),color-stop(1,#000));\r\n    background:-ms-linear-gradient(bottom,#000 0%,#000 100%);\r\n    background:-moz-linear-gradient(center bottom,#000 0%,#000 100%);\r\n    background:-o-linear-gradient(#000,#000);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=rgb($start,$start,$start),endColorstr=rgb($stop,$stop,$stop))\r\n}\r\n\r\n@mixin bordered($top-color: #EEE,$right-color: #EEE,$bottom-color: #EEE,$left-color: #EEE) {\r\n    border-top:solid 1px $top-color;\r\n    border-left:solid 1px $left-color;\r\n    border-right:solid 1px $right-color;\r\n    border-bottom:solid 1px $bottom-color\r\n}\r\n\r\n@mixin drop-shadow($x-axis: 0,$y-axis: 1px,$blur: 2px,$alpha: 0.1) {\r\n    //-webkit-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    //-moz-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin rounded($radius: 2px) {\r\n    border-radius:$radius\r\n}\r\n\r\n@mixin border-radius($topright: 0,$bottomright: 0,$bottomleft: 0,$topleft: 0) {\r\n    border-top-right-radius:$topright;\r\n    border-bottom-right-radius:$bottomright;\r\n    border-bottom-left-radius:$bottomleft;\r\n    border-top-left-radius:$topleft\r\n}\r\n\r\n@mixin opacity($opacity: 0.5) {\r\n    -moz-opacity:$opacity;\r\n    -khtml-opacity:$opacity;\r\n    -webkit-opacity:$opacity;\r\n    opacity:$opacity;\r\n    $opperc:$opacity * 100\r\n/*\r\n  -ms-filter: ~\"progid:DXImageTransform.Microsoft.Alpha(opacity=${opperc})\";\r\n  filter: ~\"alpha(opacity=${opperc})\";\r\n*/\r\n}\r\n\r\n@mixin transition-duration($duration: 0.2s) {\r\n    -moz-transition-duration:$duration;\r\n    -webkit-transition-duration:$duration;\r\n    -o-transition-duration:$duration;\r\n    transition-duration:$duration\r\n}\r\n\r\n@mixin transform($arguments) {\r\n    -webkit-transform:$arguments;\r\n    -moz-transform:$arguments;\r\n    -o-transform:$arguments;\r\n    -ms-transform:$arguments;\r\n    transform:$arguments\r\n}\r\n\r\n@mixin rotation($deg:5deg) {\r\n}\r\n\r\n@mixin scale($ratio:1.5) {\r\n}\r\n\r\n@mixin transition($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:all $duration $ease;\r\n    -moz-transition:all $duration $ease;\r\n    -o-transition:all $duration $ease;\r\n    transition:all $duration $ease\r\n}\r\n\r\n@mixin transition-color($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:color $duration $ease;\r\n    -moz-transition:color $duration $ease;\r\n    -o-transition:color $duration $ease;\r\n    transition:color $duration $ease\r\n}\r\n\r\n@mixin transition-border($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:border $duration $ease;\r\n    -moz-transition:border $duration $ease;\r\n    -o-transition:border $duration $ease;\r\n    transition:border $duration $ease\r\n}\r\n\r\n@mixin transition-background-color($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:background-color $duration $ease;\r\n    -moz-transition:background-color $duration $ease;\r\n    -o-transition:background-color $duration $ease;\r\n    transition:background-color $duration $ease\r\n}\r\n\r\n@mixin transition-fill($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:fill $duration $ease;\r\n    -moz-transition:fill $duration $ease;\r\n    -o-transition:fill $duration $ease;\r\n    transition:fill $duration $ease\r\n}\r\n\r\n@mixin inner-shadow($horizontal:0,$vertical:1px,$blur:2px,$alpha: 0.4) {\r\n    -webkit-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    -moz-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin box-shadow($arguments) {\r\n    //-webkit-box-shadow:$arguments;\r\n    //-moz-box-shadow:$arguments;\r\n    box-shadow:$arguments\r\n}\r\n\r\n@mixin box-sizing($sizing: border-box) {\r\n    //-ms-box-sizing:$sizing;\r\n    //-moz-box-sizing:$sizing;\r\n    //-webkit-box-sizing:$sizing;\r\n    box-sizing:$sizing\r\n}\r\n\r\n@mixin user-select($argument: none) {\r\n    -webkit-user-select:$argument;\r\n    -moz-user-select:$argument;\r\n    -ms-user-select:$argument;\r\n    user-select:$argument\r\n}\r\n\r\n@mixin columns($colwidth: 250px,$colcount: 0,$colgap: 50px,$columnRuleColor: #EEE,$columnRuleStyle: solid,$columnRuleWidth: 1px) {\r\n    -moz-column-width:$colwidth;\r\n    -moz-column-count:$colcount;\r\n    -moz-column-gap:$colgap;\r\n    -moz-column-rule-color:$columnRuleColor;\r\n    -moz-column-rule-style:$columnRuleStyle;\r\n    -moz-column-rule-width:$columnRuleWidth;\r\n    -webkit-column-width:$colwidth;\r\n    -webkit-column-count:$colcount;\r\n    -webkit-column-gap:$colgap;\r\n    -webkit-column-rule-color:$columnRuleColor;\r\n    -webkit-column-rule-style:$columnRuleStyle;\r\n    -webkit-column-rule-width:$columnRuleWidth;\r\n    column-width:$colwidth;\r\n    column-count:$colcount;\r\n    column-gap:$colgap;\r\n    column-rule-color:$columnRuleColor;\r\n    column-rule-style:$columnRuleStyle;\r\n    column-rule-width:$columnRuleWidth\r\n}\r\n\r\n@mixin translate($x:0,$y:0) {\r\n    -webkit-transform: translate($x,$y);\r\n    -moz-transform: translate($x,$y);\r\n    -ms-transform: translate($x,$y);\r\n    -o-transform: translate($x,$y);\r\n    transform: translate($x,$y);\r\n}\r\n\r\n@mixin translate3d($x:0,$y:0,$z:0) {\r\n  -webkit-transform: translate3d($x, $y, $z);\r\n      -ms-transform: translate3d($x, $y, $z); \r\n          transform: translate3d($x, $y, $z); \r\n}\r\n\r\n@mixin background-clip($argument: padding-box) {\r\n    -moz-background-clip:$argument;\r\n    -webkit-background-clip:$argument;\r\n    background-clip:$argument\r\n}\r\n\r\n@mixin transform($transforms) {\r\n     -moz-transform: $transforms;\r\n       -o-transform: $transforms;\r\n      -ms-transform: $transforms;\r\n  -webkit-transform: $transforms;\r\n          transform: $transforms;\r\n}\r\n// rotate\r\n@mixin rotate ($deg) {\r\n  @include transform(rotate(#{$deg}deg));\r\n}\r\n \r\n// scale\r\n@mixin scale($scale) {\r\n   @include transform(scale($scale));\r\n} \r\n// translate\r\n@mixin translate ($x, $y) {\r\n   @include transform(translate($x, $y));\r\n}\r\n// skew\r\n@mixin skew ($x, $y) {\r\n   @include transform(skew(#{$x}deg, #{$y}deg));\r\n}\r\n//transform origin\r\n@mixin transform-origin ($origin) {\r\n    -moz-transform-origin: $origin;\r\n       -o-transform-origin: $origin;\r\n      -ms-transform-origin: $origin;\r\n  -webkit-transform-origin: $origin;\r\n          transform-origin: $origin;\r\n}\r\n\r\n//Rem size support\r\n\r\n/*------------------------\r\n    Usage\r\n\r\n    h1 {\r\n      font-size: rem(32);\r\n    }\r\n\r\n    OR:\r\n\r\n    h1 {\r\n      font-size: rem(32px);\r\n    }\r\n------------------------*/\r\n\r\n$browser-context: 16;\r\n\r\n@function rem($pixels, $context: $browser-context) {\r\n  @if (unitless($pixels)) {\r\n    $pixels: $pixels * 1px;\r\n  }\r\n\r\n  @if (unitless($context)) {\r\n    $context: $context * 1px;\r\n  }\r\n\r\n  @return $pixels / $context * 1rem;\r\n}\r\n\r\n/*------------------------\r\n  FADE IN\r\n  e.g. @include fadeIn( 2s );\r\n------------------------*/\r\n\r\n//$prefix:'-moz-', '-webkit-', '-o-', '-ms-', '';\r\n//\r\n//@mixin keyframe-fadeIn {\r\n//  0%   { opacity:0; }\r\n//  100% { opacity:1; }\r\n//}\r\n//\r\n//@-moz-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-webkit-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-o-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-ms-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//\r\n//@mixin fadeIn( $arg ) {\r\n//  $keyframe-name:fadeIn;\r\n//  $duration:$arg;\r\n//  @each $p in $prefix {\r\n//    #{$p}animation:$keyframe-name $duration;\r\n//  }\r\n//}\r\n\r\n/*------------------------\r\nmixin that calculates if text needs to be light or dark\r\ndepending on the background color passed.\r\n\r\nFrom this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast\r\n\r\nusage:\r\n@include text-contrast($bgcolor)\r\n      \r\nColor brightness is determined by the following formula: \r\n((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000\r\n------------------------*/\r\n\r\n@mixin text-contrast($n:#333) {\r\n  $color-brightness: round((red($n) * 299) + (green($n) * 587) + (blue($n) * 114) / 1000);\r\n  $light-color: round((red(#ffffff) * 299) + (green(#ffffff) * 587) + (blue(#ffffff) * 114) / 1000);\r\n  \r\n  @if abs($color-brightness) < ($light-color/1.70){\r\n    color: rgba(255,255,255,1);\r\n  }\r\n\r\n  @else {\r\n    color: rgba(0,0,0,0.8);\r\n  }\r\n}\r\n\r\n/*------------------------\r\n color factory \r\n  eg: @include paint($blue-grey-50, bg-blue-grey-50);\r\n------------------------*/\r\n\r\n\r\n@mixin paint($paint:#333333,$make:bg-blue-grey-50) {\r\n\r\n    .#{$make} {\r\n      background-color: $paint;\r\n      @include text-contrast($paint)\r\n      &:hover {\r\n        @include text-contrast($paint)\r\n      }\r\n    }\r\n}\r\n\r\n@mixin brush($brush: #333,$make: red-50) {\r\n    .#{$make} {\r\n      color: $brush;\r\n    }\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin set-settings($class-element: nav-function-fixed) {\r\n\r\n    .#{$class-element} .btn-switch[data-class=\"#{$class-element}\"] {\r\n      @extend %set-settings;\r\n    }\r\n\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin paint-gradient($paint: $fusion-500, $make:bg-fusion-gradient) {\r\n\r\n    .#{$make} {\r\n      background-image: -webkit-linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n      background-image: linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n    }\r\n\r\n}\r\n\r\n/* backface visibility */\r\n@mixin backface-visibility($argument: none) {\r\n  -webkit-backface-visibility: hidden;\r\n  -moz-backface-visibility:    hidden;\r\n  -ms-backface-visibility:     hidden;\r\n   backface-visibility:        hidden;\r\n}\r\n\r\n/* generate theme button */\r\n@mixin theme-button-color ($theme-fusion:none, $theme-primary:none, $theme-info:none, $theme-success:none, $theme-warning:none, $theme-danger:none) {\r\n  background-image: -webkit-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -moz-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -ms-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: linear-gradient(to right, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n}\r\n\r\n// IE flexbox details:\r\n//\r\n// - Flexbox in IE 10:\r\n//   https://msdn.microsoft.com/en-us/library/hh673531(v=vs.85).aspx\r\n//\r\n// - IE 11 flexbox changes (includes property/value names for IE 10)\r\n//   https://msdn.microsoft.com/library/dn265027(v=vs.85).aspx\r\n\r\n@mixin flexbox ($important: false) {\r\n  display: unquote(\"-ms-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin inline-flexbox ($important: false) {\r\n  display: unquote(\"-ms-inline-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"inline-flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin align-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-line-pack: map-get($ms-map, $value) or $value;\r\n  align-content: $value;\r\n}\r\n\r\n@mixin align-items ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-align: map-get($ms-map, $value) or $value;\r\n  align-items: $value;\r\n}\r\n\r\n@mixin align-self ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-item-align: map-get($ms-map, $value) or $value;\r\n  align-self: $value;\r\n}\r\n\r\n@mixin flex ($value) {\r\n  -ms-flex: $value;\r\n  flex: $value;\r\n}\r\n\r\n@mixin flex-direction ($value) {\r\n  -ms-flex-direction: $value;\r\n  flex-direction: $value;\r\n}\r\n\r\n@mixin flex-wrap ($value) {\r\n  $ms-map: (\r\n    nowrap: none\r\n  );\r\n  -ms-flex-wrap: map-get($ms-map, $value) or $value;\r\n  flex-wrap: $value;\r\n}\r\n\r\n@mixin justify-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end,\r\n    space-around: distribute,\r\n    space-between: justify\r\n  );\r\n  -ms-flex-pack: map-get($ms-map, $value) or $value;\r\n  justify-content: $value;\r\n}\r\n\r\n@mixin order ($value) {\r\n  -ms-flex-order: $value;\r\n  order: $value;\r\n}", "/*  THEME COLORs\r\n========================================================================== */\r\n/* Looks good on chrome default color profile */\r\n$color-primary:\t\t\t\t\t\t#886ab5;\r\n$color-success:\t\t\t\t\t\t#1dc9b7;\r\n$color-info:\t\t\t\t\t\t#2196F3;\r\n$color-warning:\t\t\t\t\t\t#ffc241;\r\n$color-danger:\t\t\t\t\t\t#fd3995;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); \r\n\r\n/* looks good in sRGB but washed up on chrome default \r\n$color-primary:\t\t\t\t\t\t#826bb0;\r\n$color-success:\t\t\t\t\t\t#31cb55;\r\n$color-info:\t\t\t\t\t\t#5e93ec;\r\n$color-warning:\t\t\t\t\t\t#eec559;\r\n$color-danger:\t\t\t\t\t\t#dc4b92;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */\r\n\r\n/*  Color Polarity\r\n========================================================================== */\r\n$white:\t\t\t\t\t\t\t\t#fff !default;\r\n$black:\t\t\t\t\t\t\t\t#000 !default;\r\n$disabled:\t\t\t\t\t\t\tdarken($white, 20%) !default;\r\n\r\n/*  PAINTBUCKET MIXER\r\n========================================================================== */\r\n/* the grays */ \r\n$gray-50:\t\t\t\t\t\t\t#f9f9f9;\r\n$gray-100:\t\t\t\t\t\t\t#f8f9fa;\r\n$gray-200:\t\t\t\t\t\t\t#f3f3f3;\r\n$gray-300:\t\t\t\t\t\t\t#dee2e6;\r\n$gray-400:\t\t\t\t\t\t\t#ced4da;\r\n$gray-500:\t\t\t\t\t\t\t#adb5bd;\r\n$gray-600:\t\t\t\t\t\t\t#868e96;\r\n$gray-700:\t\t\t\t\t\t\t#495057;\r\n$gray-800:\t\t\t\t\t\t\t#343a40;\r\n$gray-900:\t\t\t\t\t\t\t#212529;\r\n\r\n/* the sapphires */\r\n$primary-50:\t\t\t\t\t\tlighten($color-primary, 25%) !default;\t\r\n$primary-100:\t\t\t\t\t\tlighten($color-primary, 20%) !default;\t\r\n$primary-200:\t\t\t\t\t\tlighten($color-primary, 15%) !default;\t\r\n$primary-300:\t\t\t\t\t\tlighten($color-primary, 10%) !default;\t\r\n$primary-400:\t\t\t\t\t\tlighten($color-primary, 5%) !default;\r\n$primary-500:\t\t\t\t\t\t$color-primary !default;\r\n$primary-600:\t\t\t\t\t\tdarken($color-primary, 5%) !default;\r\n$primary-700:\t\t\t\t\t\tdarken($color-primary, 10%) !default;\r\n$primary-800:\t\t\t\t\t\tdarken($color-primary, 15%) !default;\r\n$primary-900:\t\t\t\t\t\tdarken($color-primary, 20%) !default;\r\n\r\n/* the emeralds */\r\n$success-50:\t\t\t\t\t\tlighten($color-success, 25%) !default;\t\r\n$success-100:\t\t\t\t\t\tlighten($color-success, 20%) !default;\t\r\n$success-200:\t\t\t\t\t\tlighten($color-success, 15%) !default;\t\r\n$success-300:\t\t\t\t\t\tlighten($color-success, 10%) !default;\t\r\n$success-400:\t\t\t\t\t\tlighten($color-success, 5%) !default;\r\n$success-500:\t\t\t\t\t\t$color-success !default;\r\n$success-600:\t\t\t\t\t\tdarken($color-success, 5%) !default;\r\n$success-700:\t\t\t\t\t\tdarken($color-success, 10%) !default;\r\n$success-800:\t\t\t\t\t\tdarken($color-success, 15%) !default;\r\n$success-900:\t\t\t\t\t\tdarken($color-success, 20%) !default;\r\n\r\n/* the amethyths */\r\n$info-50:\t\t\t\t\t\t\tlighten($color-info, 25%) !default;\t\r\n$info-100:\t\t\t\t\t\t\tlighten($color-info, 20%) !default;\t\r\n$info-200:\t\t\t\t\t\t\tlighten($color-info, 15%) !default;\t\r\n$info-300:\t\t\t\t\t\t\tlighten($color-info, 10%) !default;\t\r\n$info-400:\t\t\t\t\t\t\tlighten($color-info, 5%) !default;\r\n$info-500:\t\t\t\t\t\t\t$color-info !default;\r\n$info-600:\t\t\t\t\t\t\tdarken($color-info, 5%) !default;\r\n$info-700:\t\t\t\t\t\t\tdarken($color-info, 10%) !default;\r\n$info-800:\t\t\t\t\t\t\tdarken($color-info, 15%) !default;\r\n$info-900:\t\t\t\t\t\t\tdarken($color-info, 20%) !default;\r\n\r\n/* the topaz */\r\n$warning-50:\t\t\t\t\t\tlighten($color-warning, 25%) !default;\t\r\n$warning-100:\t\t\t\t\t\tlighten($color-warning, 20%) !default;\t\r\n$warning-200:\t\t\t\t\t\tlighten($color-warning, 15%) !default;\t\r\n$warning-300:\t\t\t\t\t\tlighten($color-warning, 10%) !default;\t\r\n$warning-400:\t\t\t\t\t\tlighten($color-warning, 5%) !default;\r\n$warning-500:\t\t\t\t\t\t$color-warning !default;\r\n$warning-600:\t\t\t\t\t\tdarken($color-warning, 5%) !default;\r\n$warning-700:\t\t\t\t\t\tdarken($color-warning, 10%) !default;\r\n$warning-800:\t\t\t\t\t\tdarken($color-warning, 15%) !default;\r\n$warning-900:\t\t\t\t\t\tdarken($color-warning, 20%) !default;\r\n\r\n/* the rubies */\r\n$danger-50:\t\t\t\t\t\t\tlighten($color-danger, 25%) !default;\t\r\n$danger-100:\t\t\t\t\t\tlighten($color-danger, 20%) !default;\t\r\n$danger-200:\t\t\t\t\t\tlighten($color-danger, 15%) !default;\t\r\n$danger-300:\t\t\t\t\t\tlighten($color-danger, 10%) !default;\t\r\n$danger-400:\t\t\t\t\t\tlighten($color-danger, 5%) !default;\r\n$danger-500:\t\t\t\t\t\t$color-danger !default;\r\n$danger-600:\t\t\t\t\t\tdarken($color-danger, 5%) !default;\r\n$danger-700:\t\t\t\t\t\tdarken($color-danger, 10%) !default;\r\n$danger-800:\t\t\t\t\t\tdarken($color-danger, 15%) !default;\r\n$danger-900:\t\t\t\t\t\tdarken($color-danger, 20%) !default;\r\n\r\n/* the graphites */\r\n$fusion-50:\t\t\t\t\t\t\tlighten($color-fusion, 25%) !default;\t\r\n$fusion-100:\t\t\t\t\t\tlighten($color-fusion, 20%) !default;\t\r\n$fusion-200:\t\t\t\t\t\tlighten($color-fusion, 15%) !default;\t\r\n$fusion-300:\t\t\t\t\t\tlighten($color-fusion, 10%) !default;\t\r\n$fusion-400:\t\t\t\t\t\tlighten($color-fusion, 5%) !default;\r\n$fusion-500:\t\t\t\t\t\t$color-fusion !default;\r\n$fusion-600:\t\t\t\t\t\tdarken($color-fusion, 5%) !default;\r\n$fusion-700:\t\t\t\t\t\tdarken($color-fusion, 10%) !default;\r\n$fusion-800:\t\t\t\t\t\tdarken($color-fusion, 15%) !default;\r\n$fusion-900:\t\t\t\t\t\tdarken($color-fusion, 20%) !default;\r\n\r\n$theme-colors-extended: () !default;\r\n$theme-colors-extended: map-merge((\r\n\t\"primary-50\":\t\t\t\t\t$primary-50,\r\n\t\"primary-100\":\t\t\t\t\t$primary-100,\r\n\t\"primary-200\":\t\t\t\t\t$primary-200,\r\n\t\"primary-300\":\t\t\t\t\t$primary-300,\r\n\t\"primary-400\":\t\t\t\t\t$primary-400,\r\n\t\"primary-500\":\t\t\t\t\t$primary-500,\r\n\t\"primary-600\":\t\t\t\t\t$primary-600,\r\n\t\"primary-700\":\t\t\t\t\t$primary-700,\r\n\t\"primary-800\":\t\t\t\t\t$primary-800,\r\n\t\"primary-900\":\t\t\t\t\t$primary-900,\r\n\t\"success-50\":\t\t\t\t\t$success-50,\r\n\t\"success-100\":\t\t\t\t\t$success-100,\r\n\t\"success-200\":\t\t\t\t\t$success-200,\r\n\t\"success-300\":\t\t\t\t\t$success-300,\r\n\t\"success-400\":\t\t\t\t\t$success-400,\r\n\t\"success-500\":\t\t\t\t\t$success-500,\r\n\t\"success-600\":\t\t\t\t\t$success-600,\r\n\t\"success-700\":\t\t\t\t\t$success-700,\r\n\t\"success-800\":\t\t\t\t\t$success-800,\r\n\t\"success-900\":\t\t\t\t\t$success-900,\r\n\t\"info-50\":\t\t\t\t\t\t$info-50,\r\n\t\"info-100\":\t\t\t\t\t\t$info-100,\r\n\t\"info-200\":\t\t\t\t\t\t$info-200,\r\n\t\"info-300\":\t\t\t\t\t\t$info-300,\r\n\t\"info-400\":\t\t\t\t\t\t$info-400,\r\n\t\"info-500\":\t\t\t\t\t\t$info-500,\r\n\t\"info-600\":\t\t\t\t\t\t$info-600,\r\n\t\"info-700\":\t\t\t\t\t\t$info-700,\r\n\t\"info-800\":\t\t\t\t\t\t$info-800,\r\n\t\"info-900\":\t\t\t\t\t\t$info-900,\r\n\t\"warning-50\":\t\t\t\t\t$warning-50,\r\n\t\"warning-100\":\t\t\t\t\t$warning-100,\r\n\t\"warning-200\":\t\t\t\t\t$warning-200,\r\n\t\"warning-300\":\t\t\t\t\t$warning-300,\r\n\t\"warning-400\":\t\t\t\t\t$warning-400,\r\n\t\"warning-500\":\t\t\t\t\t$warning-500,\r\n\t\"warning-600\":\t\t\t\t\t$warning-600,\r\n\t\"warning-700\":\t\t\t\t\t$warning-700,\r\n\t\"warning-800\":\t\t\t\t\t$warning-800,\r\n\t\"warning-900\":\t\t\t\t\t$warning-900,  \r\n\t\"danger-50\":\t\t\t\t\t$danger-50,\r\n\t\"danger-100\":\t\t\t\t\t$danger-100,\r\n\t\"danger-200\":\t\t\t\t\t$danger-200,\r\n\t\"danger-300\":\t\t\t\t\t$danger-300,\r\n\t\"danger-400\":\t\t\t\t\t$danger-400,\r\n\t\"danger-500\":\t\t\t\t\t$danger-500,\r\n\t\"danger-600\":\t\t\t\t\t$danger-600,\r\n\t\"danger-700\":\t\t\t\t\t$danger-700,\r\n\t\"danger-800\":\t\t\t\t\t$danger-800,\r\n\t\"danger-900\":\t\t\t\t\t$danger-900,\r\n\t\"fusion-50\":\t\t\t\t\t$fusion-50,\r\n\t\"fusion-100\":\t\t\t\t\t$fusion-100,\r\n\t\"fusion-200\":\t\t\t\t\t$fusion-200,\r\n\t\"fusion-300\":\t\t\t\t\t$fusion-300,\r\n\t\"fusion-400\":\t\t\t\t\t$fusion-400,\r\n\t\"fusion-500\":\t\t\t\t\t$fusion-500,\r\n\t\"fusion-600\":\t\t\t\t\t$fusion-600,\r\n\t\"fusion-700\":\t\t\t\t\t$fusion-700,\r\n\t\"fusion-800\":\t\t\t\t\t$fusion-800,\r\n\t\"fusion-900\":\t\t\t\t\t$fusion-900\r\n\r\n), $theme-colors-extended);\r\n\r\n/*  Define universal border difition (div outlines, etc)\r\n========================================================================== */\r\n$theme-border-utility-size:\t\t\t\t0px;\r\n\r\n/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)\r\n========================================================================== */\r\n$grid-breakpoints: (\r\n\t// Extra small screen / phone\r\n\txs: 0,\r\n\t// Small screen / phone\r\n\tsm: 576px,\r\n\t// Medium screen / tablet\r\n\tmd: 768px,\r\n\t// Large screen / desktop\r\n\tlg: 992px, // also change 'mobileResolutionTrigger' in app.config.js\r\n\t// Decently size screen / wide laptop\r\n\txl: 1399px \r\n);\r\n\r\n$mobile-breakpoint:\t\t\t\t\t\tlg !default;                               /* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */\r\n$mobile-breakpoint-size:\t\t\t\tmap-get($grid-breakpoints, lg) !default;   /* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/\r\n$grid-gutter-width-base:\t\t\t\t3rem;\r\n$grid-gutter-width:\t\t\t\t\t\t1.5rem;\r\n\r\n$grid-gutter-widths: (\r\n\txs: $grid-gutter-width-base / 2,         \r\n\tsm: $grid-gutter-width-base / 2,          \r\n\tmd: $grid-gutter-width-base / 2,        \r\n\tlg: $grid-gutter-width-base / 2,        \r\n\txl: $grid-gutter-width-base / 2        \r\n);\r\n\r\n\r\n/* global var used for spacing*/\r\n$spacer:                  1rem;\r\n$spacers: () ;\r\n$spacers: map-merge(\r\n\t(\r\n\t\t0: 0,\r\n\t\t1: ($spacer * .25),\r\n\t\t2: ($spacer * .5),\r\n\t\t3: $spacer,\r\n\t\t4: ($spacer * 1.5),\r\n\t\t5: ($spacer * 2),\r\n\t\t6: ($spacer * 2.5)\r\n\t),\r\n\t$spacers\r\n);\r\n\r\n/* Uniform Padding variable */\r\n/* Heads up! This is a global scoped variable - changing may impact the whole template */\r\n$p-1:\t\t\t\t\t\t\t\t\t0.25rem;\r\n$p-2:\t\t\t\t\t\t\t\t\t0.5rem;\r\n$p-3:\t\t\t\t\t\t\t\t\t1rem;\r\n$p-4:\t\t\t\t\t\t\t\t\t1.5rem;\r\n$p-5:\t\t\t\t\t\t\t\t\t2rem;\r\n\r\n\r\n/*   BOOTSTRAP OVERRIDES (bootstrap variables)\r\n========================================================================== */ \r\n$grays: (\r\n\t\"100\": $gray-100,\r\n\t\"200\": $gray-200,\r\n\t\"300\": $gray-300,\r\n\t\"400\": $gray-400,\r\n\t\"500\": $gray-500,\r\n\t\"600\": $gray-600,\r\n\t\"700\": $gray-700,\r\n\t\"800\": $gray-800,\r\n\t\"900\": $gray-900\r\n);\r\n\r\n$colors: (\r\n\t\"blue\": $color-primary,\r\n\t\"red\": $color-danger,\r\n\t\"orange\": $color-warning,\r\n\t\"yellow\": $color-warning,\r\n\t\"green\": $color-success,\r\n\t\"white\": $white,\r\n\t\"gray\": $gray-600,\r\n\t\"gray-dark\": $gray-700\r\n);\r\n\r\n/* usage: theme-colors(\"primary\"); */\r\n$theme-colors: (\r\n\t\"primary\": $color-primary,\r\n\t\"secondary\": $gray-600,\r\n\t\"success\": $color-success,\r\n\t\"info\": $color-info,\r\n\t\"warning\": $color-warning,\r\n\t\"danger\": $color-danger,\r\n\t\"light\": $white,\r\n\t\"dark\": $fusion-500\r\n);\r\n\r\n/* forms */\r\n/*$input-height:\t\t\t\t\t\t\tcalc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */\r\n$input-border-color:\t\t\t\t\t#E5E5E5;\r\n$input-focus-border-color:\t\t\t\t$color-primary;\r\n$input-btn-focus-color:\t\t\t\t\ttransparent;\r\n$input-padding-y:\t\t\t\t\t\t.5rem;  \r\n$input-padding-x:\t\t\t\t\t\t.875rem;\r\n$label-margin-bottom:\t\t\t\t\t.3rem;\r\n$form-group-margin-bottom:\t\t\t\t1.5rem;\r\n\r\n/* links */\r\n$link-color:\t\t\t\t\t\t\t$primary-500;\r\n$link-hover-color:\t\t\t\t\t\t$primary-400;\r\n\r\n/* checkbox */ \r\n$custom-control-indicator-size:\t\t\t\t\t1.125rem;\r\n$custom-checkbox-indicator-border-radius:\t\t2px;\r\n$custom-control-indicator-border-width: \t\t2px;\r\n$custom-control-indicator-bg-size:\t\t\t\t0.5rem;\r\n\r\n/*$custom-file-height-inner:\t\t\t\tcalc(2.25rem - 1px);*/\r\n//$custom-file-padding-y:\t\t\t\t\t$input-padding-y;\r\n\r\n/* not part of bootstrap variable */\r\n$custom-control-indicator-bg-size-checkbox:  50% 50% !default;\r\n\r\n/* custom checkbox */\r\n// the checkbox needs to be a little darker for input groups\r\n$custom-control-indicator-checked-bg:\t\t\t\t$primary-600;\r\n$custom-control-indicator-checked-border-color: \t$primary-700;\r\n\r\n/* custom range */\r\n$custom-range-thumb-width:\t\t\t\t1rem;\r\n$custom-range-thumb-border-radius:\t\t50%;\r\n$custom-range-track-height:\t\t\t\t0.325rem;\r\n$custom-range-thumb-bg:\t\t\t\t\t$primary-500;\r\n$custom-range-thumb-active-bg:\t\t\t$primary-300;\r\n$custom-range-thumb-focus-box-shadow:\t0 0 0 1px $white, 0 0 0 0.2rem rgba($primary-500, 0.25);\r\n\r\n\r\n/* select */\r\n\r\n/* badge */\r\n$badge-font-size:\t\t\t\t\t\t85%;\r\n$badge-font-weight:\t\t\t\t\t\t500;\r\n\r\n/* cards */\r\n$card-spacer-y:\t\t\t\t\t\t\t1rem;\r\n$card-spacer-x:\t\t\t\t\t\t\t1rem;\r\n$card-cap-bg:\t\t\t\t\t\t\tinherit;\r\n$card-border-color:\t\t\t\t\t\trgba(0, 0, 0, 0.08);\r\n$list-group-border-color:\t\t\t\t$card-border-color;\r\n\r\n/*border radius*/\r\n$border-radius:\t\t\t\t\t\t\t4px;\r\n$border-radius-lg:\t\t\t\t\t\t$border-radius;\r\n$border-radius-sm:\t\t\t\t\t\t$border-radius;\r\n$border-radius-plus:\t\t\t\t\t10px;\r\n\r\n/* alert */\r\n$alert-padding-y:\t\t\t\t\t\t1rem;\r\n$alert-padding-x:\t\t\t\t\t\t1.25rem;\r\n$alert-margin-bottom:\t\t\t\t\t$grid-gutter-width + 0.5rem;\r\n\r\n/* toast */\r\n$toast-padding-y:\t\t\t\t\t\t0.5rem;\r\n$toast-padding-x:\t\t\t\t\t\t0.75rem;\r\n$toast-header-color:\t\t\t\t\t$fusion-500;\r\n\r\n/* breadcrumb */\r\n$breadcrumb-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$breadcrumb-divider-color:\t\t\t\tinherit;\r\n\r\n/* input button */\r\n$input-btn-padding-y-sm:\t\t\t\t.375rem;\r\n$input-btn-padding-x-sm:\t\t\t\t.844rem;\r\n\r\n$input-btn-padding-y:\t\t\t\t\t.5rem;\r\n$input-btn-padding-x:\t\t\t\t\t1.125rem;\r\n\r\n$input-btn-padding-y-lg:\t\t\t\t.75rem;\r\n$input-btn-padding-x-lg:\t\t\t\t1.5rem;\r\n\r\n/* nav link */\r\n$nav-link-padding-y:\t\t\t\t\t$input-btn-padding-y;\r\n$nav-link-padding-x:\t\t\t\t\t$input-btn-padding-x;\r\n\r\n/* nav, tabs, pills */\r\n$nav-tabs-border-color:\t\t\t\t\trgba($black, 0.1);\r\n$nav-tabs-link-active-border-color:\t\trgba($black, 0.1) rgba($black, 0.1) $white;\r\n$nav-tabs-link-hover-border-color:\t\trgba($black, 0.07) rgba($black, 0.07) transparent;\r\n\r\n/* tables */\r\n$table-border-color:\t\t\t\t\tlighten(desaturate($primary-500, 60%), 35%); //rgba($black, 0.09);\r\n$table-hover-bg:\t\t\t\t\t\tlighten(desaturate($primary-900, 70%), 63%);\r\n$table-accent-bg:\t\t\t\t\t\trgba($fusion-500,.02);\r\n$table-dark-bg:\t\t\t\t\t\t\t$fusion-300;\r\n$table-dark-border-color:\t\t\t\t$fusion-400;\r\n$table-dark-accent-bg:\t\t\t\t\trgba($white, .05);\r\n$table-dark-hover-bg:\t\t\t\t\t$color-primary;\r\n\r\n/* dropdowns */\r\n$dropdown-border-width:\t\t\t\t\t$theme-border-utility-size; \r\n$dropdown-padding-y:\t\t\t\t\t.3125rem;\r\n$dropdown-item-padding-y:\t\t\t\t.75rem;\r\n$dropdown-item-padding-x:\t\t\t\t1.5rem; \r\n$dropdown-link-active-bg:\t\t\t\tlighten($primary-50, 13%);  \r\n$dropdown-link-active-color:\t\t\t$primary-900;\r\n$dropdown-link-hover-color:\t\t\t\t$primary-700;\r\n\r\n/* dropdowns sizes */\r\n$dropdown-xl-width:\t\t\t\t\t\t21.875rem !default;\r\n$dropdown-lg-width:\t\t\t\t\t\t17.5rem !default;\r\n$dropdown-md-width:\t\t\t\t\t\t14rem !default;\r\n$dropdown-sm-width:\t\t\t\t\t\t8rem !default;\r\n$dropdown-shadow:\t\t\t\t\t\t0 0 15px 1px rgba(desaturate($primary-900, 20%), (20/100));   \r\n\r\n/* popovers */\r\n$popover-border-color:\t\t\t\t\trgba(0, 0, 0, 0.2);\r\n$popover-header-padding-y:\t\t\t\t1rem;\r\n$popover-header-padding-x:\t\t\t\t1rem;\r\n$popover-header-bg:\t\t\t\t\t\ttransparent;\r\n$popover-border-width:\t\t\t\t\t3px;\r\n$popover-arrow-width:\t\t\t\t\t15px;\r\n$popover-arrow-height:\t\t\t\t\t7px;\r\n$popover-arrow-outer-color:\t\t\t\tinherit;\r\n$popover-arrow-color:\t\t\t\t\ttransparent;\r\n$popover-font-size:\t\t\t\t\t\t14px;\r\n$popover-box-shadow:\t\t\t\t\t1px 0 13px rgba(90, 80, 105, 0.2);\r\n$popover-border-radius:\t\t\t\t\t0.5rem;\r\n\r\n/* tooltips */\r\n$tooltip-max-width:\t\t\t\t\t\t200px;\r\n$tooltip-color:\t\t\t\t\t\t\t$white;\r\n$tooltip-bg:\t\t\t\t\t\t\trgba($fusion-700, 0.9);\r\n$tooltip-border-radius:\t\t\t\t\t5px;\r\n$tooltip-opacity:\t\t\t\t\t\t1;\r\n$tooltip-padding-y:\t\t\t\t\t\t.3rem;\r\n$tooltip-padding-x:\t\t\t\t\t\t.6rem;\r\n$tooltip-margin:\t\t\t\t\t\t2px;\r\n$tooltip-arrow-width:\t\t\t\t\t8px;\r\n$tooltip-arrow-height:\t\t\t\t\t5px;\r\n\r\n/* modal */\r\n$modal-header-padding-y:\t\t\t\t1.25rem;\r\n$modal-header-padding-x:\t\t\t\t1.25rem;\r\n$modal-header-padding:\t\t\t\t\t$modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n$modal-inner-padding:\t\t\t\t\t1.25rem;\r\n$modal-backdrop-opacity:\t\t\t\t0.2;\r\n$modal-content-border-color:\t\t\ttransparent;\r\n$modal-header-border-width:\t\t\t\t0px;\r\n$modal-footer-border-width:\t\t\t\t0px;\r\n\r\n/* reference guide\r\nhttp://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/\r\n8px = 0.5rem\r\n9px = 0.5625rem\r\n10px = 0.625rem\r\n11px = 0.6875rem\r\n12px = 0.75rem\r\n13px = 0.8125rem\r\n14px = 0.875rem\r\n15px = 0.9375rem\r\n16px = 1rem (base)\r\n17px = 1.0625rem\r\n18px = 1.125rem\r\n19px = 1.1875rem\r\n20px = 1.25rem\r\n21px = 1.3125rem\r\n22px = 1.375rem\r\n24px = 1.5rem\r\n25px = 1.5625rem\r\n26px = 1.625rem\r\n28px = 1.75rem\r\n30px = 1.875rem\r\n32px = 2rem\r\n34px = 2.125rem\r\n36px = 2.25rem\r\n38px = 2.375rem\r\n40px = 2.5rem\r\n*/\r\n\r\n/* Fonts */\r\n$font-size-base:\t\t\t\t\t\t0.8125rem;\r\n$font-size-lg:\t\t\t\t\t\t\t1rem;\r\n$font-size-sm:\t\t\t\t\t\t\t0.75rem;\r\n$line-height-base:\t\t\t\t\t\t1.47;\r\n$headings-line-height:\t\t\t\t\t1.57;\r\n\r\n$h1-font-size:\t\t\t\t\t\t\t1.5rem;\r\n$h2-font-size:\t\t\t\t\t\t\t1.375rem;\r\n$h3-font-size:\t\t\t\t\t\t\t1.1875rem;\r\n$h4-font-size:\t\t\t\t\t\t\t1.0625rem;\r\n$h5-font-size:\t\t\t\t\t\t\t0.9375rem;\r\n$h6-font-size:\t\t\t\t\t\t\t0.875rem;\r\n\r\n$display1-size:\t\t\t\t\t\t\t5rem;\r\n$display2-size:\t\t\t\t\t\t\t4.5rem;\r\n$display3-size:\t\t\t\t\t\t\t3.5rem;\r\n$display4-size:\t\t\t\t\t\t\t2.5rem;\r\n\r\n$navbar-toggler-font-size:\t\t\t\t21px;\r\n$navbar-toggler-padding-y:\t\t\t\t7.5px; \r\n$navbar-toggler-padding-x:\t\t\t\t18px;\r\n\r\n/* carousel */\r\n$carousel-indicator-height:\t\t\t\t13px;\r\n$carousel-indicator-width:\t\t\t\t13px;\r\n\r\n/*  BASE VARS\r\n========================================================================== */\r\n// usage: background-image: url(\"#{$baseURL}img/bg.png\"); \r\n\r\n$baseURL:\t\t\t\t\t\t\t\t\"../\" !default;\r\n$webfontsURL:\t\t\t\t\t\t\t\"../webfonts\" !default;\r\n$base-text-color:\t\t\t\t\t\tdarken($white,60%) !default;\r\n\r\n/* font vars below will auto change to rem values using function rem($value)*/\r\n$fs-base:\t\t\t\t\t\t\t\t13px !default;\r\n$fs-nano:\t\t\t\t\t\t\t\t$fs-base - 2;   /* 11px   */\r\n$fs-xs: \t\t\t\t\t\t\t\t$fs-base - 1;   /* 12px   */\r\n$fs-sm: \t\t\t\t\t\t\t\t$fs-base - 0.5; /* 12.5px */\r\n$fs-md: \t\t\t\t\t\t\t\t$fs-base + 1;   /* 14px   */\r\n$fs-lg: \t\t\t\t\t\t\t\t$fs-base + 2;   /* 15px   */\r\n$fs-xl: \t\t\t\t\t\t\t\t$fs-base + 3;   /* 16px   */\r\n$fs-xxl: \t\t\t\t\t\t\t\t$fs-base + 15;  /* 28px   */\r\n\r\n/*  Font Family\r\n========================================================================== */\r\n\t\t\t\t\t\t\t\t\t\t/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */\r\n$font-import:\t\t\t\t\t\t\t\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900\" !default;\r\n$page-font:\t\t\t\t\t\t\t\t\"Roboto\", 'Helvetica Neue', Helvetica, Arial !default;\r\n$nav-font:\t\t\t\t\t\t\t\t$page-font !default;\r\n$heading-font-family:\t\t\t\t\t$page-font !default; \r\n$mobile-page-font:\t\t\t\t\t\t'HelveticaNeue-Light','Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,sans-serif;\r\n\r\n/*  ANIMATIONS\r\n========================================================================== */\r\n$nav-hide-animate: \t\t\t\t\t\tall 470ms cubic-bezier(0.34, 1.25, 0.3, 1) !default;\t\t/* this addresses all animation related to nav hide to nav minify */\r\n\r\n/*  Z-INDEX declearation\r\n========================================================================== */\r\n$space:\t\t\t\t\t\t\t\t\t1000 !default;\r\n$cloud:\t\t\t\t\t\t\t\t\t950 !default;\r\n$ground:\t\t\t\t\t\t\t\t0 !default;\r\n$water:\t\t\t\t\t\t\t\t\t-99 !default;\r\n/* we adjust bootstrap z-index to be higher than our higest z-index*/\r\n$zindex-dropdown:\t\t\t\t\t\t$space + 1000;\r\n$zindex-sticky:\t\t\t\t\t\t\t$space + 1020;\r\n$zindex-fixed:\t\t\t\t\t\t\t$space + 1030;\r\n$zindex-modal-backdrop:\t\t\t\t\t$space + 1040;\r\n$zindex-modal:\t\t\t\t\t\t\t$space + 1050;\r\n$zindex-panel-fullscreen:\t\t\t\t$space + 1055;\r\n$zindex-popover:\t\t\t\t\t\t$space + 1060;\r\n$zindex-tooltip:\t\t\t\t\t\t$space + 1070;\r\n\r\n/*  CUSTOM ICON PREFIX \r\n========================================================================== */\r\n$cust-icon-prefix:\t\t\t\t\t\tni;\r\n\r\n/*  PRINT CSS (landscape or portrait)\r\n========================================================================== */\r\n$print-page-type: \t\t\t\t\t\tportrait; \t\t\t\t\t\t\t\t\t\t\t\t  /* landscape or portrait */\r\n$print-page-size:\t\t\t\t\t\tletter;\t\t\t\t\t\t\t\t\t\t\t\t\t  /* auto, letter */\r\n$print-page-margin:\t\t\t\t\t\t1.0cm;\r\n\r\n/*  Common Element Variables\r\n========================================================================== */\r\n$body-background-color:\t\t\t\t\t$white !default;\r\n$page-bg:\t\t\t\t\t\t\t\tdesaturate(lighten($primary-500, 41.7%), 5%)  !default; //#f9f9fc\r\n\r\n/* Z-index decleartion \"birds eye view\"\r\n========================================================================== */\r\n$depth:\t\t\t\t\t\t\t\t\t999 !default;\r\n$depth-header:\t\t\t\t\t\t\t$depth + 1 !default;\r\n$depth-nav:\t\t\t\t\t\t\t\t$depth-header + 2 !default;\r\n\r\n/*  Components\r\n========================================================================== */\r\n$frame-border-color:\t\t\t\t\t#f7f9fa !default;\r\n\r\n/*  PAGE HEADER STUFF\r\n========================================================================== */\r\n\r\n/* colors */\r\n$header-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$header-border-color:\t\t\t\t\t#ccc !default;\r\n$header-border-bottom-color:\t\t\trgba(darken($primary-700, 10%), (13/100)) !default;\t\t\r\n$header-link-color:\t\t\t\t\t\t$primary-500 !default;\r\n$header-link-hover-color:\t\t\t\tdarken($header-bg, 75%) !default;\r\n\r\n/* height */\r\n$header-height:\t\t\t\t\t\t\t4.125rem !default;\r\n$header-height-nav-top:\t\t\t\t\t4.125rem !default;\r\n$header-inner-padding-x:\t\t\t\t2rem !default;\r\n$header-inner-padding-y:\t\t\t\t0 !default;\r\n\r\n/* logo */\r\n$header-logo-border-bottom:\t\t\t\trgba(darken($primary-700, 10%), (30/100)) !default;\r\n$header-logo-width:\t\t\t\t\t\tauto !default; \t\t\t\t\t\t\t\t\t\t  /* try not to go beywond the width of $main_nav_width value */\r\n$header-logo-height:\t\t\t\t\tauto !default \t\t\t\t\t\t\t\t\t\t    /* you may need to change this depending on your logo design */\r\n$header-logo-text-align:\t\t\t\tcenter; \t\t\t\t\t\t\t\t\t\t\t\t      /* adjust this as you see fit : left, right, center */\r\n\r\n/* icon font size (not button) */\r\n$header-icon-size:\t\t\t\t\t\t21px;\r\n\r\n/* search input box */\r\n$header-search-border-color:\t\t\ttransparent !default;\t\t\t\t\t\t\t\t/* suggestion: #ccced0*/\r\n$header-search-bg:\t\t\t\t\t\ttransparent !default;\r\n$header-search-width:\t\t\t\t\t25rem !default;\r\n$header-search-height:\t\t\t\t\t$header-height - 1.5rem !default; \r\n$header-search-font-size:\t\t\t\t$fs-base + 2;\r\n$header-search-padding:\t\t\t\t\t$spacer * 0.38;\r\n\r\n/* btn */\r\n$header-btn-active-bg:\t\t\t\t\t$fusion-500 !default;\r\n$header-btn-color:\t\t\t\t\t\tdarken($header-bg, 35%) !default;\r\n$header-btn-hover-color:\t\t\t\t$header-link-hover-color !default;\r\n$header-btn-active-color:\t\t\t\t$white !default;\r\n$header-btn-height: \t\t\t\t\t$header-height/2 + 0.1875rem !default;\r\n$header-btn-width: \t\t\t\t\t\t3.25rem !default;\r\n$header-btn-font-size:\t\t\t\t\t21px !default; //works only for font icons\r\n$header-btn-border-radius:\t\t\t\t$border-radius !default;\r\n$header-non-btn-width:\t\t\t\t\t3.125rem !default;\r\n$header-dropdown-arrow-color:\t\t\t$primary-700 !default;\r\n\r\n/* dropdown: app list */\r\n$header-applist-link-block-height:\t\t5.9375rem;\r\n$header-applist-link-block-width:\t\t6.25rem;\r\n$header-applist-rows-width:\t\t\t\t21.875rem;\r\n$header-applist-rows-height:\t\t\t22.5rem; \r\n$header-applist-box-padding-x:\t\t\t$p-2;\r\n$header-applist-box-padding-y:\t\t\t$p-3;\r\n$header-applist-icon-size:\t\t\t\t3.125rem;\r\n\r\n/* badge */\r\n$header-badge-min-width:\t\t\t\t1.25rem !default;\r\n$header-badge-left:\t\t\t\t\t\t1.5625rem !default;\r\n$header-badge-top:\t\t\t\t\t\t($header-height / 2 - $header-badge-min-width) + 0.28125rem !default; \r\n\r\n/* COMPONENTS & MODS */\r\n$nav-tabs-clean-link-height:\t\t\t45px !default;\r\n\r\n/*  NAVIGATION STUFF\r\n\r\nGuide:\r\n\r\naside.page-sidebar ($nav-width, $nav-background)\r\n\t.page-logo\r\n\t.primary-nav\r\n\t\t.info-card\r\n\t\tul.nav-menu\r\n\t\t\tli\r\n\t\t\t\ta (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)\r\n\t\t\t\t\ticon \r\n\t\t\t\t\tspan\r\n\t\t\t\t\tcollapse-sign \r\n\t\t\t\t\t\r\n\t\t\t\tul.nav-menu-sub-one  \r\n\t\t\t\t\tli\r\n\t\t\t\t\t\ta ($nav-level-1... $nav-sub-link-height)\r\n\t\t\t\t\t\t\tspan\r\n\t\t\t\t\t\t\tcollapse-sign\r\n\r\n\t\t\t\t\t\tul.nav-menu-sub-two\r\n\t\t\t\t\t\t\tli\r\n\t\t\t\t\t\t\t\ta ($nav-level-2... $nav-sub-link-height)\r\n\t\t\t\t\t\t\t\t\tspan\r\n\r\n\t\tp.nav-title ($nav-title-*...)\r\n\r\n\r\n========================================================================== */\r\n\r\n/* main navigation */\r\n/* left panel */\r\n$nav-background:\t\t\t\t\t\tdesaturate($primary-900, 7%) !default;\r\n$nav-background-shade:\t\t\t\t\trgba(desaturate($info-500, 15%), 0.18) !default;                  \r\n$nav-base-color:\t\t\t\t\t\tlighten($nav-background, 7%) !default;\r\n$nav-width:\t\t\t\t\t\t\t\t16.875rem !default; \r\n\r\n/* nav parent level-0 */\r\n$nav-link-color: \t\t\t\t\t\tlighten($nav-base-color, 32%) !default;\r\n$nav-font-link-size: \t\t\t\t\t$fs-base + 1 !default;\r\n$nav-collapse-sign-font-size:\t\t\tinherit !default;\t\r\n$nav-padding-x:\t\t\t\t\t\t\t2rem !default; \r\n$nav-padding-y:\t\t\t\t\t\t\t0.8125rem !default;\r\n\r\n/* nav icon sizes */\r\n$nav-font-icon-size:\t\t\t\t\t1.125rem !default; //23px for Fontawesome & 20px for NextGen icons\r\n$nav-font-icon-size-sub:\t\t\t\t1.125rem !default;\r\n\r\n$nav-icon-width:\t\t\t\t\t\t1.75rem !default;\r\n$nav-icon-margin-right:\t\t\t\t\t0.25rem !default;\r\n\r\n/* badge default */\r\n$nav-badge-color: \t\t\t\t\t\t$white !default;\r\n$nav-badge-bg-color: \t\t\t\t\t$danger-500 !default;\r\n\r\n/* all child */\r\n$nav-icon-color:\t\t\t\t\t\tlighten(darken($nav-base-color, 15%),27%) !default;\r\n$nav-icon-hover-color:\t\t\t\t\tlighten(desaturate($color-primary, 30%), 10%) !default;\r\n\r\n/* nav title */\r\n$nav-title-color: \t\t\t\t\t\tlighten($nav-base-color, 10%) !default;\r\n$nav-title-border-bottom-color: \t\tlighten($nav-base-color, 3%) !default;\r\n$nav-title-font-size: \t\t\t\t\t$fs-base - 1.8px;\r\n\r\n/* nav Minify */\r\n$nav-minify-hover-bg:\t\t\t\t\tdarken($nav-base-color, 3%) !default;\r\n$nav-minify-hover-text:\t\t\t\t\t$white !default;\r\n$nav-minify-width:\t\t\t\t\t\t4.6875rem !default;\r\n/* when the menu pops on hover */\r\n$nav-minify-sub-width:\t\t\t\t\t$nav-width - ($nav-minify-width - 1.5625rem) !default; \t\t\t\t\r\n\r\n/* navigation Width */\r\n/* partial visibility of the menu */\r\n$nav-hidden-visiblity:\t\t\t\t\t0.625rem !default; \t\t\t\t\t\t\t\t\t\t\t\r\n\r\n/* top navigation */\r\n$nav-top-height:\t\t\t\t\t\t3.5rem !default;\r\n$nav-top-drowndown-width:\t\t\t\t13rem !default;\r\n$nav-top-drowndown-background:\t\t\t$nav-base-color;\r\n$nav-top-drowndown-hover:\t\t\t\trgba($black, 0.1);;\r\n$nav-top-drowndown-color:\t\t\t\t$nav-link-color;\r\n$nav-top-drowndown-hover-color:\t\t\t$white;\r\n\r\n/* nav Info Card (appears below the logo) */\r\n$nav-infocard-height:\t\t\t\t\t9.530rem !default;\r\n$profile-image-width:\t\t\t\t\t3.125rem !default; \r\n$profile-image-width-md:\t\t\t\t2rem !default;\r\n$profile-image-width-sm:\t\t\t\t1.5625rem !default;\r\n$image-share-height:\t\t\t\t\t2.8125rem !default; /* width is auto */\r\n\r\n/* nav DL labels for all child */\r\n$nav-dl-font-size:\t\t\t\t\t\t0.625rem !default;\r\n$nav-dl-width:\t\t\t\t\t\t\t1.25rem !default;\r\n$nav-dl-height:\t\t\t\t\t\t\t1rem !default;\r\n$nav-dl-margin-right:\t\t\t\t\t0.9375rem !default;\r\n$nav-dl-margin-left:\t\t\t\t\t$nav-dl-width + $nav-dl-margin-right !default; \t/* will be pulled to left as a negative value */\r\n\r\n/*   MISC Settings\r\n========================================================================== */\r\n/* List Table */\r\n$list-table-padding-x:\t\t\t\t\t11px !default;\r\n$list-table-padding-y:\t\t\t\t\t0 !default;\r\n\r\n/*   PAGE SETTINGS\r\n========================================================================== */\r\n$settings-incompat-title:\t\t\t\t#d58100 !default;\r\n$settings-incompat-desc:\t\t\t\t#ec9f28 !default;\r\n$settings-incompat-bg:\t\t\t\t\t$warning-50 !default;\r\n$settings-incompat-border:\t\t\t\t$warning-700 !default;\r\n\r\n/*   PAGE BREADCRUMB \r\n========================================================================== */\r\n$page-breadcrumb-maxwidth:\t\t\t\t200px;\r\n\r\n/*   PAGE COMPONENT PANELS \r\n========================================================================== */\r\n$panel-spacer-y:\t\t\t\t\t\t1rem;\r\n$panel-spacer-x:\t\t\t\t\t\t1rem;\r\n$panel-hdr-font-size:\t\t\t\t\t14px;\r\n$panel-hdr-height:\t\t\t\t\t\t3rem;\r\n$panel-btn-size:\t\t\t\t\t\t1rem;\r\n$panel-btn-spacing:\t\t\t\t\t\t0.3rem;\r\n$panel-toolbar-icon:\t\t\t\t\t1.5625rem;\r\n$panel-hdr-background:\t\t\t\t\t$white; //#fafafa;\r\n$panel-edge-radius:\t\t\t\t\t\t$border-radius;\r\n$panel-placeholder-color:\t\t\t\tlighten(desaturate($primary-50, 20%), 10%);\r\n\r\n/*   PAGE COMPONENT PROGRESSBARS \r\n========================================================================== */\r\n$progress-height:\t\t\t\t\t\t.75rem;\r\n$progress-font-size:\t\t\t\t\t.625rem;\r\n$progress-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$progress-border-radius:\t\t\t\t10rem;\r\n\r\n/*   PAGE COMPONENT MESSENGER \r\n========================================================================== */\r\n$msgr-list-width:\t\t\t\t\t\t14.563rem;\r\n$msgr-list-width-collapsed:\t\t\t\t3.125rem;\r\n$msgr-get-background:\t\t\t\t\t#f1f0f0;\r\n$msgr-sent-background:\t\t\t\t\t$success-500;\r\n$msgr-animation-delay:\t\t\t\t\t100ms;\r\n\r\n/*   FOOTER\r\n========================================================================== */\r\n$footer-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$footer-text-color:\t\t\t\t\t\tdarken($base-text-color, 10%);\r\n$footer-height:\t\t\t\t\t\t\t2.8125rem !default;\r\n$footer-font-size:\t\t\t\t\t\t$fs-base !default;\r\n$footer-zindex:\t\t\t\t\t\t\t$cloud - 20 !default;\r\n\r\n/*   GLOBALS\r\n========================================================================== */\r\n$mod-main-boxed-width:\t\t\t\t\tmap-get($grid-breakpoints, xl);\r\n$slider-width:\t\t\t\t\t\t\t15rem;\r\n\r\n/* ACCESSIBILITIES */\r\n$enable-prefers-reduced-motion-media-query:   false;", "/* PLACEHOLDER \r\n============================================= \r\n\r\nEXAMPLE:\r\n\r\n%bg-image {\r\n\t\twidth: 100%;\r\n\t\tbackground-position: center center;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n}\r\n\r\n.image-one {\r\n\t\t@extend %bg-image;\r\n\t\tbackground-image:url(/img/image-one.jpg\");\r\n}\r\n\r\nRESULT:\r\n\r\n.image-one, .image-two {\r\n\t\twidth: 100%;\r\n\t\tbackground-position: center center;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n}\r\n\r\n*/\r\n\r\n%nav-bg {\r\n\tbackground-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);\r\n\tbackground-image: linear-gradient(270deg, $nav-background-shade, transparent); \r\n\tbackground-color: $nav-background;\r\n}\r\n\r\n/*\r\n%shadow-hover {\r\n\tbox-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);\r\n\ttransition: all 0.2s ease-in-out;\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);\r\n\t}\r\n}\r\n*/\r\n%btn-default {\r\n\t@include gradient-img($start: #f5f5f5,$stop: #f1f1f1);\r\n\tcolor: #444;\r\n\tborder: 1px solid rgba(0,0,0,0.1);\r\n\tbox-shadow: none;\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: none;\r\n\t\tborder: 1px solid #c6c6c6;\r\n\t\tcolor: #333;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t&:focus {\r\n\t\tborder-color: $primary-200 !important;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t&.active {\r\n\t\tbackground: $primary-300;\r\n\t\tcolor: $white;\r\n\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important;\r\n\t}\r\n}\r\n\r\n%custom-scroll {\r\n\r\n\t&::-webkit-scrollbar-track-piece {\r\n\t\tbackground-color: transparent;\r\n\t}\r\n\r\n &::-webkit-scrollbar-thumb:vertical {\r\n\t\tbackground-color: #666;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar {\r\n\t\theight: 4px;\r\n\t\twidth: 4px;\r\n\t}\r\n\r\n &::-webkit-scrollbar-corner {\r\n\t\twidth: 40px;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar-thumb:vertical {\r\n\tbackground-color: #666;\r\n\t}\r\n\r\n\toverflow: hidden;\r\n\toverflow-y: scroll;\r\n\t-webkit-overflow-scrolling: touch;\r\n\r\n}\r\n\r\n%user-select {\r\n\t\t-webkit-user-select: none; \r\n\t\t\t -moz-user-select: none; \r\n\t\t\t\t-ms-user-select: none;\r\n}\r\n\r\n%content-box {\r\n\tbox-sizing: content-box;\r\n}\r\n\r\n%flex-0-0-auto {\r\n\tflex: 0 0 auto;\r\n}\r\n\r\n%transform-3d {\r\n\t@include translate3d(0,0,0);\r\n}\r\n\r\n\r\n%stop-transform-3d {\r\n\t\t\t\t\t\ttransform: none;\r\n\t\t-webkit-transform: none;\r\n\t\t\t\t-ms-transform: none;\r\n}\r\n\r\n%general-animation {\r\n\ttransition: $nav-hide-animate;      \r\n}\r\n\r\n%common-animation-slow {\r\n\t@include transition(0.3s,ease-in-out);\r\n\r\n}\r\n\r\n%common-animation {\r\n\t@include transition(0.2s,ease-in-out);\r\n}\r\n\r\n%common-animation-easeout {\r\n\t@include transition(0.4s,ease-out);\r\n}\r\n\r\n%common-animation-opacity {\r\n\ttransition: opacity 0.5s ease-in-out;\r\n}\r\n\r\n%common-animation-opacity-faster {\r\n\ttransition: opacity 0.1s ease-in-out;\r\n}\r\n\r\n%stop-animation {\r\n\ttransition: none;\r\n}\r\n\r\n%font-smoothing {\r\n\t\t -webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n%set-settings {\r\n\tcolor:$white;\r\n\tbackground:$color-primary !important;\r\n\t&:before {\r\n\t\tcontent:\"ON\" !important;\r\n\t\tleft:7px !important;\r\n\t\tright:auto !important;\r\n\t}\r\n\t&:after {\r\n\t\tcontent: \" \" !important;\r\n\t\tright:0 !important;\r\n\t\tleft:auto !important;\r\n\t\tbackground:$white !important;\r\n\t\tcolor:$color-primary !important;\r\n\t}\r\n\r\n\t+ .onoffswitch-title {\r\n\t\tfont-weight:500;\r\n\t\tcolor: $primary-500;\r\n\t}\r\n}\r\n\r\n%bg-img-cover {\r\n\tbackground-size: cover;\r\n}\r\n\r\n%not-compatible {\r\n\t\tposition:relative;\r\n\t\t\r\n\t\t.onoffswitch-title {\r\n\t\t\tcolor: $settings-incompat-title !important;\r\n\t\t}\r\n\t\t.onoffswitch-title-desc {\r\n\t\t\tcolor: $settings-incompat-desc !important;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tcontent: \"DISABLED\";\r\n\t\t\t@extend %incompatible;\r\n\t\t}\r\n}\r\n\r\n%not-compatible-override {\r\n\t\t&:before {\r\n\t\t\tdisplay:none !important;\r\n\t\t}\r\n}\r\n\r\n%ping-badge {\r\n\tposition: absolute;\r\n\tdisplay: block;\r\n\tborder-radius: 1rem;\r\n\tbackground-color: $nav-badge-bg-color;\r\n\tcolor: $nav-badge-color;\r\n\ttext-align: center;\r\n\tcursor: pointer;\r\n\t@include box-shadow(0 0 0 1px $nav-background);\r\n\tborder: 1px solid $nav-background;\r\n\tmin-width: 2rem;\r\n\tmax-width: 1.5rem;\r\n\tpadding: 2px;\r\n\tfont-weight: 500;\r\n\tline-height: normal;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n}\r\n\r\n\r\n\r\n/*%fixed-header-shadow {\r\n\t@include box-shadow(0 2px 2px -1px rgba(0,0,0,.1));\r\n}*/\r\n\r\n%header-btn {\r\n\t//@extend %btn-default;\r\n\t@include rounded($header-btn-border-radius);\r\n\tborder: 1px solid lighten($fusion-50, 30%);\r\n\theight: $header-btn-height;\r\n\twidth: $header-btn-width;\r\n\tvertical-align: middle;\r\n\tline-height: $header-btn-height - 0.125rem;\r\n\tmargin-right: $grid-gutter-width-base/4 + 0.1875rem;\r\n\tfont-size: $header-btn-font-size;\r\n\tpadding: $list-table-padding-y $list-table-padding-x;\r\n\tcursor: default;\r\n\tcolor:$header-btn-color;\r\n\tposition: relative;\r\n\t\t//background: $primary-200;\r\n\t\t//color:$primary-200;\r\n/*\r\n\t&.active {\r\n\t\t@extend %header-btn-active;\r\n\t}*/\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: none;\r\n\t\tborder-color: $primary-500;\r\n\t\tbackground: $primary-300;\r\n\t\tcolor:$white;\r\n\r\n\t}\r\n\r\n}\r\n\r\n%expanded-box {\r\n\tbox-shadow: inset 0 1px 5px rgba(0, 0, 0, 0.125);\r\n\tborder-bottom: 1px solid rgba(0,0,0,0.06);\r\n\tborder-width: 0 0 1px 0;\r\n\tbackground: $white;\r\n\tpadding: 16px 16px 10px;\r\n}\r\n\r\n%header-btn-active {\r\n\tbackground: $header-btn-active-bg;\r\n\tborder-color: darken($header-btn-active-bg, 10%) !important;\r\n\t@include box-shadow(inset 0 0 3px 1px rgba(0,0,0,.37));\r\n\tcolor:$header-btn-active-color !important;\r\n}\r\n\r\n//@include media-breakpoint-up($mobile-breakpoint) {\r\n/*  %selected-dot {\r\n\t\t&:before {\r\n\t\t\tcontent: \" \";\r\n\t\t\tdisplay: block;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: inherit;\r\n\t\t\tbackground-image: none;\r\n\t\t\tborder: 2px solid rgba(0,0,0,0.2);\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 15px;\r\n\t\t\tleft: 15px;\r\n\t\t\theight: 20px;\r\n\t\t\twidth: 20px;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tcontent: \" \";\r\n\t\t\theight: inherit;\r\n\t\t\twidth: inherit;\r\n\t\t\tborder: 5px solid rgba(0,0,0,0.1);\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t\tborder-radius: 50%;\r\n\t\t} \r\n\t}*/\r\n//}\r\n\r\n%spin-loader {\r\n\tmargin: 5px;\r\n\theight: 20px;\r\n\twidth: 20px;\r\n\tanimation: spin 0.5s infinite linear;\r\n\tborder: 2px solid $color-primary;\r\n\tborder-right-color: transparent;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n%incompatible {\r\n\tdisplay: block;\r\n\tposition: absolute;\r\n\tbackground: $settings-incompat-bg;\r\n\tfont-size: 10px;\r\n\twidth: 65px;\r\n\ttext-align: center;\r\n\tborder: 1px solid $settings-incompat-border;\r\n\theight: 22px;\r\n\tline-height: 20px;\r\n\tborder-radius: $border-radius-plus;\r\n\tright: 13px;\r\n\ttop: 26%;\r\n\tcolor:$fusion-900;\r\n}\r\n\r\n/* patterns */\r\n%pattern-0 {\r\n\tbackground-size: 10px 10px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .07) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .07) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 10px 10px transparent;\r\n}\r\n\r\n%pattern-1 {\r\n\tbackground-size: 5px 5px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 5px 5px transparent;\r\n}\r\n\r\n%pattern-2 {\r\n\tbackground-size: 15px 15px;\r\n\tbackground-image: -webkit-linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: -moz-linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\t-pie-background: linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent) 0 0 / 15px transparent;\r\n}\r\n\r\n%pattern-3 {\r\n\tbackground-size: 15px 15px;\r\n\tbackground-image: -webkit-linear-gradient(0deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: -moz-linear-gradient(0deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\t-pie-background: linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent) 0 0 / 15px 15px transparent;\r\n}\r\n\r\n%pattern-4 {\r\n\tbackground-size: 37px 37px;\r\n\tbackground-position: 0 0, 18.5px 18.5px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\tlinear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 37px,\r\n\t\t\t\t\t\t\t\t\t linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 37px 37px / 74px,\r\n\t\t\t\t\t\t\t\t\t transparent;\r\n}\r\n\r\n%pattern-5 {\r\n\tbackground-size: 37px 37px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-webkit-linear-gradient(-45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-moz-linear-gradient(-45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\tlinear-gradient(135deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 60px,\r\n\t\t\t\t\t\t\t\t\t linear-gradient(135deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 60px,\r\n\t\t\t\t\t\t\t\t\t #eee;\r\n}\r\n\r\n%pattern-6 {\r\n\tbackground-size: 50px 50px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 50px 50px transparent;\r\n}\r\n\r\n"]}