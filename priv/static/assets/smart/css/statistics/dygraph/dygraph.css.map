{"version": 3, "sources": ["dygraph.css"], "names": [], "mappings": "AAAA;;EAEE;AAEF;EACE,kBAAkB;EAClB,eAAe;EACf,WAAW;EACX,YAAY;EAAG,mBAAA;EACf;;;;;GAKC;EACD,iBAAiB;EACjB,mBAAmB;EACnB,gBAAgB;EAChB,gBAAgB,EAAA;;AAGlB,0CAAA;AACA;EACE,qBAAqB;EACrB,kBAAkB;EAClB,YAAY;EACZ,iBAAiB;EACjB,WAAW;EACX,wBAAwB;EACxB,0BAA0B;EAC1B,yDAAA,EAA0D;;AAG5D,2EAAA;AACA;EACE,qBAAqB;EACrB,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,wBAAwB;EACxB,0BAA0B;EAC1B,yDAAA;EACA,oDAAA;EACA,oDAAA,EAAqD;;AAGvD;EACE,kBAAkB;EAClB,WAAW,EAAA;;AAGb,wEAAA;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,gBAAgB,EAAA;;AAGlB,yDAAA;AACA,8CAAA;AACA;EACE,uBAAuB;EACvB,uBAAuB;EACvB,kBAAkB,EAAA;;AAGpB;EACE,wBAAA;EACA,qBAAA;EACA,WAAW;EACX,mBAAmB;EACnB,gBAAgB;EAChB,YAAY;EAAG,uCAAA,EAAwC;;AAYzD;EACE,iBAAiB;EACjB,WAAW;EACX,kBAAkB;EAClB,2CAAA,EAA4C;;AAG9C;EACE,kBAAkB;EAClB,4CAAA,EAA6C;;AAG/C,qBAAA;AACA;EACE,kBAAkB;EAClB,8CAAA;EACA,wBAAwB;EACxB,gCAAgC;EAChC,6BAA6B;EAC7B,2BAA2B;EAC3B,4BAA4B,EAAA;;AAG9B,sBAAA;AACA;EACE,kBAAkB;EAClB,8CAAA;EACA,yBAAyB;EACzB,iCAAiC;EACjC,8BAA8B;EAC9B,4BAA4B;EAC5B,6BAA6B,EAAA", "file": "dygraph.css", "sourcesContent": ["/**\n * Default styles for the dygraphs charting library.\n */\n\n.dygraph-legend {\n  position: absolute;\n  font-size: 14px;\n  z-index: 10;\n  width: 250px;  /* labelsDivWidth */\n  /*\n  dygraphs determines these based on the presence of chart labels.\n  It might make more sense to create a wrapper div around the chart proper.\n  top: 0px;\n  right: 2px;\n  */\n  background: white;\n  line-height: normal;\n  text-align: left;\n  overflow: hidden;\n}\n\n/* styles for a solid line in the legend */\n.dygraph-legend-line {\n  display: inline-block;\n  position: relative;\n  bottom: .5ex;\n  padding-left: 1em;\n  height: 1px;\n  border-bottom-width: 2px;\n  border-bottom-style: solid;\n  /* border-bottom-color is set based on the series color */\n}\n\n/* styles for a dashed line in the legend, e.g. when strokePattern is set */\n.dygraph-legend-dash {\n  display: inline-block;\n  position: relative;\n  bottom: .5ex;\n  height: 1px;\n  border-bottom-width: 2px;\n  border-bottom-style: solid;\n  /* border-bottom-color is set based on the series color */\n  /* margin-right is set based on the stroke pattern */\n  /* padding-left is set based on the stroke pattern */\n}\n\n.dygraph-roller {\n  position: absolute;\n  z-index: 10;\n}\n\n/* This class is shared by all annotations, including those with icons */\n.dygraph-annotation {\n  position: absolute;\n  z-index: 10;\n  overflow: hidden;\n}\n\n/* This class only applies to annotations without icons */\n/* Old class name: .dygraphDefaultAnnotation */\n.dygraph-default-annotation {\n  border: 1px solid black;\n  background-color: white;\n  text-align: center;\n}\n\n.dygraph-axis-label {\n  /* position: absolute; */\n  /* font-size: 14px; */\n  z-index: 10;\n  line-height: normal;\n  overflow: hidden;\n  color: black;  /* replaces old axisLabelColor option */\n}\n\n.dygraph-axis-label-x {\n}\n\n.dygraph-axis-label-y {\n}\n\n.dygraph-axis-label-y2 {\n}\n\n.dygraph-title {\n  font-weight: bold;\n  z-index: 10;\n  text-align: center;\n  /* font-size: based on titleHeight option */\n}\n\n.dygraph-xlabel {\n  text-align: center;\n  /* font-size: based on xLabelHeight option */\n}\n\n/* For y-axis label */\n.dygraph-label-rotate-left {\n  text-align: center;\n  /* See http://caniuse.com/#feat=transforms2d */\n  transform: rotate(90deg);\n  -webkit-transform: rotate(90deg);\n  -moz-transform: rotate(90deg);\n  -o-transform: rotate(90deg);\n  -ms-transform: rotate(90deg);\n}\n\n/* For y2-axis label */\n.dygraph-label-rotate-right {\n  text-align: center;\n  /* See http://caniuse.com/#feat=transforms2d */\n  transform: rotate(-90deg);\n  -webkit-transform: rotate(-90deg);\n  -moz-transform: rotate(-90deg);\n  -o-transform: rotate(-90deg);\n  -ms-transform: rotate(-90deg);\n}\n"]}