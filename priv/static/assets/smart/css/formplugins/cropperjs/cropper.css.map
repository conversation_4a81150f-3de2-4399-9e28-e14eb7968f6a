{"version": 3, "sources": ["cropper.css"], "names": [], "mappings": "AAAA;;;;;;;;EAQE;AAEF;EACE,cAAc;EACd,YAAY;EACZ,cAAc;EACd,kBAAkB;EAClB,sBAAsB;EACtB,kBAAkB;EAClB,yBAAyB;EACzB,sBAAsB;EACtB,qBAAqB;EACrB,iBAAiB,EAAA;;AAGnB;EACE,cAAc;EACd,YAAY;EACZ,uBAAuB;EACvB,2BAA2B;EAC3B,0BAA0B;EAC1B,wBAAwB;EACxB,uBAAuB;EACvB,WAAW,EAAA;;AAGb;;;;;EAKE,SAAS;EACT,OAAO;EACP,kBAAkB;EAClB,QAAQ;EACR,MAAM,EAAA;;AAGR;;EAEE,gBAAgB,EAAA;;AAGlB;EACE,sBAAsB;EACtB,UAAU,EAAA;;AAGZ;EACE,sBAAsB;EACtB,YAAY,EAAA;;AAGd;EACE,cAAc;EACd,YAAY;EACZ,uBAAuB;EACvB,uCAAuC;EACvC,gBAAgB;EAChB,WAAW,EAAA;;AAGb;EACE,qBAAqB;EACrB,cAAc;EACd,YAAY;EACZ,kBAAkB,EAAA;;AAGpB;EACE,wBAAwB;EACxB,qBAAqB;EACrB,sBAAsB;EACtB,OAAO;EACP,mBAAmB;EACnB,WAAW,EAAA;;AAGb;EACE,sBAAsB;EACtB,uBAAuB;EACvB,YAAY;EACZ,oBAAoB;EACpB,MAAM;EACN,qBAAqB,EAAA;;AAGvB;EACE,cAAc;EACd,SAAS;EACT,SAAS;EACT,aAAa;EACb,kBAAkB;EAClB,QAAQ;EACR,QAAQ,EAAA;;AAGV;;EAEE,sBAAsB;EACtB,YAAY;EACZ,cAAc;EACd,kBAAkB,EAAA;;AAGpB;EACE,WAAW;EACX,UAAU;EACV,MAAM;EACN,UAAU,EAAA;;AAGZ;EACE,WAAW;EACX,OAAO;EACP,SAAS;EACT,UAAU,EAAA;;AAGZ;;;EAGE,cAAc;EACd,YAAY;EACZ,YAAY;EACZ,kBAAkB;EAClB,WAAW,EAAA;;AAGb;EACE,sBAAsB;EACtB,OAAO;EACP,MAAM,EAAA;;AAGR;EACE,sBAAsB,EAAA;;AAGxB;EACE,iBAAiB;EACjB,WAAW;EACX,MAAM;EACN,UAAU,EAAA;;AAGZ;EACE,iBAAiB;EACjB,WAAW;EACX,OAAO;EACP,SAAS,EAAA;;AAGX;EACE,iBAAiB;EACjB,UAAU;EACV,MAAM;EACN,UAAU,EAAA;;AAGZ;EACE,YAAY;EACZ,iBAAiB;EACjB,WAAW;EACX,OAAO,EAAA;;AAGT;EACE,sBAAsB;EACtB,WAAW;EACX,aAAa;EACb,UAAU,EAAA;;AAGZ;EACE,iBAAiB;EACjB,gBAAgB;EAChB,WAAW;EACX,QAAQ,EAAA;;AAGV;EACE,iBAAiB;EACjB,SAAS;EACT,iBAAiB;EACjB,SAAS,EAAA;;AAGX;EACE,iBAAiB;EACjB,UAAU;EACV,gBAAgB;EAChB,QAAQ,EAAA;;AAGV;EACE,YAAY;EACZ,gBAAgB;EAChB,SAAS;EACT,iBAAiB,EAAA;;AAGnB;EACE,mBAAmB;EACnB,WAAW;EACX,SAAS,EAAA;;AAGX;EACE,mBAAmB;EACnB,UAAU;EACV,SAAS,EAAA;;AAGX;EACE,YAAY;EACZ,mBAAmB;EACnB,UAAU,EAAA;;AAGZ;EACE,YAAY;EACZ,mBAAmB;EACnB,YAAY;EACZ,UAAU;EACV,WAAW;EACX,WAAW,EAAA;;AAGb;EACE;IACE,YAAY;IACZ,WAAW,EAAA,EACZ;;AAGH;EACE;IACE,YAAY;IACZ,WAAW,EAAA,EACZ;;AAGH;EACE;IACE,WAAW;IACX,aAAa;IACb,UAAU,EAAA,EACX;;AAGH;EACE,sBAAsB;EACtB,YAAY;EACZ,YAAY;EACZ,cAAc;EACd,YAAY;EACZ,UAAU;EACV,kBAAkB;EAClB,WAAW;EACX,WAAW,EAAA;;AAGb;EACE,UAAU,EAAA;;AAGZ;EACE,+QAA+Q,EAAA;;AAGjR;EACE,cAAc;EACd,SAAS;EACT,kBAAkB;EAClB,QAAQ,EAAA;;AAGV;EACE,wBAAwB,EAAA;;AAG1B;EACE,YAAY,EAAA;;AAGd;EACE,iBAAiB,EAAA;;AAGnB;;;;EAIE,mBAAmB,EAAA;;AAGrB;EACE,gBAAgB,EAAA;;AAGlB;EACE,qBAAqB;EACrB,sBAAsB;EACtB,kBAAkB;EAClB,gBAAgB;EAChB,aAAa,EAAA;;AAGf;EACE,gBAAgB,EAAA;;AAGlB;EACE,WAAW;EACX,cAAc;EACd,WAAW,EAAA;;AAGb;;EAEE,cAAc;EACd,kBAAkB,EAAA;;AAGpB;;;EAGE,WAAW;EACX,qBAAqB,EAAA;;AAGvB;;;EAGE,WAAW;EACX,qBAAqB,EAAA;;AAGvB;EACE;IACE,YAAY;IACZ,oBAAoB;IACpB,iBAAiB;IACjB,gBAAgB,EAAA,EACjB;;AAGH;;EAEE,yBAAyB;EACzB,kBAAkB;EAClB,WAAW,EAAA;;AAGb;EACE,mBAAmB;EACnB,iBAAiB;EACjB,iBAAiB,EAAA;;AAGnB;EACE;IACE,iBAAiB,EAAA,EAClB;;AAGH;EACE,eAAe,EAAA;;AAGjB;EACE,mBAAmB,EAAA;;AAGrB;EACE,WAAW;EACX,oBAAoB;EACpB,mBAAmB;EACnB,gBAAgB,EAAA;;AAGlB;EACE,eAAe,EAAA;;AAGjB;EACE,YAAY;EACZ,YAAY,EAAA;;AAGd;EACE,cAAc;EACd,WAAW,EAAA;;AAGb;EACE,eAAe;EACf,WAAW,EAAA;;AAGb;EACE,gBAAgB;EAChB,eAAe;EACf,WAAW,EAAA;;AAGb;EACE,oBAAoB,EAAA;;AAGtB;EACE,eAAe,EAAA;;AAGjB;EACE,eAAe,EAAA;;AAGjB;;;EAGE,oBAAoB;EACpB,oBAAoB,EAAA;;AAGtB;;;EAGE,oBAAoB,EAAA;;AAGtB;EACE,cAAc;EACd,sBAAsB;EACtB,qBAAqB,EAAA;;AAGvB;EACE,iBAAiB;EACjB,mBAAmB,EAAA;;AAKrB;EACE;IACE,8BAA6B,EAAA;EAG/B;IACE,mBAAmB;IACnB,oBAAoB,EAAA;EAGtB;IACE,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,oBAAoB,EAAA,EACrB;;AAGH;EACE,WAAW,EAAA;;AAGb;EACE,kBAAkB;EAClB,qBAAqB,EAAA;;AAGvB;EACE,cAAc,EAAA;;AAGhB;EACE,kBAAkB,EAAA;;AAGpB;;EAEE,eAAe,EAAA", "file": "cropper.css", "sourcesContent": ["/*!\n * Cropper.js v1.5.2\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2019-06-30T06:01:02.389Z\n */\n\n.cropper-container {\n  direction: ltr;\n  font-size: 0;\n  line-height: 0;\n  position: relative;\n  -ms-touch-action: none;\n  touch-action: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.cropper-container img {\n  display: block;\n  height: 100%;\n  image-orientation: 0deg;\n  max-height: none !important;\n  max-width: none !important;\n  min-height: 0 !important;\n  min-width: 0 !important;\n  width: 100%;\n}\n\n.cropper-wrap-box,\n.cropper-canvas,\n.cropper-drag-box,\n.cropper-crop-box,\n.cropper-modal {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.cropper-wrap-box,\n.cropper-canvas {\n  overflow: hidden;\n}\n\n.cropper-drag-box {\n  background-color: #fff;\n  opacity: 0;\n}\n\n.cropper-modal {\n  background-color: #000;\n  opacity: 0.5;\n}\n\n.cropper-view-box {\n  display: block;\n  height: 100%;\n  outline: 1px solid #39f;\n  outline-color: rgba(51, 153, 255, 0.75);\n  overflow: hidden;\n  width: 100%;\n}\n\n.cropper-dashed {\n  border: 0 dashed #eee;\n  display: block;\n  opacity: 0.5;\n  position: absolute;\n}\n\n.cropper-dashed.dashed-h {\n  border-bottom-width: 1px;\n  border-top-width: 1px;\n  height: calc(100% / 3);\n  left: 0;\n  top: calc(100% / 3);\n  width: 100%;\n}\n\n.cropper-dashed.dashed-v {\n  border-left-width: 1px;\n  border-right-width: 1px;\n  height: 100%;\n  left: calc(100% / 3);\n  top: 0;\n  width: calc(100% / 3);\n}\n\n.cropper-center {\n  display: block;\n  height: 0;\n  left: 50%;\n  opacity: 0.75;\n  position: absolute;\n  top: 50%;\n  width: 0;\n}\n\n.cropper-center::before,\n.cropper-center::after {\n  background-color: #eee;\n  content: ' ';\n  display: block;\n  position: absolute;\n}\n\n.cropper-center::before {\n  height: 1px;\n  left: -3px;\n  top: 0;\n  width: 7px;\n}\n\n.cropper-center::after {\n  height: 7px;\n  left: 0;\n  top: -3px;\n  width: 1px;\n}\n\n.cropper-face,\n.cropper-line,\n.cropper-point {\n  display: block;\n  height: 100%;\n  opacity: 0.1;\n  position: absolute;\n  width: 100%;\n}\n\n.cropper-face {\n  background-color: #fff;\n  left: 0;\n  top: 0;\n}\n\n.cropper-line {\n  background-color: #39f;\n}\n\n.cropper-line.line-e {\n  cursor: ew-resize;\n  right: -3px;\n  top: 0;\n  width: 5px;\n}\n\n.cropper-line.line-n {\n  cursor: ns-resize;\n  height: 5px;\n  left: 0;\n  top: -3px;\n}\n\n.cropper-line.line-w {\n  cursor: ew-resize;\n  left: -3px;\n  top: 0;\n  width: 5px;\n}\n\n.cropper-line.line-s {\n  bottom: -3px;\n  cursor: ns-resize;\n  height: 5px;\n  left: 0;\n}\n\n.cropper-point {\n  background-color: #39f;\n  height: 5px;\n  opacity: 0.75;\n  width: 5px;\n}\n\n.cropper-point.point-e {\n  cursor: ew-resize;\n  margin-top: -3px;\n  right: -3px;\n  top: 50%;\n}\n\n.cropper-point.point-n {\n  cursor: ns-resize;\n  left: 50%;\n  margin-left: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-w {\n  cursor: ew-resize;\n  left: -3px;\n  margin-top: -3px;\n  top: 50%;\n}\n\n.cropper-point.point-s {\n  bottom: -3px;\n  cursor: s-resize;\n  left: 50%;\n  margin-left: -3px;\n}\n\n.cropper-point.point-ne {\n  cursor: nesw-resize;\n  right: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-nw {\n  cursor: nwse-resize;\n  left: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-sw {\n  bottom: -3px;\n  cursor: nesw-resize;\n  left: -3px;\n}\n\n.cropper-point.point-se {\n  bottom: -3px;\n  cursor: nwse-resize;\n  height: 20px;\n  opacity: 1;\n  right: -3px;\n  width: 20px;\n}\n\n@media (min-width: 768px) {\n  .cropper-point.point-se {\n    height: 15px;\n    width: 15px;\n  }\n}\n\n@media (min-width: 992px) {\n  .cropper-point.point-se {\n    height: 10px;\n    width: 10px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cropper-point.point-se {\n    height: 5px;\n    opacity: 0.75;\n    width: 5px;\n  }\n}\n\n.cropper-point.point-se::before {\n  background-color: #39f;\n  bottom: -50%;\n  content: ' ';\n  display: block;\n  height: 200%;\n  opacity: 0;\n  position: absolute;\n  right: -50%;\n  width: 200%;\n}\n\n.cropper-invisible {\n  opacity: 0;\n}\n\n.cropper-bg {\n  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');\n}\n\n.cropper-hide {\n  display: block;\n  height: 0;\n  position: absolute;\n  width: 0;\n}\n\n.cropper-hidden {\n  display: none !important;\n}\n\n.cropper-move {\n  cursor: move;\n}\n\n.cropper-crop {\n  cursor: crosshair;\n}\n\n.cropper-disabled .cropper-drag-box,\n.cropper-disabled .cropper-face,\n.cropper-disabled .cropper-line,\n.cropper-disabled .cropper-point {\n  cursor: not-allowed;\n}\n\nlabel.btn {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.carbonads {\r\n  border-radius: .25rem;\r\n  border: 1px solid #ccc;\r\n  font-size: .875rem;\r\n  overflow: hidden;\r\n  padding: 1rem;\r\n}\r\n\r\n.carbon-wrap {\r\n  overflow: hidden;\r\n}\r\n\r\n.carbon-img {\r\n  clear: left;\r\n  display: block;\r\n  float: left;\r\n}\r\n\r\n.carbon-text,\r\n.carbon-poweredby {\r\n  display: block;\r\n  margin-left: 140px;\r\n}\r\n\r\n.carbon-text,\r\n.carbon-text:hover,\r\n.carbon-text:focus {\r\n  color: #fff;\r\n  text-decoration: none;\r\n}\r\n\r\n.carbon-poweredby,\r\n.carbon-poweredby:hover,\r\n.carbon-poweredby:focus {\r\n  color: #ddd;\r\n  text-decoration: none;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .carbonads {\r\n    float: right;\r\n    margin-bottom: -1rem;\r\n    margin-top: -1rem;\r\n    max-width: 360px;\r\n  }\r\n}\r\n\r\n.img-container,\r\n.img-preview {\r\n  background-color: #f7f7f7;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.img-container {\r\n  margin-bottom: 1rem;\r\n  max-height: 497px;\r\n  min-height: 200px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .img-container {\r\n    min-height: 497px;\r\n  }\r\n}\r\n\r\n.img-container > img {\r\n  max-width: 100%;\r\n}\r\n\r\n.docs-preview {\r\n  margin-right: -1rem;\r\n}\r\n\r\n.img-preview {\r\n  float: left;\r\n  margin-bottom: .5rem;\r\n  margin-right: .5rem;\r\n  overflow: hidden;\r\n}\r\n\r\n.img-preview > img {\r\n  max-width: 100%;\r\n}\r\n\r\n.preview-lg {\r\n  height: 9rem;\r\n  width: 16rem;\r\n}\r\n\r\n.preview-md {\r\n  height: 4.5rem;\r\n  width: 8rem;\r\n}\r\n\r\n.preview-sm {\r\n  height: 2.25rem;\r\n  width: 4rem;\r\n}\r\n\r\n.preview-xs {\r\n  height: 1.125rem;\r\n  margin-right: 0;\r\n  width: 2rem;\r\n}\r\n\r\n.docs-data > .input-group {\r\n  margin-bottom: .5rem;\r\n}\r\n\r\n.docs-data .input-group-prepend .input-group-text {\r\n  min-width: 4rem;\r\n}\r\n\r\n.docs-data .input-group-append .input-group-text {\r\n  min-width: 3rem;\r\n}\r\n\r\n.docs-buttons > .btn,\r\n.docs-buttons > .btn-group,\r\n.docs-buttons > .form-control {\r\n  margin-bottom: .5rem;\r\n  margin-right: .25rem;\r\n}\r\n\r\n.docs-toggles > .btn,\r\n.docs-toggles > .btn-group,\r\n.docs-toggles > .dropdown {\r\n  margin-bottom: .5rem;\r\n}\r\n\r\n.docs-tooltip {\r\n  display: block;\r\n  margin: -.5rem -.75rem;\r\n  padding: .5rem .46rem;\r\n}\r\n\r\n.docs-tooltip > .icon {\r\n  margin: 0 -.25rem;\r\n  vertical-align: top;\r\n}\r\n\r\n\r\n\r\n@media (max-width: 400px) {\r\n  .btn-group-crop {\r\n    margin-right: -1rem!important;\r\n  }\r\n\r\n  .btn-group-crop > .btn {\r\n    padding-left: .5rem;\r\n    padding-right: .5rem;\r\n  }\r\n\r\n  .btn-group-crop .docs-tooltip {\r\n    margin-left: -.5rem;\r\n    margin-right: -.5rem;\r\n    padding-left: .5rem;\r\n    padding-right: .5rem;\r\n  }\r\n}\r\n\r\n.docs-options .dropdown-menu {\r\n  width: 100%;\r\n}\r\n\r\n.docs-options .dropdown-menu > li {\r\n  font-size: .875rem;\r\n  padding: .125rem 1rem;\r\n}\r\n\r\n.docs-options .dropdown-menu .form-check-label {\r\n  display: block;\r\n}\r\n\r\n.docs-cropped .modal-body {\r\n  text-align: center;\r\n}\r\n\r\n.docs-cropped .modal-body > img,\r\n.docs-cropped .modal-body > canvas {\r\n  max-width: 100%;\r\n}"]}