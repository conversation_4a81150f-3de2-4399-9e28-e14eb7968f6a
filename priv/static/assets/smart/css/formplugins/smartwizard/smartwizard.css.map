{"version": 3, "sources": ["smartwizard.css"], "names": [], "mappings": "AAAA;;;;;;;;;;EAUE;AAEF,0BAAA;AACA;EACI,kBAAkB;EAClB,cAAc;EACd,SAAS;EACT,UAAU;EACV,iCAA+B,EAAA;;AAGnC;EACI,cAAc;EACd,SAAS;EACT,UAAU;EACV,kBAAkB,EAAA;;AAGtB;EACI,aAAa;EACb,kBAAkB;EAClB,SAAS,EAAA;;AAGb;EACI,cAAc,EAAA;;AAGlB,6BAAA;AACA;EACI,kDAAkD;EAClD,0CAA0C,EAAA;;AAG9C;EACI,iBAAiB,EAAA;;AAGrB;EACI,aAAa;EACb,yBAAyB;EACzB,sBAAsB;EACtB,gBAAgB,EAAA;;AAGpB;EACI,mBAAmB;EACnB,2BAA2B;EAC3B,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,2BAA2B,EAAA;;AAG/B;EACI,oCAAoC,EAAA;;AAGxC;EACI,iCAAiC,EAAA;;AAGrC;EACI,kBAAkB;EAClB,iBAAiB,EAAA;;AAGrB;EACI,uBAAuB;EACvB,WAAW;EACX,qBAAqB;EACrB,mBAAmB;EACnB,kCAAkC;EAClC,uBAAuB;EACvB,mBAAmB,EAAA;;AAGvB;EACI,yBAAyB;EACzB,kCAAkC;EAClC,eAAe,EAAA;;AAGnB;EACI,WAAW;EACX,mBAAmB;EACnB,WAAW;EACX,kBAAkB;EAClB,WAAW;EACX,SAAS;EACT,WAAW;EACX,qCAAqC;EACrC,6BAA6B;EAC7B,2BAA2B;EAE3B,mBAAmB,EAAA;;AAGvB;EACI,uBAAuB;EACvB,yBAAyB;EACzB,kCAAkC;EAClC,eAAe,EAAA;;AAGnB;EACI,2BAA2B;EAE3B,mBAAmB,EAAA;;AAGvB;EACI,uBAAuB;EACvB,sBAAsB;EACtB,kCAAkC;EAClC,eAAe,EAAA;;AAGnB;EACI,mBAAmB;EACnB,2BAA2B;EAE3B,mBAAmB,EAAA;;AAGvB;EACI,uBAAuB;EACvB,yBAAyB;EACzB,oCAAA;EACA,eAAe,EAAA;;AAGnB;EACI,mBAAmB;EACnB,0BAA0B;EAC1B,2BAA2B;EAE3B,mBAAmB,EAAA;;AAGvB;EACI,sBAAsB;EACtB,mBAAmB,EAAA;;AAGvB,mBAAA;AACA;EACI;IACI,sBAAsB,EAAA,EACzB;;AAGL,kBAAA;AACA;EACI,kBAAkB;EAClB,cAAc;EACd,UAAU;EACV,WAAW;EACX,MAAM;EACN,OAAO;EACP,YAAY;EACZ,WAAW;EACX,oCAAgC;EAChC,gCAAgC;EAChC,wBAAwB;EACxB,UAAU,EAAA;;AAGd;EACI,WAAW;EACX,qBAAqB;EACrB,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,0BAA0B;EAC1B,kBAAkB;EAClB,8BAA8B;EAC9B,WAAW;EACX,YAAY;EACZ,iBAAiB;EACjB,kBAAkB;EAClB,0CAA0C;EAC7C,WAAA;EACG,kCAAkC,EAAA;;AAGtC,WAAA;AACA;EACI;IACI,+BAA+B,EAAA;EAGnC;IACI,iCAAiC,EAAA,EAAA;;AAIzC;EACI;IACI,+BAA+B;IAC/B,uBAAuB,EAAA;EAG3B;IACI,iCAAiC;IACjC,yBAAyB,EAAA,EAAA;;AAIjC;;;;;;;;;;EAxBE;AAoCF,8BAAA;AACA;EACI,kBAAkB;EAClB,sBAAsB,EAAA;;AAG1B;EACI,iBAAiB,EAAA;;AAGrB;EACI,eAAe;EACf,yBAAyB;EACzB,sBAAsB;EACtB,gBAAgB,EAAA;;AAGpB;EACI,aAAa;EACb,2BAA2B,EAAA;;AAS/B;EACI,SAAS;EACT,6BAA6B;EAC7B,YAAY;EACZ,mBAAmB;EACnB,gBAAgB;EAChB,4BAA4B;EAC5B,gBAAgB;EAChB,gBAAgB,EAAA;;AAGpB;EACI,UAAU,EAAA;;AAMd;EACI,WAAW;EACX,qBAAqB;EACrB,yBAAyB;EACzB,kBAAkB;EAClB,cAAc;EACd,oBAAoB;EACpB,gBAAgB;EAChB,mBAAmB;EACnB,mBAAmB,EAAA;;AAGvB;EACI,YAAY;EACZ,cAAc;EACd,QAAQ;EACR,SAAS;EACT,kCAAkC;EAClC,qCAAqC;EACrC,+BAA+B;EAC/B,kBAAkB;EAClB,QAAQ;EACR,iBAAiB;EACjB,UAAU;EACV,UAAU,EAAA;;AAGd;EACI,YAAY;EACZ,cAAc;EACd,QAAQ;EACR,SAAS;EACT,kCAAkC;EAC3B,8CAAA;EACP,qCAAqC;EACrC,4BAA4B;EAC5B,kBAAkB;EAClB,QAAQ;EACR,iBAAiB;EACjB,gBAAgB;EAChB,UAAU;EACV,UAAU,EAAA;;AAGd;EACI,kBAAkB,EAAA;;AAGtB;EACI,WAAW;EACX,qBAAqB;EACrB,mBAAmB;EACnB,mBAAmB;EACnB,qBAAqB,EAAA;;AAGzB;EACI,0BAA0B,EAAA;;AAM9B;EACI,yBAAyB;EACzB,8BAA8B,EAAA;;AAGlC;EACI,gCAAgC;EAChC,sBAAsB;EACtB,8BAA8B,EAAA;;AAGlC;EACI,0CAA0C,EAAA;;AAG9C;EACI,gCAAgC;EACnC,aAAA;EACG,sBAAsB;EACtB,8BAA8B,EAAA;;AAGlC;EACI,+BAA+B;EAClC,WAAA,EAAY;;AAGb;EACI,gCAAgC;EAChC,sBAAsB;EACtB,8BAA8B,EAAA;;AAGlC;EACI,0CAA0C,EAAA;;AAG9C;EACI,sBAAsB,EAAA;;AAG1B,mBAAA;AACA;EACI;IACI,SAAS;IACT,2BAA2B,EAAA;EAG/B;IACI,sBAAsB;IACtB,gBAAgB,EAAA;EAGpB;IACI,kBAAkB;IAClB,eAAe;IACf,kBAAkB,EAAA;EAGtB;IACI,aAAa,EAAA,EAChB;;AAGL,wBAAA;AACA;EACI,0BAA0B;EAC1B,8BAA8B,EAAA;;AAGlC;;;;;;;;;;EAlEE;AA8EF,+BAAA;AAIA;EACI,iBAAiB,EAAA;;AAGrB;EACI,eAAe;EACf,sBAAsB;EACtB,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB;EAChB,kBAAkB;EAClB,mBAAmB;EACnB,2BAA2B,EAAA;;AAM/B;EACI,iCAAiC;EACjC,oCAAoC,EAAA;;AAGxC;EACI,kBAAkB;EAClB,gBAAgB;EAChB,YAAY;EACZ,gBAAgB;EAChB,mBAAmB,EAAA;;AAGvB;EACI,YAAY;EACZ,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,WAAW;EACX,yBAAyB;EACzB,kBAAkB;EAClB,UAAU,EAAA;;AAGd;EACI,YAAY;EACZ,iBAAiB;EACjB,WAAW,EAAA;;AAGf;EACI,yBAAyB;EACzB,mBAAmB;EACnB,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,eAAe;EACf,kBAAkB;EAClB,yDAAyD;EACzD,iDAAiD;EACjD,qBAAqB;EACrB,mBAAmB;EACnB,WAAW;EACX,WAAW;EACX,mBAAmB;EACnB,cAAc,EAAA;;AAGlB;EACI,WAAW;EACX,mBAAmB;EACnB,iBAAiB,EAAA;;AAGrB;EACI,kBAAkB;EAClB,aAAa;EACb,WAAW,EAAA;;AAGf;EACI,yBAAyB,EAAA;;AAG7B;EACI,qBAAqB;EACrB,WAAW;EACX,mBAAmB,EAAA;;AAGvB;EACI,cAAc,EAAA;;AAGlB;EACI,qBAAqB;EACrB,WAAW;EACX,mBAAmB,EAAA;;AAGvB;EACI,cAAc,EAAA;;AAGlB;EACI,qBAAqB;EACrB,cAAc;EACd,gBAAgB,EAAA;;AAGpB;EACI,cAAc,EAAA;;AAGlB;EACI,sBAAsB,EAAA;;AAG1B;;;;;;;;;;EA3FE;AAuGF,4BAAA;AAIA;EACI,iBAAiB,EAAA;;AAGrB;EACI,eAAe;EACf,YAAY;EACZ,sBAAsB;EACtB,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB;EAChB,2BAA2B;EAC3B,kBAAkB;EAClB,mBAAmB;EACnB,2BAA2B,EAAA;;AAG/B;EACI,oCAAoC,EAAA;;AAGxC;EACI,iCAAiC;EACjC,oCAAoC,EAAA;;AAGxC;EACI,kBAAkB;EAClB,gBAAgB;EAChB,iCAAiC;EACjC,gBAAgB,EAAA;;AAGpB;EACI,YAAY;EACZ,kBAAkB;EAClB,SAAS;EACT,SAAS;EACT,WAAW;EACX,WAAW;EACX,yBAAyB;EACzB,kBAAkB;EAClB,UAAU;EACV,WAAW,EAAA;;AAGf;EACI,YAAY,EAAA;;AAEhB,mBAAA;AACA;EACI,kBAAkB;EAClB,kBAAkB;EAClB,iBAAiB;EACjB,uBAAuB;EACvB,YAAY;EACZ,WAAW;EACX,qBAAqB;EACrB,mBAAmB;EACnB,WAAW;EACX,cAAc,EAAA;;AAGlB;EACI,YAAY;EACZ,kBAAkB;EAClB,WAAW;EACX,SAAS;EACT,gBAAgB;EAChB,cAAc;EACd,kBAAkB;EAClB,cAAc;EACd,mBAAmB;EACnB,YAAY;EACZ,WAAW;EACX,YAAY;EACZ,qBAAqB;EACrB,WAAW,EAAA;;AAGf;EACI,YAAY;EACZ,kBAAkB;EAClB,SAAS;EACT,WAAW;EACX,gBAAgB;EAChB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,mBAAmB;EACnB,kBAAkB;EAClB,WAAW,EAAA;;AAGf;EACI,WAAW;EACX,uBAAuB,EAAA;;AAG3B;EACI,WAAW;EACX,YAAY,EAAA;;AAGhB;EACI,WAAW,EAAA;;AAEf,mBAAA;AACA;EACI,cAAc,EAAA;;AAGlB;EACI,YAAY,EAAA;;AAGhB;EACI,mBAAmB,EAAA;;AAEvB,iBAAA;AACA;EACI,cAAc,EAAA;;AAGlB;EACI,mBAAmB,EAAA;;AAEvB,mBAAA;AACA;EACI,cAAc,EAAA;;AAGlB;EACI,mBAAmB,EAAA;;AAGvB;EACI,sBAAsB,EAAA;;AAG1B;EACI,gBAAgB,EAAA;;AAGpB,mBAAA;AACA;EACI;IACI,MAAM;IACN,SAAS;IACT,UAAU;IACV,UAAU;IACV,YAAY;IACZ,yBAAyB;IACzB,cAAc;IACd,kBAAkB,EAAA;EAGtB;IACI,iBAAiB;IACjB,cAAc;IACd,WAAW,EAAA;EAGf;IACI,gBAAgB;IAChB,cAAc;IACd,cAAc,EAAA;EAGlB;IACI,QAAQ;IACR,WAAW;IACX,kBAAkB;IAClB,cAAc,EAAA;EAGlB;IACI,UAAU;IACV,WAAW;IACX,kBAAkB;IAClB,cAAc,EAAA,EACjB", "file": "smartwizard.css", "sourcesContent": ["/*!\n * SmartWizard v4.3.x\n * jQuery Wizard Plugin\n * http://www.techlaboratory.net/smartwizard\n *\n * Created by <PERSON><PERSON>\n * http://dipuraj.me\n *\n * Licensed under the terms of MIT License\n * https://github.com/techlab/SmartWizard/blob/master/LICENSE\n */\n\n/* SmartWizard Basic CSS */\n.sw-main {\n    position: relative;\n    display: block;\n    margin: 0;\n    padding: 0;\n    border-radius: .25rem!important;\n}\n\n.sw-main .sw-container {\n    display: block;\n    margin: 0;\n    padding: 0;\n    position: relative;\n}\n\n.sw-main .step-content {\n    display: none;\n    position: relative;\n    margin: 0;\n}\n\n.sw-main .sw-toolbar {\n    margin-left: 0;\n}\n\n/* SmartWizard Theme: White */\n.sw-theme-default {\n    -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3);\n    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3);\n}\n\n.sw-theme-default .sw-container {\n    min-height: 250px;\n}\n\n.sw-theme-default .step-content {\n    padding: 10px;\n    border: 0px solid #D4D4D4;\n    background-color: #FFF;\n    text-align: left;\n}\n\n.sw-theme-default .sw-toolbar {\n    background: #f9f9f9;\n    border-radius: 0 !important;\n    padding-left: 10px;\n    padding-right: 10px;\n    padding: 10px;\n    margin-bottom: 0 !important;\n}\n\n.sw-theme-default .sw-toolbar-top {\n    border-bottom-color: #ddd !important;\n}\n\n.sw-theme-default .sw-toolbar-bottom {\n    border-top-color: #ddd !important;\n}\n\n.sw-theme-default > ul.step-anchor > li {\n    position: relative;\n    margin-right: 2px;\n}\n\n.sw-theme-default > ul.step-anchor > li > a, .sw-theme-default > ul.step-anchor > li > a:hover {\n    border: none !important;\n    color: #bbb;\n    text-decoration: none;\n    outline-style: none;\n    background: transparent !important;\n    border: none !important;\n    cursor: not-allowed;\n}\n\n.sw-theme-default > ul.step-anchor > li.clickable > a:hover {\n    color: #4285F4 !important;\n    background: transparent !important;\n    cursor: pointer;\n}\n\n.sw-theme-default > ul.step-anchor > li > a::after {\n    content: \"\";\n    background: #4285F4;\n    height: 2px;\n    position: absolute;\n    width: 100%;\n    left: 0px;\n    bottom: 0px;\n    -webkit-transition: all 250ms ease 0s;\n    transition: all 250ms ease 0s;\n    -webkit-transform: scale(0);\n    -ms-transform: scale(0);\n    transform: scale(0);\n}\n\n.sw-theme-default > ul.step-anchor > li.active > a {\n    border: none !important;\n    color: #4285F4 !important;\n    background: transparent !important;\n    cursor: pointer;\n}\n\n.sw-theme-default > ul.step-anchor > li.active > a::after {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n}\n\n.sw-theme-default > ul.step-anchor > li.done > a {\n    border: none !important;\n    color: #000 !important;\n    background: transparent !important;\n    cursor: pointer;\n}\n\n.sw-theme-default > ul.step-anchor > li.done > a::after {\n    background: #5cb85c;\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n}\n\n.sw-theme-default > ul.step-anchor > li.danger > a {\n    border: none !important;\n    color: #d9534f !important;\n    /* background: #d9534f !important; */\n    cursor: pointer;\n}\n\n.sw-theme-default > ul.step-anchor > li.danger > a::after {\n    background: #d9534f;\n    border-left-color: #f8d7da;\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n}\n\n.sw-theme-default > ul.step-anchor > li.disabled > a, .sw-theme-default > ul.step-anchor > li.disabled > a:hover {\n    color: #eee !important;\n    cursor: not-allowed;\n}\n\n/* Responsive CSS */\n@media screen and (max-width: 768px) {\n    .sw-theme-default > .nav-tabs > li {\n        float: none !important;\n    }\n}\n\n/* Common Loader */\n.sw-loading::after {\n    position: absolute;\n    display: block;\n    opacity: 1;\n    content: \"\";\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n    background: rgba(255,255,255,.7);\n    -webkit-transition: all .2s ease;\n    transition: all .2s ease;\n    z-index: 2;\n}\n\n.sw-loading::before {\n    content: '';\n    display: inline-block;\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    z-index: 10;\n    border: 10px solid #f3f3f3;\n    border-radius: 50%;\n    border-top: 10px solid #3498db;\n    width: 80px;\n    height: 80px;\n    margin-top: -40px;\n    margin-left: -40px;\n    -webkit-animation: spin 1s linear infinite;\n /* Safari */\n    animation: spin 1s linear infinite;\n}\n\n/* Safari */\n@-webkit-keyframes spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n    }\n\n    100% {\n        -webkit-transform: rotate(360deg);\n    }\n}\n\n@keyframes spin {\n    0% {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n\n    100% {\n        -webkit-transform: rotate(360deg);\n        transform: rotate(360deg);\n    }\n}\n\n/*!\n * SmartWizard v4.3.x\n * jQuery Wizard Plugin\n * http://www.techlaboratory.net/smartwizard\n *\n * Created by Dipu Raj\n * http://dipuraj.me\n *\n * Licensed under the terms of MIT License\n * https://github.com/techlab/SmartWizard/blob/master/LICENSE\n */\n\n/* SmartWizard Theme: Arrows */\n.sw-theme-arrows {\n    border-radius: 5px;\n    border: 1px solid #ddd;\n}\n\n.sw-theme-arrows > .sw-container {\n    min-height: 200px;\n}\n\n.sw-theme-arrows .step-content {\n    padding: 0 10px;\n    border: 0px solid #D4D4D4;\n    background-color: #FFF;\n    text-align: left;\n}\n\n.sw-theme-arrows .sw-toolbar {\n    padding: 10px;\n    margin-bottom: 0 !important;\n}\n\n.sw-theme-arrows > .sw-toolbar-top {\n}\n\n.sw-theme-arrows > .sw-toolbar-bottom {\n}\n\n.sw-theme-arrows > ul.step-anchor {\n    border: 0;\n    border-bottom: 1px solid #ddd;\n    padding: 0px;\n    background: #f5f5f5;\n    border-radius: 0;\n    border-top-right-radius: 5px;\n    list-style: none;\n    overflow: hidden;\n}\n\n.sw-theme-arrows > ul.step-anchor li+li:before {\n    padding: 0;\n}\n\n.sw-theme-arrows > ul.step-anchor > li {\n}\n\n.sw-theme-arrows > ul.step-anchor > li > a, .sw-theme-arrows > ul.step-anchor > li > a:hover {\n    color: #bbb;\n    text-decoration: none;\n    padding: 10px 0 10px 45px;\n    position: relative;\n    display: block;\n    border: 0 !important;\n    border-radius: 0;\n    outline-style: none;\n    background: #f5f5f5;\n}\n\n.sw-theme-arrows > ul.step-anchor > li > a:after {\n    content: \" \";\n    display: block;\n    width: 0;\n    height: 0;\n    border-top: 50px solid transparent;\n    border-bottom: 50px solid transparent;\n    border-left: 30px solid #f5f5f5;\n    position: absolute;\n    top: 50%;\n    margin-top: -50px;\n    left: 100%;\n    z-index: 2;\n}\n\n.sw-theme-arrows > ul.step-anchor > li > a:before {\n    content: \" \";\n    display: block;\n    width: 0;\n    height: 0;\n    border-top: 50px solid transparent;\n           /* Go big on the size, and let overflow hide */\n    border-bottom: 50px solid transparent;\n    border-left: 30px solid #ddd;\n    position: absolute;\n    top: 50%;\n    margin-top: -50px;\n    margin-left: 1px;\n    left: 100%;\n    z-index: 1;\n}\n\n.sw-theme-arrows > ul.step-anchor > li:first-child > a {\n    padding-left: 15px;\n}\n\n.sw-theme-arrows > ul.step-anchor > li > a:hover {\n    color: #bbb;\n    text-decoration: none;\n    outline-style: none;\n    background: #f5f5f5;\n    border-color: #f5f5f5;\n}\n\n.sw-theme-arrows > ul.step-anchor > li > a:hover:after {\n    border-left-color: #f5f5f5;\n}\n\n.sw-theme-arrows > ul.step-anchor > li > a small {\n}\n\n.sw-theme-arrows > ul.step-anchor > li.clickable > a:hover {\n    color: #4285F4 !important;\n    background: #46b8da !important;\n}\n\n.sw-theme-arrows > ul.step-anchor > li.active > a {\n    border-color: #5cb85c !important;\n    color: #fff !important;\n    background: #5cb85c !important;\n}\n\n.sw-theme-arrows > ul.step-anchor > li.active > a:after {\n    border-left: 30px solid #5cb85c !important;\n}\n\n.sw-theme-arrows > ul.step-anchor > li.done > a {\n    border-color: #b1dfbb !important;\n /*  #5cb85c */\n    color: #fff !important;\n    background: #b1dfbb !important;\n}\n\n.sw-theme-arrows > ul.step-anchor > li.done > a:after {\n    border-left: 30px solid #b1dfbb;\n /* c3e6cb */;\n}\n\n.sw-theme-arrows > ul.step-anchor > li.danger > a {\n    border-color: #d9534f !important;\n    color: #fff !important;\n    background: #d9534f !important;\n}\n\n.sw-theme-arrows > ul.step-anchor > li.danger > a:after {\n    border-left: 30px solid #d9534f !important;\n}\n\n.sw-theme-arrows > ul.step-anchor > li.disabled > a, .sw-theme-arrows > ul.step-anchor > li.disabled > a:hover {\n    color: #eee !important;\n}\n\n/* Responsive CSS */\n@media screen and (max-width: 768px) {\n    .sw-theme-arrows > ul.step-anchor {\n        border: 0;\n        background: #ddd !important;\n    }\n\n    .sw-theme-arrows > .nav-tabs > li {\n        float: none !important;\n        margin-bottom: 0;\n    }\n\n    .sw-theme-arrows > ul.step-anchor > li > a, .sw-theme-arrows > ul.step-anchor > li > a:hover {\n        padding-left: 15px;\n        margin-right: 0;\n        margin-bottom: 1px;\n    }\n\n    .sw-theme-arrows > ul.step-anchor > li > a:after, .sw-theme-arrows > ul.step-anchor > li > a:before {\n        display: none;\n    }\n}\n\n/* Loader Custom Style */\n.sw-theme-arrows::before {\n    border: 10px solid #f3f3f3;\n    border-top: 10px solid #5cb85c;\n}\n\n/*!\n * SmartWizard v4.3.x\n * jQuery Wizard Plugin\n * http://www.techlaboratory.net/smartwizard\n *\n * Created by Dipu Raj\n * http://dipuraj.me\n *\n * Licensed under the terms of MIT License\n * https://github.com/techlab/SmartWizard/blob/master/LICENSE\n */\n\n/* SmartWizard Theme: Circles */\n.sw-theme-circles {\n}\n\n.sw-theme-circles .sw-container {\n    min-height: 300px;\n}\n\n.sw-theme-circles .step-content {\n    padding: 10px 0;\n    background-color: #FFF;\n    text-align: left;\n}\n\n.sw-theme-circles .sw-toolbar {\n    background: #fff;\n    padding-left: 10px;\n    padding-right: 10px;\n    margin-bottom: 0 !important;\n}\n\n.sw-theme-circles .sw-toolbar-top {\n}\n\n.sw-theme-circles .sw-toolbar-bottom {\n    border-top-color: #ddd !important;\n    border-bottom-color: #ddd !important;\n}\n\n.sw-theme-circles > ul.step-anchor {\n    position: relative;\n    background: #fff;\n    border: none;\n    list-style: none;\n    margin-bottom: 40px;\n}\n\n.sw-theme-circles > ul.step-anchor:before {\n    content: \" \";\n    position: absolute;\n    top: 50%;\n    bottom: 0;\n    width: 100%;\n    height: 5px;\n    background-color: #f5f5f5;\n    border-radius: 3px;\n    z-index: 0;\n}\n\n.sw-theme-circles > ul.step-anchor > li {\n    border: none;\n    margin-left: 40px;\n    z-index: 98;\n}\n\n.sw-theme-circles > ul.step-anchor > li > a {\n    border: 2px solid #f5f5f5;\n    background: #f5f5f5;\n    width: 75px;\n    height: 75px;\n    text-align: center;\n    padding: 25px 0;\n    border-radius: 50%;\n    -webkit-box-shadow: inset 0px 0px 0px 3px #fff !important;\n    box-shadow: inset 0px 0px 0px 3px #fff !important;\n    text-decoration: none;\n    outline-style: none;\n    z-index: 99;\n    color: #bbb;\n    background: #f5f5f5;\n    line-height: 1;\n}\n\n.sw-theme-circles > ul.step-anchor > li > a:hover {\n    color: #bbb;\n    background: #f5f5f5;\n    border-width: 2px;\n}\n\n.sw-theme-circles > ul.step-anchor > li > a > small {\n    position: relative;\n    bottom: -40px;\n    color: #ccc;\n}\n\n.sw-theme-circles > ul.step-anchor > li.clickable > a:hover {\n    color: #4285F4 !important;\n}\n\n.sw-theme-circles > ul.step-anchor > li.active > a {\n    border-color: #5bc0de;\n    color: #fff;\n    background: #5bc0de;\n}\n\n.sw-theme-circles > ul.step-anchor > li.active > a > small {\n    color: #5bc0de;\n}\n\n.sw-theme-circles > ul.step-anchor > li.done > a {\n    border-color: #5cb85c;\n    color: #fff;\n    background: #5cb85c;\n}\n\n.sw-theme-circles > ul.step-anchor > li.done > a > small {\n    color: #5cb85c;\n}\n\n.sw-theme-circles > ul.step-anchor > li.danger > a {\n    border-color: #d9534f;\n    color: #d9534f;\n    background: #fff;\n}\n\n.sw-theme-circles > ul.step-anchor > li.danger > a > small {\n    color: #d9534f;\n}\n\n.sw-theme-circles > ul.step-anchor > li.disabled > a, .sw-theme-circles > ul.step-anchor > li.disabled > a:hover {\n    color: #eee !important;\n}\n\n/*!\n * SmartWizard v4.3.x\n * jQuery Wizard Plugin\n * http://www.techlaboratory.net/smartwizard\n *\n * Created by Dipu Raj\n * http://dipuraj.me\n *\n * Licensed under the terms of MIT License\n * https://github.com/techlab/SmartWizard/blob/master/LICENSE\n */\n\n/* SmartWizard Theme: Dots */\n.sw-theme-dots {\n}\n\n.sw-theme-dots .sw-container {\n    min-height: 300px;\n}\n\n.sw-theme-dots .step-content {\n    padding: 10px 0;\n    border: none;\n    background-color: #FFF;\n    text-align: left;\n}\n\n.sw-theme-dots .sw-toolbar {\n    background: #fff;\n    border-radius: 0 !important;\n    padding-left: 10px;\n    padding-right: 10px;\n    margin-bottom: 0 !important;\n}\n\n.sw-theme-dots .sw-toolbar-top {\n    border-bottom-color: #ddd !important;\n}\n\n.sw-theme-dots .sw-toolbar-bottom {\n    border-top-color: #ddd !important;\n    border-bottom-color: #ddd !important;\n}\n\n.sw-theme-dots > ul.step-anchor {\n    position: relative;\n    background: #fff;\n    border: 0px solid #ccc !important;\n    list-style: none;\n}\n\n.sw-theme-dots > ul.step-anchor:before {\n    content: \" \";\n    position: absolute;\n    top: 70px;\n    bottom: 0;\n    width: 100%;\n    height: 5px;\n    background-color: #f5f5f5;\n    border-radius: 3px;\n    z-order: 0;\n    z-index: 95;\n}\n\n.sw-theme-dots > ul.step-anchor > li {\n    border: none;\n}\n/* Anchors styles */\n.sw-theme-dots > ul.step-anchor > li > a {\n    position: relative;\n    text-align: center;\n    font-weight: bold;\n    background: transparent;\n    border: none;\n    color: #ccc;\n    text-decoration: none;\n    outline-style: none;\n    z-index: 96;\n    display: block;\n}\n\n.sw-theme-dots > ul.step-anchor > li > a:before {\n    content: ' ';\n    position: absolute;\n    bottom: 2px;\n    left: 40%;\n    margin-top: 10px;\n    display: block;\n    border-radius: 50%;\n    color: #428bca;\n    background: #f5f5f5;\n    border: none;\n    width: 30px;\n    height: 30px;\n    text-decoration: none;\n    z-index: 98;\n}\n\n.sw-theme-dots > ul.step-anchor > li > a:after {\n    content: ' ';\n    position: relative;\n    left: 43%;\n    bottom: 2px;\n    margin-top: 10px;\n    display: block;\n    width: 15px;\n    height: 15px;\n    background: #f5f5f5;\n    border-radius: 50%;\n    z-index: 99;\n}\n\n.sw-theme-dots > ul.step-anchor > li > a:hover {\n    color: #ccc;\n    background: transparent;\n}\n\n.sw-theme-dots > ul.step-anchor > li > a:focus {\n    color: #ccc;\n    border: none;\n}\n\n.sw-theme-dots > ul.step-anchor > li.clickable > a:hover {\n    color: #999;\n}\n/* Active anchors */\n.sw-theme-dots > ul.step-anchor > li.active > a {\n    color: #5bc0de;\n}\n\n.sw-theme-dots > ul.step-anchor > li.active > a:hover {\n    border: none;\n}\n\n.sw-theme-dots > ul.step-anchor > li.active > a:after {\n    background: #5bc0de;\n}\n/* Done anchors */\n.sw-theme-dots > ul.step-anchor > li.done > a {\n    color: #5cb85c;\n}\n\n.sw-theme-dots > ul.step-anchor > li.done > a:after {\n    background: #5cb85c;\n}\n/* Danger anchors */\n.sw-theme-dots > ul.step-anchor > li.danger > a {\n    color: #d9534f;\n}\n\n.sw-theme-dots > ul.step-anchor > li.danger > a:after {\n    background: #d9534f;\n}\n\n.sw-theme-dots > ul.step-anchor > li.disabled > a, .sw-theme-dots > ul.step-anchor > li.disabled > a:hover {\n    color: #eee !important;\n}\n\n.sw-theme-dots > ul.step-anchor > li.disabled > a:after {\n    background: #eee;\n}\n\n/* Responsive CSS */\n@media screen and (max-width: 768px) {\n    .sw-theme-dots > ul.step-anchor:before {\n        top: 0;\n        bottom: 0;\n        left: 10px;\n        width: 5px;\n        height: 100%;\n        background-color: #f5f5f5;\n        display: block;\n        margin-right: 10px;\n    }\n\n    .sw-theme-dots > ul.step-anchor > li {\n        margin-left: 20px;\n        display: block;\n        clear: both;\n    }\n\n    .sw-theme-dots > ul.step-anchor > li > a {\n        text-align: left;\n        margin-left: 0;\n        display: block;\n    }\n\n    .sw-theme-dots > ul.step-anchor > li > a:before {\n        top: 5px;\n        left: -23px;\n        margin-right: 10px;\n        display: block;\n    }\n\n    .sw-theme-dots > ul.step-anchor > li > a:after {\n        top: -38px;\n        left: -31px;\n        margin-right: 10px;\n        display: block;\n    }\n}\n"]}