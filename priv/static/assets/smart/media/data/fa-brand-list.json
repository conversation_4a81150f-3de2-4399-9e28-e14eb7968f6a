["-500px", "-accessible-icon", "-accusoft", "-adn", "-adversal", "-affiliatetheme", "-algolia", "-amazon", "-amazon-pay", "-amilia", "-android", "-angellist", "-angrycreative", "-angular", "-app-store", "-app-store-ios", "-apper", "-apple", "-apple-pay", "-asymmetrik", "-audible", "-autoprefixer", "-avianex", "-aviato", "-aws", "-bandcamp", "-behance", "-behance-square", "-bimobject", "-bitbucket", "-bitcoin", "-bity", "-black-tie", "-blackberry", "-blogger", "-blogger-b", "-bluetooth", "-bluetooth-b", "-btc", "-buromobelexperte", "-buysellads", "-cc-amazon-pay", "-cc-amex", "-cc-apple-pay", "-cc-diners-club", "-cc-discover", "-cc-jcb", "-cc-mastercard", "-cc-paypal", "-cc-stripe", "-cc-visa", "-centercode", "-chrome", "-cloudscale", "-cloudsmith", "-cloudversify", "-codepen", "-codie<PERSON>", "-connectdevelop", "-contao", "-c<PERSON>el", "-creative-commons", "-css3", "-css3-alt", "-cuttlefish", "-d-and-d", "-dashcube", "-delicious", "-deploydog", "-deskpro", "-deviantart", "-digg", "-digital-ocean", "-discord", "-discourse", "-do<PERSON>b", "-docker", "-draft2digital", "-dribbble", "-dribbble-square", "-dropbox", "-drupal", "-dyalog", "-earlybirds", "-edge", "-elementor", "-ember", "-empire", "-envira", "-er<PERSON>", "-ethereum", "-etsy", "-expeditedssl", "-facebook", "-facebook-f", "-facebook-messenger", "-facebook-square", "-firefox", "-first-order", "-firstdraft", "-flickr", "-flipboard", "-fly", "-font-awesome", "-font-awesome-alt", "-font-awesome-flag", "-fonticons", "-fonticons-fi", "-fort-awesome", "-fort-awesome-alt", "-forumbee", "-foursquare", "-free-code-camp", "-freebsd", "-get-pocket", "-gg", "-gg-circle", "-git", "-git-square", "-gith<PERSON>", "-github-alt", "-github-square", "-gitkraken", "-git<PERSON>b", "-gitter", "-glide", "-glide-g", "-gofore", "-goodreads", "-goodreads-g", "-google", "-google-drive", "-google-play", "-google-plus", "-google-plus-g", "-google-plus-square", "-google-wallet", "-grat<PERSON><PERSON>", "-grav", "-gripfire", "-grunt", "-gulp", "-hacker-news", "-hacker-news-square", "-hips", "-hire-a-helper", "-hooli", "-hotjar", "-<PERSON><PERSON>z", "-html5", "-hubspot", "-imdb", "-instagram", "-internet-explorer", "-ioxhost", "-itunes", "-itunes-note", "-jenkins", "-joget", "-j<PERSON><PERSON>", "-js", "-js-square", "-jsfiddle", "-keycdn", "-kickstarter", "-kickstarter-k", "-kor<PERSON>", "-la<PERSON>l", "-lastfm", "-lastfm-square", "-lean<PERSON>b", "-less", "-line", "-linkedin", "-linkedin-in", "-linode", "-linux", "-lyft", "-magento", "-maxcdn", "-medapps", "-medium", "-medium-m", "-medrt", "-meetup", "-microsoft", "-mix", "-mixcloud", "-mi<PERSON><PERSON>", "-modx", "-monero", "-napster", "-nintendo-switch", "-node", "-node-js", "-npm", "-ns8", "-nutritionix", "-odnoklassniki", "-odnoklassniki-square", "-opencart", "-openid", "-opera", "-optin-monster", "-osi", "-page4", "-pagelines", "-palfed", "-patreon", "-paypal", "-periscope", "-phabricator", "-phoenix-framework", "-php", "-pied-piper", "-pied-piper-alt", "-pied-piper-pp", "-pinterest", "-pinterest-p", "-pinterest-square", "-playstation", "-product-hunt", "-pushed", "-python", "-qq", "-quinscape", "-quora", "-ravelry", "-react", "-rebel", "-red-river", "-reddit", "-reddit-alien", "-reddit-square", "-rendact", "-renren", "-replyd", "-resolving", "-rocketchat", "-rockrms", "-safari", "-sass", "-schlix", "-scribd", "-searchengin", "-sellcast", "-sellsy", "-servicestack", "-shirtsinbulk", "-simplybuilt", "-sistrix", "-skyatlas", "-skype", "-slack", "-slack-hash", "-slideshare", "-snapchat", "-snapchat-ghost", "-snapchat-square", "-soundcloud", "-speakap", "-spotify", "-stack-exchange", "-stack-overflow", "-staylinked", "-steam", "-steam-square", "-steam-symbol", "-sticker-mule", "-strava", "-stripe", "-stripe-s", "-<PERSON><PERSON><PERSON>", "-stumbleupon", "-stumbleupon-circle", "-superpowers", "-supple", "-telegram", "-telegram-plane", "-tencent-weibo", "-<PERSON><PERSON><PERSON>", "-trello", "-tripadvisor", "-tumblr", "-tumblr-square", "-twitch", "-twitter", "-twitter-square", "-typo3", "-uber", "-u<PERSON>t", "-uniregistry", "-untappd", "-usb", "-<PERSON><PERSON><PERSON>", "-vaadin", "-viacoin", "-viadeo", "-viadeo-square", "-viber", "-vimeo", "-vimeo-square", "-vimeo-v", "-vine", "-vk", "-vnv", "-v<PERSON><PERSON>s", "-we<PERSON>o", "-weixin", "-whatsapp", "-whatsapp-square", "-whmcs", "-wikipedia-w", "-windows", "-wordpress", "-wordpress-simple", "-wpbe<PERSON>ner", "-wpexplorer", "-wpforms", "-xbox", "-xing", "-xing-square", "-y-combinator", "-yahoo", "-yandex", "-yandex-international", "-yelp", "-yoast", "-youtube", "-youtube-square"]