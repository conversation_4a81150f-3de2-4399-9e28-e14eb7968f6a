(()=>{var Hs=Object.create;var ti=Object.defineProperty,Fs=Object.defineProperties,js=Object.getOwnPropertyDescriptor,Us=Object.getOwnPropertyDescriptors,Bs=Object.getOwnPropertyNames,gt=Object.getOwnPropertySymbols,Js=Object.getPrototypeOf,ii=Object.prototype.hasOwnProperty,pn=Object.prototype.propertyIsEnumerable;var fn=(e,t,i)=>t in e?ti(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,N=(e,t)=>{for(var i in t||(t={}))ii.call(t,i)&&fn(e,i,t[i]);if(gt)for(var i of gt(t))pn.call(t,i)&&fn(e,i,t[i]);return e},gn=(e,t)=>Fs(e,Us(t));var mn=(e,t)=>{var i={};for(var n in e)ii.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(e!=null&&gt)for(var n of gt(e))t.indexOf(n)<0&&pn.call(e,n)&&(i[n]=e[n]);return i};var Vs=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Ws=(e,t,i,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Bs(t))!ii.call(e,r)&&r!==i&&ti(e,r,{get:()=>t[r],enumerable:!(n=js(t,r))||n.enumerable});return e};var qs=(e,t,i)=>(i=e!=null?Hs(Js(e)):{},Ws(t||!e||!e.__esModule?ti(i,"default",{value:e,enumerable:!0}):i,e));var nr=Vs((ir,Pt)=>{(function(e,t){"use strict";(function(){for(var f=0,v=["ms","moz","webkit","o"],b=0;b<v.length&&!e.requestAnimationFrame;++b)e.requestAnimationFrame=e[v[b]+"RequestAnimationFrame"],e.cancelAnimationFrame=e[v[b]+"CancelAnimationFrame"]||e[v[b]+"CancelRequestAnimationFrame"];e.requestAnimationFrame||(e.requestAnimationFrame=function(m,w){var _=new Date().getTime(),x=Math.max(0,16-(_-f)),C=e.setTimeout(function(){m(_+x)},x);return f=_+x,C}),e.cancelAnimationFrame||(e.cancelAnimationFrame=function(m){clearTimeout(m)})})();var i,n,r,s=null,o=null,a=null,l=function(f,v,b){f.addEventListener?f.addEventListener(v,b,!1):f.attachEvent?f.attachEvent("on"+v,b):f["on"+v]=b},h={autoRun:!0,barThickness:3,barColors:{0:"rgba(26,  188, 156, .9)",".25":"rgba(52,  152, 219, .9)",".50":"rgba(241, 196, 15,  .9)",".75":"rgba(230, 126, 34,  .9)","1.0":"rgba(211, 84,  0,   .9)"},shadowBlur:10,shadowColor:"rgba(0,   0,   0,   .6)",className:null},c=function(){i.width=e.innerWidth,i.height=h.barThickness*5;var f=i.getContext("2d");f.shadowBlur=h.shadowBlur,f.shadowColor=h.shadowColor;var v=f.createLinearGradient(0,0,i.width,0);for(var b in h.barColors)v.addColorStop(b,h.barColors[b]);f.lineWidth=h.barThickness,f.beginPath(),f.moveTo(0,h.barThickness/2),f.lineTo(Math.ceil(n*i.width),h.barThickness/2),f.strokeStyle=v,f.stroke()},u=function(){i=t.createElement("canvas");var f=i.style;f.position="fixed",f.top=f.left=f.right=f.margin=f.padding=0,f.zIndex=100001,f.display="none",h.className&&i.classList.add(h.className),t.body.appendChild(i),l(e,"resize",c)},g={config:function(f){for(var v in f)h.hasOwnProperty(v)&&(h[v]=f[v])},show:function(f){if(!r)if(f){if(a)return;a=setTimeout(()=>g.show(),f)}else r=!0,o!==null&&e.cancelAnimationFrame(o),i||u(),i.style.opacity=1,i.style.display="block",g.progress(0),h.autoRun&&function v(){s=e.requestAnimationFrame(v),g.progress("+"+.05*Math.pow(1-Math.sqrt(n),2))}()},progress:function(f){return typeof f=="undefined"||(typeof f=="string"&&(f=(f.indexOf("+")>=0||f.indexOf("-")>=0?n:0)+parseFloat(f)),n=f>1?1:f,c()),n},hide:function(){clearTimeout(a),a=null,r&&(r=!1,s!=null&&(e.cancelAnimationFrame(s),s=null),function f(){if(g.progress("+.1")>=1&&(i.style.opacity-=.05,i.style.opacity<=.05)){i.style.display="none",o=null;return}o=e.requestAnimationFrame(f)}())}};typeof Pt=="object"&&typeof Pt.exports=="object"?Pt.exports=g:typeof define=="function"&&define.amd?define(function(){return g}):this.topbar=g}).call(ir,window,document)});(function(){var e=t();function t(){if(typeof window.CustomEvent=="function")return window.CustomEvent;function r(s,o){o=o||{bubbles:!1,cancelable:!1,detail:void 0};var a=document.createEvent("CustomEvent");return a.initCustomEvent(s,o.bubbles,o.cancelable,o.detail),a}return r.prototype=window.Event.prototype,r}function i(r,s){var o=document.createElement("input");return o.type="hidden",o.name=r,o.value=s,o}function n(r,s){var o=r.getAttribute("data-to"),a=i("_method",r.getAttribute("data-method")),l=i("_csrf_token",r.getAttribute("data-csrf")),h=document.createElement("form"),c=document.createElement("input"),u=r.getAttribute("target");h.method=r.getAttribute("data-method")==="get"?"get":"post",h.action=o,h.style.display="none",u?h.target=u:s&&(h.target="_blank"),h.appendChild(l),h.appendChild(a),document.body.appendChild(h),c.type="submit",h.appendChild(c),c.click()}window.addEventListener("click",function(r){var s=r.target;if(!r.defaultPrevented)for(;s&&s.getAttribute;){var o=new e("phoenix.link.click",{bubbles:!0,cancelable:!0});if(!s.dispatchEvent(o))return r.preventDefault(),r.stopImmediatePropagation(),!1;if(s.getAttribute("data-method")&&s.getAttribute("data-to"))return n(s,r.metaKey||r.shiftKey),r.preventDefault(),!1;s=s.parentNode}},!1),window.addEventListener("phoenix.link.click",function(r){var s=r.target.getAttribute("data-confirm");s&&!window.confirm(s)&&r.preventDefault()},!1)})();var Je=e=>typeof e=="function"?e:function(){return e},Ks=typeof self!="undefined"?self:null,Be=typeof window!="undefined"?window:null,be=Ks||Be||be,zs="2.0.0",X={connecting:0,open:1,closing:2,closed:3},Xs=1e4,Gs=1e3,U={closed:"closed",errored:"errored",joined:"joined",joining:"joining",leaving:"leaving"},ne={close:"phx_close",error:"phx_error",join:"phx_join",reply:"phx_reply",leave:"phx_leave"},ni={longpoll:"longpoll",websocket:"websocket"},Ys={complete:4},mt=class{constructor(e,t,i,n){this.channel=e,this.event=t,this.payload=i||function(){return{}},this.receivedResp=null,this.timeout=n,this.timeoutTimer=null,this.recHooks=[],this.sent=!1}resend(e){this.timeout=e,this.reset(),this.send()}send(){this.hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload(),ref:this.ref,join_ref:this.channel.joinRef()}))}receive(e,t){return this.hasReceived(e)&&t(this.receivedResp.response),this.recHooks.push({status:e,callback:t}),this}reset(){this.cancelRefEvent(),this.ref=null,this.refEvent=null,this.receivedResp=null,this.sent=!1}matchReceive({status:e,response:t,_ref:i}){this.recHooks.filter(n=>n.status===e).forEach(n=>n.callback(t))}cancelRefEvent(){this.refEvent&&this.channel.off(this.refEvent)}cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=null}startTimeout(){this.timeoutTimer&&this.cancelTimeout(),this.ref=this.channel.socket.makeRef(),this.refEvent=this.channel.replyEventName(this.ref),this.channel.on(this.refEvent,e=>{this.cancelRefEvent(),this.cancelTimeout(),this.receivedResp=e,this.matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}trigger(e,t){this.channel.trigger(this.refEvent,{status:e,response:t})}},vn=class{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=null,this.tries=0}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}},Zs=class{constructor(e,t,i){this.state=U.closed,this.topic=e,this.params=Je(t||{}),this.socket=i,this.bindings=[],this.bindingRef=0,this.timeout=this.socket.timeout,this.joinedOnce=!1,this.joinPush=new mt(this,ne.join,this.params,this.timeout),this.pushBuffer=[],this.stateChangeRefs=[],this.rejoinTimer=new vn(()=>{this.socket.isConnected()&&this.rejoin()},this.socket.rejoinAfterMs),this.stateChangeRefs.push(this.socket.onError(()=>this.rejoinTimer.reset())),this.stateChangeRefs.push(this.socket.onOpen(()=>{this.rejoinTimer.reset(),this.isErrored()&&this.rejoin()})),this.joinPush.receive("ok",()=>{this.state=U.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(n=>n.send()),this.pushBuffer=[]}),this.joinPush.receive("error",()=>{this.state=U.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.onClose(()=>{this.rejoinTimer.reset(),this.socket.hasLogger()&&this.socket.log("channel",`close ${this.topic} ${this.joinRef()}`),this.state=U.closed,this.socket.remove(this)}),this.onError(n=>{this.socket.hasLogger()&&this.socket.log("channel",`error ${this.topic}`,n),this.isJoining()&&this.joinPush.reset(),this.state=U.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.joinPush.receive("timeout",()=>{this.socket.hasLogger()&&this.socket.log("channel",`timeout ${this.topic} (${this.joinRef()})`,this.joinPush.timeout),new mt(this,ne.leave,Je({}),this.timeout).send(),this.state=U.errored,this.joinPush.reset(),this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.on(ne.reply,(n,r)=>{this.trigger(this.replyEventName(r),n)})}join(e=this.timeout){if(this.joinedOnce)throw new Error("tried to join multiple times. 'join' can only be called a single time per channel instance");return this.timeout=e,this.joinedOnce=!0,this.rejoin(),this.joinPush}onClose(e){this.on(ne.close,e)}onError(e){return this.on(ne.error,t=>e(t))}on(e,t){let i=this.bindingRef++;return this.bindings.push({event:e,ref:i,callback:t}),i}off(e,t){this.bindings=this.bindings.filter(i=>!(i.event===e&&(typeof t=="undefined"||t===i.ref)))}canPush(){return this.socket.isConnected()&&this.isJoined()}push(e,t,i=this.timeout){if(t=t||{},!this.joinedOnce)throw new Error(`tried to push '${e}' to '${this.topic}' before joining. Use channel.join() before pushing events`);let n=new mt(this,e,function(){return t},i);return this.canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}leave(e=this.timeout){this.rejoinTimer.reset(),this.joinPush.cancelTimeout(),this.state=U.leaving;let t=()=>{this.socket.hasLogger()&&this.socket.log("channel",`leave ${this.topic}`),this.trigger(ne.close,"leave")},i=new mt(this,ne.leave,Je({}),e);return i.receive("ok",()=>t()).receive("timeout",()=>t()),i.send(),this.canPush()||i.trigger("ok",{}),i}onMessage(e,t,i){return t}isMember(e,t,i,n){return this.topic!==e?!1:n&&n!==this.joinRef()?(this.socket.hasLogger()&&this.socket.log("channel","dropping outdated message",{topic:e,event:t,payload:i,joinRef:n}),!1):!0}joinRef(){return this.joinPush.ref}rejoin(e=this.timeout){this.isLeaving()||(this.socket.leaveOpenTopic(this.topic),this.state=U.joining,this.joinPush.resend(e))}trigger(e,t,i,n){let r=this.onMessage(e,t,i,n);if(t&&!r)throw new Error("channel onMessage callbacks must return the payload, modified or unmodified");let s=this.bindings.filter(o=>o.event===e);for(let o=0;o<s.length;o++)s[o].callback(r,i,n||this.joinRef())}replyEventName(e){return`chan_reply_${e}`}isClosed(){return this.state===U.closed}isErrored(){return this.state===U.errored}isJoined(){return this.state===U.joined}isJoining(){return this.state===U.joining}isLeaving(){return this.state===U.leaving}},bt=class{static request(e,t,i,n,r,s,o){if(be.XDomainRequest){let a=new be.XDomainRequest;return this.xdomainRequest(a,e,t,n,r,s,o)}else{let a=new be.XMLHttpRequest;return this.xhrRequest(a,e,t,i,n,r,s,o)}}static xdomainRequest(e,t,i,n,r,s,o){return e.timeout=r,e.open(t,i),e.onload=()=>{let a=this.parseJSON(e.responseText);o&&o(a)},s&&(e.ontimeout=s),e.onprogress=()=>{},e.send(n),e}static xhrRequest(e,t,i,n,r,s,o,a){return e.open(t,i,!0),e.timeout=s,e.setRequestHeader("Content-Type",n),e.onerror=()=>a&&a(null),e.onreadystatechange=()=>{if(e.readyState===Ys.complete&&a){let l=this.parseJSON(e.responseText);a(l)}},o&&(e.ontimeout=o),e.send(r),e}static parseJSON(e){if(!e||e==="")return null;try{return JSON.parse(e)}catch(t){return console&&console.log("failed to parse JSON response",e),null}}static serialize(e,t){let i=[];for(var n in e){if(!Object.prototype.hasOwnProperty.call(e,n))continue;let r=t?`${t}[${n}]`:n,s=e[n];typeof s=="object"?i.push(this.serialize(s,r)):i.push(encodeURIComponent(r)+"="+encodeURIComponent(s))}return i.join("&")}static appendParams(e,t){if(Object.keys(t).length===0)return e;let i=e.match(/\?/)?"&":"?";return`${e}${i}${this.serialize(t)}`}},Qs=e=>{let t="",i=new Uint8Array(e),n=i.byteLength;for(let r=0;r<n;r++)t+=String.fromCharCode(i[r]);return btoa(t)},Ue=class{constructor(e){this.endPoint=null,this.token=null,this.skipHeartbeat=!0,this.reqs=new Set,this.awaitingBatchAck=!1,this.currentBatch=null,this.currentBatchTimer=null,this.batchBuffer=[],this.onopen=function(){},this.onerror=function(){},this.onmessage=function(){},this.onclose=function(){},this.pollEndpoint=this.normalizeEndpoint(e),this.readyState=X.connecting,setTimeout(()=>this.poll(),0)}normalizeEndpoint(e){return e.replace("ws://","http://").replace("wss://","https://").replace(new RegExp("(.*)/"+ni.websocket),"$1/"+ni.longpoll)}endpointURL(){return bt.appendParams(this.pollEndpoint,{token:this.token})}closeAndRetry(e,t,i){this.close(e,t,i),this.readyState=X.connecting}ontimeout(){this.onerror("timeout"),this.closeAndRetry(1005,"timeout",!1)}isActive(){return this.readyState===X.open||this.readyState===X.connecting}poll(){this.ajax("GET","application/json",null,()=>this.ontimeout(),e=>{if(e){var{status:t,token:i,messages:n}=e;this.token=i}else t=0;switch(t){case 200:n.forEach(r=>{setTimeout(()=>this.onmessage({data:r}),0)}),this.poll();break;case 204:this.poll();break;case 410:this.readyState=X.open,this.onopen({}),this.poll();break;case 403:this.onerror(403),this.close(1008,"forbidden",!1);break;case 0:case 500:this.onerror(500),this.closeAndRetry(1011,"internal server error",500);break;default:throw new Error(`unhandled poll status ${t}`)}})}send(e){typeof e!="string"&&(e=Qs(e)),this.currentBatch?this.currentBatch.push(e):this.awaitingBatchAck?this.batchBuffer.push(e):(this.currentBatch=[e],this.currentBatchTimer=setTimeout(()=>{this.batchSend(this.currentBatch),this.currentBatch=null},0))}batchSend(e){this.awaitingBatchAck=!0,this.ajax("POST","application/x-ndjson",e.join(`
`),()=>this.onerror("timeout"),t=>{this.awaitingBatchAck=!1,!t||t.status!==200?(this.onerror(t&&t.status),this.closeAndRetry(1011,"internal server error",!1)):this.batchBuffer.length>0&&(this.batchSend(this.batchBuffer),this.batchBuffer=[])})}close(e,t,i){for(let r of this.reqs)r.abort();this.readyState=X.closed;let n=Object.assign({code:1e3,reason:void 0,wasClean:!0},{code:e,reason:t,wasClean:i});this.batchBuffer=[],clearTimeout(this.currentBatchTimer),this.currentBatchTimer=null,typeof CloseEvent!="undefined"?this.onclose(new CloseEvent("close",n)):this.onclose(n)}ajax(e,t,i,n,r){let s,o=()=>{this.reqs.delete(s),n()};s=bt.request(e,this.endpointURL(),t,i,this.timeout,o,a=>{this.reqs.delete(s),this.isActive()&&r(a)}),this.reqs.add(s)}};var vt={HEADER_LENGTH:1,META_LENGTH:4,KINDS:{push:0,reply:1,broadcast:2},encode(e,t){if(e.payload.constructor===ArrayBuffer)return t(this.binaryEncode(e));{let i=[e.join_ref,e.ref,e.topic,e.event,e.payload];return t(JSON.stringify(i))}},decode(e,t){if(e.constructor===ArrayBuffer)return t(this.binaryDecode(e));{let[i,n,r,s,o]=JSON.parse(e);return t({join_ref:i,ref:n,topic:r,event:s,payload:o})}},binaryEncode(e){let{join_ref:t,ref:i,event:n,topic:r,payload:s}=e,o=this.META_LENGTH+t.length+i.length+r.length+n.length,a=new ArrayBuffer(this.HEADER_LENGTH+o),l=new DataView(a),h=0;l.setUint8(h++,this.KINDS.push),l.setUint8(h++,t.length),l.setUint8(h++,i.length),l.setUint8(h++,r.length),l.setUint8(h++,n.length),Array.from(t,u=>l.setUint8(h++,u.charCodeAt(0))),Array.from(i,u=>l.setUint8(h++,u.charCodeAt(0))),Array.from(r,u=>l.setUint8(h++,u.charCodeAt(0))),Array.from(n,u=>l.setUint8(h++,u.charCodeAt(0)));var c=new Uint8Array(a.byteLength+s.byteLength);return c.set(new Uint8Array(a),0),c.set(new Uint8Array(s),a.byteLength),c.buffer},binaryDecode(e){let t=new DataView(e),i=t.getUint8(0),n=new TextDecoder;switch(i){case this.KINDS.push:return this.decodePush(e,t,n);case this.KINDS.reply:return this.decodeReply(e,t,n);case this.KINDS.broadcast:return this.decodeBroadcast(e,t,n)}},decodePush(e,t,i){let n=t.getUint8(1),r=t.getUint8(2),s=t.getUint8(3),o=this.HEADER_LENGTH+this.META_LENGTH-1,a=i.decode(e.slice(o,o+n));o=o+n;let l=i.decode(e.slice(o,o+r));o=o+r;let h=i.decode(e.slice(o,o+s));o=o+s;let c=e.slice(o,e.byteLength);return{join_ref:a,ref:null,topic:l,event:h,payload:c}},decodeReply(e,t,i){let n=t.getUint8(1),r=t.getUint8(2),s=t.getUint8(3),o=t.getUint8(4),a=this.HEADER_LENGTH+this.META_LENGTH,l=i.decode(e.slice(a,a+n));a=a+n;let h=i.decode(e.slice(a,a+r));a=a+r;let c=i.decode(e.slice(a,a+s));a=a+s;let u=i.decode(e.slice(a,a+o));a=a+o;let g=e.slice(a,e.byteLength),f={status:u,response:g};return{join_ref:l,ref:h,topic:c,event:ne.reply,payload:f}},decodeBroadcast(e,t,i){let n=t.getUint8(1),r=t.getUint8(2),s=this.HEADER_LENGTH+2,o=i.decode(e.slice(s,s+n));s=s+n;let a=i.decode(e.slice(s,s+r));s=s+r;let l=e.slice(s,e.byteLength);return{join_ref:null,ref:null,topic:o,event:a,payload:l}}},ri=class{constructor(e,t={}){this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.channels=[],this.sendBuffer=[],this.ref=0,this.timeout=t.timeout||Xs,this.transport=t.transport||be.WebSocket||Ue,this.primaryPassedHealthCheck=!1,this.longPollFallbackMs=t.longPollFallbackMs,this.fallbackTimer=null,this.sessionStore=t.sessionStorage||be&&be.sessionStorage,this.establishedConnections=0,this.defaultEncoder=vt.encode.bind(vt),this.defaultDecoder=vt.decode.bind(vt),this.closeWasClean=!1,this.binaryType=t.binaryType||"arraybuffer",this.connectClock=1,this.transport!==Ue?(this.encode=t.encode||this.defaultEncoder,this.decode=t.decode||this.defaultDecoder):(this.encode=this.defaultEncoder,this.decode=this.defaultDecoder);let i=null;Be&&Be.addEventListener&&(Be.addEventListener("pagehide",n=>{this.conn&&(this.disconnect(),i=this.connectClock)}),Be.addEventListener("pageshow",n=>{i===this.connectClock&&(i=null,this.connect())})),this.heartbeatIntervalMs=t.heartbeatIntervalMs||3e4,this.rejoinAfterMs=n=>t.rejoinAfterMs?t.rejoinAfterMs(n):[1e3,2e3,5e3][n-1]||1e4,this.reconnectAfterMs=n=>t.reconnectAfterMs?t.reconnectAfterMs(n):[10,50,100,150,200,250,500,1e3,2e3][n-1]||5e3,this.logger=t.logger||null,!this.logger&&t.debug&&(this.logger=(n,r,s)=>{console.log(`${n}: ${r}`,s)}),this.longpollerTimeout=t.longpollerTimeout||2e4,this.params=Je(t.params||{}),this.endPoint=`${e}/${ni.websocket}`,this.vsn=t.vsn||zs,this.heartbeatTimeoutTimer=null,this.heartbeatTimer=null,this.pendingHeartbeatRef=null,this.reconnectTimer=new vn(()=>{this.teardown(()=>this.connect())},this.reconnectAfterMs)}getLongPollTransport(){return Ue}replaceTransport(e){this.connectClock++,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.conn&&(this.conn.close(),this.conn=null),this.transport=e}protocol(){return location.protocol.match(/^https/)?"wss":"ws"}endPointURL(){let e=bt.appendParams(bt.appendParams(this.endPoint,this.params()),{vsn:this.vsn});return e.charAt(0)!=="/"?e:e.charAt(1)==="/"?`${this.protocol()}:${e}`:`${this.protocol()}://${location.host}${e}`}disconnect(e,t,i){this.connectClock++,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.teardown(e,t,i)}connect(e){e&&(console&&console.log("passing params to connect is deprecated. Instead pass :params to the Socket constructor"),this.params=Je(e)),!this.conn&&(this.longPollFallbackMs&&this.transport!==Ue?this.connectWithFallback(Ue,this.longPollFallbackMs):this.transportConnect())}log(e,t,i){this.logger&&this.logger(e,t,i)}hasLogger(){return this.logger!==null}onOpen(e){let t=this.makeRef();return this.stateChangeCallbacks.open.push([t,e]),t}onClose(e){let t=this.makeRef();return this.stateChangeCallbacks.close.push([t,e]),t}onError(e){let t=this.makeRef();return this.stateChangeCallbacks.error.push([t,e]),t}onMessage(e){let t=this.makeRef();return this.stateChangeCallbacks.message.push([t,e]),t}ping(e){if(!this.isConnected())return!1;let t=this.makeRef(),i=Date.now();this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:t});let n=this.onMessage(r=>{r.ref===t&&(this.off([n]),e(Date.now()-i))});return!0}transportConnect(){this.connectClock++,this.closeWasClean=!1,this.conn=new this.transport(this.endPointURL()),this.conn.binaryType=this.binaryType,this.conn.timeout=this.longpollerTimeout,this.conn.onopen=()=>this.onConnOpen(),this.conn.onerror=e=>this.onConnError(e),this.conn.onmessage=e=>this.onConnMessage(e),this.conn.onclose=e=>this.onConnClose(e)}getSession(e){return this.sessionStore&&this.sessionStore.getItem(e)}storeSession(e,t){this.sessionStore&&this.sessionStore.setItem(e,t)}connectWithFallback(e,t=2500){clearTimeout(this.fallbackTimer);let i=!1,n=!0,r,s,o=a=>{this.log("transport",`falling back to ${e.name}...`,a),this.off([r,s]),n=!1,this.replaceTransport(e),this.transportConnect()};if(this.getSession(`phx:fallback:${e.name}`))return o("memorized");this.fallbackTimer=setTimeout(o,t),s=this.onError(a=>{this.log("transport","error",a),n&&!i&&(clearTimeout(this.fallbackTimer),o(a))}),this.onOpen(()=>{if(i=!0,!n)return this.primaryPassedHealthCheck||this.storeSession(`phx:fallback:${e.name}`,"true"),this.log("transport",`established ${e.name} fallback`);clearTimeout(this.fallbackTimer),this.fallbackTimer=setTimeout(o,t),this.ping(a=>{this.log("transport","connected to primary after",a),this.primaryPassedHealthCheck=!0,clearTimeout(this.fallbackTimer)})}),this.transportConnect()}clearHeartbeats(){clearTimeout(this.heartbeatTimer),clearTimeout(this.heartbeatTimeoutTimer)}onConnOpen(){this.hasLogger()&&this.log("transport",`${this.transport.name} connected to ${this.endPointURL()}`),this.closeWasClean=!1,this.establishedConnections++,this.flushSendBuffer(),this.reconnectTimer.reset(),this.resetHeartbeat(),this.stateChangeCallbacks.open.forEach(([,e])=>e())}heartbeatTimeout(){this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null,this.hasLogger()&&this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.triggerChanError(),this.closeWasClean=!1,this.teardown(()=>this.reconnectTimer.scheduleTimeout(),Gs,"heartbeat timeout"))}resetHeartbeat(){this.conn&&this.conn.skipHeartbeat||(this.pendingHeartbeatRef=null,this.clearHeartbeats(),this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs))}teardown(e,t,i){if(!this.conn)return e&&e();this.waitForBufferDone(()=>{this.conn&&(t?this.conn.close(t,i||""):this.conn.close()),this.waitForSocketClosed(()=>{this.conn&&(this.conn.onopen=function(){},this.conn.onerror=function(){},this.conn.onmessage=function(){},this.conn.onclose=function(){},this.conn=null),e&&e()})})}waitForBufferDone(e,t=1){if(t===5||!this.conn||!this.conn.bufferedAmount){e();return}setTimeout(()=>{this.waitForBufferDone(e,t+1)},150*t)}waitForSocketClosed(e,t=1){if(t===5||!this.conn||this.conn.readyState===X.closed){e();return}setTimeout(()=>{this.waitForSocketClosed(e,t+1)},150*t)}onConnClose(e){let t=e&&e.code;this.hasLogger()&&this.log("transport","close",e),this.triggerChanError(),this.clearHeartbeats(),!this.closeWasClean&&t!==1e3&&this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(([,i])=>i(e))}onConnError(e){this.hasLogger()&&this.log("transport",e);let t=this.transport,i=this.establishedConnections;this.stateChangeCallbacks.error.forEach(([,n])=>{n(e,t,i)}),(t===this.transport||i>0)&&this.triggerChanError()}triggerChanError(){this.channels.forEach(e=>{e.isErrored()||e.isLeaving()||e.isClosed()||e.trigger(ne.error)})}connectionState(){switch(this.conn&&this.conn.readyState){case X.connecting:return"connecting";case X.open:return"open";case X.closing:return"closing";default:return"closed"}}isConnected(){return this.connectionState()==="open"}remove(e){this.off(e.stateChangeRefs),this.channels=this.channels.filter(t=>t!==e)}off(e){for(let t in this.stateChangeCallbacks)this.stateChangeCallbacks[t]=this.stateChangeCallbacks[t].filter(([i])=>e.indexOf(i)===-1)}channel(e,t={}){let i=new Zs(e,t,this);return this.channels.push(i),i}push(e){if(this.hasLogger()){let{topic:t,event:i,payload:n,ref:r,join_ref:s}=e;this.log("push",`${t} ${i} (${s}, ${r})`,n)}this.isConnected()?this.encode(e,t=>this.conn.send(t)):this.sendBuffer.push(()=>this.encode(e,t=>this.conn.send(t)))}makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}sendHeartbeat(){this.pendingHeartbeatRef&&!this.isConnected()||(this.pendingHeartbeatRef=this.makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatTimeoutTimer=setTimeout(()=>this.heartbeatTimeout(),this.heartbeatIntervalMs))}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}onConnMessage(e){this.decode(e.data,t=>{let{topic:i,event:n,payload:r,ref:s,join_ref:o}=t;s&&s===this.pendingHeartbeatRef&&(this.clearHeartbeats(),this.pendingHeartbeatRef=null,this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)),this.hasLogger()&&this.log("receive",`${r.status||""} ${i} ${n} ${s&&"("+s+")"||""}`,r);for(let a=0;a<this.channels.length;a++){let l=this.channels[a];l.isMember(i,n,r,o)&&l.trigger(n,r,s,o)}for(let a=0;a<this.stateChangeCallbacks.message.length;a++){let[,l]=this.stateChangeCallbacks.message[a];l(t)}})}leaveOpenTopic(e){let t=this.channels.find(i=>i.topic===e&&(i.isJoined()||i.isJoining()));t&&(this.hasLogger()&&this.log("transport",`leaving duplicate topic "${e}"`),t.leave())}};var Wn="consecutive-reloads",eo=10,to=5e3,io=1e4,no=3e4,qn=["phx-click-loading","phx-change-loading","phx-submit-loading","phx-keydown-loading","phx-keyup-loading","phx-blur-loading","phx-focus-loading","phx-hook-loading"],se="data-phx-component",si="data-phx-link",ro="track-static",so="data-phx-link-state",Le="data-phx-ref-loading",q="data-phx-ref-src",J="data-phx-ref-lock",Kn="track-uploads",oe="data-phx-upload-ref",bi="data-phx-preflighted-refs",oo="data-phx-done-refs",bn="drop-target",fi="data-phx-active-refs",Ct="phx:live-file:updated",zn="data-phx-skip",Xn="data-phx-id",_n="data-phx-prune",pi="page-loading",yn="phx-connected",Ve="phx-loading",_t="phx-error",wn="phx-client-error",oi="phx-server-error",we="data-phx-parent-id",_i="data-phx-main",de="data-phx-root-id",gi="viewport-top",mi="viewport-bottom",ao="trigger-action",Tt="phx-has-focused",lo=["text","textarea","number","email","password","search","tel","url","date","time","datetime-local","color","range"],Gn=["checkbox","radio"],et="phx-has-submitted",Q="data-phx-session",Me=`[${Q}]`,An="data-phx-sticky",ye="data-phx-static",vi="data-phx-readonly",Ie="data-phx-disabled",Sn="disable-with",Rt="data-phx-disable-with-restore",We="hook",ho="debounce",co="throttle",Ze="update",ai="stream",li="data-phx-stream",uo="key",G="phxPrivate",xn="auto-recover",yt="phx:live-socket:debug",hi="phx:live-socket:profiling",ci="phx:live-socket:latency-sim",fo="progress",En="mounted",po=1,go=200,mo="phx-",vo=3e4,qe="debounce-trigger",Ke="throttled",Cn="debounce-prev-key",bo={debounce:300,throttle:300},kn=[Le,q,J,pi],wt="d",Y="s",ui="r",$="c",Tn="e",Rn="r",Pn="t",_o="p",On="stream",yo=class{constructor(e,t,i){this.liveSocket=i,this.entry=e,this.offset=0,this.chunkSize=t,this.chunkTimer=null,this.errored=!1,this.uploadChannel=i.channel(`lvu:${e.ref}`,{token:e.metadata()})}error(e){this.errored||(this.uploadChannel.leave(),this.errored=!0,clearTimeout(this.chunkTimer),this.entry.error(e))}upload(){this.uploadChannel.onError(e=>this.error(e)),this.uploadChannel.join().receive("ok",e=>this.readNextChunk()).receive("error",e=>this.error(e))}isDone(){return this.offset>=this.entry.file.size}readNextChunk(){let e=new window.FileReader,t=this.entry.file.slice(this.offset,this.chunkSize+this.offset);e.onload=i=>{if(i.target.error===null)this.offset+=i.target.result.byteLength,this.pushChunk(i.target.result);else return B("Read error: "+i.target.error)},e.readAsArrayBuffer(t)}pushChunk(e){this.uploadChannel.isJoined()&&this.uploadChannel.push("chunk",e).receive("ok",()=>{this.entry.progress(this.offset/this.entry.file.size*100),this.isDone()||(this.chunkTimer=setTimeout(()=>this.readNextChunk(),this.liveSocket.getLatencySim()||0))}).receive("error",({reason:t})=>this.error(t))}},B=(e,t)=>console.error&&console.error(e,t),re=e=>{let t=typeof e;return t==="number"||t==="string"&&/^(0|[1-9]\d*)$/.test(e)};function wo(){let e=new Set,t=document.querySelectorAll("*[id]");for(let i=0,n=t.length;i<n;i++)e.has(t[i].id)?console.error(`Multiple IDs detected: ${t[i].id}. Ensure unique element ids.`):e.add(t[i].id)}var Ao=(e,t,i,n)=>{e.liveSocket.isDebugEnabled()&&console.log(`${e.id} ${t}: ${i} - `,n)},ze=e=>typeof e=="function"?e:function(){return e},kt=e=>JSON.parse(JSON.stringify(e)),Qe=(e,t,i)=>{do{if(e.matches(`[${t}]`)&&!e.disabled)return e;e=e.parentElement||e.parentNode}while(e!==null&&e.nodeType===1&&!(i&&i.isSameNode(e)||e.matches(Me)));return null},Oe=e=>e!==null&&typeof e=="object"&&!(e instanceof Array),So=(e,t)=>JSON.stringify(e)===JSON.stringify(t),In=e=>{for(let t in e)return!1;return!0},ue=(e,t)=>e&&t(e),xo=function(e,t,i,n){e.forEach(r=>{new yo(r,i.config.chunk_size,n).upload()})},Yn={canPushState(){return typeof history.pushState!="undefined"},dropLocal(e,t,i){return e.removeItem(this.localKey(t,i))},updateLocal(e,t,i,n,r){let s=this.getLocal(e,t,i),o=this.localKey(t,i),a=s===null?n:r(s);return e.setItem(o,JSON.stringify(a)),a},getLocal(e,t,i){return JSON.parse(e.getItem(this.localKey(t,i)))},updateCurrentState(e){this.canPushState()&&history.replaceState(e(history.state||{}),"",window.location.href)},pushState(e,t,i){if(this.canPushState()){if(i!==window.location.href){if(t.type=="redirect"&&t.scroll){let r=history.state||{};r.scroll=t.scroll,history.replaceState(r,"",window.location.href)}delete t.scroll,history[e+"State"](t,"",i||null);let n=this.getHashTargetEl(window.location.hash);n?n.scrollIntoView():t.type==="redirect"&&window.scroll(0,0)}}else this.redirect(i)},setCookie(e,t){document.cookie=`${e}=${t}`},getCookie(e){return document.cookie.replace(new RegExp(`(?:(?:^|.*;s*)${e}s*=s*([^;]*).*$)|^.*$`),"$1")},redirect(e,t){t&&Yn.setCookie("__phoenix_flash__",t+"; max-age=60000; path=/"),window.location=e},localKey(e,t){return`${e}-${t}`},getHashTargetEl(e){let t=e.toString().substring(1);if(t!=="")return document.getElementById(t)||document.querySelector(`a[name="${t}"]`)}},Z=Yn,Eo={focusMain(){let e=document.querySelector("main h1, main, h1");if(e){let t=e.tabIndex;e.tabIndex=-1,e.focus(),e.tabIndex=t}},anyOf(e,t){return t.find(i=>e instanceof i)},isFocusable(e,t){return e instanceof HTMLAnchorElement&&e.rel!=="ignore"||e instanceof HTMLAreaElement&&e.href!==void 0||!e.disabled&&this.anyOf(e,[HTMLInputElement,HTMLSelectElement,HTMLTextAreaElement,HTMLButtonElement])||e instanceof HTMLIFrameElement||e.tabIndex>0||!t&&e.getAttribute("tabindex")!==null&&e.getAttribute("aria-hidden")!=="true"},attemptFocus(e,t){if(this.isFocusable(e,t))try{e.focus()}catch(i){}return!!document.activeElement&&document.activeElement.isSameNode(e)},focusFirstInteractive(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t,!0)||this.focusFirstInteractive(t,!0))return!0;t=t.nextElementSibling}},focusFirst(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t)||this.focusFirst(t))return!0;t=t.nextElementSibling}},focusLast(e){let t=e.lastElementChild;for(;t;){if(this.attemptFocus(t)||this.focusLast(t))return!0;t=t.previousElementSibling}}},De=Eo,Ln=[],Dn=200,Co={exec(e,t,i,n,r){let[s,o]=r||[null,{callback:r&&r.callback}];(t.charAt(0)==="["?JSON.parse(t):[[s,o]]).forEach(([l,h])=>{l===s&&o.data&&(h.data=Object.assign(h.data||{},o.data),h.callback=h.callback||o.callback),this.filterToEls(n,h).forEach(c=>{this[`exec_${l}`](e,t,i,n,c,h)})})},isVisible(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length>0)},isInViewport(e){let t=e.getBoundingClientRect(),i=window.innerHeight||document.documentElement.clientHeight,n=window.innerWidth||document.documentElement.clientWidth;return t.right>0&&t.bottom>0&&t.left<n&&t.top<i},exec_exec(e,t,i,n,r,{attr:s,to:o}){(o?d.all(document,o):[n]).forEach(l=>{let h=l.getAttribute(s);if(!h)throw new Error(`expected ${s} to contain JS command on "${o}"`);i.liveSocket.execJS(l,h,e)})},exec_dispatch(e,t,i,n,r,{to:s,event:o,detail:a,bubbles:l}){a=a||{},a.dispatcher=n,d.dispatchEvent(r,o,{detail:a,bubbles:l})},exec_push(e,t,i,n,r,s){let{event:o,data:a,target:l,page_loading:h,loading:c,value:u,dispatcher:g,callback:f}=s,v={loading:c,value:u,target:l,page_loading:!!h},b=e==="change"&&g?g:n,m=l||b.getAttribute(i.binding("target"))||b;i.withinTargets(m,(w,_)=>{if(w.isConnected())if(e==="change"){let{newCid:x,_target:C}=s;C=C||(d.isFormInput(n)?n.name:void 0),C&&(v._target=C),w.pushInput(n,_,x,o||t,v,f)}else if(e==="submit"){let{submitter:x}=s;w.submitForm(n,_,o||t,x,v,f)}else w.pushEvent(e,n,_,o||t,a,v,f)})},exec_navigate(e,t,i,n,r,{href:s,replace:o}){i.liveSocket.historyRedirect(s,o?"replace":"push")},exec_patch(e,t,i,n,r,{href:s,replace:o}){i.liveSocket.pushHistoryPatch(s,o?"replace":"push",n)},exec_focus(e,t,i,n,r){window.requestAnimationFrame(()=>De.attemptFocus(r))},exec_focus_first(e,t,i,n,r){window.requestAnimationFrame(()=>De.focusFirstInteractive(r)||De.focusFirst(r))},exec_push_focus(e,t,i,n,r){window.requestAnimationFrame(()=>Ln.push(r||n))},exec_pop_focus(e,t,i,n,r){window.requestAnimationFrame(()=>{let s=Ln.pop();s&&s.focus()})},exec_add_class(e,t,i,n,r,{names:s,transition:o,time:a,blocking:l}){this.addOrRemoveClasses(r,s,[],o,a,i,l)},exec_remove_class(e,t,i,n,r,{names:s,transition:o,time:a,blocking:l}){this.addOrRemoveClasses(r,[],s,o,a,i,l)},exec_toggle_class(e,t,i,n,r,{to:s,names:o,transition:a,time:l,blocking:h}){this.toggleClasses(r,o,a,l,i,h)},exec_toggle_attr(e,t,i,n,r,{attr:[s,o,a]}){r.hasAttribute(s)?a!==void 0?r.getAttribute(s)===o?this.setOrRemoveAttrs(r,[[s,a]],[]):this.setOrRemoveAttrs(r,[[s,o]],[]):this.setOrRemoveAttrs(r,[],[s]):this.setOrRemoveAttrs(r,[[s,o]],[])},exec_transition(e,t,i,n,r,{time:s,transition:o,blocking:a}){this.addOrRemoveClasses(r,[],[],o,s,i,a)},exec_toggle(e,t,i,n,r,{display:s,ins:o,outs:a,time:l,blocking:h}){this.toggle(e,i,r,s,o,a,l,h)},exec_show(e,t,i,n,r,{display:s,transition:o,time:a,blocking:l}){this.show(e,i,r,s,o,a,l)},exec_hide(e,t,i,n,r,{display:s,transition:o,time:a,blocking:l}){this.hide(e,i,r,s,o,a,l)},exec_set_attr(e,t,i,n,r,{attr:[s,o]}){this.setOrRemoveAttrs(r,[[s,o]],[])},exec_remove_attr(e,t,i,n,r,{attr:s}){this.setOrRemoveAttrs(r,[],[s])},show(e,t,i,n,r,s,o){this.isVisible(i)||this.toggle(e,t,i,n,r,null,s,o)},hide(e,t,i,n,r,s,o){this.isVisible(i)&&this.toggle(e,t,i,n,null,r,s,o)},toggle(e,t,i,n,r,s,o,a){o=o||Dn;let[l,h,c]=r||[[],[],[]],[u,g,f]=s||[[],[],[]];if(l.length>0||u.length>0)if(this.isVisible(i)){let v=()=>{this.addOrRemoveClasses(i,g,l.concat(h).concat(c)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,u,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,f,g))})},b=()=>{this.addOrRemoveClasses(i,[],u.concat(f)),d.putSticky(i,"toggle",m=>m.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))};i.dispatchEvent(new Event("phx:hide-start")),a===!1?(v(),setTimeout(b,o)):t.transition(o,v,b)}else{if(e==="remove")return;let v=()=>{this.addOrRemoveClasses(i,h,u.concat(g).concat(f));let m=n||this.defaultDisplay(i);d.putSticky(i,"toggle",w=>w.style.display=m),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,l,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,c,h))})},b=()=>{this.addOrRemoveClasses(i,[],l.concat(c)),i.dispatchEvent(new Event("phx:show-end"))};i.dispatchEvent(new Event("phx:show-start")),a===!1?(v(),setTimeout(b,o)):t.transition(o,v,b)}else this.isVisible(i)?window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:hide-start")),d.putSticky(i,"toggle",v=>v.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))}):window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:show-start"));let v=n||this.defaultDisplay(i);d.putSticky(i,"toggle",b=>b.style.display=v),i.dispatchEvent(new Event("phx:show-end"))})},toggleClasses(e,t,i,n,r){window.requestAnimationFrame(()=>{let[s,o]=d.getSticky(e,"classes",[[],[]]),a=t.filter(h=>s.indexOf(h)<0&&!e.classList.contains(h)),l=t.filter(h=>o.indexOf(h)<0&&e.classList.contains(h));this.addOrRemoveClasses(e,a,l,i,n,r)})},addOrRemoveClasses(e,t,i,n,r,s,o){r=r||Dn;let[a,l,h]=n||[[],[],[]];if(a.length>0){let c=()=>{this.addOrRemoveClasses(e,l,[].concat(a).concat(h)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(e,a,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(e,h,l))})},u=()=>this.addOrRemoveClasses(e,t.concat(h),i.concat(a).concat(l));o===!1?(c(),setTimeout(u,r)):s.transition(r,c,u);return}window.requestAnimationFrame(()=>{let[c,u]=d.getSticky(e,"classes",[[],[]]),g=t.filter(m=>c.indexOf(m)<0&&!e.classList.contains(m)),f=i.filter(m=>u.indexOf(m)<0&&e.classList.contains(m)),v=c.filter(m=>i.indexOf(m)<0).concat(g),b=u.filter(m=>t.indexOf(m)<0).concat(f);d.putSticky(e,"classes",m=>(m.classList.remove(...b),m.classList.add(...v),[v,b]))})},setOrRemoveAttrs(e,t,i){let[n,r]=d.getSticky(e,"attrs",[[],[]]),s=t.map(([l,h])=>l).concat(i),o=n.filter(([l,h])=>!s.includes(l)).concat(t),a=r.filter(l=>!s.includes(l)).concat(i);d.putSticky(e,"attrs",l=>(a.forEach(h=>l.removeAttribute(h)),o.forEach(([h,c])=>l.setAttribute(h,c)),[o,a]))},hasAllClasses(e,t){return t.every(i=>e.classList.contains(i))},isToggledOut(e,t){return!this.isVisible(e)||this.hasAllClasses(e,t)},filterToEls(e,{to:t}){return t?d.all(document,t):[e]},defaultDisplay(e){return{tr:"table-row",td:"table-cell"}[e.tagName.toLowerCase()]||"block"}},W=Co,_e={byId(e){return document.getElementById(e)||B(`no id found for ${e}`)},removeClass(e,t){e.classList.remove(t),e.classList.length===0&&e.removeAttribute("class")},all(e,t,i){if(!e)return[];let n=Array.from(e.querySelectorAll(t));return i?n.forEach(i):n},childNodeLength(e){let t=document.createElement("template");return t.innerHTML=e,t.content.childElementCount},isUploadInput(e){return e.type==="file"&&e.getAttribute(oe)!==null},isAutoUpload(e){return e.hasAttribute("data-phx-auto-upload")},findUploadInputs(e){let t=e.id,i=this.all(document,`input[type="file"][${oe}][form="${t}"]`);return this.all(e,`input[type="file"][${oe}]`).concat(i)},findComponentNodeList(e,t){return this.filterWithinSameLiveView(this.all(e,`[${se}="${t}"]`),e)},isPhxDestroyed(e){return!!(e.id&&_e.private(e,"destroyed"))},wantsNewTab(e){let t=e.ctrlKey||e.shiftKey||e.metaKey||e.button&&e.button===1,i=e.target instanceof HTMLAnchorElement&&e.target.hasAttribute("download"),n=e.target.hasAttribute("target")&&e.target.getAttribute("target").toLowerCase()==="_blank",r=e.target.hasAttribute("target")&&!e.target.getAttribute("target").startsWith("_");return t||n||i||r},isUnloadableFormSubmit(e){return e.target&&e.target.getAttribute("method")==="dialog"||e.submitter&&e.submitter.getAttribute("formmethod")==="dialog"?!1:!e.defaultPrevented&&!this.wantsNewTab(e)},isNewPageClick(e,t){let i=e.target instanceof HTMLAnchorElement?e.target.getAttribute("href"):null,n;if(e.defaultPrevented||i===null||this.wantsNewTab(e)||i.startsWith("mailto:")||i.startsWith("tel:")||e.target.isContentEditable)return!1;try{n=new URL(i)}catch(r){try{n=new URL(i,t)}catch(s){return!0}}return n.host===t.host&&n.protocol===t.protocol&&n.pathname===t.pathname&&n.search===t.search?n.hash===""&&!n.href.endsWith("#"):n.protocol.startsWith("http")},markPhxChildDestroyed(e){this.isPhxChild(e)&&e.setAttribute(Q,""),this.putPrivate(e,"destroyed",!0)},findPhxChildrenInFragment(e,t){let i=document.createElement("template");return i.innerHTML=e,this.findPhxChildren(i.content,t)},isIgnored(e,t){return(e.getAttribute(t)||e.getAttribute("data-phx-update"))==="ignore"},isPhxUpdate(e,t,i){return e.getAttribute&&i.indexOf(e.getAttribute(t))>=0},findPhxSticky(e){return this.all(e,`[${An}]`)},findPhxChildren(e,t){return this.all(e,`${Me}[${we}="${t}"]`)},findExistingParentCIDs(e,t){let i=new Set,n=new Set;return t.forEach(r=>{this.filterWithinSameLiveView(this.all(e,`[${se}="${r}"]`),e).forEach(s=>{i.add(r),this.all(s,`[${se}]`).map(o=>parseInt(o.getAttribute(se))).forEach(o=>n.add(o))})}),n.forEach(r=>i.delete(r)),i},filterWithinSameLiveView(e,t){return t.querySelector(Me)?e.filter(i=>this.withinSameLiveView(i,t)):e},withinSameLiveView(e,t){for(;e=e.parentNode;){if(e.isSameNode(t))return!0;if(e.getAttribute(Q)!==null)return!1}},private(e,t){return e[G]&&e[G][t]},deletePrivate(e,t){e[G]&&delete e[G][t]},putPrivate(e,t,i){e[G]||(e[G]={}),e[G][t]=i},updatePrivate(e,t,i,n){let r=this.private(e,t);r===void 0?this.putPrivate(e,t,n(i)):this.putPrivate(e,t,n(r))},syncPendingAttrs(e,t){e.hasAttribute(q)&&(qn.forEach(i=>{e.classList.contains(i)&&t.classList.add(i)}),kn.filter(i=>e.hasAttribute(i)).forEach(i=>{t.setAttribute(i,e.getAttribute(i))}))},copyPrivates(e,t){t[G]&&(e[G]=t[G])},putTitle(e){let t=document.querySelector("title");if(t){let{prefix:i,suffix:n}=t.dataset;document.title=`${i||""}${e}${n||""}`}else document.title=e},debounce(e,t,i,n,r,s,o,a){let l=e.getAttribute(i),h=e.getAttribute(r);l===""&&(l=n),h===""&&(h=s);let c=l||h;switch(c){case null:return a();case"blur":this.once(e,"debounce-blur")&&e.addEventListener("blur",()=>{o()&&a()});return;default:let u=parseInt(c),g=()=>h?this.deletePrivate(e,Ke):a(),f=this.incCycle(e,qe,g);if(isNaN(u))return B(`invalid throttle/debounce value: ${c}`);if(h){let b=!1;if(t.type==="keydown"){let m=this.private(e,Cn);this.putPrivate(e,Cn,t.key),b=m!==t.key}if(!b&&this.private(e,Ke))return!1;{a();let m=setTimeout(()=>{o()&&this.triggerCycle(e,qe)},u);this.putPrivate(e,Ke,m)}}else setTimeout(()=>{o()&&this.triggerCycle(e,qe,f)},u);let v=e.form;v&&this.once(v,"bind-debounce")&&v.addEventListener("submit",()=>{Array.from(new FormData(v).entries(),([b])=>{let m=v.querySelector(`[name="${b}"]`);this.incCycle(m,qe),this.deletePrivate(m,Ke)})}),this.once(e,"bind-debounce")&&e.addEventListener("blur",()=>{clearTimeout(this.private(e,Ke)),this.triggerCycle(e,qe)})}},triggerCycle(e,t,i){let[n,r]=this.private(e,t);i||(i=n),i===n&&(this.incCycle(e,t),r())},once(e,t){return this.private(e,t)===!0?!1:(this.putPrivate(e,t,!0),!0)},incCycle(e,t,i=function(){}){let[n]=this.private(e,t)||[0,i];return n++,this.putPrivate(e,t,[n,i]),n},maybeAddPrivateHooks(e,t,i){e.hasAttribute&&(e.hasAttribute(t)||e.hasAttribute(i))&&e.setAttribute("data-phx-hook","Phoenix.InfiniteScroll")},isUsedInput(e){return e.nodeType===Node.ELEMENT_NODE&&(this.private(e,Tt)||this.private(e,et))},resetForm(e){Array.from(e.elements).forEach(t=>{this.deletePrivate(t,Tt),this.deletePrivate(t,et)})},isPhxChild(e){return e.getAttribute&&e.getAttribute(we)},isPhxSticky(e){return e.getAttribute&&e.getAttribute(An)!==null},isChildOfAny(e,t){return!!t.find(i=>i.contains(e))},firstPhxChild(e){return this.isPhxChild(e)?e:this.all(e,`[${we}]`)[0]},dispatchEvent(e,t,i={}){let n=!0;e.nodeName==="INPUT"&&e.type==="file"&&t==="click"&&(n=!1);let o={bubbles:i.bubbles===void 0?n:!!i.bubbles,cancelable:!0,detail:i.detail||{}},a=t==="click"?new MouseEvent("click",o):new CustomEvent(t,o);e.dispatchEvent(a)},cloneNode(e,t){if(typeof t=="undefined")return e.cloneNode(!0);{let i=e.cloneNode(!1);return i.innerHTML=t,i}},mergeAttrs(e,t,i={}){let n=new Set(i.exclude||[]),r=i.isIgnored,s=t.attributes;for(let a=s.length-1;a>=0;a--){let l=s[a].name;if(n.has(l))l==="value"&&e.value===t.value&&e.setAttribute("value",t.getAttribute(l));else{let h=t.getAttribute(l);e.getAttribute(l)!==h&&(!r||r&&l.startsWith("data-"))&&e.setAttribute(l,h)}}let o=e.attributes;for(let a=o.length-1;a>=0;a--){let l=o[a].name;r?l.startsWith("data-")&&!t.hasAttribute(l)&&!kn.includes(l)&&e.removeAttribute(l):t.hasAttribute(l)||e.removeAttribute(l)}},mergeFocusedInput(e,t){e instanceof HTMLSelectElement||_e.mergeAttrs(e,t,{exclude:["value"]}),t.readOnly?e.setAttribute("readonly",!0):e.removeAttribute("readonly")},hasSelectionRange(e){return e.setSelectionRange&&(e.type==="text"||e.type==="textarea")},restoreFocus(e,t,i){if(e instanceof HTMLSelectElement&&e.focus(),!_e.isTextualInput(e))return;e.matches(":focus")||e.focus(),this.hasSelectionRange(e)&&e.setSelectionRange(t,i)},isFormInput(e){return/^(?:input|select|textarea)$/i.test(e.tagName)&&e.type!=="button"},syncAttrsToProps(e){e instanceof HTMLInputElement&&Gn.indexOf(e.type.toLocaleLowerCase())>=0&&(e.checked=e.getAttribute("checked")!==null)},isTextualInput(e){return lo.indexOf(e.type)>=0},isNowTriggerFormExternal(e,t){return e.getAttribute&&e.getAttribute(t)!==null},cleanChildNodes(e,t){if(_e.isPhxUpdate(e,t,["append","prepend"])){let i=[];e.childNodes.forEach(n=>{n.id||(!(n.nodeType===Node.TEXT_NODE&&n.nodeValue.trim()==="")&&n.nodeType!==Node.COMMENT_NODE&&B(`only HTML element tags with an id are allowed inside containers with phx-update.

removing illegal node: "${(n.outerHTML||n.nodeValue).trim()}"

`),i.push(n))}),i.forEach(n=>n.remove())}},replaceRootContainer(e,t,i){let n=new Set(["id",Q,ye,_i,de]);if(e.tagName.toLowerCase()===t.toLowerCase())return Array.from(e.attributes).filter(r=>!n.has(r.name.toLowerCase())).forEach(r=>e.removeAttribute(r.name)),Object.keys(i).filter(r=>!n.has(r.toLowerCase())).forEach(r=>e.setAttribute(r,i[r])),e;{let r=document.createElement(t);return Object.keys(i).forEach(s=>r.setAttribute(s,i[s])),n.forEach(s=>r.setAttribute(s,e.getAttribute(s))),r.innerHTML=e.innerHTML,e.replaceWith(r),r}},getSticky(e,t,i){let n=(_e.private(e,"sticky")||[]).find(([r])=>t===r);if(n){let[r,s,o]=n;return o}else return typeof i=="function"?i():i},deleteSticky(e,t){this.updatePrivate(e,"sticky",[],i=>i.filter(([n,r])=>n!==t))},putSticky(e,t,i){let n=i(e);this.updatePrivate(e,"sticky",[],r=>{let s=r.findIndex(([o])=>t===o);return s>=0?r[s]=[t,i,n]:r.push([t,i,n]),r})},applyStickyOperations(e){let t=_e.private(e,"sticky");t&&t.forEach(([i,n,r])=>this.putSticky(e,i,n))}},d=_e,Xe=class{static isActive(e,t){let i=t._phxRef===void 0,r=e.getAttribute(fi).split(",").indexOf(O.genFileRef(t))>=0;return t.size>0&&(i||r)}static isPreflighted(e,t){return e.getAttribute(bi).split(",").indexOf(O.genFileRef(t))>=0&&this.isActive(e,t)}static isPreflightInProgress(e){return e._preflightInProgress===!0}static markPreflightInProgress(e){e._preflightInProgress=!0}constructor(e,t,i,n){this.ref=O.genFileRef(t),this.fileEl=e,this.file=t,this.view=i,this.meta=null,this._isCancelled=!1,this._isDone=!1,this._progress=0,this._lastProgressSent=-1,this._onDone=function(){},this._onElUpdated=this.onElUpdated.bind(this),this.fileEl.addEventListener(Ct,this._onElUpdated),this.autoUpload=n}metadata(){return this.meta}progress(e){this._progress=Math.floor(e),this._progress>this._lastProgressSent&&(this._progress>=100?(this._progress=100,this._lastProgressSent=100,this._isDone=!0,this.view.pushFileProgress(this.fileEl,this.ref,100,()=>{O.untrackFile(this.fileEl,this.file),this._onDone()})):(this._lastProgressSent=this._progress,this.view.pushFileProgress(this.fileEl,this.ref,this._progress)))}isCancelled(){return this._isCancelled}cancel(){this.file._preflightInProgress=!1,this._isCancelled=!0,this._isDone=!0,this._onDone()}isDone(){return this._isDone}error(e="failed"){this.fileEl.removeEventListener(Ct,this._onElUpdated),this.view.pushFileProgress(this.fileEl,this.ref,{error:e}),this.isAutoUpload()||O.clearFiles(this.fileEl)}isAutoUpload(){return this.autoUpload}onDone(e){this._onDone=()=>{this.fileEl.removeEventListener(Ct,this._onElUpdated),e()}}onElUpdated(){this.fileEl.getAttribute(fi).split(",").indexOf(this.ref)===-1&&(O.untrackFile(this.fileEl,this.file),this.cancel())}toPreflightPayload(){return{last_modified:this.file.lastModified,name:this.file.name,relative_path:this.file.webkitRelativePath,size:this.file.size,type:this.file.type,ref:this.ref,meta:typeof this.file.meta=="function"?this.file.meta():void 0}}uploader(e){if(this.meta.uploader){let t=e[this.meta.uploader]||B(`no uploader configured for ${this.meta.uploader}`);return{name:this.meta.uploader,callback:t}}else return{name:"channel",callback:xo}}zipPostFlight(e){this.meta=e.entries[this.ref],this.meta||B(`no preflight upload response returned with ref ${this.ref}`,{input:this.fileEl,response:e})}},ko=0,O=class{static genFileRef(e){let t=e._phxRef;return t!==void 0?t:(e._phxRef=(ko++).toString(),e._phxRef)}static getEntryDataURL(e,t,i){let n=this.activeFiles(e).find(r=>this.genFileRef(r)===t);i(URL.createObjectURL(n))}static hasUploadsInProgress(e){let t=0;return d.findUploadInputs(e).forEach(i=>{i.getAttribute(bi)!==i.getAttribute(oo)&&t++}),t>0}static serializeUploads(e){let t=this.activeFiles(e),i={};return t.forEach(n=>{let r={path:e.name},s=e.getAttribute(oe);i[s]=i[s]||[],r.ref=this.genFileRef(n),r.last_modified=n.lastModified,r.name=n.name||r.ref,r.relative_path=n.webkitRelativePath,r.type=n.type,r.size=n.size,typeof n.meta=="function"&&(r.meta=n.meta()),i[s].push(r)}),i}static clearFiles(e){e.value=null,e.removeAttribute(oe),d.putPrivate(e,"files",[])}static untrackFile(e,t){d.putPrivate(e,"files",d.private(e,"files").filter(i=>!Object.is(i,t)))}static trackFiles(e,t,i){if(e.getAttribute("multiple")!==null){let n=t.filter(r=>!this.activeFiles(e).find(s=>Object.is(s,r)));d.updatePrivate(e,"files",[],r=>r.concat(n)),e.value=null}else i&&i.files.length>0&&(e.files=i.files),d.putPrivate(e,"files",t)}static activeFileInputs(e){let t=d.findUploadInputs(e);return Array.from(t).filter(i=>i.files&&this.activeFiles(i).length>0)}static activeFiles(e){return(d.private(e,"files")||[]).filter(t=>Xe.isActive(e,t))}static inputsAwaitingPreflight(e){let t=d.findUploadInputs(e);return Array.from(t).filter(i=>this.filesAwaitingPreflight(i).length>0)}static filesAwaitingPreflight(e){return this.activeFiles(e).filter(t=>!Xe.isPreflighted(e,t)&&!Xe.isPreflightInProgress(t))}static markPreflightInProgress(e){e.forEach(t=>Xe.markPreflightInProgress(t.file))}constructor(e,t,i){this.autoUpload=d.isAutoUpload(e),this.view=t,this.onComplete=i,this._entries=Array.from(O.filesAwaitingPreflight(e)||[]).map(n=>new Xe(e,n,t,this.autoUpload)),O.markPreflightInProgress(this._entries),this.numEntriesInProgress=this._entries.length}isAutoUpload(){return this.autoUpload}entries(){return this._entries}initAdapterUpload(e,t,i){this._entries=this._entries.map(r=>(r.isCancelled()?(this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()):(r.zipPostFlight(e),r.onDone(()=>{this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()})),r));let n=this._entries.reduce((r,s)=>{if(!s.meta)return r;let{name:o,callback:a}=s.uploader(i.uploaders);return r[o]=r[o]||{callback:a,entries:[]},r[o].entries.push(s),r},{});for(let r in n){let{callback:s,entries:o}=n[r];s(o,t,e,i)}}},Zn={LiveFileUpload:{activeRefs(){return this.el.getAttribute(fi)},preflightedRefs(){return this.el.getAttribute(bi)},mounted(){this.preflightedWas=this.preflightedRefs()},updated(){let e=this.preflightedRefs();this.preflightedWas!==e&&(this.preflightedWas=e,e===""&&this.__view.cancelSubmit(this.el.form)),this.activeRefs()===""&&(this.el.value=null),this.el.dispatchEvent(new CustomEvent(Ct))}},LiveImgPreview:{mounted(){this.ref=this.el.getAttribute("data-phx-entry-ref"),this.inputEl=document.getElementById(this.el.getAttribute(oe)),O.getEntryDataURL(this.inputEl,this.ref,e=>{this.url=e,this.el.src=e})},destroyed(){URL.revokeObjectURL(this.url)}},FocusWrap:{mounted(){this.focusStart=this.el.firstElementChild,this.focusEnd=this.el.lastElementChild,this.focusStart.addEventListener("focus",()=>De.focusLast(this.el)),this.focusEnd.addEventListener("focus",()=>De.focusFirst(this.el)),this.el.addEventListener("phx:show-end",()=>this.el.focus()),window.getComputedStyle(this.el).display!=="none"&&De.focusFirst(this.el)}}},Qn=e=>["HTML","BODY"].indexOf(e.nodeName.toUpperCase())>=0?null:["scroll","auto"].indexOf(getComputedStyle(e).overflowY)>=0?e:Qn(e.parentElement),Mn=e=>e?e.scrollTop:document.documentElement.scrollTop||document.body.scrollTop,yi=e=>e?e.getBoundingClientRect().bottom:window.innerHeight||document.documentElement.clientHeight,wi=e=>e?e.getBoundingClientRect().top:0,To=(e,t)=>{let i=e.getBoundingClientRect();return i.top>=wi(t)&&i.left>=0&&i.top<=yi(t)},Ro=(e,t)=>{let i=e.getBoundingClientRect();return i.right>=wi(t)&&i.left>=0&&i.bottom<=yi(t)},Nn=(e,t)=>{let i=e.getBoundingClientRect();return i.top>=wi(t)&&i.left>=0&&i.top<=yi(t)};Zn.InfiniteScroll={mounted(){this.scrollContainer=Qn(this.el);let e=Mn(this.scrollContainer),t=!1,i=500,n=null,r=this.throttle(i,(a,l)=>{n=()=>!0,this.liveSocket.execJSHookPush(this.el,a,{id:l.id,_overran:!0},()=>{n=null})}),s=this.throttle(i,(a,l)=>{n=()=>l.scrollIntoView({block:"start"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{n=null,window.requestAnimationFrame(()=>{Nn(l,this.scrollContainer)||l.scrollIntoView({block:"start"})})})}),o=this.throttle(i,(a,l)=>{n=()=>l.scrollIntoView({block:"end"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{n=null,window.requestAnimationFrame(()=>{Nn(l,this.scrollContainer)||l.scrollIntoView({block:"end"})})})});this.onScroll=a=>{let l=Mn(this.scrollContainer);if(n)return e=l,n();let h=this.el.getBoundingClientRect(),c=this.el.getAttribute(this.liveSocket.binding("viewport-top")),u=this.el.getAttribute(this.liveSocket.binding("viewport-bottom")),g=this.el.lastElementChild,f=this.el.firstElementChild,v=l<e,b=l>e;v&&c&&!t&&h.top>=0?(t=!0,r(c,f)):b&&t&&h.top<=0&&(t=!1),c&&v&&To(f,this.scrollContainer)?s(c,f):u&&b&&Ro(g,this.scrollContainer)&&o(u,g),e=l},this.scrollContainer?this.scrollContainer.addEventListener("scroll",this.onScroll):window.addEventListener("scroll",this.onScroll)},destroyed(){this.scrollContainer?this.scrollContainer.removeEventListener("scroll",this.onScroll):window.removeEventListener("scroll",this.onScroll)},throttle(e,t){let i=0,n;return(...r)=>{let s=Date.now(),o=e-(s-i);o<=0||o>e?(n&&(clearTimeout(n),n=null),i=s,t(...r)):n||(n=setTimeout(()=>{i=Date.now(),n=null,t(...r)},o))}}};var Po=Zn,Oo=class{constructor(e){this.el=e,this.loadingRef=e.hasAttribute(Le)?parseInt(e.getAttribute(Le),10):null,this.lockRef=e.hasAttribute(J)?parseInt(e.getAttribute(J),10):null}maybeUndo(e,t){this.isWithin(e)&&(this.undoLocks(e,t),this.undoLoading(e),this.isFullyResolvedBy(e)&&this.el.removeAttribute(q))}isWithin(e){return!(this.loadingRef!==null&&this.loadingRef>e&&this.lockRef!==null&&this.lockRef>e)}undoLocks(e,t){if(!this.isLockUndoneBy(e))return;let i=d.private(this.el,J);i&&(t(i),d.deletePrivate(this.el,J)),this.el.removeAttribute(J),this.el.dispatchEvent(new CustomEvent("phx:unlock",{bubbles:!0,cancelable:!1}))}undoLoading(e){if(!this.isLoadingUndoneBy(e)){this.canUndoLoading(e)&&this.el.classList.contains("phx-submit-loading")&&this.el.classList.remove("phx-change-loading");return}if(this.canUndoLoading(e)){this.el.removeAttribute(Le);let t=this.el.getAttribute(Ie),i=this.el.getAttribute(vi);i!==null&&(this.el.readOnly=i==="true",this.el.removeAttribute(vi)),t!==null&&(this.el.disabled=t==="true",this.el.removeAttribute(Ie));let n=this.el.getAttribute(Rt);n!==null&&(this.el.innerText=n,this.el.removeAttribute(Rt))}qn.forEach(t=>{(t!=="phx-submit-loading"||this.canUndoLoading(e))&&d.removeClass(this.el,t)})}isLoadingUndoneBy(e){return this.loadingRef===null?!1:this.loadingRef<=e}isLockUndoneBy(e){return this.lockRef===null?!1:this.lockRef<=e}isFullyResolvedBy(e){return(this.loadingRef===null||this.loadingRef<=e)&&(this.lockRef===null||this.lockRef<=e)}canUndoLoading(e){return this.lockRef===null||this.lockRef<=e}},Io=class{constructor(e,t,i){let n=new Set,r=new Set([...t.children].map(o=>o.id)),s=[];Array.from(e.children).forEach(o=>{if(o.id&&(n.add(o.id),r.has(o.id))){let a=o.previousElementSibling&&o.previousElementSibling.id;s.push({elementId:o.id,previousElementId:a})}}),this.containerId=t.id,this.updateType=i,this.elementsToModify=s,this.elementIdsToAdd=[...r].filter(o=>!n.has(o))}perform(){let e=d.byId(this.containerId);this.elementsToModify.forEach(t=>{t.previousElementId?ue(document.getElementById(t.previousElementId),i=>{ue(document.getElementById(t.elementId),n=>{n.previousElementSibling&&n.previousElementSibling.id==i.id||i.insertAdjacentElement("afterend",n)})}):ue(document.getElementById(t.elementId),i=>{i.previousElementSibling==null||e.insertAdjacentElement("afterbegin",i)})}),this.updateType=="prepend"&&this.elementIdsToAdd.reverse().forEach(t=>{ue(document.getElementById(t),i=>e.insertAdjacentElement("afterbegin",i))})}},$n=11;function Lo(e,t){var i=t.attributes,n,r,s,o,a;if(!(t.nodeType===$n||e.nodeType===$n)){for(var l=i.length-1;l>=0;l--)n=i[l],r=n.name,s=n.namespaceURI,o=n.value,s?(r=n.localName||r,a=e.getAttributeNS(s,r),a!==o&&(n.prefix==="xmlns"&&(r=n.name),e.setAttributeNS(s,r,o))):(a=e.getAttribute(r),a!==o&&e.setAttribute(r,o));for(var h=e.attributes,c=h.length-1;c>=0;c--)n=h[c],r=n.name,s=n.namespaceURI,s?(r=n.localName||r,t.hasAttributeNS(s,r)||e.removeAttributeNS(s,r)):t.hasAttribute(r)||e.removeAttribute(r)}}var At,Do="http://www.w3.org/1999/xhtml",F=typeof document=="undefined"?void 0:document,Mo=!!F&&"content"in F.createElement("template"),No=!!F&&F.createRange&&"createContextualFragment"in F.createRange();function $o(e){var t=F.createElement("template");return t.innerHTML=e,t.content.childNodes[0]}function Ho(e){At||(At=F.createRange(),At.selectNode(F.body));var t=At.createContextualFragment(e);return t.childNodes[0]}function Fo(e){var t=F.createElement("body");return t.innerHTML=e,t.childNodes[0]}function jo(e){return e=e.trim(),Mo?$o(e):No?Ho(e):Fo(e)}function St(e,t){var i=e.nodeName,n=t.nodeName,r,s;return i===n?!0:(r=i.charCodeAt(0),s=n.charCodeAt(0),r<=90&&s>=97?i===n.toUpperCase():s<=90&&r>=97?n===i.toUpperCase():!1)}function Uo(e,t){return!t||t===Do?F.createElement(e):F.createElementNS(t,e)}function Bo(e,t){for(var i=e.firstChild;i;){var n=i.nextSibling;t.appendChild(i),i=n}return t}function di(e,t,i){e[i]!==t[i]&&(e[i]=t[i],e[i]?e.setAttribute(i,""):e.removeAttribute(i))}var Hn={OPTION:function(e,t){var i=e.parentNode;if(i){var n=i.nodeName.toUpperCase();n==="OPTGROUP"&&(i=i.parentNode,n=i&&i.nodeName.toUpperCase()),n==="SELECT"&&!i.hasAttribute("multiple")&&(e.hasAttribute("selected")&&!t.selected&&(e.setAttribute("selected","selected"),e.removeAttribute("selected")),i.selectedIndex=-1)}di(e,t,"selected")},INPUT:function(e,t){di(e,t,"checked"),di(e,t,"disabled"),e.value!==t.value&&(e.value=t.value),t.hasAttribute("value")||e.removeAttribute("value")},TEXTAREA:function(e,t){var i=t.value;e.value!==i&&(e.value=i);var n=e.firstChild;if(n){var r=n.nodeValue;if(r==i||!i&&r==e.placeholder)return;n.nodeValue=i}},SELECT:function(e,t){if(!t.hasAttribute("multiple")){for(var i=-1,n=0,r=e.firstChild,s,o;r;)if(o=r.nodeName&&r.nodeName.toUpperCase(),o==="OPTGROUP")s=r,r=s.firstChild;else{if(o==="OPTION"){if(r.hasAttribute("selected")){i=n;break}n++}r=r.nextSibling,!r&&s&&(r=s.nextSibling,s=null)}e.selectedIndex=i}}},Ge=1,Fn=11,jn=3,Un=8;function ce(){}function Jo(e){if(e)return e.getAttribute&&e.getAttribute("id")||e.id}function Vo(e){return function(i,n,r){if(r||(r={}),typeof n=="string")if(i.nodeName==="#document"||i.nodeName==="HTML"||i.nodeName==="BODY"){var s=n;n=F.createElement("html"),n.innerHTML=s}else n=jo(n);else n.nodeType===Fn&&(n=n.firstElementChild);var o=r.getNodeKey||Jo,a=r.onBeforeNodeAdded||ce,l=r.onNodeAdded||ce,h=r.onBeforeElUpdated||ce,c=r.onElUpdated||ce,u=r.onBeforeNodeDiscarded||ce,g=r.onNodeDiscarded||ce,f=r.onBeforeElChildrenUpdated||ce,v=r.skipFromChildren||ce,b=r.addChild||function(S,A){return S.appendChild(A)},m=r.childrenOnly===!0,w=Object.create(null),_=[];function x(S){_.push(S)}function C(S,A){if(S.nodeType===Ge)for(var R=S.firstChild;R;){var E=void 0;A&&(E=o(R))?x(E):(g(R),R.firstChild&&C(R,A)),R=R.nextSibling}}function p(S,A,R){u(S)!==!1&&(A&&A.removeChild(S),g(S),C(S,R))}function y(S){if(S.nodeType===Ge||S.nodeType===Fn)for(var A=S.firstChild;A;){var R=o(A);R&&(w[R]=A),y(A),A=A.nextSibling}}y(i);function D(S){l(S);for(var A=S.firstChild;A;){var R=A.nextSibling,E=o(A);if(E){var k=w[E];k&&St(A,k)?(A.parentNode.replaceChild(k,A),M(k,A)):D(A)}else D(A);A=R}}function j(S,A,R){for(;A;){var E=A.nextSibling;(R=o(A))?x(R):p(A,S,!0),A=E}}function M(S,A,R){var E=o(A);if(E&&delete w[E],!R){var k=h(S,A);if(k===!1||(k instanceof HTMLElement&&(S=k),e(S,A),c(S),f(S,A)===!1))return}S.nodeName!=="TEXTAREA"?te(S,A):Hn.TEXTAREA(S,A)}function te(S,A){var R=v(S,A),E=A.firstChild,k=S.firstChild,Re,ie,Pe,ft,le;e:for(;E;){for(ft=E.nextSibling,Re=o(E);!R&&k;){if(Pe=k.nextSibling,E.isSameNode&&E.isSameNode(k)){E=ft,k=Pe;continue e}ie=o(k);var pt=k.nodeType,he=void 0;if(pt===E.nodeType&&(pt===Ge?(Re?Re!==ie&&((le=w[Re])?Pe===le?he=!1:(S.insertBefore(le,k),ie?x(ie):p(k,S,!0),k=le,ie=o(k)):he=!1):ie&&(he=!1),he=he!==!1&&St(k,E),he&&M(k,E)):(pt===jn||pt==Un)&&(he=!0,k.nodeValue!==E.nodeValue&&(k.nodeValue=E.nodeValue))),he){E=ft,k=Pe;continue e}ie?x(ie):p(k,S,!0),k=Pe}if(Re&&(le=w[Re])&&St(le,E))R||b(S,le),M(le,E);else{var ei=a(E);ei!==!1&&(ei&&(E=ei),E.actualize&&(E=E.actualize(S.ownerDocument||F)),b(S,E),D(E))}E=ft,k=Pe}j(S,k,ie);var dn=Hn[S.nodeName];dn&&dn(S,A)}var P=i,dt=P.nodeType,un=n.nodeType;if(!m){if(dt===Ge)un===Ge?St(i,n)||(g(i),P=Bo(i,Uo(n.nodeName,n.namespaceURI))):P=n;else if(dt===jn||dt===Un){if(un===dt)return P.nodeValue!==n.nodeValue&&(P.nodeValue=n.nodeValue),P;P=n}}if(P===n)g(i);else{if(n.isSameNode&&n.isSameNode(P))return;if(M(P,n,m),_)for(var Zt=0,$s=_.length;Zt<$s;Zt++){var Qt=w[_[Zt]];Qt&&p(Qt,Qt.parentNode,!1)}}return!m&&P!==i&&i.parentNode&&(P.actualize&&(P=P.actualize(i.ownerDocument||F)),i.parentNode.replaceChild(P,i)),P}}var Wo=Vo(Lo),Bn=Wo,xt=class{static patchWithClonedTree(e,t,i){let n=i.getActiveElement(),r=i.binding(Ze);Bn(e,t,{childrenOnly:!1,onBeforeElUpdated:(s,o)=>{if(d.syncPendingAttrs(s,o),!e.isSameNode(s)&&s.hasAttribute(J)||d.isIgnored(s,r))return!1;if(n&&n.isSameNode(s)&&d.isFormInput(s))return d.mergeFocusedInput(s,o),!1}})}constructor(e,t,i,n,r,s){this.view=e,this.liveSocket=e.liveSocket,this.container=t,this.id=i,this.rootID=e.root.id,this.html=n,this.streams=r,this.streamInserts={},this.streamComponentRestore={},this.targetCID=s,this.cidPatch=re(this.targetCID),this.pendingRemoves=[],this.phxRemove=this.liveSocket.binding("remove"),this.targetContainer=this.isCIDPatch()?this.targetCIDContainer(n):t,this.callbacks={beforeadded:[],beforeupdated:[],beforephxChildAdded:[],afteradded:[],afterupdated:[],afterdiscarded:[],afterphxChildAdded:[],aftertransitionsDiscarded:[]}}before(e,t){this.callbacks[`before${e}`].push(t)}after(e,t){this.callbacks[`after${e}`].push(t)}trackBefore(e,...t){this.callbacks[`before${e}`].forEach(i=>i(...t))}trackAfter(e,...t){this.callbacks[`after${e}`].forEach(i=>i(...t))}markPrunableContentForRemoval(){let e=this.liveSocket.binding(Ze);d.all(this.container,`[${e}=append] > *, [${e}=prepend] > *`,t=>{t.setAttribute(_n,"")})}perform(e){let{view:t,liveSocket:i,html:n,container:r,targetContainer:s}=this;if(this.isCIDPatch()&&!s)return;let o=i.getActiveElement(),{selectionStart:a,selectionEnd:l}=o&&d.hasSelectionRange(o)?o:{},h=i.binding(Ze),c=i.binding(gi),u=i.binding(mi),g=i.binding(ao),f=[],v=[],b=[],m=null;function w(_,x,C=!1){Bn(_,x,{childrenOnly:_.getAttribute(se)===null&&!C,getNodeKey:p=>d.isPhxDestroyed(p)?null:e?p.id:p.id||p.getAttribute&&p.getAttribute(Xn),skipFromChildren:p=>p.getAttribute(h)===ai,addChild:(p,y)=>{let{ref:D,streamAt:j}=this.getStreamInsert(y);if(D===void 0)return p.appendChild(y);if(this.setStreamRef(y,D),j===0)p.insertAdjacentElement("afterbegin",y);else if(j===-1)p.appendChild(y);else if(j>0){let M=Array.from(p.children)[j];p.insertBefore(y,M)}},onBeforeNodeAdded:p=>{d.maybeAddPrivateHooks(p,c,u),this.trackBefore("added",p);let y=p;return!e&&this.streamComponentRestore[p.id]&&(y=this.streamComponentRestore[p.id],delete this.streamComponentRestore[p.id],w.call(this,y,p,!0)),y},onNodeAdded:p=>{p.getAttribute&&this.maybeReOrderStream(p,!0),p instanceof HTMLImageElement&&p.srcset?p.srcset=p.srcset:p instanceof HTMLVideoElement&&p.autoplay&&p.play(),d.isNowTriggerFormExternal(p,g)&&(m=p),(d.isPhxChild(p)&&t.ownsElement(p)||d.isPhxSticky(p)&&t.ownsElement(p.parentNode))&&this.trackAfter("phxChildAdded",p),f.push(p)},onNodeDiscarded:p=>this.onNodeDiscarded(p),onBeforeNodeDiscarded:p=>p.getAttribute&&p.getAttribute(_n)!==null?!0:!(p.parentElement!==null&&p.id&&d.isPhxUpdate(p.parentElement,h,[ai,"append","prepend"])||this.maybePendingRemove(p)||this.skipCIDSibling(p)),onElUpdated:p=>{d.isNowTriggerFormExternal(p,g)&&(m=p),v.push(p),this.maybeReOrderStream(p,!1)},onBeforeElUpdated:(p,y)=>{if(d.syncPendingAttrs(p,y),d.maybeAddPrivateHooks(y,c,u),d.cleanChildNodes(y,h),this.skipCIDSibling(y))return this.maybeReOrderStream(p),!1;if(d.isPhxSticky(p))return[Q,ye,de].map(M=>[M,p.getAttribute(M),y.getAttribute(M)]).forEach(([M,te,P])=>{P&&te!==P&&p.setAttribute(M,P)}),!1;if(d.isIgnored(p,h)||p.form&&p.form.isSameNode(m))return this.trackBefore("updated",p,y),d.mergeAttrs(p,y,{isIgnored:d.isIgnored(p,h)}),v.push(p),d.applyStickyOperations(p),!1;if(p.type==="number"&&p.validity&&p.validity.badInput)return!1;let D=o&&p.isSameNode(o)&&d.isFormInput(p),j=D&&this.isChangedSelect(p,y);if(p.hasAttribute(q)){d.isUploadInput(p)&&(d.mergeAttrs(p,y,{isIgnored:!0}),this.trackBefore("updated",p,y),v.push(p)),d.applyStickyOperations(p);let te=p.hasAttribute(J)?d.private(p,J)||p.cloneNode(!0):null;te&&(d.putPrivate(p,J,te),D||(p=te))}if(d.isPhxChild(y)){let M=p.getAttribute(Q);return d.mergeAttrs(p,y,{exclude:[ye]}),M!==""&&p.setAttribute(Q,M),p.setAttribute(de,this.rootID),d.applyStickyOperations(p),!1}return d.copyPrivates(y,p),D&&p.type!=="hidden"&&!j?(this.trackBefore("updated",p,y),d.mergeFocusedInput(p,y),d.syncAttrsToProps(p),v.push(p),d.applyStickyOperations(p),!1):(j&&p.blur(),d.isPhxUpdate(y,h,["append","prepend"])&&b.push(new Io(p,y,y.getAttribute(h))),d.syncAttrsToProps(y),d.applyStickyOperations(y),this.trackBefore("updated",p,y),p)}})}return this.trackBefore("added",r),this.trackBefore("updated",r,r),i.time("morphdom",()=>{this.streams.forEach(([_,x,C,p])=>{x.forEach(([y,D,j])=>{this.streamInserts[y]={ref:_,streamAt:D,limit:j,reset:p}}),p!==void 0&&d.all(r,`[${li}="${_}"]`,y=>{this.removeStreamChildElement(y)}),C.forEach(y=>{let D=r.querySelector(`[id="${y}"]`);D&&this.removeStreamChildElement(D)})}),e&&d.all(this.container,`[${h}=${ai}]`,_=>{this.liveSocket.owner(_,x=>{x===this.view&&Array.from(_.children).forEach(C=>{this.removeStreamChildElement(C)})})}),w.call(this,s,n)}),i.isDebugEnabled()&&(wo(),Array.from(document.querySelectorAll("input[name=id]")).forEach(_=>{_.form&&console.error(`Detected an input with name="id" inside a form! This will cause problems when patching the DOM.
`,_)})),b.length>0&&i.time("post-morph append/prepend restoration",()=>{b.forEach(_=>_.perform())}),i.silenceEvents(()=>d.restoreFocus(o,a,l)),d.dispatchEvent(document,"phx:update"),f.forEach(_=>this.trackAfter("added",_)),v.forEach(_=>this.trackAfter("updated",_)),this.transitionPendingRemoves(),m&&(i.unload(),Object.getPrototypeOf(m).submit.call(m)),!0}onNodeDiscarded(e){(d.isPhxChild(e)||d.isPhxSticky(e))&&this.liveSocket.destroyViewByEl(e),this.trackAfter("discarded",e)}maybePendingRemove(e){return e.getAttribute&&e.getAttribute(this.phxRemove)!==null?(this.pendingRemoves.push(e),!0):!1}removeStreamChildElement(e){this.streamInserts[e.id]?(this.streamComponentRestore[e.id]=e,e.remove()):this.maybePendingRemove(e)||(e.remove(),this.onNodeDiscarded(e))}getStreamInsert(e){return(e.id?this.streamInserts[e.id]:{})||{}}setStreamRef(e,t){d.putSticky(e,li,i=>i.setAttribute(li,t))}maybeReOrderStream(e,t){let{ref:i,streamAt:n,reset:r}=this.getStreamInsert(e);if(n!==void 0&&(this.setStreamRef(e,i),!(!r&&!t)&&e.parentElement)){if(n===0)e.parentElement.insertBefore(e,e.parentElement.firstElementChild);else if(n>0){let s=Array.from(e.parentElement.children),o=s.indexOf(e);if(n>=s.length-1)e.parentElement.appendChild(e);else{let a=s[n];o>n?e.parentElement.insertBefore(e,a):e.parentElement.insertBefore(e,a.nextElementSibling)}}this.maybeLimitStream(e)}}maybeLimitStream(e){let{limit:t}=this.getStreamInsert(e),i=t!==null&&Array.from(e.parentElement.children);t&&t<0&&i.length>t*-1?i.slice(0,i.length+t).forEach(n=>this.removeStreamChildElement(n)):t&&t>=0&&i.length>t&&i.slice(t).forEach(n=>this.removeStreamChildElement(n))}transitionPendingRemoves(){let{pendingRemoves:e,liveSocket:t}=this;e.length>0&&(t.transitionRemoves(e),t.requestDOMUpdate(()=>{e.forEach(i=>{let n=d.firstPhxChild(i);n&&t.destroyViewByEl(n),i.remove()}),this.trackAfter("transitionsDiscarded",e)}))}isChangedSelect(e,t){if(!(e instanceof HTMLSelectElement)||e.multiple)return!1;if(e.options.length!==t.options.length)return!0;let i=e.selectedOptions[0],n=t.selectedOptions[0];return i&&i.hasAttribute("selected")&&n.setAttribute("selected",i.getAttribute("selected")),!e.isEqualNode(t)}isCIDPatch(){return this.cidPatch}skipCIDSibling(e){return e.nodeType===Node.ELEMENT_NODE&&e.hasAttribute(zn)}targetCIDContainer(e){if(!this.isCIDPatch())return;let[t,...i]=d.findComponentNodeList(this.container,this.targetCID);return i.length===0&&d.childNodeLength(e)===1?t:t&&t.parentNode}indexOf(e,t){return Array.from(e.children).indexOf(t)}},qo=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),Ko=new Set(["'",'"']),Jn=(e,t,i)=>{let n=0,r=!1,s,o,a,l,h,c,u=e.match(/^(\s*(?:<!--.*?-->\s*)*)<([^\s\/>]+)/);if(u===null)throw new Error(`malformed html ${e}`);for(n=u[0].length,s=u[1],a=u[2],l=n,n;n<e.length&&e.charAt(n)!==">";n++)if(e.charAt(n)==="="){let v=e.slice(n-3,n)===" id";n++;let b=e.charAt(n);if(Ko.has(b)){let m=n;for(n++,n;n<e.length&&e.charAt(n)!==b;n++);if(v){h=e.slice(m+1,n);break}}}let g=e.length-1;for(r=!1;g>=s.length+a.length;){let v=e.charAt(g);if(r)v==="-"&&e.slice(g-3,g)==="<!-"?(r=!1,g-=4):g-=1;else if(v===">"&&e.slice(g-2,g)==="--")r=!0,g-=3;else{if(v===">")break;g-=1}}o=e.slice(g+1,e.length);let f=Object.keys(t).map(v=>t[v]===!0?v:`${v}="${t[v]}"`).join(" ");if(i){let v=h?` id="${h}"`:"";qo.has(a)?c=`<${a}${v}${f===""?"":" "}${f}/>`:c=`<${a}${v}${f===""?"":" "}${f}></${a}>`}else{let v=e.slice(l,g+1);c=`<${a}${f===""?"":" "}${f}${v}`}return[c,s,o]},Vn=class{static extract(e){let{[Rn]:t,[Tn]:i,[Pn]:n}=e;return delete e[Rn],delete e[Tn],delete e[Pn],{diff:e,title:n,reply:t||null,events:i||[]}}constructor(e,t){this.viewId=e,this.rendered={},this.magicId=0,this.mergeDiff(t)}parentViewId(){return this.viewId}toString(e){let[t,i]=this.recursiveToString(this.rendered,this.rendered[$],e,!0,{});return[t,i]}recursiveToString(e,t=e[$],i,n,r){i=i?new Set(i):null;let s={buffer:"",components:t,onlyCids:i,streams:new Set};return this.toOutputBuffer(e,null,s,n,r),[s.buffer,s.streams]}componentCIDs(e){return Object.keys(e[$]||{}).map(t=>parseInt(t))}isComponentOnlyDiff(e){return e[$]?Object.keys(e).length===1:!1}getComponent(e,t){return e[$][t]}resetRender(e){this.rendered[$][e]&&(this.rendered[$][e].reset=!0)}mergeDiff(e){let t=e[$],i={};if(delete e[$],this.rendered=this.mutableMerge(this.rendered,e),this.rendered[$]=this.rendered[$]||{},t){let n=this.rendered[$];for(let r in t)t[r]=this.cachedFindComponent(r,t[r],n,t,i);for(let r in t)n[r]=t[r];e[$]=t}}cachedFindComponent(e,t,i,n,r){if(r[e])return r[e];{let s,o,a=t[Y];if(re(a)){let l;a>0?l=this.cachedFindComponent(a,n[a],i,n,r):l=i[-a],o=l[Y],s=this.cloneMerge(l,t,!0),s[Y]=o}else s=t[Y]!==void 0||i[e]===void 0?t:this.cloneMerge(i[e],t,!1);return r[e]=s,s}}mutableMerge(e,t){return t[Y]!==void 0?t:(this.doMutableMerge(e,t),e)}doMutableMerge(e,t){for(let i in t){let n=t[i],r=e[i];Oe(n)&&n[Y]===void 0&&Oe(r)?this.doMutableMerge(r,n):e[i]=n}e[ui]&&(e.newRender=!0)}cloneMerge(e,t,i){let n=N(N({},e),t);for(let r in n){let s=t[r],o=e[r];Oe(s)&&s[Y]===void 0&&Oe(o)?n[r]=this.cloneMerge(o,s,i):s===void 0&&Oe(o)&&(n[r]=this.cloneMerge(o,{},i))}return i?(delete n.magicId,delete n.newRender):e[ui]&&(n.newRender=!0),n}componentToString(e){let[t,i]=this.recursiveCIDToString(this.rendered[$],e,null),[n,r,s]=Jn(t,{});return[n,i]}pruneCIDs(e){e.forEach(t=>delete this.rendered[$][t])}get(){return this.rendered}isNewFingerprint(e={}){return!!e[Y]}templateStatic(e,t){return typeof e=="number"?t[e]:e}nextMagicID(){return this.magicId++,`m${this.magicId}-${this.parentViewId()}`}toOutputBuffer(e,t,i,n,r={}){if(e[wt])return this.comprehensionToBuffer(e,t,i);let{[Y]:s}=e;s=this.templateStatic(s,t);let o=e[ui],a=i.buffer;o&&(i.buffer=""),n&&o&&!e.magicId&&(e.newRender=!0,e.magicId=this.nextMagicID()),i.buffer+=s[0];for(let l=1;l<s.length;l++)this.dynamicToBuffer(e[l-1],t,i,n),i.buffer+=s[l];if(o){let l=!1,h;n||e.magicId?(l=n&&!e.newRender,h=N({[Xn]:e.magicId},r)):h=r,l&&(h[zn]=!0);let[c,u,g]=Jn(i.buffer,h,l);e.newRender=!1,i.buffer=a+u+c+g}}comprehensionToBuffer(e,t,i){let{[wt]:n,[Y]:r,[On]:s}=e,[o,a,l,h]=s||[null,{},[],null];r=this.templateStatic(r,t);let c=t||e[_o];for(let u=0;u<n.length;u++){let g=n[u];i.buffer+=r[0];for(let f=1;f<r.length;f++){let v=!1;this.dynamicToBuffer(g[f-1],c,i,v),i.buffer+=r[f]}}s!==void 0&&(e[wt].length>0||l.length>0||h)&&(delete e[On],e[wt]=[],i.streams.add(s))}dynamicToBuffer(e,t,i,n){if(typeof e=="number"){let[r,s]=this.recursiveCIDToString(i.components,e,i.onlyCids);i.buffer+=r,i.streams=new Set([...i.streams,...s])}else Oe(e)?this.toOutputBuffer(e,t,i,n,{}):i.buffer+=e}recursiveCIDToString(e,t,i){let n=e[t]||B(`no component for CID ${t}`,e),r={[se]:t},s=i&&!i.has(t);n.newRender=!s,n.magicId=`c${t}-${this.parentViewId()}`;let o=!n.reset,[a,l]=this.recursiveToString(n,e,i,o,r);return delete n.reset,[a,l]}},zo=1,Ye=class{static makeID(){return zo++}static elementID(e){return e.phxHookId}constructor(e,t,i){this.__view=e,this.liveSocket=e.liveSocket,this.__callbacks=i,this.__listeners=new Set,this.__isDisconnected=!1,this.el=t,this.el.phxHookId=this.constructor.makeID();for(let n in this.__callbacks)this[n]=this.__callbacks[n]}__mounted(){this.mounted&&this.mounted()}__updated(){this.updated&&this.updated()}__beforeUpdate(){this.beforeUpdate&&this.beforeUpdate()}__destroyed(){this.destroyed&&this.destroyed()}__reconnected(){this.__isDisconnected&&(this.__isDisconnected=!1,this.reconnected&&this.reconnected())}__disconnected(){this.__isDisconnected=!0,this.disconnected&&this.disconnected()}pushEvent(e,t={},i=function(){}){return this.__view.pushHookEvent(this.el,null,e,t,i)}pushEventTo(e,t,i={},n=function(){}){return this.__view.withinTargets(e,(r,s)=>r.pushHookEvent(this.el,s,t,i,n))}handleEvent(e,t){let i=(n,r)=>r?e:t(n.detail);return window.addEventListener(`phx:${e}`,i),this.__listeners.add(i),i}removeHandleEvent(e){let t=e(null,!0);window.removeEventListener(`phx:${t}`,e),this.__listeners.delete(e)}upload(e,t){return this.__view.dispatchUploads(null,e,t)}uploadTo(e,t,i){return this.__view.withinTargets(e,(n,r)=>{n.dispatchUploads(r,t,i)})}__cleanup__(){this.__listeners.forEach(e=>this.removeHandleEvent(e))}},Xo=(e,t)=>{let i=e.endsWith("[]"),n=i?e.slice(0,-2):e;return n=n.replace(/([^\[\]]+)(\]?$)/,`${t}$1$2`),i&&(n+="[]"),n},Et=(e,t,i=[])=>{let c=t,{submitter:n}=c,r=mn(c,["submitter"]),s;if(n&&n.name){let u=document.createElement("input");u.type="hidden";let g=n.getAttribute("form");g&&u.setAttribute("form",g),u.name=n.name,u.value=n.value,n.parentElement.insertBefore(u,n),s=u}let o=new FormData(e),a=[];o.forEach((u,g,f)=>{u instanceof File&&a.push(g)}),a.forEach(u=>o.delete(u));let l=new URLSearchParams,h=Array.from(e.elements);for(let[u,g]of o.entries())if(i.length===0||i.indexOf(u)>=0){let f=h.filter(m=>m.name===u),v=!f.some(m=>d.private(m,Tt)||d.private(m,et)),b=f.every(m=>m.type==="hidden");v&&!(n&&n.name==u)&&!b&&l.append(Xo(u,"_unused_"),""),l.append(u,g)}n&&s&&n.parentElement.removeChild(s);for(let u in r)l.append(u,r[u]);return l.toString()},er=class{constructor(e,t,i,n,r){this.isDead=!1,this.liveSocket=t,this.flash=n,this.parent=i,this.root=i?i.root:this,this.el=e,this.id=this.el.id,this.ref=0,this.childJoins=0,this.loaderTimer=null,this.pendingDiffs=[],this.pendingForms=new Set,this.redirect=!1,this.href=null,this.joinCount=this.parent?this.parent.joinCount-1:0,this.joinPending=!0,this.destroyed=!1,this.joinCallback=function(s){s&&s()},this.stopCallback=function(){},this.pendingJoinOps=this.parent?null:[],this.viewHooks={},this.formSubmits=[],this.children=this.parent?null:{},this.root.children[this.id]={},this.formsForRecovery={},this.channel=this.liveSocket.channel(`lv:${this.id}`,()=>{let s=this.href&&this.expandURL(this.href);return{redirect:this.redirect?s:void 0,url:this.redirect?void 0:s||void 0,params:this.connectParams(r),session:this.getSession(),static:this.getStatic(),flash:this.flash}})}setHref(e){this.href=e}setRedirect(e){this.redirect=!0,this.href=e}isMain(){return this.el.hasAttribute(_i)}connectParams(e){let t=this.liveSocket.params(this.el),i=d.all(document,`[${this.binding(ro)}]`).map(n=>n.src||n.href).filter(n=>typeof n=="string");return i.length>0&&(t._track_static=i),t._mounts=this.joinCount,t._live_referer=e,t}isConnected(){return this.channel.canPush()}getSession(){return this.el.getAttribute(Q)}getStatic(){let e=this.el.getAttribute(ye);return e===""?null:e}destroy(e=function(){}){this.destroyAllChildren(),this.destroyed=!0,delete this.root.children[this.id],this.parent&&delete this.root.children[this.parent.id][this.id],clearTimeout(this.loaderTimer);let t=()=>{e();for(let i in this.viewHooks)this.destroyHook(this.viewHooks[i])};d.markPhxChildDestroyed(this.el),this.log("destroyed",()=>["the child has been removed from the parent"]),this.channel.leave().receive("ok",t).receive("error",t).receive("timeout",t)}setContainerClasses(...e){this.el.classList.remove(yn,Ve,_t,wn,oi),this.el.classList.add(...e)}showLoader(e){if(clearTimeout(this.loaderTimer),e)this.loaderTimer=setTimeout(()=>this.showLoader(),e);else{for(let t in this.viewHooks)this.viewHooks[t].__disconnected();this.setContainerClasses(Ve)}}execAll(e){d.all(this.el,`[${e}]`,t=>this.liveSocket.execJS(t,t.getAttribute(e)))}hideLoader(){clearTimeout(this.loaderTimer),this.setContainerClasses(yn),this.execAll(this.binding("connected"))}triggerReconnected(){for(let e in this.viewHooks)this.viewHooks[e].__reconnected()}log(e,t){this.liveSocket.log(this,e,t)}transition(e,t,i=function(){}){this.liveSocket.transition(e,t,i)}withinTargets(e,t,i=document,n){if(e instanceof HTMLElement||e instanceof SVGElement)return this.liveSocket.owner(e,r=>t(r,e));if(re(e))d.findComponentNodeList(n||this.el,e).length===0?B(`no component found matching phx-target of ${e}`):t(this,parseInt(e));else{let r=Array.from(i.querySelectorAll(e));r.length===0&&B(`nothing found matching the phx-target selector "${e}"`),r.forEach(s=>this.liveSocket.owner(s,o=>t(o,s)))}}applyDiff(e,t,i){this.log(e,()=>["",kt(t)]);let{diff:n,reply:r,events:s,title:o}=Vn.extract(t);i({diff:n,reply:r,events:s}),typeof o=="string"&&window.requestAnimationFrame(()=>d.putTitle(o))}onJoin(e){let{rendered:t,container:i,liveview_version:n}=e;if(i){let[r,s]=i;this.el=d.replaceRootContainer(this.el,r,s)}this.childJoins=0,this.joinPending=!0,this.flash=null,this.root===this&&(this.formsForRecovery=this.getFormsForRecovery()),n!==this.liveSocket.version()&&console.error(`LiveView asset version mismatch. JavaScript version ${this.liveSocket.version()} vs. server ${n}. To avoid issues, please ensure that your assets use the same version as the server.`),Z.dropLocal(this.liveSocket.localStorage,window.location.pathname,Wn),this.applyDiff("mount",t,({diff:r,events:s})=>{this.rendered=new Vn(this.id,r);let[o,a]=this.renderContainer(null,"join");this.dropPendingRefs(),this.joinCount++,this.maybeRecoverForms(o,()=>{this.onJoinComplete(e,o,a,s)})})}dropPendingRefs(){d.all(document,`[${q}="${this.refSrc()}"]`,e=>{e.removeAttribute(Le),e.removeAttribute(q),e.removeAttribute(J)})}onJoinComplete({live_patch:e},t,i,n){if(this.joinCount>1||this.parent&&!this.parent.isJoinPending())return this.applyJoinPatch(e,t,i,n);d.findPhxChildrenInFragment(t,this.id).filter(s=>{let o=s.id&&this.el.querySelector(`[id="${s.id}"]`),a=o&&o.getAttribute(ye);return a&&s.setAttribute(ye,a),o&&o.setAttribute(de,this.root.id),this.joinChild(s)}).length===0?this.parent?(this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,n)]),this.parent.ackJoin(this)):(this.onAllChildJoinsComplete(),this.applyJoinPatch(e,t,i,n)):this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,n)])}attachTrueDocEl(){this.el=d.byId(this.id),this.el.setAttribute(de,this.root.id)}execNewMounted(e=this.el){let t=this.binding(gi),i=this.binding(mi);d.all(e,`[${t}], [${i}]`,n=>{this.ownsElement(n)&&(d.maybeAddPrivateHooks(n,t,i),this.maybeAddNewHook(n))}),d.all(e,`[${this.binding(We)}], [data-phx-${We}]`,n=>{this.ownsElement(n)&&this.maybeAddNewHook(n)}),d.all(e,`[${this.binding(En)}]`,n=>{this.ownsElement(n)&&this.maybeMounted(n)})}applyJoinPatch(e,t,i,n){this.attachTrueDocEl();let r=new xt(this,this.el,this.id,t,i,null);if(r.markPrunableContentForRemoval(),this.performPatch(r,!1,!0),this.joinNewChildren(),this.execNewMounted(),this.joinPending=!1,this.liveSocket.dispatchEvents(n),this.applyPendingUpdates(),e){let{kind:s,to:o}=e;this.liveSocket.historyPatch(o,s)}this.hideLoader(),this.joinCount>1&&this.triggerReconnected(),this.stopCallback()}triggerBeforeUpdateHook(e,t){this.liveSocket.triggerDOM("onBeforeElUpdated",[e,t]);let i=this.getHook(e),n=i&&d.isIgnored(e,this.binding(Ze));if(i&&!e.isEqualNode(t)&&!(n&&So(e.dataset,t.dataset)))return i.__beforeUpdate(),i}maybeMounted(e){let t=e.getAttribute(this.binding(En)),i=t&&d.private(e,"mounted");t&&!i&&(this.liveSocket.execJS(e,t),d.putPrivate(e,"mounted",!0))}maybeAddNewHook(e,t){let i=this.addHook(e);i&&i.__mounted()}performPatch(e,t,i=!1){let n=[],r=!1,s=new Set;return this.liveSocket.triggerDOM("onPatchStart",[e.targetContainer]),e.after("added",o=>{this.liveSocket.triggerDOM("onNodeAdded",[o]);let a=this.binding(gi),l=this.binding(mi);d.maybeAddPrivateHooks(o,a,l),this.maybeAddNewHook(o),o.getAttribute&&this.maybeMounted(o)}),e.after("phxChildAdded",o=>{d.isPhxSticky(o)?this.liveSocket.joinRootViews():r=!0}),e.before("updated",(o,a)=>{this.triggerBeforeUpdateHook(o,a)&&s.add(o.id)}),e.after("updated",o=>{s.has(o.id)&&this.getHook(o).__updated()}),e.after("discarded",o=>{o.nodeType===Node.ELEMENT_NODE&&n.push(o)}),e.after("transitionsDiscarded",o=>this.afterElementsRemoved(o,t)),e.perform(i),this.afterElementsRemoved(n,t),this.liveSocket.triggerDOM("onPatchEnd",[e.targetContainer]),r}afterElementsRemoved(e,t){let i=[];e.forEach(n=>{let r=d.all(n,`[${se}]`),s=d.all(n,`[${this.binding(We)}]`);r.concat(n).forEach(o=>{let a=this.componentID(o);re(a)&&i.indexOf(a)===-1&&i.push(a)}),s.concat(n).forEach(o=>{let a=this.getHook(o);a&&this.destroyHook(a)})}),t&&this.maybePushComponentsDestroyed(i)}joinNewChildren(){d.findPhxChildren(this.el,this.id).forEach(e=>this.joinChild(e))}maybeRecoverForms(e,t){let i=this.binding("change"),n=this.root.formsForRecovery,r=document.createElement("template");r.innerHTML=e;let s=r.content.firstElementChild;s.id=this.id,s.setAttribute(de,this.root.id),s.setAttribute(Q,this.getSession()),s.setAttribute(ye,this.getStatic()),s.setAttribute(we,this.parent?this.parent.id:null);let o=d.all(r.content,"form").filter(a=>a.id&&n[a.id]).filter(a=>!this.pendingForms.has(a.id)).filter(a=>n[a.id].getAttribute(i)===a.getAttribute(i)).map(a=>[n[a.id],a]);if(o.length===0)return t();o.forEach(([a,l],h)=>{this.pendingForms.add(l.id),this.pushFormRecovery(a,l,r.content.firstElementChild,()=>{this.pendingForms.delete(l.id),h===o.length-1&&t()})})}getChildById(e){return this.root.children[this.id][e]}getDescendentByEl(e){return e.id===this.id?this:this.children[e.getAttribute(we)][e.id]}destroyDescendent(e){for(let t in this.root.children)for(let i in this.root.children[t])if(i===e)return this.root.children[t][i].destroy()}joinChild(e){if(!this.getChildById(e.id)){let i=new er(e,this.liveSocket,this);return this.root.children[this.id][i.id]=i,i.join(),this.childJoins++,!0}}isJoinPending(){return this.joinPending}ackJoin(e){this.childJoins--,this.childJoins===0&&(this.parent?this.parent.ackJoin(this):this.onAllChildJoinsComplete())}onAllChildJoinsComplete(){this.pendingForms.clear(),this.formsForRecovery={},this.joinCallback(()=>{this.pendingJoinOps.forEach(([e,t])=>{e.isDestroyed()||t()}),this.pendingJoinOps=[]})}update(e,t){if(this.isJoinPending()||this.liveSocket.hasPendingLink()&&this.root.isMain())return this.pendingDiffs.push({diff:e,events:t});this.rendered.mergeDiff(e);let i=!1;this.rendered.isComponentOnlyDiff(e)?this.liveSocket.time("component patch complete",()=>{d.findExistingParentCIDs(this.el,this.rendered.componentCIDs(e)).forEach(r=>{this.componentPatch(this.rendered.getComponent(e,r),r)&&(i=!0)})}):In(e)||this.liveSocket.time("full patch complete",()=>{let[n,r]=this.renderContainer(e,"update"),s=new xt(this,this.el,this.id,n,r,null);i=this.performPatch(s,!0)}),this.liveSocket.dispatchEvents(t),i&&this.joinNewChildren()}renderContainer(e,t){return this.liveSocket.time(`toString diff (${t})`,()=>{let i=this.el.tagName,n=e?this.rendered.componentCIDs(e):null,[r,s]=this.rendered.toString(n);return[`<${i}>${r}</${i}>`,s]})}componentPatch(e,t){if(In(e))return!1;let[i,n]=this.rendered.componentToString(t),r=new xt(this,this.el,this.id,i,n,t);return this.performPatch(r,!0)}getHook(e){return this.viewHooks[Ye.elementID(e)]}addHook(e){if(Ye.elementID(e)||!e.getAttribute)return;let t=e.getAttribute(`data-phx-${We}`)||e.getAttribute(this.binding(We));if(t&&!this.ownsElement(e))return;let i=this.liveSocket.getHookCallbacks(t);if(i){e.id||B(`no DOM ID for hook "${t}". Hooks require a unique ID on each element.`,e);let n=new Ye(this,e,i);return this.viewHooks[Ye.elementID(n.el)]=n,n}else t!==null&&B(`unknown hook found for "${t}"`,e)}destroyHook(e){e.__destroyed(),e.__cleanup__(),delete this.viewHooks[Ye.elementID(e.el)]}applyPendingUpdates(){this.pendingDiffs.forEach(({diff:e,events:t})=>this.update(e,t)),this.pendingDiffs=[],this.eachChild(e=>e.applyPendingUpdates())}eachChild(e){let t=this.root.children[this.id]||{};for(let i in t)e(this.getChildById(i))}onChannel(e,t){this.liveSocket.onChannel(this.channel,e,i=>{this.isJoinPending()?this.root.pendingJoinOps.push([this,()=>t(i)]):this.liveSocket.requestDOMUpdate(()=>t(i))})}bindChannel(){this.liveSocket.onChannel(this.channel,"diff",e=>{this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",e,({diff:t,events:i})=>this.update(t,i))})}),this.onChannel("redirect",({to:e,flash:t})=>this.onRedirect({to:e,flash:t})),this.onChannel("live_patch",e=>this.onLivePatch(e)),this.onChannel("live_redirect",e=>this.onLiveRedirect(e)),this.channel.onError(e=>this.onError(e)),this.channel.onClose(e=>this.onClose(e))}destroyAllChildren(){this.eachChild(e=>e.destroy())}onLiveRedirect(e){let{to:t,kind:i,flash:n}=e,r=this.expandURL(t);this.liveSocket.historyRedirect(r,i,n)}onLivePatch(e){let{to:t,kind:i}=e;this.href=this.expandURL(t),this.liveSocket.historyPatch(t,i)}expandURL(e){return e.startsWith("/")?`${window.location.protocol}//${window.location.host}${e}`:e}onRedirect({to:e,flash:t}){this.liveSocket.redirect(e,t)}isDestroyed(){return this.destroyed}joinDead(){this.isDead=!0}join(e){this.showLoader(this.liveSocket.loaderTimeout),this.bindChannel(),this.isMain()&&(this.stopCallback=this.liveSocket.withPageLoading({to:this.href,kind:"initial"})),this.joinCallback=t=>{t=t||function(){},e?e(this.joinCount,t):t()},this.liveSocket.wrapPush(this,{timeout:!1},()=>this.channel.join().receive("ok",t=>{this.isDestroyed()||this.liveSocket.requestDOMUpdate(()=>this.onJoin(t))}).receive("error",t=>!this.isDestroyed()&&this.onJoinError(t)).receive("timeout",()=>!this.isDestroyed()&&this.onJoinError({reason:"timeout"})))}onJoinError(e){if(e.reason==="reload"){this.log("error",()=>[`failed mount with ${e.status}. Falling back to page request`,e]),this.isMain()&&this.onRedirect({to:this.href});return}else if(e.reason==="unauthorized"||e.reason==="stale"){this.log("error",()=>["unauthorized live_redirect. Falling back to page request",e]),this.isMain()&&this.onRedirect({to:this.href});return}if((e.redirect||e.live_redirect)&&(this.joinPending=!1,this.channel.leave()),e.redirect)return this.onRedirect(e.redirect);if(e.live_redirect)return this.onLiveRedirect(e.live_redirect);this.displayError([Ve,_t,oi]),this.log("error",()=>["unable to join",e]),this.liveSocket.isConnected()&&this.liveSocket.reloadWithJitter(this)}onClose(e){if(!this.isDestroyed()){if(this.liveSocket.hasPendingLink()&&e!=="leave")return this.liveSocket.reloadWithJitter(this);this.destroyAllChildren(),this.liveSocket.dropActiveElement(this),document.activeElement&&document.activeElement.blur(),this.liveSocket.isUnloaded()&&this.showLoader(go)}}onError(e){this.onClose(e),this.liveSocket.isConnected()&&this.log("error",()=>["view crashed",e]),this.liveSocket.isUnloaded()||(this.liveSocket.isConnected()?this.displayError([Ve,_t,oi]):this.displayError([Ve,_t,wn]))}displayError(e){this.isMain()&&d.dispatchEvent(window,"phx:page-loading-start",{detail:{to:this.href,kind:"error"}}),this.showLoader(),this.setContainerClasses(...e),this.execAll(this.binding("disconnected"))}pushWithReply(e,t,i,n=function(){}){if(!this.isConnected())return;let[r,[s],o]=e?e():[null,[],{}],a=function(){};return(o.page_loading||s&&s.getAttribute(this.binding(pi))!==null)&&(a=this.liveSocket.withPageLoading({kind:"element",target:s})),typeof i.cid!="number"&&delete i.cid,this.liveSocket.wrapPush(this,{timeout:!0},()=>this.channel.push(t,i,vo).receive("ok",l=>{let h=c=>{l.redirect&&this.onRedirect(l.redirect),l.live_patch&&this.onLivePatch(l.live_patch),l.live_redirect&&this.onLiveRedirect(l.live_redirect),a(),n(l,c)};l.diff?this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",l.diff,({diff:c,reply:u,events:g})=>{r!==null&&this.undoRefs(r),this.update(c,g),h(u)})}):(r!==null&&this.undoRefs(r),h(null))}))}undoRefs(e,t){t=t?new Set(t):null,this.isConnected()&&d.all(document,`[${q}="${this.refSrc()}"]`,i=>{t&&!t.has(i)||this.undoElRef(i,e)})}undoElRef(e,t){new Oo(e).maybeUndo(t,n=>{let r=this.triggerBeforeUpdateHook(e,n);xt.patchWithClonedTree(e,n,this.liveSocket),d.all(e,`[${q}="${this.refSrc()}"]`,s=>this.undoElRef(s,t)),this.execNewMounted(e),r&&r.__updated()})}refSrc(){return this.el.id}putRef(e,t,i={}){let n=this.ref++,r=this.binding(Sn);if(i.loading){let s=d.all(document,i.loading).map(o=>({el:o,lock:!0,loading:!0}));e=e.concat(s)}for(let{el:s,lock:o,loading:a}of e){if(!o&&!a)throw new Error("putRef requires lock or loading");if(s.setAttribute(q,this.refSrc()),a&&s.setAttribute(Le,n),o&&s.setAttribute(J,n),!a||i.submitter&&!(s===i.submitter||s===i.form))continue;s.classList.add(`phx-${t}-loading`),s.dispatchEvent(new CustomEvent(`phx:${t}-loading`,{bubbles:!0,cancelable:!1}));let l=s.getAttribute(r);l!==null&&(s.getAttribute(Rt)||s.setAttribute(Rt,s.innerText),l!==""&&(s.innerText=l),s.setAttribute(Ie,s.getAttribute(Ie)||s.disabled),s.setAttribute("disabled",""))}return[n,e.map(({el:s})=>s),i]}componentID(e){let t=e.getAttribute&&e.getAttribute(se);return t?parseInt(t):null}targetComponentID(e,t,i={}){if(re(t))return t;let n=i.target||e.getAttribute(this.binding("target"));return re(n)?parseInt(n):t&&(n!==null||i.target)?this.closestComponentID(t):null}closestComponentID(e){return re(e)?e:e?ue(e.closest(`[${se}]`),t=>this.ownsElement(t)&&this.componentID(t)):null}pushHookEvent(e,t,i,n,r){if(!this.isConnected())return this.log("hook",()=>["unable to push hook event. LiveView not connected",i,n]),!1;let[s,o,a]=this.putRef([{el:e,loading:!0,lock:!0}],"hook");return this.pushWithReply(()=>[s,o,a],"event",{type:"hook",event:i,value:n,cid:this.closestComponentID(t)},(l,h)=>r(h,s)),s}extractMeta(e,t,i){let n=this.binding("value-");for(let r=0;r<e.attributes.length;r++){t||(t={});let s=e.attributes[r].name;s.startsWith(n)&&(t[s.replace(n,"")]=e.getAttribute(s))}if(e.value!==void 0&&!(e instanceof HTMLFormElement)&&(t||(t={}),t.value=e.value,e.tagName==="INPUT"&&Gn.indexOf(e.type)>=0&&!e.checked&&delete t.value),i){t||(t={});for(let r in i)t[r]=i[r]}return t}pushEvent(e,t,i,n,r,s={},o){this.pushWithReply(()=>this.putRef([{el:t,loading:!0,lock:!0}],e,s),"event",{type:e,event:n,value:this.extractMeta(t,r,s.value),cid:this.targetComponentID(t,i,s)},(a,l)=>o&&o(l))}pushFileProgress(e,t,i,n=function(){}){this.liveSocket.withinOwners(e.form,(r,s)=>{r.pushWithReply(null,"progress",{event:e.getAttribute(r.binding(fo)),ref:e.getAttribute(oe),entry_ref:t,progress:i,cid:r.targetComponentID(e.form,s)},n)})}pushInput(e,t,i,n,r,s){let o,a=re(i)?i:this.targetComponentID(e.form,t,r),l=()=>this.putRef([{el:e,loading:!0,lock:!0},{el:e.form,loading:!0,lock:!0}],"change",r),h,c=this.extractMeta(e.form);e instanceof HTMLButtonElement&&(c.submitter=e),e.getAttribute(this.binding("change"))?h=Et(e.form,N({_target:r._target},c),[e.name]):h=Et(e.form,N({_target:r._target},c)),d.isUploadInput(e)&&e.files&&e.files.length>0&&O.trackFiles(e,Array.from(e.files)),o=O.serializeUploads(e);let u={type:"form",event:n,value:h,uploads:o,cid:a};this.pushWithReply(l,"event",u,g=>{if(d.isUploadInput(e)&&d.isAutoUpload(e)){if(O.filesAwaitingPreflight(e).length>0){let[f,v]=l();this.undoRefs(f,[e.form]),this.uploadFiles(e.form,t,f,a,b=>{s&&s(g),this.triggerAwaitingSubmit(e.form),this.undoRefs(f)})}}else s&&s(g)})}triggerAwaitingSubmit(e){let t=this.getScheduledSubmit(e);if(t){let[i,n,r,s]=t;this.cancelSubmit(e),s()}}getScheduledSubmit(e){return this.formSubmits.find(([t,i,n,r])=>t.isSameNode(e))}scheduleSubmit(e,t,i,n){if(this.getScheduledSubmit(e))return!0;this.formSubmits.push([e,t,i,n])}cancelSubmit(e){this.formSubmits=this.formSubmits.filter(([t,i,n])=>t.isSameNode(e)?(this.undoRefs(i),!1):!0)}disableForm(e,t={}){let i=u=>!(Qe(u,`${this.binding(Ze)}=ignore`,u.form)||Qe(u,"data-phx-update=ignore",u.form)),n=u=>u.hasAttribute(this.binding(Sn)),r=u=>u.tagName=="BUTTON",s=u=>["INPUT","TEXTAREA","SELECT"].includes(u.tagName),o=Array.from(e.elements),a=o.filter(n),l=o.filter(r).filter(i),h=o.filter(s).filter(i);l.forEach(u=>{u.setAttribute(Ie,u.disabled),u.disabled=!0}),h.forEach(u=>{u.setAttribute(vi,u.readOnly),u.readOnly=!0,u.files&&(u.setAttribute(Ie,u.disabled),u.disabled=!0)}),e.setAttribute(this.binding(pi),"");let c=a.concat(l).concat(h).map(u=>({el:u,loading:!0,lock:!0}));return this.putRef([{el:e,loading:!0,lock:!1}].concat(c),"submit",t)}pushFormSubmit(e,t,i,n,r,s){let o=()=>this.disableForm(e,gn(N({},r),{form:e,submitter:n})),a=this.targetComponentID(e,t);if(O.hasUploadsInProgress(e)){let[l,h]=o(),c=()=>this.pushFormSubmit(e,t,i,n,r,s);return this.scheduleSubmit(e,l,r,c)}else if(O.inputsAwaitingPreflight(e).length>0){let[l,h]=o(),c=()=>[l,h,r];this.uploadFiles(e,t,l,a,u=>{if(O.inputsAwaitingPreflight(e).length>0)return this.undoRefs(l);let g=this.extractMeta(e),f=Et(e,N({submitter:n},g));this.pushWithReply(c,"event",{type:"form",event:i,value:f,cid:a},s)})}else if(!(e.hasAttribute(q)&&e.classList.contains("phx-submit-loading"))){let l=this.extractMeta(e),h=Et(e,N({submitter:n},l));this.pushWithReply(o,"event",{type:"form",event:i,value:h,cid:a},s)}}uploadFiles(e,t,i,n,r){let s=this.joinCount,o=O.activeFileInputs(e),a=o.length;o.forEach(l=>{let h=new O(l,this,()=>{a--,a===0&&r()}),c=h.entries().map(g=>g.toPreflightPayload());if(c.length===0){a--;return}let u={ref:l.getAttribute(oe),entries:c,cid:this.targetComponentID(l.form,t)};this.log("upload",()=>["sending preflight request",u]),this.pushWithReply(null,"allow_upload",u,g=>{if(this.log("upload",()=>["got preflight response",g]),h.entries().forEach(f=>{g.entries&&!g.entries[f.ref]&&this.handleFailedEntryPreflight(f.ref,"failed preflight",h)}),g.error||Object.keys(g.entries).length===0)this.undoRefs(i),(g.error||[]).map(([v,b])=>{this.handleFailedEntryPreflight(v,b,h)});else{let f=v=>{this.channel.onError(()=>{this.joinCount===s&&v()})};h.initAdapterUpload(g,f,this.liveSocket)}})})}handleFailedEntryPreflight(e,t,i){if(i.isAutoUpload()){let n=i.entries().find(r=>r.ref===e.toString());n&&n.cancel()}else i.entries().map(n=>n.cancel());this.log("upload",()=>[`error for entry ${e}`,t])}dispatchUploads(e,t,i){let n=this.targetCtxElement(e)||this.el,r=d.findUploadInputs(n).filter(s=>s.name===t);r.length===0?B(`no live file inputs found matching the name "${t}"`):r.length>1?B(`duplicate live file inputs found matching the name "${t}"`):d.dispatchEvent(r[0],Kn,{detail:{files:i}})}targetCtxElement(e){if(re(e)){let[t]=d.findComponentNodeList(this.el,e);return t}else return e||null}pushFormRecovery(e,t,i,n){let r=this.binding("change"),s=t.getAttribute(this.binding("target"))||t,o=t.getAttribute(this.binding(xn))||t.getAttribute(this.binding("change")),a=Array.from(e.elements).filter(c=>d.isFormInput(c)&&c.name&&!c.hasAttribute(r));if(a.length===0)return;a.forEach(c=>c.hasAttribute(oe)&&O.clearFiles(c));let l=a.find(c=>c.type!=="hidden")||a[0],h=0;this.withinTargets(s,(c,u)=>{let g=this.targetComponentID(t,u);h++,c.pushInput(l,u,g,o,{_target:l.name},()=>{h--,h===0&&n()})},i,i)}pushLinkPatch(e,t,i){let n=this.liveSocket.setPendingLink(e),r=t?()=>this.putRef([{el:t,loading:!0,lock:!0}],"click"):null,s=()=>this.liveSocket.redirect(window.location.href),o=e.startsWith("/")?`${location.protocol}//${location.host}${e}`:e,a=this.pushWithReply(r,"live_patch",{url:o},l=>{this.liveSocket.requestDOMUpdate(()=>{l.link_redirect?this.liveSocket.replaceMain(e,null,i,n):(this.liveSocket.commitPendingLink(n)&&(this.href=e),this.applyPendingUpdates(),i&&i(n))})});a?a.receive("timeout",s):s()}getFormsForRecovery(){if(this.joinCount===0)return{};let e=this.binding("change");return d.all(this.el,`form[${e}]`).filter(t=>t.id).filter(t=>t.elements.length>0).filter(t=>t.getAttribute(this.binding(xn))!=="ignore").map(t=>t.cloneNode(!0)).reduce((t,i)=>(t[i.id]=i,t),{})}maybePushComponentsDestroyed(e){let t=e.filter(i=>d.findComponentNodeList(this.el,i).length===0);t.length>0&&(t.forEach(i=>this.rendered.resetRender(i)),this.pushWithReply(null,"cids_will_destroy",{cids:t},()=>{this.liveSocket.requestDOMUpdate(()=>{let i=t.filter(n=>d.findComponentNodeList(this.el,n).length===0);i.length>0&&this.pushWithReply(null,"cids_destroyed",{cids:i},n=>{this.rendered.pruneCIDs(n.cids)})})}))}ownsElement(e){let t=e.closest(Me);return e.getAttribute(we)===this.id||t&&t.id===this.id||!t&&this.isDead}submitForm(e,t,i,n,r={}){d.putPrivate(e,et,!0),Array.from(e.elements).forEach(o=>d.putPrivate(o,et,!0)),this.liveSocket.blurActiveElement(this),this.pushFormSubmit(e,t,i,n,r,()=>{this.liveSocket.restorePreviouslyActiveFocus()})}binding(e){return this.liveSocket.binding(e)}};var tr=class{constructor(e,t,i={}){if(this.unloaded=!1,!t||t.constructor.name==="Object")throw new Error(`
      a phoenix Socket must be provided as the second argument to the LiveSocket constructor. For example:

          import {Socket} from "phoenix"
          import {LiveSocket} from "phoenix_live_view"
          let liveSocket = new LiveSocket("/live", Socket, {...})
      `);this.socket=new t(e,i),this.bindingPrefix=i.bindingPrefix||mo,this.opts=i,this.params=ze(i.params||{}),this.viewLogger=i.viewLogger,this.metadataCallbacks=i.metadata||{},this.defaults=Object.assign(kt(bo),i.defaults||{}),this.activeElement=null,this.prevActive=null,this.silenced=!1,this.main=null,this.outgoingMainEl=null,this.clickStartedAtTarget=null,this.linkRef=1,this.roots={},this.href=window.location.href,this.pendingLink=null,this.currentLocation=kt(window.location),this.hooks=i.hooks||{},this.uploaders=i.uploaders||{},this.loaderTimeout=i.loaderTimeout||po,this.reloadWithJitterTimer=null,this.maxReloads=i.maxReloads||eo,this.reloadJitterMin=i.reloadJitterMin||to,this.reloadJitterMax=i.reloadJitterMax||io,this.failsafeJitter=i.failsafeJitter||no,this.localStorage=i.localStorage||window.localStorage,this.sessionStorage=i.sessionStorage||window.sessionStorage,this.boundTopLevelEvents=!1,this.boundEventNames=new Set,this.serverCloseRef=null,this.domCallbacks=Object.assign({onPatchStart:ze(),onPatchEnd:ze(),onNodeAdded:ze(),onBeforeElUpdated:ze()},i.dom||{}),this.transitions=new Go,window.addEventListener("pagehide",n=>{this.unloaded=!0}),this.socket.onOpen(()=>{this.isUnloaded()&&window.location.reload()})}version(){return"1.0.0-rc.6"}isProfileEnabled(){return this.sessionStorage.getItem(hi)==="true"}isDebugEnabled(){return this.sessionStorage.getItem(yt)==="true"}isDebugDisabled(){return this.sessionStorage.getItem(yt)==="false"}enableDebug(){this.sessionStorage.setItem(yt,"true")}enableProfiling(){this.sessionStorage.setItem(hi,"true")}disableDebug(){this.sessionStorage.setItem(yt,"false")}disableProfiling(){this.sessionStorage.removeItem(hi)}enableLatencySim(e){this.enableDebug(),console.log("latency simulator enabled for the duration of this browser session. Call disableLatencySim() to disable"),this.sessionStorage.setItem(ci,e)}disableLatencySim(){this.sessionStorage.removeItem(ci)}getLatencySim(){let e=this.sessionStorage.getItem(ci);return e?parseInt(e):null}getSocket(){return this.socket}connect(){window.location.hostname==="localhost"&&!this.isDebugDisabled()&&this.enableDebug();let e=()=>{this.joinRootViews()?(this.bindTopLevelEvents(),this.socket.connect()):this.main?this.socket.connect():this.bindTopLevelEvents({dead:!0}),this.joinDeadView()};["complete","loaded","interactive"].indexOf(document.readyState)>=0?e():document.addEventListener("DOMContentLoaded",()=>e())}disconnect(e){clearTimeout(this.reloadWithJitterTimer),this.serverCloseRef&&(this.socket.off(this.serverCloseRef),this.serverCloseRef=null),this.socket.disconnect(e)}replaceTransport(e){clearTimeout(this.reloadWithJitterTimer),this.socket.replaceTransport(e),this.connect()}execJS(e,t,i=null){this.owner(e,n=>W.exec(i,t,n,e))}execJSHookPush(e,t,i,n){this.withinOwners(e,r=>{W.exec("hook",t,r,e,["push",{data:i,callback:n}])})}unload(){this.unloaded||(this.main&&this.isConnected()&&this.log(this.main,"socket",()=>["disconnect for page nav"]),this.unloaded=!0,this.destroyAllViews(),this.disconnect())}triggerDOM(e,t){this.domCallbacks[e](...t)}time(e,t){if(!this.isProfileEnabled()||!console.time)return t();console.time(e);let i=t();return console.timeEnd(e),i}log(e,t,i){if(this.viewLogger){let[n,r]=i();this.viewLogger(e,t,n,r)}else if(this.isDebugEnabled()){let[n,r]=i();Ao(e,t,n,r)}}requestDOMUpdate(e){this.transitions.after(e)}transition(e,t,i=function(){}){this.transitions.addTransition(e,t,i)}onChannel(e,t,i){e.on(t,n=>{let r=this.getLatencySim();r?setTimeout(()=>i(n),r):i(n)})}wrapPush(e,t,i){let n=this.getLatencySim(),r=e.joinCount;if(!n)return this.isConnected()&&t.timeout?i().receive("timeout",()=>{e.joinCount===r&&!e.isDestroyed()&&this.reloadWithJitter(e,()=>{this.log(e,"timeout",()=>["received timeout while communicating with server. Falling back to hard refresh for recovery"])})}):i();let s={receives:[],receive(o,a){this.receives.push([o,a])}};return setTimeout(()=>{e.isDestroyed()||s.receives.reduce((o,[a,l])=>o.receive(a,l),i())},n),s}reloadWithJitter(e,t){clearTimeout(this.reloadWithJitterTimer),this.disconnect();let i=this.reloadJitterMin,n=this.reloadJitterMax,r=Math.floor(Math.random()*(n-i+1))+i,s=Z.updateLocal(this.localStorage,window.location.pathname,Wn,0,o=>o+1);s>this.maxReloads&&(r=this.failsafeJitter),this.reloadWithJitterTimer=setTimeout(()=>{e.isDestroyed()||e.isConnected()||(e.destroy(),t?t():this.log(e,"join",()=>[`encountered ${s} consecutive reloads`]),s>this.maxReloads&&this.log(e,"join",()=>[`exceeded ${this.maxReloads} consecutive reloads. Entering failsafe mode`]),this.hasPendingLink()?window.location=this.pendingLink:window.location.reload())},r)}getHookCallbacks(e){return e&&e.startsWith("Phoenix.")?Po[e.split(".")[1]]:this.hooks[e]}isUnloaded(){return this.unloaded}isConnected(){return this.socket.isConnected()}getBindingPrefix(){return this.bindingPrefix}binding(e){return`${this.getBindingPrefix()}${e}`}channel(e,t){return this.socket.channel(e,t)}joinDeadView(){let e=document.body;if(e&&!this.isPhxView(e)&&!this.isPhxView(document.firstElementChild)){let t=this.newRootView(e);t.setHref(this.getHref()),t.joinDead(),this.main||(this.main=t),window.requestAnimationFrame(()=>t.execNewMounted())}}joinRootViews(){let e=!1;return d.all(document,`${Me}:not([${we}])`,t=>{if(!this.getRootById(t.id)){let i=this.newRootView(t);i.setHref(this.getHref()),i.join(),t.hasAttribute(_i)&&(this.main=i)}e=!0}),e}redirect(e,t){this.unload(),Z.redirect(e,t)}replaceMain(e,t,i=null,n=this.setPendingLink(e)){let r=this.currentLocation.href;this.outgoingMainEl=this.outgoingMainEl||this.main.el;let s=d.cloneNode(this.outgoingMainEl,"");this.main.showLoader(this.loaderTimeout),this.main.destroy(),this.main=this.newRootView(s,t,r),this.main.setRedirect(e),this.transitionRemoves(null,!0),this.main.join((o,a)=>{o===1&&this.commitPendingLink(n)&&this.requestDOMUpdate(()=>{d.findPhxSticky(document).forEach(l=>s.appendChild(l)),this.outgoingMainEl.replaceWith(s),this.outgoingMainEl=null,i&&i(n),a()})})}transitionRemoves(e,t){let i=this.binding("remove");if(e=e||d.all(document,`[${i}]`),t){let n=d.findPhxSticky(document)||[];e=e.filter(r=>!d.isChildOfAny(r,n))}e.forEach(n=>{for(let r of this.boundEventNames)n.addEventListener(r,s=>{s.preventDefault(),s.stopImmediatePropagation()},!0);this.execJS(n,n.getAttribute(i),"remove")})}isPhxView(e){return e.getAttribute&&e.getAttribute(Q)!==null}newRootView(e,t,i){let n=new er(e,this,null,t,i);return this.roots[n.id]=n,n}owner(e,t){let i=ue(e.closest(Me),n=>this.getViewByEl(n))||this.main;i&&t(i)}withinOwners(e,t){this.owner(e,i=>t(i,e))}getViewByEl(e){let t=e.getAttribute(de);return ue(this.getRootById(t),i=>i.getDescendentByEl(e))}getRootById(e){return this.roots[e]}destroyAllViews(){for(let e in this.roots)this.roots[e].destroy(),delete this.roots[e];this.main=null}destroyViewByEl(e){let t=this.getRootById(e.getAttribute(de));t&&t.id===e.id?(t.destroy(),delete this.roots[t.id]):t&&t.destroyDescendent(e.id)}setActiveElement(e){if(this.activeElement===e)return;this.activeElement=e;let t=()=>{e===this.activeElement&&(this.activeElement=null),e.removeEventListener("mouseup",this),e.removeEventListener("touchend",this)};e.addEventListener("mouseup",t),e.addEventListener("touchend",t)}getActiveElement(){return document.activeElement===document.body?this.activeElement||document.activeElement:document.activeElement||document.body}dropActiveElement(e){this.prevActive&&e.ownsElement(this.prevActive)&&(this.prevActive=null)}restorePreviouslyActiveFocus(){this.prevActive&&this.prevActive!==document.body&&this.prevActive.focus()}blurActiveElement(){this.prevActive=this.getActiveElement(),this.prevActive!==document.body&&this.prevActive.blur()}bindTopLevelEvents({dead:e}={}){this.boundTopLevelEvents||(this.boundTopLevelEvents=!0,this.serverCloseRef=this.socket.onClose(t=>{if(t&&t.code===1e3&&this.main)return this.reloadWithJitter(this.main)}),document.body.addEventListener("click",function(){}),window.addEventListener("pageshow",t=>{t.persisted&&(this.getSocket().disconnect(),this.withPageLoading({to:window.location.href,kind:"redirect"}),window.location.reload())},!0),e||this.bindNav(),this.bindClicks(),e||this.bindForms(),this.bind({keyup:"keyup",keydown:"keydown"},(t,i,n,r,s,o)=>{let a=r.getAttribute(this.binding(uo)),l=t.key&&t.key.toLowerCase();if(a&&a.toLowerCase()!==l)return;let h=N({key:t.key},this.eventMeta(i,t,r));W.exec(i,s,n,r,["push",{data:h}])}),this.bind({blur:"focusout",focus:"focusin"},(t,i,n,r,s,o)=>{if(!o){let a=N({key:t.key},this.eventMeta(i,t,r));W.exec(i,s,n,r,["push",{data:a}])}}),this.bind({blur:"blur",focus:"focus"},(t,i,n,r,s,o)=>{if(o==="window"){let a=this.eventMeta(i,t,r);W.exec(i,s,n,r,["push",{data:a}])}}),this.on("dragover",t=>t.preventDefault()),this.on("drop",t=>{t.preventDefault();let i=ue(Qe(t.target,this.binding(bn)),s=>s.getAttribute(this.binding(bn))),n=i&&document.getElementById(i),r=Array.from(t.dataTransfer.files||[]);!n||n.disabled||r.length===0||!(n.files instanceof FileList)||(O.trackFiles(n,r,t.dataTransfer),n.dispatchEvent(new Event("input",{bubbles:!0})))}),this.on(Kn,t=>{let i=t.target;if(!d.isUploadInput(i))return;let n=Array.from(t.detail.files||[]).filter(r=>r instanceof File||r instanceof Blob);O.trackFiles(i,n),i.dispatchEvent(new Event("input",{bubbles:!0}))}))}eventMeta(e,t,i){let n=this.metadataCallbacks[e];return n?n(t,i):{}}setPendingLink(e){return this.linkRef++,this.pendingLink=e,this.linkRef}commitPendingLink(e){return this.linkRef!==e?!1:(this.href=this.pendingLink,this.pendingLink=null,!0)}getHref(){return this.href}hasPendingLink(){return!!this.pendingLink}bind(e,t){for(let i in e){let n=e[i];this.on(n,r=>{let s=this.binding(i),o=this.binding(`window-${i}`),a=r.target.getAttribute&&r.target.getAttribute(s);a?this.debounce(r.target,r,n,()=>{this.withinOwners(r.target,l=>{t(r,i,l,r.target,a,null)})}):d.all(document,`[${o}]`,l=>{let h=l.getAttribute(o);this.debounce(l,r,n,()=>{this.withinOwners(l,c=>{t(r,i,c,l,h,"window")})})})})}}bindClicks(){this.on("mousedown",e=>this.clickStartedAtTarget=e.target),this.bindClick("click","click")}bindClick(e,t){let i=this.binding(t);window.addEventListener(e,n=>{let r=null;n.detail===0&&(this.clickStartedAtTarget=n.target);let s=this.clickStartedAtTarget||n.target;r=Qe(s,i),this.dispatchClickAway(n,s),this.clickStartedAtTarget=null;let o=r&&r.getAttribute(i);if(!o){d.isNewPageClick(n,window.location)&&this.unload();return}r.getAttribute("href")==="#"&&n.preventDefault(),!r.hasAttribute(q)&&this.debounce(r,n,"click",()=>{this.withinOwners(r,a=>{W.exec("click",o,a,r,["push",{data:this.eventMeta("click",n,r)}])})})},!1)}dispatchClickAway(e,t){let i=this.binding("click-away");d.all(document,`[${i}]`,n=>{n.isSameNode(t)||n.contains(t)||this.withinOwners(n,r=>{let s=n.getAttribute(i);W.isVisible(n)&&W.isInViewport(n)&&W.exec("click",s,r,n,["push",{data:this.eventMeta("click",e,e.target)}])})})}bindNav(){if(!Z.canPushState())return;history.scrollRestoration&&(history.scrollRestoration="manual");let e=null;window.addEventListener("scroll",t=>{clearTimeout(e),e=setTimeout(()=>{Z.updateCurrentState(i=>Object.assign(i,{scroll:window.scrollY}))},100)}),window.addEventListener("popstate",t=>{if(!this.registerNewLocation(window.location))return;let{type:i,id:n,root:r,scroll:s}=t.state||{},o=window.location.href;d.dispatchEvent(window,"phx:navigate",{detail:{href:o,patch:i==="patch",pop:!0}}),this.requestDOMUpdate(()=>{this.main.isConnected()&&i==="patch"&&n===this.main.id?this.main.pushLinkPatch(o,null,()=>{this.maybeScroll(s)}):this.replaceMain(o,null,()=>{r&&this.replaceRootHistory(),this.maybeScroll(s)})})},!1),window.addEventListener("click",t=>{let i=Qe(t.target,si),n=i&&i.getAttribute(si);if(!n||!this.isConnected()||!this.main||d.wantsNewTab(t))return;let r=i.href instanceof SVGAnimatedString?i.href.baseVal:i.href,s=i.getAttribute(so);t.preventDefault(),t.stopImmediatePropagation(),this.pendingLink!==r&&this.requestDOMUpdate(()=>{if(n==="patch")this.pushHistoryPatch(r,s,i);else if(n==="redirect")this.historyRedirect(r,s);else throw new Error(`expected ${si} to be "patch" or "redirect", got: ${n}`);let o=i.getAttribute(this.binding("click"));o&&this.requestDOMUpdate(()=>this.execJS(i,o,"click"))})},!1)}maybeScroll(e){typeof e=="number"&&requestAnimationFrame(()=>{window.scrollTo(0,e)})}dispatchEvent(e,t={}){d.dispatchEvent(window,`phx:${e}`,{detail:t})}dispatchEvents(e){e.forEach(([t,i])=>this.dispatchEvent(t,i))}withPageLoading(e,t){d.dispatchEvent(window,"phx:page-loading-start",{detail:e});let i=()=>d.dispatchEvent(window,"phx:page-loading-stop",{detail:e});return t?t(i):i}pushHistoryPatch(e,t,i){if(!this.isConnected()||!this.main.isMain())return Z.redirect(e);this.withPageLoading({to:e,kind:"patch"},n=>{this.main.pushLinkPatch(e,i,r=>{this.historyPatch(e,t,r),n()})})}historyPatch(e,t,i=this.setPendingLink(e)){this.commitPendingLink(i)&&(Z.pushState(t,{type:"patch",id:this.main.id},e),d.dispatchEvent(window,"phx:navigate",{detail:{patch:!0,href:e,pop:!1}}),this.registerNewLocation(window.location))}historyRedirect(e,t,i){if(!this.isConnected()||!this.main.isMain())return Z.redirect(e,i);if(/^\/$|^\/[^\/]+.*$/.test(e)){let{protocol:r,host:s}=window.location;e=`${r}//${s}${e}`}let n=window.scrollY;this.withPageLoading({to:e,kind:"redirect"},r=>{this.replaceMain(e,i,s=>{s===this.linkRef&&(Z.pushState(t,{type:"redirect",id:this.main.id,scroll:n},e),d.dispatchEvent(window,"phx:navigate",{detail:{href:e,patch:!1,pop:!1}}),this.registerNewLocation(window.location)),r()})})}replaceRootHistory(){Z.pushState("replace",{root:!0,type:"patch",id:this.main.id})}registerNewLocation(e){let{pathname:t,search:i}=this.currentLocation;return t+i===e.pathname+e.search?!1:(this.currentLocation=kt(e),!0)}bindForms(){let e=0,t=!1;this.on("submit",i=>{let n=i.target.getAttribute(this.binding("submit")),r=i.target.getAttribute(this.binding("change"));!t&&r&&!n&&(t=!0,i.preventDefault(),this.withinOwners(i.target,s=>{s.disableForm(i.target),window.requestAnimationFrame(()=>{d.isUnloadableFormSubmit(i)&&this.unload(),i.target.submit()})}))}),this.on("submit",i=>{let n=i.target.getAttribute(this.binding("submit"));if(!n){d.isUnloadableFormSubmit(i)&&this.unload();return}i.preventDefault(),i.target.disabled=!0,this.withinOwners(i.target,r=>{W.exec("submit",n,r,i.target,["push",{submitter:i.submitter}])})});for(let i of["change","input"])this.on(i,n=>{let r=this.binding("change"),s=n.target,o=s.getAttribute(r),a=s.form&&s.form.getAttribute(r),l=o||a;if(!l||s.type==="number"&&s.validity&&s.validity.badInput)return;let h=o?s:s.form,c=e;e++;let{at:u,type:g}=d.private(s,"prev-iteration")||{};u===c-1&&i==="change"&&g==="input"||(d.putPrivate(s,"prev-iteration",{at:c,type:i}),this.debounce(s,n,i,()=>{this.withinOwners(h,f=>{d.putPrivate(s,Tt,!0),d.isTextualInput(s)||this.setActiveElement(s),W.exec("change",l,f,s,["push",{_target:n.target.name,dispatcher:h}])})}))});this.on("reset",i=>{let n=i.target;d.resetForm(n);let r=Array.from(n.elements).find(s=>s.type==="reset");r&&window.requestAnimationFrame(()=>{r.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!1}))})})}debounce(e,t,i,n){if(i==="blur"||i==="focusout")return n();let r=this.binding(ho),s=this.binding(co),o=this.defaults.debounce.toString(),a=this.defaults.throttle.toString();this.withinOwners(e,l=>{let h=()=>!l.isDestroyed()&&document.body.contains(e);d.debounce(e,t,r,o,s,a,h,()=>{n()})})}silenceEvents(e){this.silenced=!0,e(),this.silenced=!1}on(e,t){this.boundEventNames.add(e),window.addEventListener(e,i=>{this.silenced||t(i)})}},Go=class{constructor(){this.transitions=new Set,this.pendingOps=[]}reset(){this.transitions.forEach(e=>{clearTimeout(e),this.transitions.delete(e)}),this.flushPendingOps()}after(e){this.size()===0?e():this.pushPendingOp(e)}addTransition(e,t,i){t();let n=setTimeout(()=>{this.transitions.delete(n),i(),this.flushPendingOps()},e);this.transitions.add(n)}pushPendingOp(e){this.pendingOps.push(e)}size(){return this.transitions.size}flushPendingOps(){if(this.size()>0)return;let e=this.pendingOps.shift();e&&(e(),this.flushPendingOps())}};var Bh=qs(nr());var ki=!1,Ti=!1,xe=[],Ri=-1;function Yo(e){Zo(e)}function Zo(e){xe.includes(e)||xe.push(e),Qo()}function br(e){let t=xe.indexOf(e);t!==-1&&t>Ri&&xe.splice(t,1)}function Qo(){!Ti&&!ki&&(ki=!0,queueMicrotask(ea))}function ea(){ki=!1,Ti=!0;for(let e=0;e<xe.length;e++)xe[e](),Ri=e;xe.length=0,Ri=-1,Ti=!1}var He,Te,Fe,_r,Pi=!0;function ta(e){Pi=!1,e(),Pi=!0}function ia(e){He=e.reactive,Fe=e.release,Te=t=>e.effect(t,{scheduler:i=>{Pi?Yo(i):i()}}),_r=e.raw}function rr(e){Te=e}function na(e){let t=()=>{};return[n=>{let r=Te(n);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(s=>s())}),e._x_effects.add(r),t=()=>{r!==void 0&&(e._x_effects.delete(r),Fe(r))},r},()=>{t()}]}function yr(e,t){let i=!0,n,r=Te(()=>{let s=e();JSON.stringify(s),i?n=s:queueMicrotask(()=>{t(s,n),n=s}),i=!1});return()=>Fe(r)}var wr=[],Ar=[],Sr=[];function ra(e){Sr.push(e)}function Vi(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,Ar.push(t))}function xr(e){wr.push(e)}function Er(e,t,i){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(i)}function Cr(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([i,n])=>{(t===void 0||t.includes(i))&&(n.forEach(r=>r()),delete e._x_attributeCleanups[i])})}function sa(e){if(e._x_cleanups)for(;e._x_cleanups.length;)e._x_cleanups.pop()()}var Wi=new MutationObserver(Xi),qi=!1;function Ki(){Wi.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),qi=!0}function kr(){oa(),Wi.disconnect(),qi=!1}var tt=[];function oa(){let e=Wi.takeRecords();tt.push(()=>e.length>0&&Xi(e));let t=tt.length;queueMicrotask(()=>{if(tt.length===t)for(;tt.length>0;)tt.shift()()})}function L(e){if(!qi)return e();kr();let t=e();return Ki(),t}var zi=!1,Ht=[];function aa(){zi=!0}function la(){zi=!1,Xi(Ht),Ht=[]}function Xi(e){if(zi){Ht=Ht.concat(e);return}let t=new Set,i=new Set,n=new Map,r=new Map;for(let s=0;s<e.length;s++)if(!e[s].target._x_ignoreMutationObserver&&(e[s].type==="childList"&&(e[s].addedNodes.forEach(o=>o.nodeType===1&&t.add(o)),e[s].removedNodes.forEach(o=>o.nodeType===1&&i.add(o))),e[s].type==="attributes")){let o=e[s].target,a=e[s].attributeName,l=e[s].oldValue,h=()=>{n.has(o)||n.set(o,[]),n.get(o).push({name:a,value:o.getAttribute(a)})},c=()=>{r.has(o)||r.set(o,[]),r.get(o).push(a)};o.hasAttribute(a)&&l===null?h():o.hasAttribute(a)?(c(),h()):c()}r.forEach((s,o)=>{Cr(o,s)}),n.forEach((s,o)=>{wr.forEach(a=>a(o,s))});for(let s of i)t.has(s)||Ar.forEach(o=>o(s));t.forEach(s=>{s._x_ignoreSelf=!0,s._x_ignore=!0});for(let s of t)i.has(s)||s.isConnected&&(delete s._x_ignoreSelf,delete s._x_ignore,Sr.forEach(o=>o(s)),s._x_ignore=!0,s._x_ignoreSelf=!0);t.forEach(s=>{delete s._x_ignoreSelf,delete s._x_ignore}),t=null,i=null,n=null,r=null}function Tr(e){return ht(Ne(e))}function lt(e,t,i){return e._x_dataStack=[t,...Ne(i||e)],()=>{e._x_dataStack=e._x_dataStack.filter(n=>n!==t)}}function Ne(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?Ne(e.host):e.parentNode?Ne(e.parentNode):[]}function ht(e){return new Proxy({objects:e},ha)}var ha={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(i=>Object.prototype.hasOwnProperty.call(i,t)||Reflect.has(i,t))},get({objects:e},t,i){return t=="toJSON"?ca:Reflect.get(e.find(n=>Reflect.has(n,t))||{},t,i)},set({objects:e},t,i,n){let r=e.find(o=>Object.prototype.hasOwnProperty.call(o,t))||e[e.length-1],s=Object.getOwnPropertyDescriptor(r,t);return s!=null&&s.set&&(s!=null&&s.get)?s.set.call(n,i)||!0:Reflect.set(r,t,i)}};function ca(){return Reflect.ownKeys(this).reduce((t,i)=>(t[i]=Reflect.get(this,i),t),{})}function Rr(e){let t=n=>typeof n=="object"&&!Array.isArray(n)&&n!==null,i=(n,r="")=>{Object.entries(Object.getOwnPropertyDescriptors(n)).forEach(([s,{value:o,enumerable:a}])=>{if(a===!1||o===void 0||typeof o=="object"&&o!==null&&o.__v_skip)return;let l=r===""?s:`${r}.${s}`;typeof o=="object"&&o!==null&&o._x_interceptor?n[s]=o.initialize(e,l,s):t(o)&&o!==n&&!(o instanceof Element)&&i(o,l)})};return i(e)}function Pr(e,t=()=>{}){let i={initialValue:void 0,_x_interceptor:!0,initialize(n,r,s){return e(this.initialValue,()=>ua(n,r),o=>Oi(n,r,o),r,s)}};return t(i),n=>{if(typeof n=="object"&&n!==null&&n._x_interceptor){let r=i.initialize.bind(i);i.initialize=(s,o,a)=>{let l=n.initialize(s,o,a);return i.initialValue=l,r(s,o,a)}}else i.initialValue=n;return i}}function ua(e,t){return t.split(".").reduce((i,n)=>i[n],e)}function Oi(e,t,i){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=i;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),Oi(e[t[0]],t.slice(1),i)}}var Or={};function z(e,t){Or[e]=t}function Ii(e,t){return Object.entries(Or).forEach(([i,n])=>{let r=null;function s(){if(r)return r;{let[o,a]=$r(t);return r=N({interceptor:Pr},o),Vi(t,a),r}}Object.defineProperty(e,`$${i}`,{get(){return n(t,s())},enumerable:!1})}),e}function da(e,t,i,...n){try{return i(...n)}catch(r){at(r,e,t)}}function at(e,t,i=void 0){e=Object.assign(e!=null?e:{message:"No error message given."},{el:t,expression:i}),console.warn(`Alpine Expression Error: ${e.message}

${i?'Expression: "'+i+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var Nt=!0;function Ir(e){let t=Nt;Nt=!1;let i=e();return Nt=t,i}function Ee(e,t,i={}){let n;return H(e,t)(r=>n=r,i),n}function H(...e){return Lr(...e)}var Lr=Dr;function fa(e){Lr=e}function Dr(e,t){let i={};Ii(i,e);let n=[i,...Ne(e)],r=typeof t=="function"?pa(n,t):ma(n,t,e);return da.bind(null,e,t,r)}function pa(e,t){return(i=()=>{},{scope:n={},params:r=[]}={})=>{let s=t.apply(ht([n,...e]),r);Ft(i,s)}}var Ai={};function ga(e,t){if(Ai[e])return Ai[e];let i=Object.getPrototypeOf(async function(){}).constructor,n=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,s=(()=>{try{let o=new i(["__self","scope"],`with (scope) { __self.result = ${n} }; __self.finished = true; return __self.result;`);return Object.defineProperty(o,"name",{value:`[Alpine] ${e}`}),o}catch(o){return at(o,t,e),Promise.resolve()}})();return Ai[e]=s,s}function ma(e,t,i){let n=ga(t,i);return(r=()=>{},{scope:s={},params:o=[]}={})=>{n.result=void 0,n.finished=!1;let a=ht([s,...e]);if(typeof n=="function"){let l=n(n,a).catch(h=>at(h,i,t));n.finished?(Ft(r,n.result,a,o,i),n.result=void 0):l.then(h=>{Ft(r,h,a,o,i)}).catch(h=>at(h,i,t)).finally(()=>n.result=void 0)}}}function Ft(e,t,i,n,r){if(Nt&&typeof t=="function"){let s=t.apply(i,n);s instanceof Promise?s.then(o=>Ft(e,o,i,n)).catch(o=>at(o,r,t)):e(s)}else typeof t=="object"&&t instanceof Promise?t.then(s=>e(s)):e(t)}var Gi="x-";function je(e=""){return Gi+e}function va(e){Gi=e}var jt={};function I(e,t){return jt[e]=t,{before(i){if(!jt[i]){console.warn(String.raw`Cannot find directive \`${i}\`. \`${e}\` will use the default order of execution`);return}let n=Se.indexOf(i);Se.splice(n>=0?n:Se.indexOf("DEFAULT"),0,e)}}}function ba(e){return Object.keys(jt).includes(e)}function Yi(e,t,i){if(t=Array.from(t),e._x_virtualDirectives){let s=Object.entries(e._x_virtualDirectives).map(([a,l])=>({name:a,value:l})),o=Mr(s);s=s.map(a=>o.find(l=>l.name===a.name)?{name:`x-bind:${a.name}`,value:`"${a.value}"`}:a),t=t.concat(s)}let n={};return t.map(jr((s,o)=>n[s]=o)).filter(Br).map(wa(n,i)).sort(Aa).map(s=>ya(e,s))}function Mr(e){return Array.from(e).map(jr()).filter(t=>!Br(t))}var Li=!1,rt=new Map,Nr=Symbol();function _a(e){Li=!0;let t=Symbol();Nr=t,rt.set(t,[]);let i=()=>{for(;rt.get(t).length;)rt.get(t).shift()();rt.delete(t)},n=()=>{Li=!1,i()};e(i),n()}function $r(e){let t=[],i=a=>t.push(a),[n,r]=na(e);return t.push(r),[{Alpine:ut,effect:n,cleanup:i,evaluateLater:H.bind(H,e),evaluate:Ee.bind(Ee,e)},()=>t.forEach(a=>a())]}function ya(e,t){let i=()=>{},n=jt[t.type]||i,[r,s]=$r(e);Er(e,t.original,s);let o=()=>{e._x_ignore||e._x_ignoreSelf||(n.inline&&n.inline(e,t,r),n=n.bind(n,e,t,r),Li?rt.get(Nr).push(n):n())};return o.runCleanups=s,o}var Hr=(e,t)=>({name:i,value:n})=>(i.startsWith(e)&&(i=i.replace(e,t)),{name:i,value:n}),Fr=e=>e;function jr(e=()=>{}){return({name:t,value:i})=>{let{name:n,value:r}=Ur.reduce((s,o)=>o(s),{name:t,value:i});return n!==t&&e(n,t),{name:n,value:r}}}var Ur=[];function Zi(e){Ur.push(e)}function Br({name:e}){return Jr().test(e)}var Jr=()=>new RegExp(`^${Gi}([^:^.]+)\\b`);function wa(e,t){return({name:i,value:n})=>{let r=i.match(Jr()),s=i.match(/:([a-zA-Z0-9\-_:]+)/),o=i.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],a=t||e[i]||i;return{type:r?r[1]:null,value:s?s[1]:null,modifiers:o.map(l=>l.replace(".","")),expression:n,original:a}}}var Di="DEFAULT",Se=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",Di,"teleport"];function Aa(e,t){let i=Se.indexOf(e.type)===-1?Di:e.type,n=Se.indexOf(t.type)===-1?Di:t.type;return Se.indexOf(i)-Se.indexOf(n)}function st(e,t,i={}){e.dispatchEvent(new CustomEvent(t,{detail:i,bubbles:!0,composed:!0,cancelable:!0}))}function pe(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(r=>pe(r,t));return}let i=!1;if(t(e,()=>i=!0),i)return;let n=e.firstElementChild;for(;n;)pe(n,t,!1),n=n.nextElementSibling}function V(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var sr=!1;function Sa(){sr&&V("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),sr=!0,document.body||V("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),st(document,"alpine:init"),st(document,"alpine:initializing"),Ki(),ra(t=>ae(t,pe)),Vi(t=>Gr(t)),xr((t,i)=>{Yi(t,i).forEach(n=>n())});let e=t=>!Bt(t.parentElement,!0);Array.from(document.querySelectorAll(qr().join(","))).filter(e).forEach(t=>{ae(t)}),st(document,"alpine:initialized"),setTimeout(()=>{Ca()})}var Qi=[],Vr=[];function Wr(){return Qi.map(e=>e())}function qr(){return Qi.concat(Vr).map(e=>e())}function Kr(e){Qi.push(e)}function zr(e){Vr.push(e)}function Bt(e,t=!1){return ct(e,i=>{if((t?qr():Wr()).some(r=>i.matches(r)))return!0})}function ct(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return ct(e.parentElement,t)}}function xa(e){return Wr().some(t=>e.matches(t))}var Xr=[];function Ea(e){Xr.push(e)}function ae(e,t=pe,i=()=>{}){_a(()=>{t(e,(n,r)=>{i(n,r),Xr.forEach(s=>s(n,r)),Yi(n,n.attributes).forEach(s=>s()),n._x_ignore&&r()})})}function Gr(e,t=pe){t(e,i=>{Cr(i),sa(i)})}function Ca(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,i,n])=>{ba(i)||n.some(r=>{if(document.querySelector(r))return V(`found "${r}", but missing ${t} plugin`),!0})})}var Mi=[],en=!1;function tn(e=()=>{}){return queueMicrotask(()=>{en||setTimeout(()=>{Ni()})}),new Promise(t=>{Mi.push(()=>{e(),t()})})}function Ni(){for(en=!1;Mi.length;)Mi.shift()()}function ka(){en=!0}function nn(e,t){return Array.isArray(t)?or(e,t.join(" ")):typeof t=="object"&&t!==null?Ta(e,t):typeof t=="function"?nn(e,t()):or(e,t)}function or(e,t){let i=s=>s.split(" ").filter(Boolean),n=s=>s.split(" ").filter(o=>!e.classList.contains(o)).filter(Boolean),r=s=>(e.classList.add(...s),()=>{e.classList.remove(...s)});return t=t===!0?t="":t||"",r(n(t))}function Ta(e,t){let i=a=>a.split(" ").filter(Boolean),n=Object.entries(t).flatMap(([a,l])=>l?i(a):!1).filter(Boolean),r=Object.entries(t).flatMap(([a,l])=>l?!1:i(a)).filter(Boolean),s=[],o=[];return r.forEach(a=>{e.classList.contains(a)&&(e.classList.remove(a),o.push(a))}),n.forEach(a=>{e.classList.contains(a)||(e.classList.add(a),s.push(a))}),()=>{o.forEach(a=>e.classList.add(a)),s.forEach(a=>e.classList.remove(a))}}function Jt(e,t){return typeof t=="object"&&t!==null?Ra(e,t):Pa(e,t)}function Ra(e,t){let i={};return Object.entries(t).forEach(([n,r])=>{i[n]=e.style[n],n.startsWith("--")||(n=Oa(n)),e.style.setProperty(n,r)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{Jt(e,i)}}function Pa(e,t){let i=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",i||"")}}function Oa(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function $i(e,t=()=>{}){let i=!1;return function(){i?t.apply(this,arguments):(i=!0,e.apply(this,arguments))}}I("transition",(e,{value:t,modifiers:i,expression:n},{evaluate:r})=>{typeof n=="function"&&(n=r(n)),n!==!1&&(!n||typeof n=="boolean"?La(e,i,t):Ia(e,n,t))});function Ia(e,t,i){Yr(e,nn,""),{enter:r=>{e._x_transition.enter.during=r},"enter-start":r=>{e._x_transition.enter.start=r},"enter-end":r=>{e._x_transition.enter.end=r},leave:r=>{e._x_transition.leave.during=r},"leave-start":r=>{e._x_transition.leave.start=r},"leave-end":r=>{e._x_transition.leave.end=r}}[i](t)}function La(e,t,i){Yr(e,Jt);let n=!t.includes("in")&&!t.includes("out")&&!i,r=n||t.includes("in")||["enter"].includes(i),s=n||t.includes("out")||["leave"].includes(i);t.includes("in")&&!n&&(t=t.filter((w,_)=>_<t.indexOf("out"))),t.includes("out")&&!n&&(t=t.filter((w,_)=>_>t.indexOf("out")));let o=!t.includes("opacity")&&!t.includes("scale"),a=o||t.includes("opacity"),l=o||t.includes("scale"),h=a?0:1,c=l?it(t,"scale",95)/100:1,u=it(t,"delay",0)/1e3,g=it(t,"origin","center"),f="opacity, transform",v=it(t,"duration",150)/1e3,b=it(t,"duration",75)/1e3,m="cubic-bezier(0.4, 0.0, 0.2, 1)";r&&(e._x_transition.enter.during={transformOrigin:g,transitionDelay:`${u}s`,transitionProperty:f,transitionDuration:`${v}s`,transitionTimingFunction:m},e._x_transition.enter.start={opacity:h,transform:`scale(${c})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),s&&(e._x_transition.leave.during={transformOrigin:g,transitionDelay:`${u}s`,transitionProperty:f,transitionDuration:`${b}s`,transitionTimingFunction:m},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:h,transform:`scale(${c})`})}function Yr(e,t,i={}){e._x_transition||(e._x_transition={enter:{during:i,start:i,end:i},leave:{during:i,start:i,end:i},in(n=()=>{},r=()=>{}){Hi(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},n,r)},out(n=()=>{},r=()=>{}){Hi(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},n,r)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,i,n){let r=document.visibilityState==="visible"?requestAnimationFrame:setTimeout,s=()=>r(i);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(i):s():e._x_transition?e._x_transition.in(i):s();return}e._x_hidePromise=e._x_transition?new Promise((o,a)=>{e._x_transition.out(()=>{},()=>o(n)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>a({isFromCancelledTransition:!0}))}):Promise.resolve(n),queueMicrotask(()=>{let o=Zr(e);o?(o._x_hideChildren||(o._x_hideChildren=[]),o._x_hideChildren.push(e)):r(()=>{let a=l=>{let h=Promise.all([l._x_hidePromise,...(l._x_hideChildren||[]).map(a)]).then(([c])=>c==null?void 0:c());return delete l._x_hidePromise,delete l._x_hideChildren,h};a(e).catch(l=>{if(!l.isFromCancelledTransition)throw l})})})};function Zr(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:Zr(t)}function Hi(e,t,{during:i,start:n,end:r}={},s=()=>{},o=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(i).length===0&&Object.keys(n).length===0&&Object.keys(r).length===0){s(),o();return}let a,l,h;Da(e,{start(){a=t(e,n)},during(){l=t(e,i)},before:s,end(){a(),h=t(e,r)},after:o,cleanup(){l(),h()}})}function Da(e,t){let i,n,r,s=$i(()=>{L(()=>{i=!0,n||t.before(),r||(t.end(),Ni()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(o){this.beforeCancels.push(o)},cancel:$i(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();s()}),finish:s},L(()=>{t.start(),t.during()}),ka(),requestAnimationFrame(()=>{if(i)return;let o=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,a=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;o===0&&(o=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),L(()=>{t.before()}),n=!0,requestAnimationFrame(()=>{i||(L(()=>{t.end()}),Ni(),setTimeout(e._x_transitioning.finish,o+a),r=!0)})})}function it(e,t,i){if(e.indexOf(t)===-1)return i;let n=e[e.indexOf(t)+1];if(!n||t==="scale"&&isNaN(n))return i;if(t==="duration"||t==="delay"){let r=n.match(/([0-9]+)ms/);if(r)return r[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[n,e[e.indexOf(t)+2]].join(" "):n}var ge=!1;function ve(e,t=()=>{}){return(...i)=>ge?t(...i):e(...i)}function Ma(e){return(...t)=>ge&&e(...t)}var Qr=[];function Vt(e){Qr.push(e)}function Na(e,t){Qr.forEach(i=>i(e,t)),ge=!0,es(()=>{ae(t,(i,n)=>{n(i,()=>{})})}),ge=!1}var Fi=!1;function $a(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),ge=!0,Fi=!0,es(()=>{Ha(t)}),ge=!1,Fi=!1}function Ha(e){let t=!1;ae(e,(n,r)=>{pe(n,(s,o)=>{if(t&&xa(s))return o();t=!0,r(s,o)})})}function es(e){let t=Te;rr((i,n)=>{let r=t(i);return Fe(r),()=>{}}),e(),rr(t)}function ts(e,t,i,n=[]){switch(e._x_bindings||(e._x_bindings=He({})),e._x_bindings[t]=i,t=n.includes("camel")?qa(t):t,t){case"value":Fa(e,i);break;case"style":Ua(e,i);break;case"class":ja(e,i);break;case"selected":case"checked":Ba(e,t,i);break;default:is(e,t,i);break}}function Fa(e,t){if(e.type==="radio")e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=$t(e.value)===t:e.checked=ar(e.value,t));else if(e.type==="checkbox")Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(i=>ar(i,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")Wa(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function ja(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=nn(e,t)}function Ua(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=Jt(e,t)}function Ba(e,t,i){is(e,t,i),Va(e,t,i)}function is(e,t,i){[null,void 0,!1].includes(i)&&Ka(t)?e.removeAttribute(t):(ns(t)&&(i=t),Ja(e,t,i))}function Ja(e,t,i){e.getAttribute(t)!=i&&e.setAttribute(t,i)}function Va(e,t,i){e[t]!==i&&(e[t]=i)}function Wa(e,t){let i=[].concat(t).map(n=>n+"");Array.from(e.options).forEach(n=>{n.selected=i.includes(n.value)})}function qa(e){return e.toLowerCase().replace(/-(\w)/g,(t,i)=>i.toUpperCase())}function ar(e,t){return e==t}function $t(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}function ns(e){return["disabled","checked","required","readonly","open","selected","autofocus","itemscope","multiple","novalidate","allowfullscreen","allowpaymentrequest","formnovalidate","autoplay","controls","loop","muted","playsinline","default","ismap","reversed","async","defer","nomodule"].includes(e)}function Ka(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function za(e,t,i){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:rs(e,t,i)}function Xa(e,t,i,n=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let r=e._x_inlineBindings[t];return r.extract=n,Ir(()=>Ee(e,r.expression))}return rs(e,t,i)}function rs(e,t,i){let n=e.getAttribute(t);return n===null?typeof i=="function"?i():i:n===""?!0:ns(t)?!![t,"true"].includes(n):n}function ss(e,t){var i;return function(){var n=this,r=arguments,s=function(){i=null,e.apply(n,r)};clearTimeout(i),i=setTimeout(s,t)}}function os(e,t){let i;return function(){let n=this,r=arguments;i||(e.apply(n,r),i=!0,setTimeout(()=>i=!1,t))}}function as({get:e,set:t},{get:i,set:n}){let r=!0,s,o,a=Te(()=>{let l=e(),h=i();if(r)n(Si(l)),r=!1;else{let c=JSON.stringify(l),u=JSON.stringify(h);c!==s?n(Si(l)):c!==u&&t(Si(h))}s=JSON.stringify(e()),o=JSON.stringify(i())});return()=>{Fe(a)}}function Si(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function Ga(e){(Array.isArray(e)?e:[e]).forEach(i=>i(ut))}var Ae={},lr=!1;function Ya(e,t){if(lr||(Ae=He(Ae),lr=!0),t===void 0)return Ae[e];Ae[e]=t,typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&Ae[e].init(),Rr(Ae[e])}function Za(){return Ae}var ls={};function Qa(e,t){let i=typeof t!="function"?()=>t:t;return e instanceof Element?hs(e,i()):(ls[e]=i,()=>{})}function el(e){return Object.entries(ls).forEach(([t,i])=>{Object.defineProperty(e,t,{get(){return(...n)=>i(...n)}})}),e}function hs(e,t,i){let n=[];for(;n.length;)n.pop()();let r=Object.entries(t).map(([o,a])=>({name:o,value:a})),s=Mr(r);return r=r.map(o=>s.find(a=>a.name===o.name)?{name:`x-bind:${o.name}`,value:`"${o.value}"`}:o),Yi(e,r,i).map(o=>{n.push(o.runCleanups),o()}),()=>{for(;n.length;)n.pop()()}}var cs={};function tl(e,t){cs[e]=t}function il(e,t){return Object.entries(cs).forEach(([i,n])=>{Object.defineProperty(e,i,{get(){return(...r)=>n.bind(t)(...r)},enumerable:!1})}),e}var nl={get reactive(){return He},get release(){return Fe},get effect(){return Te},get raw(){return _r},version:"3.14.1",flushAndStopDeferringMutations:la,dontAutoEvaluateFunctions:Ir,disableEffectScheduling:ta,startObservingMutations:Ki,stopObservingMutations:kr,setReactivityEngine:ia,onAttributeRemoved:Er,onAttributesAdded:xr,closestDataStack:Ne,skipDuringClone:ve,onlyDuringClone:Ma,addRootSelector:Kr,addInitSelector:zr,interceptClone:Vt,addScopeToNode:lt,deferMutations:aa,mapAttributes:Zi,evaluateLater:H,interceptInit:Ea,setEvaluator:fa,mergeProxies:ht,extractProp:Xa,findClosest:ct,onElRemoved:Vi,closestRoot:Bt,destroyTree:Gr,interceptor:Pr,transition:Hi,setStyles:Jt,mutateDom:L,directive:I,entangle:as,throttle:os,debounce:ss,evaluate:Ee,initTree:ae,nextTick:tn,prefixed:je,prefix:va,plugin:Ga,magic:z,store:Ya,start:Sa,clone:$a,cloneNode:Na,bound:za,$data:Tr,watch:yr,walk:pe,data:tl,bind:Qa},ut=nl;function us(e,t){let i=Object.create(null),n=e.split(",");for(let r=0;r<n.length;r++)i[n[r]]=!0;return t?r=>!!i[r.toLowerCase()]:r=>!!i[r]}var rl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Th=us(rl+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected"),sl=Object.freeze({}),Rh=Object.freeze([]),ol=Object.prototype.hasOwnProperty,Wt=(e,t)=>ol.call(e,t),Ce=Array.isArray,ot=e=>ds(e)==="[object Map]",al=e=>typeof e=="string",rn=e=>typeof e=="symbol",qt=e=>e!==null&&typeof e=="object",ll=Object.prototype.toString,ds=e=>ll.call(e),fs=e=>ds(e).slice(8,-1),sn=e=>al(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Kt=e=>{let t=Object.create(null);return i=>t[i]||(t[i]=e(i))},hl=/-(\w)/g,Ph=Kt(e=>e.replace(hl,(t,i)=>i?i.toUpperCase():"")),cl=/\B([A-Z])/g,Oh=Kt(e=>e.replace(cl,"-$1").toLowerCase()),ps=Kt(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ih=Kt(e=>e?`on${ps(e)}`:""),gs=(e,t)=>e!==t&&(e===e||t===t),ji=new WeakMap,nt=[],ee,ke=Symbol("iterate"),Ui=Symbol("Map key iterate");function ul(e){return e&&e._isEffect===!0}function dl(e,t=sl){ul(e)&&(e=e.raw);let i=gl(e,t);return t.lazy||i(),i}function fl(e){e.active&&(ms(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var pl=0;function gl(e,t){let i=function(){if(!i.active)return e();if(!nt.includes(i)){ms(i);try{return vl(),nt.push(i),ee=i,e()}finally{nt.pop(),vs(),ee=nt[nt.length-1]}}};return i.id=pl++,i.allowRecurse=!!t.allowRecurse,i._isEffect=!0,i.active=!0,i.raw=e,i.deps=[],i.options=t,i}function ms(e){let{deps:t}=e;if(t.length){for(let i=0;i<t.length;i++)t[i].delete(e);t.length=0}}var $e=!0,on=[];function ml(){on.push($e),$e=!1}function vl(){on.push($e),$e=!0}function vs(){let e=on.pop();$e=e===void 0?!0:e}function K(e,t,i){if(!$e||ee===void 0)return;let n=ji.get(e);n||ji.set(e,n=new Map);let r=n.get(i);r||n.set(i,r=new Set),r.has(ee)||(r.add(ee),ee.deps.push(r),ee.options.onTrack&&ee.options.onTrack({effect:ee,target:e,type:t,key:i}))}function me(e,t,i,n,r,s){let o=ji.get(e);if(!o)return;let a=new Set,l=c=>{c&&c.forEach(u=>{(u!==ee||u.allowRecurse)&&a.add(u)})};if(t==="clear")o.forEach(l);else if(i==="length"&&Ce(e))o.forEach((c,u)=>{(u==="length"||u>=n)&&l(c)});else switch(i!==void 0&&l(o.get(i)),t){case"add":Ce(e)?sn(i)&&l(o.get("length")):(l(o.get(ke)),ot(e)&&l(o.get(Ui)));break;case"delete":Ce(e)||(l(o.get(ke)),ot(e)&&l(o.get(Ui)));break;case"set":ot(e)&&l(o.get(ke));break}let h=c=>{c.options.onTrigger&&c.options.onTrigger({effect:c,target:e,key:i,type:t,newValue:n,oldValue:r,oldTarget:s}),c.options.scheduler?c.options.scheduler(c):c()};a.forEach(h)}var bl=us("__proto__,__v_isRef,__isVue"),bs=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(rn)),_l=_s(),yl=_s(!0),hr=wl();function wl(){let e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...i){let n=T(this);for(let s=0,o=this.length;s<o;s++)K(n,"get",s+"");let r=n[t](...i);return r===-1||r===!1?n[t](...i.map(T)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...i){ml();let n=T(this)[t].apply(this,i);return vs(),n}}),e}function _s(e=!1,t=!1){return function(n,r,s){if(r==="__v_isReactive")return!e;if(r==="__v_isReadonly")return e;if(r==="__v_raw"&&s===(e?t?$l:Ss:t?Nl:As).get(n))return n;let o=Ce(n);if(!e&&o&&Wt(hr,r))return Reflect.get(hr,r,s);let a=Reflect.get(n,r,s);return(rn(r)?bs.has(r):bl(r))||(e||K(n,"get",r),t)?a:Bi(a)?!o||!sn(r)?a.value:a:qt(a)?e?xs(a):cn(a):a}}var Al=Sl();function Sl(e=!1){return function(i,n,r,s){let o=i[n];if(!e&&(r=T(r),o=T(o),!Ce(i)&&Bi(o)&&!Bi(r)))return o.value=r,!0;let a=Ce(i)&&sn(n)?Number(n)<i.length:Wt(i,n),l=Reflect.set(i,n,r,s);return i===T(s)&&(a?gs(r,o)&&me(i,"set",n,r,o):me(i,"add",n,r)),l}}function xl(e,t){let i=Wt(e,t),n=e[t],r=Reflect.deleteProperty(e,t);return r&&i&&me(e,"delete",t,void 0,n),r}function El(e,t){let i=Reflect.has(e,t);return(!rn(t)||!bs.has(t))&&K(e,"has",t),i}function Cl(e){return K(e,"iterate",Ce(e)?"length":ke),Reflect.ownKeys(e)}var kl={get:_l,set:Al,deleteProperty:xl,has:El,ownKeys:Cl},Tl={get:yl,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},an=e=>qt(e)?cn(e):e,ln=e=>qt(e)?xs(e):e,hn=e=>e,zt=e=>Reflect.getPrototypeOf(e);function Ot(e,t,i=!1,n=!1){e=e.__v_raw;let r=T(e),s=T(t);t!==s&&!i&&K(r,"get",t),!i&&K(r,"get",s);let{has:o}=zt(r),a=n?hn:i?ln:an;if(o.call(r,t))return a(e.get(t));if(o.call(r,s))return a(e.get(s));e!==r&&e.get(t)}function It(e,t=!1){let i=this.__v_raw,n=T(i),r=T(e);return e!==r&&!t&&K(n,"has",e),!t&&K(n,"has",r),e===r?i.has(e):i.has(e)||i.has(r)}function Lt(e,t=!1){return e=e.__v_raw,!t&&K(T(e),"iterate",ke),Reflect.get(e,"size",e)}function cr(e){e=T(e);let t=T(this);return zt(t).has.call(t,e)||(t.add(e),me(t,"add",e,e)),this}function ur(e,t){t=T(t);let i=T(this),{has:n,get:r}=zt(i),s=n.call(i,e);s?ws(i,n,e):(e=T(e),s=n.call(i,e));let o=r.call(i,e);return i.set(e,t),s?gs(t,o)&&me(i,"set",e,t,o):me(i,"add",e,t),this}function dr(e){let t=T(this),{has:i,get:n}=zt(t),r=i.call(t,e);r?ws(t,i,e):(e=T(e),r=i.call(t,e));let s=n?n.call(t,e):void 0,o=t.delete(e);return r&&me(t,"delete",e,void 0,s),o}function fr(){let e=T(this),t=e.size!==0,i=ot(e)?new Map(e):new Set(e),n=e.clear();return t&&me(e,"clear",void 0,void 0,i),n}function Dt(e,t){return function(n,r){let s=this,o=s.__v_raw,a=T(o),l=t?hn:e?ln:an;return!e&&K(a,"iterate",ke),o.forEach((h,c)=>n.call(r,l(h),l(c),s))}}function Mt(e,t,i){return function(...n){let r=this.__v_raw,s=T(r),o=ot(s),a=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,h=r[e](...n),c=i?hn:t?ln:an;return!t&&K(s,"iterate",l?Ui:ke),{next(){let{value:u,done:g}=h.next();return g?{value:u,done:g}:{value:a?[c(u[0]),c(u[1])]:c(u),done:g}},[Symbol.iterator](){return this}}}}function fe(e){return function(...t){{let i=t[0]?`on key "${t[0]}" `:"";console.warn(`${ps(e)} operation ${i}failed: target is readonly.`,T(this))}return e==="delete"?!1:this}}function Rl(){let e={get(s){return Ot(this,s)},get size(){return Lt(this)},has:It,add:cr,set:ur,delete:dr,clear:fr,forEach:Dt(!1,!1)},t={get(s){return Ot(this,s,!1,!0)},get size(){return Lt(this)},has:It,add:cr,set:ur,delete:dr,clear:fr,forEach:Dt(!1,!0)},i={get(s){return Ot(this,s,!0)},get size(){return Lt(this,!0)},has(s){return It.call(this,s,!0)},add:fe("add"),set:fe("set"),delete:fe("delete"),clear:fe("clear"),forEach:Dt(!0,!1)},n={get(s){return Ot(this,s,!0,!0)},get size(){return Lt(this,!0)},has(s){return It.call(this,s,!0)},add:fe("add"),set:fe("set"),delete:fe("delete"),clear:fe("clear"),forEach:Dt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=Mt(s,!1,!1),i[s]=Mt(s,!0,!1),t[s]=Mt(s,!1,!0),n[s]=Mt(s,!0,!0)}),[e,i,t,n]}var[Pl,Ol,Il,Ll]=Rl();function ys(e,t){let i=t?e?Ll:Il:e?Ol:Pl;return(n,r,s)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(Wt(i,r)&&r in n?i:n,r,s)}var Dl={get:ys(!1,!1)},Ml={get:ys(!0,!1)};function ws(e,t,i){let n=T(i);if(n!==i&&t.call(e,n)){let r=fs(e);console.warn(`Reactive ${r} contains both the raw and reactive versions of the same object${r==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var As=new WeakMap,Nl=new WeakMap,Ss=new WeakMap,$l=new WeakMap;function Hl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Fl(e){return e.__v_skip||!Object.isExtensible(e)?0:Hl(fs(e))}function cn(e){return e&&e.__v_isReadonly?e:Es(e,!1,kl,Dl,As)}function xs(e){return Es(e,!0,Tl,Ml,Ss)}function Es(e,t,i,n,r){if(!qt(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;let s=r.get(e);if(s)return s;let o=Fl(e);if(o===0)return e;let a=new Proxy(e,o===2?n:i);return r.set(e,a),a}function T(e){return e&&T(e.__v_raw)||e}function Bi(e){return!!(e&&e.__v_isRef===!0)}z("nextTick",()=>tn);z("dispatch",e=>st.bind(st,e));z("watch",(e,{evaluateLater:t,cleanup:i})=>(n,r)=>{let s=t(n),a=yr(()=>{let l;return s(h=>l=h),l},r);i(a)});z("store",Za);z("data",e=>Tr(e));z("root",e=>Bt(e));z("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=ht(jl(e))),e._x_refs_proxy));function jl(e){let t=[];return ct(e,i=>{i._x_refs&&t.push(i._x_refs)}),t}var xi={};function Cs(e){return xi[e]||(xi[e]=0),++xi[e]}function Ul(e,t){return ct(e,i=>{if(i._x_ids&&i._x_ids[t])return!0})}function Bl(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=Cs(t))}z("id",(e,{cleanup:t})=>(i,n=null)=>{let r=`${i}${n?`-${n}`:""}`;return Jl(e,r,t,()=>{let s=Ul(e,i),o=s?s._x_ids[i]:Cs(i);return n?`${i}-${o}-${n}`:`${i}-${o}`})});Vt((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function Jl(e,t,i,n){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let r=n();return e._x_id[t]=r,i(()=>{delete e._x_id[t]}),r}z("el",e=>e);ks("Focus","focus","focus");ks("Persist","persist","persist");function ks(e,t,i){z(t,n=>V(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${i}`,n))}I("modelable",(e,{expression:t},{effect:i,evaluateLater:n,cleanup:r})=>{let s=n(t),o=()=>{let c;return s(u=>c=u),c},a=n(`${t} = __placeholder`),l=c=>a(()=>{},{scope:{__placeholder:c}}),h=o();l(h),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let c=e._x_model.get,u=e._x_model.set,g=as({get(){return c()},set(f){u(f)}},{get(){return o()},set(f){l(f)}});r(g)})});I("teleport",(e,{modifiers:t,expression:i},{cleanup:n})=>{e.tagName.toLowerCase()!=="template"&&V("x-teleport can only be used on a <template> tag",e);let r=pr(i),s=e.content.cloneNode(!0).firstElementChild;e._x_teleport=s,s._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),s.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(a=>{s.addEventListener(a,l=>{l.stopPropagation(),e.dispatchEvent(new l.constructor(l.type,l))})}),lt(s,{},e);let o=(a,l,h)=>{h.includes("prepend")?l.parentNode.insertBefore(a,l):h.includes("append")?l.parentNode.insertBefore(a,l.nextSibling):l.appendChild(a)};L(()=>{o(s,r,t),ve(()=>{ae(s),s._x_ignore=!0})()}),e._x_teleportPutBack=()=>{let a=pr(i);L(()=>{o(e._x_teleport,a,t)})},n(()=>s.remove())});var Vl=document.createElement("div");function pr(e){let t=ve(()=>document.querySelector(e),()=>Vl)();return t||V(`Cannot find x-teleport element for selector: "${e}"`),t}var Ts=()=>{};Ts.inline=(e,{modifiers:t},{cleanup:i})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,i(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};I("ignore",Ts);I("effect",ve((e,{expression:t},{effect:i})=>{i(H(e,t))}));function Ji(e,t,i,n){let r=e,s=l=>n(l),o={},a=(l,h)=>c=>h(l,c);if(i.includes("dot")&&(t=Wl(t)),i.includes("camel")&&(t=ql(t)),i.includes("passive")&&(o.passive=!0),i.includes("capture")&&(o.capture=!0),i.includes("window")&&(r=window),i.includes("document")&&(r=document),i.includes("debounce")){let l=i[i.indexOf("debounce")+1]||"invalid-wait",h=Ut(l.split("ms")[0])?Number(l.split("ms")[0]):250;s=ss(s,h)}if(i.includes("throttle")){let l=i[i.indexOf("throttle")+1]||"invalid-wait",h=Ut(l.split("ms")[0])?Number(l.split("ms")[0]):250;s=os(s,h)}return i.includes("prevent")&&(s=a(s,(l,h)=>{h.preventDefault(),l(h)})),i.includes("stop")&&(s=a(s,(l,h)=>{h.stopPropagation(),l(h)})),i.includes("once")&&(s=a(s,(l,h)=>{l(h),r.removeEventListener(t,s,o)})),(i.includes("away")||i.includes("outside"))&&(r=document,s=a(s,(l,h)=>{e.contains(h.target)||h.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&l(h))})),i.includes("self")&&(s=a(s,(l,h)=>{h.target===e&&l(h)})),(zl(t)||Rs(t))&&(s=a(s,(l,h)=>{Xl(h,i)||l(h)})),r.addEventListener(t,s,o),()=>{r.removeEventListener(t,s,o)}}function Wl(e){return e.replace(/-/g,".")}function ql(e){return e.toLowerCase().replace(/-(\w)/g,(t,i)=>i.toUpperCase())}function Ut(e){return!Array.isArray(e)&&!isNaN(e)}function Kl(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function zl(e){return["keydown","keyup"].includes(e)}function Rs(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function Xl(e,t){let i=t.filter(s=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(s));if(i.includes("debounce")){let s=i.indexOf("debounce");i.splice(s,Ut((i[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(i.includes("throttle")){let s=i.indexOf("throttle");i.splice(s,Ut((i[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(i.length===0||i.length===1&&gr(e.key).includes(i[0]))return!1;let r=["ctrl","shift","alt","meta","cmd","super"].filter(s=>i.includes(s));return i=i.filter(s=>!r.includes(s)),!(r.length>0&&r.filter(o=>((o==="cmd"||o==="super")&&(o="meta"),e[`${o}Key`])).length===r.length&&(Rs(e.type)||gr(e.key).includes(i[0])))}function gr(e){if(!e)return[];e=Kl(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(i=>{if(t[i]===e)return i}).filter(i=>i)}I("model",(e,{modifiers:t,expression:i},{effect:n,cleanup:r})=>{let s=e;t.includes("parent")&&(s=e.parentNode);let o=H(s,i),a;typeof i=="string"?a=H(s,`${i} = __placeholder`):typeof i=="function"&&typeof i()=="string"?a=H(s,`${i()} = __placeholder`):a=()=>{};let l=()=>{let g;return o(f=>g=f),mr(g)?g.get():g},h=g=>{let f;o(v=>f=v),mr(f)?f.set(g):a(()=>{},{scope:{__placeholder:g}})};typeof i=="string"&&e.type==="radio"&&L(()=>{e.hasAttribute("name")||e.setAttribute("name",i)});var c=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let u=ge?()=>{}:Ji(e,c,t,g=>{h(Ei(e,t,g,l()))});if(t.includes("fill")&&([void 0,null,""].includes(l())||e.type==="checkbox"&&Array.isArray(l())||e.tagName.toLowerCase()==="select"&&e.multiple)&&h(Ei(e,t,{target:e},l())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=u,r(()=>e._x_removeModelListeners.default()),e.form){let g=Ji(e.form,"reset",[],f=>{tn(()=>e._x_model&&e._x_model.set(Ei(e,t,{target:e},l())))});r(()=>g())}e._x_model={get(){return l()},set(g){h(g)}},e._x_forceModelUpdate=g=>{g===void 0&&typeof i=="string"&&i.match(/\./)&&(g=""),window.fromModel=!0,L(()=>ts(e,"value",g)),delete window.fromModel},n(()=>{let g=l();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(g)})});function Ei(e,t,i,n){return L(()=>{if(i instanceof CustomEvent&&i.detail!==void 0)return i.detail!==null&&i.detail!==void 0?i.detail:i.target.value;if(e.type==="checkbox")if(Array.isArray(n)){let r=null;return t.includes("number")?r=Ci(i.target.value):t.includes("boolean")?r=$t(i.target.value):r=i.target.value,i.target.checked?n.includes(r)?n:n.concat([r]):n.filter(s=>!Gl(s,r))}else return i.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(i.target.selectedOptions).map(r=>{let s=r.value||r.text;return Ci(s)}):t.includes("boolean")?Array.from(i.target.selectedOptions).map(r=>{let s=r.value||r.text;return $t(s)}):Array.from(i.target.selectedOptions).map(r=>r.value||r.text);{let r;return e.type==="radio"?i.target.checked?r=i.target.value:r=n:r=i.target.value,t.includes("number")?Ci(r):t.includes("boolean")?$t(r):t.includes("trim")?r.trim():r}}})}function Ci(e){let t=e?parseFloat(e):null;return Yl(t)?t:e}function Gl(e,t){return e==t}function Yl(e){return!Array.isArray(e)&&!isNaN(e)}function mr(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}I("cloak",e=>queueMicrotask(()=>L(()=>e.removeAttribute(je("cloak")))));zr(()=>`[${je("init")}]`);I("init",ve((e,{expression:t},{evaluate:i})=>typeof t=="string"?!!t.trim()&&i(t,{},!1):i(t,{},!1)));I("text",(e,{expression:t},{effect:i,evaluateLater:n})=>{let r=n(t);i(()=>{r(s=>{L(()=>{e.textContent=s})})})});I("html",(e,{expression:t},{effect:i,evaluateLater:n})=>{let r=n(t);i(()=>{r(s=>{L(()=>{e.innerHTML=s,e._x_ignoreSelf=!0,ae(e),delete e._x_ignoreSelf})})})});Zi(Hr(":",Fr(je("bind:"))));var Ps=(e,{value:t,modifiers:i,expression:n,original:r},{effect:s,cleanup:o})=>{if(!t){let l={};el(l),H(e,n)(c=>{hs(e,c,r)},{scope:l});return}if(t==="key")return Zl(e,n);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let a=H(e,n);s(()=>a(l=>{l===void 0&&typeof n=="string"&&n.match(/\./)&&(l=""),L(()=>ts(e,t,l,i))})),o(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};Ps.inline=(e,{value:t,modifiers:i,expression:n})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:n,extract:!1})};I("bind",Ps);function Zl(e,t){e._x_keyExpression=t}Kr(()=>`[${je("data")}]`);I("data",(e,{expression:t},{cleanup:i})=>{if(Ql(e))return;t=t===""?"{}":t;let n={};Ii(n,e);let r={};il(r,n);let s=Ee(e,t,{scope:r});(s===void 0||s===!0)&&(s={}),Ii(s,e);let o=He(s);Rr(o);let a=lt(e,o);o.init&&Ee(e,o.init),i(()=>{o.destroy&&Ee(e,o.destroy),a()})});Vt((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function Ql(e){return ge?Fi?!0:e.hasAttribute("data-has-alpine-state"):!1}I("show",(e,{modifiers:t,expression:i},{effect:n})=>{let r=H(e,i);e._x_doHide||(e._x_doHide=()=>{L(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{L(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let s=()=>{e._x_doHide(),e._x_isShown=!1},o=()=>{e._x_doShow(),e._x_isShown=!0},a=()=>setTimeout(o),l=$i(u=>u?o():s(),u=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,u,o,s):u?a():s()}),h,c=!0;n(()=>r(u=>{!c&&u===h||(t.includes("immediate")&&(u?a():s()),l(u),h=u,c=!1)}))});I("for",(e,{expression:t},{effect:i,cleanup:n})=>{let r=th(t),s=H(e,r.items),o=H(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},i(()=>eh(e,r,s,o)),n(()=>{Object.values(e._x_lookup).forEach(a=>a.remove()),delete e._x_prevKeys,delete e._x_lookup})});function eh(e,t,i,n){let r=o=>typeof o=="object"&&!Array.isArray(o),s=e;i(o=>{ih(o)&&o>=0&&(o=Array.from(Array(o).keys(),m=>m+1)),o===void 0&&(o=[]);let a=e._x_lookup,l=e._x_prevKeys,h=[],c=[];if(r(o))o=Object.entries(o).map(([m,w])=>{let _=vr(t,w,m,o);n(x=>{c.includes(x)&&V("Duplicate key on x-for",e),c.push(x)},{scope:N({index:m},_)}),h.push(_)});else for(let m=0;m<o.length;m++){let w=vr(t,o[m],m,o);n(_=>{c.includes(_)&&V("Duplicate key on x-for",e),c.push(_)},{scope:N({index:m},w)}),h.push(w)}let u=[],g=[],f=[],v=[];for(let m=0;m<l.length;m++){let w=l[m];c.indexOf(w)===-1&&f.push(w)}l=l.filter(m=>!f.includes(m));let b="template";for(let m=0;m<c.length;m++){let w=c[m],_=l.indexOf(w);if(_===-1)l.splice(m,0,w),u.push([b,m]);else if(_!==m){let x=l.splice(m,1)[0],C=l.splice(_-1,1)[0];l.splice(m,0,C),l.splice(_,0,x),g.push([x,C])}else v.push(w);b=w}for(let m=0;m<f.length;m++){let w=f[m];a[w]._x_effects&&a[w]._x_effects.forEach(br),a[w].remove(),a[w]=null,delete a[w]}for(let m=0;m<g.length;m++){let[w,_]=g[m],x=a[w],C=a[_],p=document.createElement("div");L(()=>{C||V('x-for ":key" is undefined or invalid',s,_,a),C.after(p),x.after(C),C._x_currentIfEl&&C.after(C._x_currentIfEl),p.before(x),x._x_currentIfEl&&x.after(x._x_currentIfEl),p.remove()}),C._x_refreshXForScope(h[c.indexOf(_)])}for(let m=0;m<u.length;m++){let[w,_]=u[m],x=w==="template"?s:a[w];x._x_currentIfEl&&(x=x._x_currentIfEl);let C=h[_],p=c[_],y=document.importNode(s.content,!0).firstElementChild,D=He(C);lt(y,D,s),y._x_refreshXForScope=j=>{Object.entries(j).forEach(([M,te])=>{D[M]=te})},L(()=>{x.after(y),ve(()=>ae(y))()}),typeof p=="object"&&V("x-for key cannot be an object, it must be a string or an integer",s),a[p]=y}for(let m=0;m<v.length;m++)a[v[m]]._x_refreshXForScope(h[c.indexOf(v[m])]);s._x_prevKeys=c})}function th(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,i=/^\s*\(|\)\s*$/g,n=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,r=e.match(n);if(!r)return;let s={};s.items=r[2].trim();let o=r[1].replace(i,"").trim(),a=o.match(t);return a?(s.item=o.replace(t,"").trim(),s.index=a[1].trim(),a[2]&&(s.collection=a[2].trim())):s.item=o,s}function vr(e,t,i,n){let r={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(o=>o.trim()).forEach((o,a)=>{r[o]=t[a]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(o=>o.trim()).forEach(o=>{r[o]=t[o]}):r[e.item]=t,e.index&&(r[e.index]=i),e.collection&&(r[e.collection]=n),r}function ih(e){return!Array.isArray(e)&&!isNaN(e)}function Os(){}Os.inline=(e,{expression:t},{cleanup:i})=>{let n=Bt(e);n._x_refs||(n._x_refs={}),n._x_refs[t]=e,i(()=>delete n._x_refs[t])};I("ref",Os);I("if",(e,{expression:t},{effect:i,cleanup:n})=>{e.tagName.toLowerCase()!=="template"&&V("x-if can only be used on a <template> tag",e);let r=H(e,t),s=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let a=e.content.cloneNode(!0).firstElementChild;return lt(a,{},e),L(()=>{e.after(a),ve(()=>ae(a))()}),e._x_currentIfEl=a,e._x_undoIf=()=>{pe(a,l=>{l._x_effects&&l._x_effects.forEach(br)}),a.remove(),delete e._x_currentIfEl},a},o=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};i(()=>r(a=>{a?s():o()})),n(()=>e._x_undoIf&&e._x_undoIf())});I("id",(e,{expression:t},{evaluate:i})=>{i(t).forEach(r=>Bl(e,r))});Vt((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});Zi(Hr("@",Fr(je("on:"))));I("on",ve((e,{value:t,modifiers:i,expression:n},{cleanup:r})=>{let s=n?H(e,n):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let o=Ji(e,t,i,a=>{s(()=>{},{scope:{$event:a},params:[a]})});r(()=>o())}));Xt("Collapse","collapse","collapse");Xt("Intersect","intersect","intersect");Xt("Focus","trap","focus");Xt("Mask","mask","mask");function Xt(e,t,i){I(t,n=>V(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${i}`,n))}ut.setEvaluator(Dr);ut.setReactivityEngine({reactive:cn,effect:dl,release:fl,raw:T});var nh=ut,Gt=nh;function rh(e){e.directive("collapse",t),t.inline=(i,{modifiers:n})=>{n.includes("min")&&(i._x_doShow=()=>{},i._x_doHide=()=>{})};function t(i,{modifiers:n}){let r=Is(n,"duration",250)/1e3,s=Is(n,"min",0),o=!n.includes("min");i._x_isShown||(i.style.height=`${s}px`),!i._x_isShown&&o&&(i.hidden=!0),i._x_isShown||(i.style.overflow="hidden");let a=(h,c)=>{let u=e.setStyles(h,c);return c.height?()=>{}:u},l={transitionProperty:"height",transitionDuration:`${r}s`,transitionTimingFunction:"cubic-bezier(0.4, 0.0, 0.2, 1)"};i._x_transition={in(h=()=>{},c=()=>{}){o&&(i.hidden=!1),o&&(i.style.display=null);let u=i.getBoundingClientRect().height;i.style.height="auto";let g=i.getBoundingClientRect().height;u===g&&(u=s),e.transition(i,e.setStyles,{during:l,start:{height:u+"px"},end:{height:g+"px"}},()=>i._x_isShown=!0,()=>{Math.abs(i.getBoundingClientRect().height-g)<1&&(i.style.overflow=null)})},out(h=()=>{},c=()=>{}){let u=i.getBoundingClientRect().height;e.transition(i,a,{during:l,start:{height:u+"px"},end:{height:s+"px"}},()=>i.style.overflow="hidden",()=>{i._x_isShown=!1,i.style.height==`${s}px`&&o&&(i.style.display="none",i.hidden=!0)})}}}}function Is(e,t,i){if(e.indexOf(t)===-1)return i;let n=e[e.indexOf(t)+1];if(!n)return i;if(t==="duration"){let r=n.match(/([0-9]+)ms/);if(r)return r[1]}if(t==="min"){let r=n.match(/([0-9]+)px/);if(r)return r[1]}return n}var Ls=rh;var sh={mounted(){this.handleEvent("update",({data:e})=>{e&&this.pushEvent("data_updated",{data:e})})},updated(){},destroyed(){}},Ds=sh;var Ms={WalletHook:Ds};window.Hooks=Ms;window.Alpine=Gt;Gt.plugin(Ls);Gt.start();var oh=document.querySelector("meta[name='csrf-token']").getAttribute("content"),Ns=new tr("/live",ri,{longPollFallbackMs:2500,params:{_csrf_token:oh,current_path:window.location.pathname},hooks:window.Hooks,dom:{onBeforeElUpdated(e,t){e._x_dataStack&&window.Alpine.clone(e,t)}}});window.addEventListener("phx:page-loading-start",e=>{document.getElementById("loader").style.display="flex"});window.addEventListener("phx:page-loading-stop",e=>{document.getElementById("loader").style.display="none"});Ns.connect();window.liveSocket=Ns;var Yt=new ri("/socket",{params:{api_key:window.apiKey||"18c0bcde-1c62-48b5-b89b-7d607f1b5295"}});Yt.onError(e=>console.error("Socket error:",e));Yt.onClose(()=>console.log(""));Yt.connect();window.socket=Yt;})();
/**
 * @license MIT
 * topbar 2.0.0, 2023-02-04
 * https://buunguyen.github.io/topbar
 * Copyright (c) 2021 Buu Nguyen
 */
