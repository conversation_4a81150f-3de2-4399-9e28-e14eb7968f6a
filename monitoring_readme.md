# API/URL Uptime Monitor

A GenServer-based service that monitors API endpoints and URLs for uptime, with Telegram notifications for status changes.

## Features

- Reads endpoints from a text file (one per line)
- Periodically checks if each endpoint is up
- Maintains in-memory state of service status
- Sends Telegram alerts when services go down
- Avoids duplicate alerts for continued downtime
- Sends recovery notifications when services come back up
- Supports both GET and POST requests with response validation

## Configuration

The uptime monitor is configured in `config/config.exs`:

```elixir
config :service_manager, ServiceManager.Monitoring.UptimeMonitor,
  check_interval: 60_000,  # 1 minute
  endpoints_file: "endpoints.txt"
```

You can adjust the check interval and the path to the endpoints file as needed.

## Endpoints Configuration

Endpoints are configured in the `endpoints.txt` file with the following format:

```
METHOD|URL|[REQUEST_BODY]|[EXPECTED_RESPONSE]
```

Examples:

```
# Simple GET request expecting a 200 status code
GET|https://example.com/api/health|200

# GET request expecting a specific JSON response
GET|https://example.com/api/status|{"status":"ok"}

# POST request with a request body and expected response
POST|https://example.com/api/check|{"test":"data"}|{"status":"ok"}
```

## Response Validation

The monitor supports different types of response validation:

1. **Status Code Validation**: If the expected response is a number, it will be treated as an HTTP status code.
2. **JSON Validation**: If the expected response starts with `{`, it will be treated as a JSON pattern to match against the response body.
3. **String Pattern Validation**: Otherwise, the expected response will be treated as a string pattern to find in the response body.

## Telegram Notifications

The monitor uses the configured Telegram channel ID to send notifications:

```elixir
config :service_manager, :telegram_channel_id, "your-channel-id"
```

## API

The uptime monitor provides the following API:

```elixir
# Force an immediate check of all endpoints
ServiceManager.Monitoring.UptimeMonitor.check_now()

# Get the current status of all monitored endpoints
ServiceManager.Monitoring.UptimeMonitor.get_status()

# Reload endpoint configurations from the file
ServiceManager.Monitoring.UptimeMonitor.reload_endpoints()
```

## Implementation Details

The uptime monitor consists of three main modules:

1. `ServiceManager.Monitoring.UptimeMonitor`: The main GenServer that manages the monitoring process.
2. `ServiceManager.Monitoring.EndpointConfig`: Handles parsing and loading of endpoint configurations.
3. `ServiceManager.Monitoring.HttpChecker`: Handles HTTP requests and response validation.

The monitor is automatically started as part of the application supervision tree.
