Remove the example APIs and define the following:

APIs
The following APIs should be implemented to interact with the Temenos T24 system
3.1 getCustomerByPhoneNumber
Retrieves customer information using the customer&#39;s phone number.
3.2. getCustomerByCustomerNumber
Retrieves customer information using the customer number.
3.3 getAccountsByPhoneNumber
Retrieves customer accounts using the customer’s phone number.
3.4 getAccountsByCustomerNumber
Retrieves customer accounts using the customer’s number.
3.5. getAccount
Retrieves customer account using account number.
3.6 getAccountTransactions

Retrieves transactions using account number, start date, and end date.
3.7 getCustomerTransactions
Retrieve transactions using customer number, start date, end date, account number,
transaction type, and recipient account.
3.8 getTransaction
Retrieves transaction using transaction reference.
3.9 initiateTransaction
Initiates transactions by accepting credit account, debit account, amount, and note.
3.10 currencyExchanges
Retrieves currency exchange rates.

make sure you indicate all the important properties like content type which should be json, headers and create axample requests and responses for each api and create a robust request and response structure