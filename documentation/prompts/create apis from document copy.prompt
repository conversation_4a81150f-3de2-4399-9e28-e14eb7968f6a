# Temenos T24 API Documentation

## Introduction

This document provides comprehensive documentation for the Temenos T24 API. This API allows interaction with the Temenos T24 system, enabling operations such as retrieving customer information, account details, transactions, and initiating new transactions.

## Table of Contents

1. [Versioning](#versioning)
2. [Naming Conventions](#naming-conventions)
3. [Base URL](#base-url)
4. [Authentication](#authentication)
5. [Rate Limiting](#rate-limiting)
6. [API Security](#api-security)
7. [Webhook Support](#webhook-support)
8. [APIs](#apis)
   8.1. [getCustomerByPhoneNumber](#81-getcustomerbyphonenumber)
   8.2. [getCustomerByCustomerNumber](#82-getcustomerbycustomernumber)
   8.3. [getAccountsByPhoneNumber](#83-getaccountsbyphonenumber)
   8.4. [getAccountsByCustomerNumber](#84-getaccountsbycustomernumber)
   8.5. [getAccount](#85-getaccount)
   8.6. [getAccountTransactions](#86-getaccounttransactions)
   8.7. [getCustomerTransactions](#87-getcustomertransactions)
   8.8. [getTransaction](#88-gettransaction)
   8.9. [initiateTransaction](#89-initiatetransaction)
   8.10. [currencyExchanges](#810-currencyexchanges)
9. [Error Handling](#error-handling)
10. [Pagination](#pagination)
11. [API Status and Uptime](#api-status-and-uptime)
12. [Integration Testing](#integration-testing)
13. [Code Examples](#code-examples)
14. [Changelog](#changelog)

## Versioning

The Temenos T24 API uses semantic versioning (MAJOR.MINOR.PATCH). The current version is v1.2.0.

### Versioning Strategy

- MAJOR version increments indicate incompatible API changes
- MINOR version increments add functionality in a backwards-compatible manner
- PATCH version increments indicate backwards-compatible bug fixes

We maintain support for one previous major version for 6 months after a new major version is released.

| Version | Release Date | Description | Support End Date |
|---------|--------------|-------------|-------------------|
| 1.0.0   | 2023-06-15   | Initial API release | 2023-12-15 |
| 1.1.0   | 2023-06-16   | Changed all GET requests to POST | 2023-12-15 |
| 1.1.1   | 2023-06-17   | Numbered all APIs for easier reference | 2023-12-15 |
| 1.2.0   | 2023-06-18   | Updated response structure, added detailed response codes and business logic information | Current |

## Naming Conventions

- All API endpoints use camelCase
- All request and response properties use camelCase
- All date parameters should be in ISO 8601 format (YYYY-MM-DD)

## Base URL

All API requests should be made to: `https://api.t24.example.com/v1`

## Authentication

This API uses Bearer token authentication. Include the token in the Authorization header of all requests:

```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## Rate Limiting

To ensure the stability and availability of the API, rate limiting is implemented:

- 1000 requests per hour per API key
- 10,000 requests per day per API key

Rate limit information is included in the response headers:

- `X-RateLimit-Limit`: The number of requests allowed in the current period
- `X-RateLimit-Remaining`: The number of requests remaining in the current period
- `X-RateLimit-Reset`: The time at which the current rate limit window resets, in UTC epoch seconds

If you exceed the rate limit, you will receive a 429 Too Many Requests response.

## API Security

To ensure the security of your API integration:

1. Keep your API keys secure and do not share them.
2. Use HTTPS for all API calls.
3. Implement OAuth 2.0 for user authentication when applicable.
4. Regularly rotate your API keys.
5. Use the principle of least privilege when assigning API key permissions.

## Webhook Support

The API supports webhooks for real-time notifications of specific events. To set up a webhook:

1. Register a webhook URL in your API account settings.
2. Select the events you want to receive notifications for.
3. Implement an endpoint at your webhook URL to receive and process the notifications.

Webhook payloads are signed using HMAC SHA-256. Verify the signature to ensure the webhook came from our system.

## APIs

[The existing API documentation for sections 8.1 to 8.10 remains unchanged]

## Error Handling

All endpoints follow the same error response format:

```json
{
  "status": 1,
  "message": "An error occurred",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error message"
  }
}
```

### Common HTTP Status Codes

- 200 OK: The request was successful
- 400 Bad Request: The request was invalid or cannot be served
- 401 Unauthorized: The request requires authentication
- 403 Forbidden: The server understood the request but refuses to authorize it
- 404 Not Found: The requested resource could not be found
- 422 Unprocessable Entity: The request was well-formed but was unable to be followed due to semantic errors
- 429 Too Many Requests: The user has sent too many requests in a given amount of time
- 500 Internal Server Error: The server encountered an unexpected condition

### Custom Error Codes

In addition to HTTP status codes, we provide custom error codes for more specific error handling:

- `INVALID_INPUT`: The provided input is invalid or missing required fields
- `INSUFFICIENT_FUNDS`: The account does not have sufficient funds for the requested operation
- `ACCOUNT_LOCKED`: The account is locked and cannot perform the requested operation
- `TRANSACTION_LIMIT_EXCEEDED`: The transaction exceeds the allowed limit
- `CURRENCY_NOT_SUPPORTED`: The requested currency is not supported

## Pagination

For endpoints that return large datasets (e.g., getAccountTransactions, getCustomerTransactions), pagination is implemented using the following query parameters:

- `pageSize`: The number of items to return per page (default: 50, max: 100)
- `pageNumber`: The page number to return (default: 1)

The response includes pagination metadata:

```json
{
  "status": 0,
  "message": "Success",
  "data": {
    "items": [...],
    "pagination": {
      "totalItems": 1000,
      "totalPages": 20,
      "currentPage": 1,
      "pageSize": 50
    }
  }
}
```

## API Status and Uptime

You can check the current API status and view historical uptime at our status page: https://status.t24api.example.com

We also provide an API endpoint for programmatically checking the API status:

- **URL**: `/status`
- **Method**: GET
- **Authentication**: Not required

## Integration Testing

