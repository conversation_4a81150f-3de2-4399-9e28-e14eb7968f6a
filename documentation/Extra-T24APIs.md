# T24 EXTRA APIs

## Introduction

This document provides comprehensive documentation for additional Temenos T24 APIs. These APIs enable operations related to cheque management, loan processing, and loan scoring matrix calculations.

## Table of Contents

1. [Versioning](#versioning)
2. [Naming Conventions](#naming-conventions)
3. [Base URL](#base-url)
4. [Authentication](#authentication)
5. [APIs](#apis)
   5.1. [Cheque Management](#51-cheque-management)
      - [createCheque](#create-cheque)
      - [cancelCheque](#cancel-cheque)
      - [updateChequeStatus](#update-cheque-status)
   5.2. [Loan Management](#52-loan-management)
      - [applyLoan](#apply-loan)
      - [approveLoan](#approve-loan)
      - [disburseLoan](#disburse-loan)
   5.3. [Scoring Matrix](#53-scoring-matrix)
      - [calculateCreditScore](#calculate-credit-score)
      - [assessRisk](#assess-risk)
      - [updateScoringModel](#update-scoring-model)
6. [Error Handling](#error-handling)

## Versioning

| Version | Release Date | Description |
|---------|--------------|-------------|
| 1.0.0   | 2023-02-15  | Initial API release |
| 1.1.0   | 2023-03-01  | Added loan scoring matrix endpoints |
| 1.2.0   | 2023-04-15  | Enhanced cheque management features |

## Naming Conventions

- All API endpoints use camelCase
- All request and response properties use camelCase
- All date parameters should be in ISO 8601 format (YYYY-MM-DD)
- All monetary amounts are represented as decimal numbers
- All status codes are returned as integers

## Base URL

All API requests should be made to: `https://api.t24.example.com/v1`

## Authentication

This API uses Bearer token authentication. Include the token in the Authorization header of all requests:

```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## APIs

### 5.1. Cheque Management

#### Create Cheque

Creates a new cheque record in the system.

- **URL**: `/cheques`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "chequeNumber": "C001",
  "payeeName": "John Doe",
  "amount": 1000,
  "chequeDate": "2023-02-15"
}
```

##### Example Request

```
POST /cheques HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "chequeNumber": "C001",
  "payeeName": "John Doe",
  "amount": 1000,
  "chequeDate": "2023-02-15"
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Cheque created successfully",
  "data": {
    "chequeNumber": "C001",
    "payeeName": "John Doe",
    "amount": 1000,
    "chequeDate": "2023-02-15",
    "status": "ISSUED"
  }
}
```

##### Possible Response Codes

- 201 Created: Successfully created the cheque
- 400 Bad Request: Invalid cheque details
- 403 Forbidden: Insufficient permissions
- 409 Conflict: Cheque number already exists
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Cheque numbers must be unique
- Amount must be greater than zero
- Cheque date cannot be in the past
- User must have permission to issue cheques
- Initial status is set to "ISSUED"

#### Cancel Cheque

Cancels an existing cheque.

- **URL**: `/cheques/{chequeNumber}/cancel`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "reasonForCancellation": "Overdrawn account"
}
```

##### Example Request

```
POST /cheques/C001/cancel HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "reasonForCancellation": "Overdrawn account"
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Cheque cancelled successfully",
  "data": {
    "chequeNumber": "C001",
    "status": "CANCELLED",
    "cancellationReason": "Overdrawn account",
    "cancellationDate": "2023-02-15T10:30:00Z"
  }
}
```

##### Possible Response Codes

- 200 OK: Successfully cancelled the cheque
- 400 Bad Request: Invalid request
- 404 Not Found: Cheque not found
- 409 Conflict: Cheque already cancelled/processed
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Only issued cheques can be cancelled
- Cancellation reason is required
- Cancelled cheques cannot be reactivated
- System records cancellation date automatically

#### Update Cheque Status

Updates the status of an existing cheque.

- **URL**: `/cheques/{chequeNumber}`
- **Method**: PATCH
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "status": "PAID"
}
```

##### Example Request

```
PATCH /cheques/C001 HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "status": "PAID"
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Cheque status updated successfully",
  "data": {
    "chequeNumber": "C001",
    "status": "PAID",
    "lastUpdated": "2023-02-15T14:30:00Z"
  }
}
```

##### Possible Response Codes

- 200 OK: Successfully updated cheque status
- 400 Bad Request: Invalid status
- 404 Not Found: Cheque not found
- 409 Conflict: Invalid status transition
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Valid status transitions must be followed
- Cannot update status of cancelled cheques
- System records update timestamp automatically
- Only authorized personnel can update cheque status

### 5.2. Loan Management

#### Apply Loan

Submits a new loan application.

- **URL**: `/loans`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "borrowerInformation": {
    "name": "Jane Doe",
    "address": "123 Main St"
  },
  "loanAmount": 5000,
  "repaymentTerm": 12
}
```

##### Example Request

```
POST /loans HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "borrowerInformation": {
    "name": "Jane Doe",
    "address": "123 Main St"
  },
  "loanAmount": 5000,
  "repaymentTerm": 12
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Loan application submitted successfully",
  "data": {
    "loanId": "L001",
    "borrowerInformation": {
      "name": "Jane Doe",
      "address": "123 Main St"
    },
    "loanAmount": 5000,
    "repaymentTerm": 12,
    "applicationDate": "2023-02-15T10:30:00Z",
    "status": "PENDING"
  }
}
```

##### Possible Response Codes

- 201 Created: Successfully submitted loan application
- 400 Bad Request: Invalid loan details
- 403 Forbidden: Insufficient permissions
- 422 Unprocessable Entity: Business validation failed
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Loan amount must be within allowed limits
- Repayment term must be valid
- Borrower information must be complete
- Initial status is set to "PENDING"
- System automatically assigns a unique loan ID

#### Approve Loan

Approves a pending loan application.

- **URL**: `/loans/{loanId}/approve`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "reasonForApproval": "Approved for loan"
}
```

##### Example Request

```
POST /loans/L001/approve HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "reasonForApproval": "Approved for loan"
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Loan approved successfully",
  "data": {
    "loanId": "L001",
    "status": "APPROVED",
    "approvalDate": "2023-02-15T14:30:00Z",
    "approvedBy": "APPROVER123",
    "nextSteps": "Proceed with disbursement"
  }
}
```

##### Possible Response Codes

- 200 OK: Successfully approved loan
- 400 Bad Request: Invalid request
- 404 Not Found: Loan not found
- 409 Conflict: Loan not in approvable state
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Only pending loans can be approved
- Approval reason must be provided
- Only authorized approvers can approve loans
- System records approval details automatically

#### Disburse Loan

Disburses an approved loan.

- **URL**: `/loans/{loanId}/disburse`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "amountDisbursed": 1000
}
```

##### Example Request

```
POST /loans/L001/disburse HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "amountDisbursed": 1000
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Loan disbursed successfully",
  "data": {
    "loanId": "L001",
    "amountDisbursed": 1000,
    "disbursementDate": "2023-02-15T15:30:00Z",
    "status": "DISBURSED",
    "disbursementDetails": {
      "accountNumber": "A987654",
      "transactionId": "T123456"
    }
  }
}
```

##### Possible Response Codes

- 200 OK: Successfully disbursed loan
- 400 Bad Request: Invalid disbursement amount
- 404 Not Found: Loan not found
- 409 Conflict: Loan not in disbursable state
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Only approved loans can be disbursed
- Disbursement amount must not exceed approved amount
- System creates associated transaction records
- Multiple disbursements may be allowed based on loan type

### 5.3. Scoring Matrix

#### Calculate Credit Score

Calculates credit score for a loan application.

- **URL**: `/loans/{loanId}/score`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "creditScore": 600
}
```

##### Example Request

```
POST /loans/L001/score HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "creditScore": 600
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Credit score calculated successfully",
  "data": {
    "creditScore": 600,
    "riskAssessment": "Low",
    "scoringDetails": {
      "creditHistory": "Good",
      "paymentHistory": "Excellent",
      "debtToIncome": "Moderate"
    }
  }
}
```

##### Possible Response Codes

- 200 OK: Successfully calculated credit score
- 400 Bad Request: Invalid credit score input
- 404 Not Found: Loan not found
- 422 Unprocessable Entity: Unable to calculate score
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Credit score must be between 300 and 850
- Score calculation considers multiple factors
- Risk assessment is automatically determined
- Scoring details provide breakdown of factors

#### Assess Risk

Performs risk assessment for a loan application.

- **URL**: `/loans/{loanId}/risk-assessment`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "riskAssessment": "High"
}
```

##### Example Request

```
POST /loans/L001/risk-assessment HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "riskAssessment": "High"
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Risk assessment completed successfully",
  "data": {
    "riskAssessment": "High",
    "riskFactors": {
      "creditScore": "Below Average",
      "employmentStatus": "Temporary",
      "debtToIncome": "High"
    },
    "recommendations": {
      "maxLoanAmount": 2000,
      "requiredCollateral": true
    }
  }
}
```

##### Possible Response Codes

- 200 OK: Successfully completed risk assessment
- 400 Bad Request: Invalid risk assessment input
- 404 Not Found: Loan not found
- 422 Unprocessable Entity: Unable to assess risk
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Risk assessment considers multiple factors
- Assessment impacts loan approval decision
- System provides recommendations based on risk level
- Risk factors are documented for reference

#### Update Scoring Model

Updates the scoring model parameters.

- **URL**: `/loans/{loanId}/scoring-model`
- **Method**: POST
- **Authentication**: Required

##### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

##### Request Body

```json
{
  "scoringModel": {
    "weightage1": 0.3,
    "weightage2": 0.7
  }
}
```

##### Example Request

```
POST /loans/L001/scoring-model HTTP/1.1
Host: api.t24.example.com
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN

{
  "scoringModel": {
    "weightage1": 0.3,
    "weightage2": 0.7
  }
}
```

##### Example Response

```json
{
  "status": 0,
  "message": "Scoring model updated successfully",
  "data": {
    "scoringModel": {
      "weightage1": 0.3,
      "weightage2": 0.7
    },
    "lastUpdated": "2023-02-15T16:30:00Z",
    "updatedBy": "ADMIN123"
  }
}
```

##### Possible Response Codes

- 200 OK: Successfully updated scoring model
- 400 Bad Request: Invalid model parameters
- 403 Forbidden: Insufficient permissions
- 422 Unprocessable Entity: Invalid weightage distribution
- 500 Internal Server Error: An unexpected error occurred

##### Business Logic

- Weightages must sum to 1.0
- Only authorized administrators can update model
- System validates model parameters
- Changes are logged for audit purposes

## Error Handling

All endpoints follow the same error response format:

```json
{
  "status": 1,
  "message": "An error occurred",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error message"
  }
}
```

Common HTTP status codes:

- 200 OK: The request was successful
- 201 Created: A new resource was successfully created
- 400 Bad Request: The request was invalid
- 401 Unauthorized: Authentication is required
- 403 Forbidden: The authenticated user lacks required permissions
- 404 Not Found: The requested resource does not exist
- 409 Conflict: The request conflicts with current state
- 422 Unprocessable Entity: Request validation failed
- 429 Too Many Requests: Rate limit exceeded
- 500 Internal Server Error: Server encountered an unexpected condition

### Error Codes

| Code           | Description                                      |
|----------------|--------------------------------------------------|
| AUTH_001       | Invalid authentication token                      |
| AUTH_002       | Token expired                                    |
| CHEQUE_001     | Invalid cheque number format                     |
| CHEQUE_002     | Cheque already exists                           |
| LOAN_001       | Invalid loan amount                             |
| LOAN_002       | Customer not eligible for loan                   |
| SCORE_001      | Invalid credit score range                       |
| SCORE_002      | Risk assessment calculation failed               |
