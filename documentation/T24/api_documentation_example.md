# Temenos T24 API Documentation

## Introduction

This document provides comprehensive documentation for the Temenos T24 API. This API allows interaction with the Temenos T24 system, enabling operations such as retrieving customer information, account details, transactions, and initiating new transactions.

## Table of Contents

1. [Versioning](#versioning)
2. [Naming Conventions](#naming-conventions)
3. [Base URL](#base-url)
4. [Authentication](#authentication)
5. [APIs](#apis)
   5.1. [getCustomerByPhoneNumber](#51-getcustomerbyphonenumber)
   5.2. [getCustomerByCustomerNumber](#52-getcustomerbycustomernumber)
   5.3. [getAccountsByPhoneNumber](#53-getaccountsbyphonenumber)
   5.4. [getAccountsByCustomerNumber](#54-getaccountsbycustomernumber)
   5.5. [getAccount](#55-getaccount)
   5.6. [getAccountTransactions](#56-getaccounttransactions)
   5.7. [getCustomerTransactions](#57-getcustomertransactions)
   5.8. [getTransaction](#58-gettransaction)
   5.9. [initiateTransaction](#59-initiatetransaction)
   5.10. [currencyExchanges](#510-currencyexchanges)
6. [Error Handling](#error-handling)

## Versioning

| Version | Release Date | Description |
|---------|--------------|-------------|
| 1.0.0   | 2023-06-15   | Initial API release |
| 1.1.0   | 2023-06-16   | Changed all GET requests to POST |
| 1.1.1   | 2023-06-17   | Numbered all APIs for easier reference |
| 1.2.0   | 2023-06-18   | Updated response structure, added detailed response codes and business logic information |

## Naming Conventions

- All API endpoints use camelCase
- All request and response properties use camelCase
- All date parameters should be in ISO 8601 format (YYYY-MM-DD)

## Base URL

All API requests should be made to: `https://api.t24.example.com/v1`

## Authentication

This API uses Bearer token authentication. Include the token in the Authorization header of all requests:

```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## APIs

### 5.1. getCustomerByPhoneNumber

Retrieves customer information using the customer's phone number.

- **URL**: `/customers/phone`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "phoneNumber": "+**********"
}
```

#### Example Request

```
POST /customers/phone HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "phoneNumber": "+**********"
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Customer information retrieved successfully",
  "data": {
    "customerId": "C123456",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "+**********",
    "email": "<EMAIL>",
    "address": {
      "street": "123 Main St",
      "city": "Anytown",
      "country": "USA",
      "postalCode": "12345"
    },
    "customerStatus": "Active",
    "lastLoginDate": "2023-06-15T10:30:00Z"
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved customer information
- 400 Bad Request: Invalid phone number format
- 404 Not Found: No customer found with the given phone number
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- The phone number must be in a valid international format (e.g., +**********)
- If multiple customers are associated with the same phone number, the most recently updated account will be returned
- Inactive or closed customer accounts will still be returned, with their status reflected in the `customerStatus` field

### 5.2. getCustomerByCustomerNumber

Retrieves customer information using the customer number.

- **URL**: `/customers`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "customerNumber": "C123456"
}
```

#### Example Request

```
POST /customers HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "customerNumber": "C123456"
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Customer information retrieved successfully",
  "data": {
    "customerId": "C123456",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "+**********",
    "email": "<EMAIL>",
    "address": {
      "street": "123 Main St",
      "city": "Anytown",
      "country": "USA",
      "postalCode": "12345"
    },
    "customerStatus": "Active",
    "lastLoginDate": "2023-06-15T10:30:00Z",
    "accountSummary": {
      "totalAccounts": 3,
      "totalBalance": 25000.00,
      "primaryCurrency": "USD"
    }
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved customer information
- 400 Bad Request: Invalid customer number format
- 404 Not Found: No customer found with the given customer number
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- Customer numbers are unique identifiers in the format C followed by 6 digits
- This endpoint provides more detailed information than getCustomerByPhoneNumber, including an account summary
- The account summary includes the total number of accounts, total balance across all accounts, and the primary currency

### 5.3. getAccountsByPhoneNumber

Retrieves customer accounts using the customer's phone number.

- **URL**: `/accounts/phone`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "phoneNumber": "+**********"
}
```

#### Example Request

```
POST /accounts/phone HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "phoneNumber": "+**********"
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Customer accounts retrieved successfully",
  "data": {
    "customerId": "C123456",
    "totalAccounts": 2,
    "accounts": [
      {
        "accountNumber": "A987654",
        "accountType": "Savings",
        "balance": 5000.00,
        "currency": "USD",
        "status": "Active",
        "lastTransactionDate": "2023-06-14T09:30:00Z"
      },
      {
        "accountNumber": "A987655",
        "accountType": "Checking",
        "balance": 2500.00,
        "currency": "USD",
        "status": "Active",
        "lastTransactionDate": "2023-06-15T14:45:00Z"
      }
    ]
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved customer accounts
- 400 Bad Request: Invalid phone number format
- 404 Not Found: No customer found with the given phone number
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- The phone number must be in a valid international format (e.g., +**********)
- If multiple customers are associated with the same phone number, accounts for all matching customers will be returned
- Closed accounts are not included in the response
- The response includes a summary of the total number of active accounts

### 5.4. getAccountsByCustomerNumber

Retrieves customer accounts using the customer's number.

- **URL**: `/accounts/customer`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "customerNumber": "C123456"
}
```

#### Example Request

```
POST /accounts/customer HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "customerNumber": "C123456"
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Customer accounts retrieved successfully",
  "data": {
    "customerId": "C123456",
    "totalAccounts": 3,
    "totalBalance": 12500.00,
    "primaryCurrency": "USD",
    "accounts": [
      {
        "accountNumber": "A987654",
        "accountType": "Savings",
        "balance": 5000.00,
        "currency": "USD",
        "status": "Active",
        "interestRate": 0.01,
        "lastTransactionDate": "2023-06-14T09:30:00Z"
      },
      {
        "accountNumber": "A987655",
        "accountType": "Checking",
        "balance": 2500.00,
        "currency": "USD",
        "status": "Active",
        "overdraftLimit": 1000.00,
        "lastTransactionDate": "2023-06-15T14:45:00Z"
      },
      {
        "accountNumber": "A987656",
        "accountType": "FixedDeposit",
        "balance": 5000.00,
        "currency": "USD",
        "status": "Active",
        "maturityDate": "2024-06-15",
        "interestRate": 0.025
      }
    ]
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved customer accounts
- 400 Bad Request: Invalid customer number format
- 404 Not Found: No customer found with the given customer number
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- Customer numbers are unique identifiers in the format C followed by 6 digits
- This endpoint provides more detailed account information compared to getAccountsByPhoneNumber
- All accounts associated with the customer are returned, including closed accounts (with status "Closed")
- Account-specific details (e.g., interest rates, overdraft limits) are included based on the account type
- The response includes a summary of the total number of accounts and total balance across all accounts

### 5.5. getAccount

Retrieves detailed information for a specific account using the account number.

- **URL**: `/accounts`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "accountNumber": "A987654"
}
```

#### Example Request

```
POST /accounts HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "accountNumber": "A987654"
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Account information retrieved successfully",
  "data": {
    "accountNumber": "A987654",
    "accountType": "Savings",
    "balance": 5000.00,
    "availableBalance": 5000.00,
    "currency": "USD",
    "status": "Active",
    "openDate": "2022-01-01",
    "lastTransactionDate": "2023-06-14T09:30:00Z",
    "customerId": "C123456",
    "interestRate": 0.01,
    "annualPercentageYield": 0.0101,
    "minimumBalance": 100.00,
    "monthlyFee": 0,
    "transactionLimits": {
      "dailyWithdrawalLimit": 1000.00,
      "monthlyWithdrawalLimit": 5000.00
    }
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved account information
- 400 Bad Request: Invalid account number format
- 404 Not Found: No account found with the given account number
- 403 Forbidden: User does not have permission to access this account
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- Account numbers are unique identifiers in the format A followed by 6 digits
- The response includes detailed account information, which varies based on the account type
- Available balance may differ from the actual balance due to pending transactions or holds
- Interest rates and annual percentage yields are expressed as decimals (e.g., 0.01 for 1%)
- Transaction limits are included if applicable to the account type
- The user must have permission to access the requested account; this is typically limited to accounts owned by the authenticated user or accounts they have been granted explicit access to

### 5.6. getAccountTransactions

Retrieves transactions for a specific account within a given date range.

- **URL**: `/accounts/transactions`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "accountNumber": "A987654",
  "startDate": "2023-01-01",
  "endDate": "2023-06-15",
  "pageSize": 50,
  "pageNumber": 1
}
```

#### Example Request

```
POST /accounts/transactions HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "accountNumber": "A987654",
  "startDate": "2023-01-01",
  "endDate": "2023-06-15",
  "pageSize": 50,
  "pageNumber": 1
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Account transactions retrieved successfully",
  "data": {
    "accountNumber": "A987654",
    "startDate": "2023-01-01",
    "endDate": "2023-06-15",
    "totalTransactions": 120,
    "pageSize": 50,
    "pageNumber": 1,
    "totalPages": 3,
    "transactions": [
      {
        "transactionId": "T123456",
        "date": "2023-06-10T14:30:00Z",
        "amount": -100.00,
        "description": "ATM Withdrawal",
        "type": "DEBIT",
        "category": "CASH_WITHDRAWAL",
        "balance": 5000.00,
        "status": "COMPLETED"
      },
      {
        "transactionId": "T123457",
        "date": "2023-06-12T09:15:00Z",
        "amount": 2000.00,
        "description": "Salary Deposit",
        "type": "CREDIT",
        "category": "DEPOSIT",
        "balance": 7000.00,
        "status": "COMPLETED"
      }
      // ... (more transactions)
    ]
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved account transactions
- 400 Bad Request: Invalid account number format, invalid date range, or invalid pagination parameters
- 404 Not Found: No account found with the given account number
- 403 Forbidden: User does not have permission to access this account
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- Account numbers are unique identifiers in the format A followed by 6 digits
- The date range is inclusive of both the start and end dates
- Transactions are returned in descending order by date (most recent first)
- Pagination is implemented to limit the number of transactions returned in a single response
- If no pageSize is specified, a default of 50 is used
- The response includes metadata about the total number of transactions and pages for the given date range
- Transaction types include DEBIT (money leaving the account) and CREDIT (money entering the account)
- Transaction categories provide more specific classification of the transaction type
- The balance field shows the account balance after the transaction was completed
- Transaction status can be COMPLETED, PENDING, or FAILED

### 5.7. getCustomerTransactions

Retrieves transactions across all accounts for a customer within a given date range, with optional filtering.

- **URL**: `/customers/transactions`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "customerNumber": "C123456",
  "startDate": "2023-01-01",
  "endDate": "2023-06-15",
  "accountNumber": "A987654",  // Optional
  "transactionType": "DEBIT",  // Optional
  "recipientAccount": "A987655",  // Optional
  "pageSize": 50,
  "pageNumber": 1
}
```

#### Example Request

```
POST /customers/transactions HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "customerNumber": "C123456",
  "startDate": "2023-01-01",
  "endDate": "2023-06-15",
  "accountNumber": "A987654",
  "transactionType": "DEBIT",
  "recipientAccount": "A987655",
  "pageSize": 50,
  "pageNumber": 1
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Customer transactions retrieved successfully",
  "data": {
    "customerNumber": "C123456",
    "startDate": "2023-01-01",
    "endDate": "2023-06-15",
    "totalTransactions": 85,
    "pageSize": 50,
    "pageNumber": 1,
    "totalPages": 2,
    "transactions": [
      {
        "transactionId": "T123456",
        "accountNumber": "A987654",
        "date": "2023-06-10T14:30:00Z",
        "amount": -100.00,
        "description": "Transfer to A987655",
        "type": "DEBIT",
        "category": "TRANSFER",
        "recipientAccount": "A987655",
        "balance": 5000.00,
        "status": "COMPLETED"
      },
      {
        "transactionId": "T123457",
        "accountNumber": "A987654",
        "date": "2023-06-05T11:20:00Z",
        "amount": -50.00,
        "description": "Online Purchase - Amazon",
        "type": "DEBIT",
        "category": "PURCHASE",
        "balance": 5100.00,
        "status": "COMPLETED"
      }
      // ... (more transactions)
    ]
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved customer transactions
- 400 Bad Request: Invalid customer number format, invalid date range, or invalid pagination parameters
- 404 Not Found: No customer found with the given customer number
- 403 Forbidden: User does not have permission to access this customer's transactions
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- Customer numbers are unique identifiers in the format C followed by 6 digits
- The date range is inclusive of both the start and end dates
- Transactions are returned in descending order by date (most recent first)
- Pagination is implemented to limit the number of transactions returned in a single response
- If no pageSize is specified, a default of 50 is used
- The response includes metadata about the total number of transactions and pages for the given criteria
- Optional filters can be applied:
  - accountNumber: Limits transactions to a specific account
  - transactionType: Can be DEBIT or CREDIT
  - recipientAccount: Filters for transactions involving a specific recipient account
- Transaction types include DEBIT (money leaving the account) and CREDIT (money entering the account)
- Transaction categories provide more specific classification of the transaction type
- The balance field shows the account balance after the transaction was completed
- Transaction status can be COMPLETED, PENDING, or FAILED

### 5.8. getTransaction

Retrieves detailed information for a specific transaction using the transaction reference.

- **URL**: `/transactions`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "transactionId": "T123456"
}
```

#### Example Request

```
POST /transactions HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "transactionId": "T123456"
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Transaction details retrieved successfully",
  "data": {
    "transactionId": "T123456",
    "accountNumber": "A987654",
    "customerId": "C123456",
    "date": "2023-06-10T14:30:00Z",
    "amount": -100.00,
    "description": "Transfer to A987655",
    "type": "DEBIT",
    "category": "TRANSFER",
    "recipientAccount": "A987655",
    "recipientName": "Jane Doe",
    "balance": 5000.00,
    "status": "COMPLETED",
    "referenceNumber": "REF123456",
    "notes": "Monthly rent payment",
    "fees": 0.00,
    "location": {
      "ipAddress": "***********",
      "deviceId": "DEVICE123",
      "geoLocation": "New York, NY"
    }
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved transaction details
- 400 Bad Request: Invalid transaction ID format
- 404 Not Found: No transaction found with the given transaction ID
- 403 Forbidden: User does not have permission to access this transaction
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- Transaction IDs are unique identifiers in the format T followed by 6 digits
- The response includes detailed information about the transaction, including associated account and customer IDs
- For security reasons, only partial information about the recipient (name only) is provided
- The balance field shows the account balance after the transaction was completed
- Transaction status can be COMPLETED, PENDING, or FAILED
- Additional metadata such as reference numbers, notes, and location information is included when available
- Fees associated with the transaction, if any, are listed separately
- The user must have permission to access the transaction; this is typically limited to transactions on accounts owned by the authenticated user or accounts they have been granted explicit access to

### 5.9. initiateTransaction

Initiates a new transaction, such as a transfer between accounts or a payment.

- **URL**: `/transactions/initiate`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "debitAccount": "A987654",
  "creditAccount": "A987655",
  "amount": 100.00,
  "currency": "USD",
  "description": "Rent payment",
  "note": "June rent",
  "transactionDate": "2023-06-15T10:00:00Z"
}
```

#### Example Request

```
POST /transactions/initiate HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "debitAccount": "A987654",
  "creditAccount": "A987655",
  "amount": 100.00,
  "currency": "USD",
  "description": "Rent payment",
  "note": "June rent",
  "transactionDate": "2023-06-15T10:00:00Z"
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Transaction initiated successfully",
  "data": {
    "transactionId": "T123458",
    "status": "PENDING",
    "debitAccount": "A987654",
    "creditAccount": "A987655",
    "amount": 100.00,
    "currency": "USD",
    "description": "Rent payment",
    "note": "June rent",
    "transactionDate": "2023-06-15T10:00:00Z",
    "processingDetails": {
      "estimatedCompletionTime": "2023-06-15T10:05:00Z",
      "feeAmount": 0.50,
      "feeCurrency": "USD"
    },
    "debitAccountBalance": {
      "previousBalance": 5000.00,
      "newBalance": 4899.50
    }
  }
}
```

#### Possible Response Codes

- 200 OK: Transaction initiated successfully
- 400 Bad Request: Invalid request parameters (e.g., invalid account numbers, insufficient funds)
- 403 Forbidden: User does not have permission to initiate transactions from the specified account
- 404 Not Found: One or both of the specified accounts not found
- 422 Unprocessable Entity: Transaction violates business rules (e.g., exceeds daily limit)
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- Both debit and credit account numbers must be valid and active
- The user must have permission to initiate transactions from the debit account
- The debit account must have sufficient funds (including any transaction fees)
- The amount must be positive and within allowed transaction limits
- If the transaction is between accounts with different currencies, exchange rates will be applied
- Transactions may be subject to additional verification or processing steps, reflected in the status
- Fees, if any, are deducted from the debit account in addition to the transaction amount
- The transaction date can be set to a future date for scheduled transactions
- If no transaction date is provided, the current date and time will be used
- The response includes the new transaction ID, which can be used to check the status later
- The debit account's balance is updated immediately for display purposes, but the transaction may still be pending

### 5.10. currencyExchanges

Retrieves current currency exchange rates.

- **URL**: `/currency-exchanges`
- **Method**: POST
- **Authentication**: Required

#### Headers

| Header          | Value            | Description                   |
|-----------------|------------------|-------------------------------|
| Content-Type    | application/json | Specifies the request format  |
| Accept          | application/json | Specifies the response format |
| Authorization   | Bearer TOKEN     | Authentication token          |

#### Request Body

```json
{
  "baseCurrency": "USD",
  "targetCurrencies": ["EUR", "GBP", "JPY"]
}
```

#### Example Request

```
POST /currency-exchanges HTTP/1.1
Host: api.t24.example.com
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json

{
  "baseCurrency": "USD",
  "targetCurrencies": ["EUR", "GBP", "JPY"]
}
```

#### Example Response

```json
{
  "status": 0,
  "message": "Currency exchange rates retrieved successfully",
  "data": {
    "baseCurrency": "USD",
    "timestamp": "2023-06-15T14:30:00Z",
    "rates": {
      "EUR": 0.92,
      "GBP": 0.79,
      "JPY": 141.05
    },
    "metadata": {
      "provider": "ExchangeRateAPI",
      "lastUpdated": "2023-06-15T14:25:00Z",
      "nextUpdate": "2023-06-15T14:55:00Z"
    }
  }
}
```

#### Possible Response Codes

- 200 OK: Successfully retrieved currency exchange rates
- 400 Bad Request: Invalid base currency or target currencies
- 404 Not Found: One or more requested currencies not found
- 429 Too Many Requests: Rate limit exceeded for currency exchange requests
- 500 Internal Server Error: An unexpected error occurred

#### Business Logic

- The base currency is the currency against which exchange rates are provided
- If no base currency is specified, USD is used as the default
- If no target currencies are specified, rates for all available currencies are returned
- Exchange rates are typically updated every 30 minutes during business hours
- The timestamp in the response indicates when the rates were last updated
- Rates are provided as the amount of target currency per one unit of the base currency
- The metadata includes information about the data provider and update schedule
- Users may be subject to rate limits on how frequently they can request exchange rates
- Exchange rates provided are indicative and may not be the exact rates used for currency conversion in transactions

## Error Handling

All endpoints follow the same error response format:

```json
{
  "status": 1,
  "message": "An error occurred",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error message"
  }
}
```

Common HTTP status codes:

- 200 OK: The request was successful
- 400 Bad Request: The request was invalid or cannot be served
- 401 Unauthorized: The request requires authentication
- 403 Forbidden: The server understood the request but refuses to authorize it
- 404 Not Found: The requested resource could not be found
- 422 Unprocessable Entity: The request was well-formed but was unable to be followed due to semantic errors
- 429 Too Many Requests: The user has sent too many requests in a given amount of time
- 500 Internal Server Error: The server encountered an unexpected condition
