### API Coverage Statistics

**Total APIs**: 21
**T24 APIs Present**: 11 (52.4%)
**Provided APIs Present**: 21 (100%)

*Analysis*: While all APIs are implemented in the Provided APIs system, only about half (52.4%) have corresponding implementations in T24. The Provided APIs extend functionality beyond T24's capabilities, particularly in areas like virtual accounts, customer queries by phone number, and transaction management.

### T24 APIs vs Provided APIs

**T24 APIs vs Provided APIs Detailed Table:**

| API Name                        | Type   | T24 APIs Presence | Provided APIs Presence | Additional Notes                           |
|---------------------------------|--------|-------------------|------------------------|--------------------------------------------|
| CreateFundsTransfer             | POST   | Yes               | Yes                     | Available in T24 for fund creation         |
| Get Customer Profile            | GET    | Yes               | Yes                     | General customer profile in T24            |
| Get Account Balance             | GET    | Yes               | Yes                     | Retrieves account balance in T24           |
| Open Current Account            | POST   | Yes               | Yes                     | Opens a new current account in T24         |
| Get Book Balance                | GET    | Yes               | Yes                     | Retrieves book balance in T24              |
| Reserve Funds                   | POST   | Yes               | Yes                    | Both APIs include reserve fund functionality|
| Get Transaction                 | GET    | Yes               | Yes                    | Both APIs allow fetching a transaction     |
| Release Reserved Funds          | POST   | Yes               | Yes                     | Releases reserved funds in T24             |
| Get Account Transactions        | GET    | Yes               | Yes                    | Retrieves all transactions for an account  |
| Get Currency Exchanges          | GET    | Yes               | Yes                    | Retrieves currency exchange rates          |
| getCustomerByPhoneNumber        | GET    | No                | Yes                    | Provided API supports customer query by phone|
| getCustomerByCustomerNumber     | GET    | No                | Yes                    | Provided API supports customer query by customer number |
| getAccountsByPhoneNumber        | GET    | No                | Yes                    | Provided API allows querying accounts by phone number |
| getAccountsByCustomerNumber     | GET    | No                | Yes                    | Provided API allows querying accounts by customer number |
| getAccount                      | GET    | Yes                | Yes                    | Retrieves account details in Provided API  |
| getCustomerTransactions         | GET    | No                | Yes                    | Retrieves transactions by customer in Provided API |
| initiateTransaction             | POST   | No                | Yes                    | Initiates a new transaction in Provided API|
| createAccount                   | POST   | Yes                | Yes                    | Creates a new account in Provided API      |
| createVirtualAccount            | POST   | No                | Yes                    | Creates a virtual account in Provided API  |
| getVirtualAccountDetails        | GET    | No                | Yes                    | Retrieves virtual account details          |
| reverseTransaction              | POST   | No                | Yes                    | Reverses a completed transaction in Provided API |




### **Timelines**

Below is a table that can be used to represents timelines, and start and end dates.

| **Step** | **API Name**                | **HTTP Method** | **Description**                               | **Dependency**                    | **Timeline** | **Start Date** | **End Date** |
|----------|-----------------------------|----------------|-----------------------------------------------|-----------------------------------|--------------|----------------|--------------|
| 1        | Create Fund Transfer        | POST           | Initiate a fund transfer request.             | -                                 | Day 1        | 2024-11-01     | 2024-11-01   |
| 2        | Get Customer Profile        | GET            | Retrieve customer's detailed information.     | Create Fund Transfer              | Day 1        | 2024-11-01     | 2024-11-01   |
| 3        | Get Account Balance         | GET            | Check account balance for available funds.   | Get Customer Profile              | Day 2        | 2024-11-02     | 2024-11-02   |
| 4        | Open Current Account        | GET            | Open a new current account if needed.        | Get Customer Profile (Optional)   | Day 2        | 2024-11-02     | 2024-11-02   |
| 5        | Get Book Balance            | GET            | Fetch book balance to reconcile funds.       | Get Account Balance               | Day 3        | 2024-11-03     | 2024-11-03   |
| 6        | Reserve Funds               | POST           | Reserve funds before transaction.            | Get Book Balance                  | Day 3        | 2024-11-03     | 2024-11-03   |
| 7        | Get Transaction             | GET            | Retrieve transaction details.                | Reserve Funds                     | Day 4        | 2024-11-04     | 2024-11-04   |
| 8        | Release Reserved Funds      | POST           | Release reserved funds if transaction cancels.| Get Transaction                  | Day 4        | 2024-11-04     | 2024-11-04   |
| 9        | Get Account Transactions    | GET            | Retrieve transaction history of account.     | Release Reserved Funds            | Day 5        | 2024-11-05     | 2024-11-05   |
| 10       | Get Currency Exchanges      | GET            | Retrieve exchange rates for transactions.    | Get Account Transactions          | Day 5        | 2024-11-05     | 2024-11-05   |

---

