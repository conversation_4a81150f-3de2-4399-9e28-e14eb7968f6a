Callback API Documentation and Examples
=====================================

All endpoints are under: /api/third-party/callback
Authentication: Requires valid API key in X-API-Key header

1. Register New Callback
-----------------------
POST /register

Example 1 - Basic Registration:
{
  "callback_url": "https://api.example.com/webhook",
  "callback_type": "transaction",  # or "wallet"
  "api_key": "your-api-key",
  "secret_key": "your-secret-key",
  "headers": {
    "Custom-Header": "custom-value"
  }
}

Example 2 - Registration with Validation Scheme:
{
  "callback_url": "https://api.example.com/webhook",
  "callback_type": "transaction",
  "api_key": "your-api-key",
  "secret_key": "your-secret-key",
  "headers": {
    "Custom-Header": "custom-value"
  },
  "validation_scheme": {
    "reference": "transaction_id",
    "transaction.credit_account": "credit_account",
    "transaction.debit_account": "debit_account",
    "transaction.description": "narration",
    "transaction.type": "transaction_type",
    "transaction.transaction_details.merchant_id": "merchant_id",
    "transaction.transaction_details.customer_name": "customer_name",
    "transaction_status": "status"
  }
}

Response:
{
  "data": {
    "id": 1,
    "callback_url": "https://api.example.com/webhook",
    "callback_type": "transaction",
    "status": "active",
    "api_key": "your-api-key",
    "headers": {
      "Custom-Header": "custom-value"
    },
    "validation_scheme": {
      "reference": "transaction_id",
      ...
    },
    "inserted_at": "2024-03-20T10:00:00Z",
    "updated_at": "2024-03-20T10:00:00Z"
  }
}

2. List All Callbacks
-------------------
POST /list

Request:
{
  "api_key": "your-api-key"
}

Response:
{
  "data": [
    {
      "id": 1,
      "callback_url": "https://api.example.com/webhook",
      "callback_type": "transaction",
      "status": "active",
      "api_key": "your-api-key",
      "headers": {
        "Custom-Header": "custom-value"
      },
      "validation_scheme": {
        "reference": "transaction_id",
        ...
      },
      "inserted_at": "2024-03-20T10:00:00Z",
      "updated_at": "2024-03-20T10:00:00Z"
    }
  ]
}

3. Get Specific Callback
----------------------
POST /show

Request:
{
  "api_key": "your-api-key",
  "id": 1
}

Response:
{
  "data": {
    "id": 1,
    "callback_url": "https://api.example.com/webhook",
    "callback_type": "transaction",
    "status": "active",
    "api_key": "your-api-key",
    "headers": {
      "Custom-Header": "custom-value"
    },
    "validation_scheme": {
      "reference": "transaction_id",
      ...
    },
    "inserted_at": "2024-03-20T10:00:00Z",
    "updated_at": "2024-03-20T10:00:00Z"
  }
}

4. Update Callback
----------------
POST /update

Example 1 - Basic Update:
{
  "api_key": "your-api-key",
  "id": 1,
  "callback_url": "https://api.example.com/new-webhook",
  "status": "inactive",
  "headers": {
    "New-Header": "new-value"
  }
}

Example 2 - Update with Validation Scheme:
{
  "api_key": "your-api-key",
  "id": 1,
  "validation_scheme": {
    "reference": "new_transaction_id",
    "transaction.credit_account": "destination_account",
    "transaction.debit_account": "source_account",
    "transaction.description": "description",
    "transaction.type": "type",
    "transaction_status": "transaction_status"
  }
}

Response:
{
  "data": {
    "id": 1,
    "callback_url": "https://api.example.com/new-webhook",
    "callback_type": "transaction",
    "status": "inactive",
    "api_key": "your-api-key",
    "headers": {
      "New-Header": "new-value"
    },
    "validation_scheme": {
      "reference": "new_transaction_id",
      ...
    },
    "inserted_at": "2024-03-20T10:00:00Z",
    "updated_at": "2024-03-20T10:30:00Z"
  }
}

5. Delete Callback
----------------
POST /delete

Request:
{
  "api_key": "your-api-key",
  "id": 1
}

Response: 204 No Content

6. Get Callback Status
--------------------
POST /status

Request:
{
  "api_key": "your-api-key",
  "id": 1
}

Response:
{
  "data": {
    "total_callbacks": 100,
    "successful_callbacks": 95,
    "failed_callbacks": 3,
    "pending_callbacks": 2,
    "average_duration_ms": 245.5
  }
}

7. Get Callback Report
--------------------
POST /report

Request:
{
  "api_key": "your-api-key",
  "status": "failed",  # Optional: Filter by status (success, failed, pending)
  "from": "2024-03-19T00:00:00Z",  # Optional: Start date (ISO 8601)
  "to": "2024-03-20T23:59:59Z",    # Optional: End date (ISO 8601)
  "registry_id": 1,  # Optional: Filter by specific callback registry
  "limit": 10       # Optional: Limit number of results
}

Response:
{
  "data": [
    {
      "id": 1,
      "callback_type": "transaction",
      "callback_url": "https://api.example.com/webhook",
      "request_headers": {
        "Content-Type": "application/json",
        "X-API-Key": "your-api-key"
      },
      "request_body": {
        "transaction_id": "123",
        "amount": 100
      },
      "response_status": 500,
      "response_headers": {
        "Content-Type": "application/json"
      },
      "response_body": {
        "error": "Internal server error"
      },
      "duration_ms": 350,
      "status": "failed",
      "error_message": "HTTP 500 response",
      "retry_count": 2,
      "inserted_at": "2024-03-20T10:15:00Z"
    }
  ]
}

8. Schema Validation Endpoints
---------------------------
POST /schema/fields
Request:
{
  "api_key": "your-api-key",
  "type": "transaction"  # or "wallet"
}

Response:
{
  "data": {
    "type": "transaction",
    "available_fields": [
      "transaction_id",
      "reference",
      "status",
      "amount",
      "currency",
      "narration",
      "transaction_type",
      "credit_account",
      "debit_account",
      ...
    ]
  }
}

POST /schema/validate
Request:
{
  "api_key": "your-api-key",
  "type": "transaction",
  "schema": {
    "reference": "transaction_id",
    "transaction.credit_account": "credit_account"
  }
}

Response:
{
  "data": {
    "valid": true,
    "schema": {
      "reference": "transaction_id",
      "transaction.credit_account": "credit_account"
    }
  }
}

POST /schema/test
Request:
{
  "api_key": "your-api-key",
  "type": "transaction",
  "schema": {
    "reference": "transaction_id",
    "transaction.credit_account": "credit_account"
  },
  "payload": {
    "reference": "123",
    "transaction": {
      "credit_account": "456"
    }
  }
}

Response:
{
  "data": {
    "transaction_id": "123",
    "credit_account": "456"
  }
}

Example Callback Payload
----------------------
When a callback is triggered, your endpoint will receive a POST request with this structure:

{
  "event_type": "transaction",
  "timestamp": "2024-03-20T10:15:00Z",
  "data": {
    // Event-specific data mapped according to validation_scheme
  },
  "metadata": {
    "attempt": 1,
    "callback_id": "123"
  }
}

The request will include these headers:
- Content-Type: application/json
- X-API-Key: your configured API key
- X-Signature: HMAC SHA-256 signature (if secret key was provided)

To verify the signature:
1. Get the raw request body
2. Create HMAC SHA-256 hash using your secret key
3. Compare with X-Signature header (case-insensitive)

Example signature verification (pseudo-code):
```
expected = Base16.encode(hmac_sha256(secret_key, raw_body)).lowercase()
actual = X-Signature.header.lowercase()
valid = expected == actual
