defmodule ServiceManagerWeb.Backend.DynamicFormsLive.TriggersIndex do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Triggers.TriggerManager
  alias ServiceManager.Triggers.TriggerRegistry
  alias ServiceManager.Triggers.RateLimiter
  alias ServiceManagerWeb.Backend.DynamicFormsLive.TriggerFormComponent

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:page_title, "Trigger Management")
      |> assign(:triggers, [])
      |> assign(:execution_stats, %{})
      |> assign(:registry_stats, %{})
      |> assign(:rate_limit_stats, %{})
      |> assign(:loading, true)
      |> assign(:selected_trigger, nil)
      |> assign(:filter_module, "")
      |> assign(:filter_status, "all")

    socket =
      if connected?(socket) do
        :timer.send_interval(10_000, :refresh_stats)
        load_triggers_and_stats(socket)
      else
        socket
      end

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    trigger = TriggerManager.get_trigger!(id)
    socket
    |> assign(:page_title, "Edit Trigger")
    |> assign(:selected_trigger, trigger)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Trigger")
    |> assign(:selected_trigger, nil)
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Trigger Management")
    |> assign(:selected_trigger, nil)
  end

  @impl true
  def handle_info(:refresh_stats, socket) do
    socket = load_triggers_and_stats(socket)
    {:noreply, socket}
  end

  def handle_info({TriggerFormComponent, {:saved, trigger}}, socket) do
    socket =
      socket
      |> put_flash(:info, "Trigger #{trigger.name} saved successfully")
      |> load_triggers_and_stats()

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter", %{"module" => module, "status" => status}, socket) do
    socket =
      socket
      |> assign(:filter_module, module)
      |> assign(:filter_status, status)
      |> load_triggers_and_stats()

    {:noreply, socket}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    trigger = TriggerManager.get_trigger!(id)

    case TriggerManager.delete_trigger(trigger) do
      {:ok, _} ->
        socket =
          socket
          |> put_flash(:info, "Trigger deleted successfully")
          |> load_triggers_and_stats()

        {:noreply, socket}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Unable to delete trigger")}
    end
  end

  @impl true
  def handle_event("toggle_status", %{"id" => id}, socket) do
    trigger = TriggerManager.get_trigger!(id)
    new_status = not trigger.enabled

    case TriggerManager.update_trigger(trigger, %{enabled: new_status}) do
      {:ok, _} ->
        socket =
          socket
          |> put_flash(:info, "Trigger #{if new_status, do: "enabled", else: "disabled"}")
          |> load_triggers_and_stats()

        {:noreply, socket}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Unable to update trigger status")}
    end
  end

  @impl true
  def handle_event("test_trigger", %{"mount_point" => mount_point}, socket) do
    case ServiceManager.Triggers.TriggerExecutor.test_trigger(mount_point, %{}) do
      {:ok, result} ->
        socket =
          socket
          |> put_flash(:info, "Trigger test successful: #{inspect(result)}")
          |> load_triggers_and_stats()

        {:noreply, socket}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Trigger test failed: #{reason}")}
    end
  end

  @impl true
  def handle_event("refresh_registry", _params, socket) do
    TriggerRegistry.refresh_triggers()

    socket =
      socket
      |> put_flash(:info, "Trigger registry refreshed")
      |> load_triggers_and_stats()

    {:noreply, socket}
  end

  defp load_triggers_and_stats(socket) do
    # Load triggers with filters
    triggers = load_filtered_triggers(socket.assigns.filter_module, socket.assigns.filter_status)

    # Load statistics - use last 24 hours as default range
    twenty_four_hours_ago = DateTime.utc_now() |> DateTime.add(-24 * 3600, :second)
    execution_stats = TriggerManager.get_execution_statistics(twenty_four_hours_ago)
    registry_stats = TriggerRegistry.get_registry_stats()
    
    # Handle RateLimiter.get_stats() which might not exist yet
    rate_limit_stats = case function_exported?(RateLimiter, :get_stats, 0) do
      true -> RateLimiter.get_stats()
      false -> %{healthy: true, stats: %{total_requests: 0, blocked_requests: 0}}
    end

    socket
    |> assign(:triggers, triggers)
    |> assign(:execution_stats, execution_stats)
    |> assign(:registry_stats, registry_stats)
    |> assign(:rate_limit_stats, rate_limit_stats)
    |> assign(:loading, false)
  end

  defp load_filtered_triggers(module_filter, status_filter) do
    triggers = TriggerManager.list_triggers()

    triggers
    |> filter_by_module(module_filter)
    |> filter_by_status(status_filter)
    |> Enum.sort_by(& &1.updated_at, {:desc, DateTime})
  end

  defp filter_by_module(triggers, ""), do: triggers
  defp filter_by_module(triggers, module_filter) do
    Enum.filter(triggers, fn trigger ->
      String.contains?(String.downcase(trigger.module_name), String.downcase(module_filter))
    end)
  end

  defp filter_by_status(triggers, "all"), do: triggers
  defp filter_by_status(triggers, "enabled") do
    Enum.filter(triggers, & &1.enabled)
  end
  defp filter_by_status(triggers, "disabled") do
    Enum.filter(triggers, &(not &1.enabled))
  end
  defp filter_by_status(triggers, "rate_limited") do
    Enum.filter(triggers, & &1.rate_limiting_enabled)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="Trigger Management"
        subtitle="Manage dynamic function triggers"
        current_page={:triggers}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Triggers"}
        ]}
      />

      <div class="px-6 py-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Triggers -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-bolt" class="h-6 w-6 text-indigo-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Triggers</p>
                <p class="text-2xl font-bold text-gray-900"><%= length(@triggers) %></p>
                <p class="text-xs text-indigo-600">
                  <%= Enum.count(@triggers, & &1.enabled) %> enabled
                </p>
              </div>
            </div>
          </div>

          <!-- Registry Stats -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-server" class="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">In Registry</p>
                <p class="text-2xl font-bold text-gray-900"><%= @registry_stats[:trigger_count] || 0 %></p>
                <p class="text-xs text-green-600">
                  <%= @registry_stats[:mount_points_count] || 0 %> mount points
                </p>
              </div>
            </div>
          </div>

          <!-- Execution Stats -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-play" class="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Executions</p>
                <p class="text-2xl font-bold text-gray-900"><%= @execution_stats[:total_executions] || 0 %></p>
                <p class="text-xs text-blue-600">
                  <%= @execution_stats[:successful_executions] || 0 %> successful
                </p>
              </div>
            </div>
          </div>

          <!-- Rate Limit Stats -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-shield-check" class="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Rate Limits</p>
                <p class="text-2xl font-bold text-gray-900"><%= @rate_limit_stats[:total_requests] || 0 %></p>
                <p class="text-xs text-orange-600">
                  <%= @rate_limit_stats[:blocked_requests] || 0 %> blocked
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Controls -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div class="flex space-x-4">
                <.link
                  patch={~p"/mobileBanking/dynamic-forms/triggers/new"}
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                  New Trigger
                </.link>

                <button
                  type="button"
                  phx-click="refresh_registry"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <.icon name="hero-arrow-path" class="h-4 w-4 mr-2" />
                  Refresh Registry
                </button>
              </div>

              <div class="flex space-x-4">
                <!-- Module Filter -->
                <div class="flex-1 min-w-0">
                  <input
                    type="text"
                    placeholder="Filter by module..."
                    value={@filter_module}
                    phx-change="filter"
                    name="module"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>

                <!-- Status Filter -->
                <div class="flex-shrink-0">
                  <select
                    name="status"
                    phx-change="filter"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  >
                    <option value="all" selected={@filter_status == "all"}>All Status</option>
                    <option value="enabled" selected={@filter_status == "enabled"}>Enabled</option>
                    <option value="disabled" selected={@filter_status == "disabled"}>Disabled</option>
                    <option value="rate_limited" selected={@filter_status == "rate_limited"}>Rate Limited</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Triggers Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Triggers</h3>
            <p class="mt-1 text-sm text-gray-600">
              Manage your dynamic function triggers and monitor their execution.
            </p>
          </div>

          <%= if @loading do %>
            <div class="p-6">
              <div class="animate-pulse space-y-4">
                <%= for _ <- 1..5 do %>
                  <div class="flex space-x-4">
                    <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                <% end %>
              </div>
            </div>
          <% else %>
            <%= if Enum.empty?(@triggers) do %>
              <div class="p-12 text-center">
                <.icon name="hero-bolt" class="mx-auto h-12 w-12 text-gray-400" />
                <h3 class="mt-2 text-sm font-medium text-gray-900">No triggers</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating a new trigger.</p>
                <div class="mt-6">
                  <.link
                    patch={~p"/mobileBanking/dynamic-forms/triggers/new"}
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                    New Trigger
                  </.link>
                </div>
              </div>
            <% else %>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trigger
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Mount Point
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Settings
                      </th>
                      <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for trigger <- @triggers do %>
                      <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="flex items-center">
                            <div class="flex-shrink-0">
                              <div class={[
                                "h-10 w-10 rounded-lg flex items-center justify-center",
                                (if trigger.enabled, do: "bg-green-100", else: "bg-gray-100")
                              ]}>
                                <.icon name="hero-bolt" class={[
                                  "h-5 w-5",
                                  (if trigger.enabled, do: "text-green-600", else: "text-gray-400")
                                ]} />
                              </div>
                            </div>
                            <div class="ml-4">
                              <div class="text-sm font-medium text-gray-900"><%= trigger.name %></div>
                              <div class="text-sm text-gray-500">
                                <%= trigger.module_name %>.<%= trigger.function_name %>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <code class="text-sm text-gray-900 bg-gray-100 px-2 py-1 rounded">
                            <%= trigger.mount_point %>
                          </code>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class={[
                            "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                            case trigger.execution_type do
                              :sync -> "bg-blue-100 text-blue-800"
                              :async -> "bg-purple-100 text-purple-800"
                              _ -> "bg-gray-100 text-gray-800"
                            end
                          ]}>
                            <%= trigger.execution_type %>
                          </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class={[
                            "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                            (if trigger.enabled, do: "bg-green-100 text-green-800", else: "bg-red-100 text-red-800")
                          ]}>
                            <%= if trigger.enabled, do: "Enabled", else: "Disabled" %>
                          </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div class="flex space-x-2">
                            <%= if trigger.rate_limiting_enabled do %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                                Rate Limited
                              </span>
                            <% end %>
                            <%= if trigger.logging_enabled do %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                Logged
                              </span>
                            <% end %>
                            <%= if trigger.sandbox_execution do %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                Sandboxed
                              </span>
                            <% end %>
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div class="flex space-x-2">
                            <button
                              type="button"
                              phx-click="test_trigger"
                              phx-value-mount_point={trigger.mount_point}
                              class="text-indigo-600 hover:text-indigo-900"
                              title="Test trigger"
                            >
                              <.icon name="hero-play" class="h-4 w-4" />
                            </button>
                            <.link
                              patch={~p"/mobileBanking/dynamic-forms/triggers/#{trigger}/edit"}
                              class="text-indigo-600 hover:text-indigo-900"
                              title="Edit trigger"
                            >
                              <.icon name="hero-pencil" class="h-4 w-4" />
                            </.link>
                            <button
                              type="button"
                              phx-click="toggle_status"
                              phx-value-id={trigger.id}
                              class={[
                                "hover:text-gray-900",
                                (if trigger.enabled, do: "text-red-600", else: "text-green-600")
                              ]}
                              title={if trigger.enabled, do: "Disable trigger", else: "Enable trigger"}
                            >
                              <.icon name={if trigger.enabled, do: "hero-pause", else: "hero-play"} class="h-4 w-4" />
                            </button>
                            <button
                              type="button"
                              phx-click="delete"
                              phx-value-id={trigger.id}
                              data-confirm="Are you sure?"
                              class="text-red-600 hover:text-red-900"
                              title="Delete trigger"
                            >
                              <.icon name="hero-trash" class="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Modal for New/Edit Trigger -->
    <.modal
      :if={@live_action in [:new, :edit]}
      id="trigger-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/triggers")}
    >
      <.live_component
        module={TriggerFormComponent}
        id={@selected_trigger && @selected_trigger.id || :new}
        title={@page_title}
        action={@live_action}
        trigger={@selected_trigger}
        patch={~p"/mobileBanking/dynamic-forms/triggers"}
      />
    </.modal>
    """
  end
end