defmodule ServiceManagerWeb.Backend.DynamicFormsLive.TriggerFormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Triggers.TriggerManager
  alias ServiceManager.Triggers.Schemas.Trigger

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Create or edit a trigger to expose functions via HTTP API</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="trigger-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input field={@form[:name]} type="text" label="Name" required />
        <.input field={@form[:description]} type="textarea" label="Description" />
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <.input field={@form[:module_name]} type="text" label="Module Name" required />
          <.input field={@form[:function_name]} type="text" label="Function Name" required />
        </div>

        <.input
          field={@form[:mount_point]}
          type="text"
          label="Mount Point"
          readonly
          help="Auto-generated from module and function names"
        />

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <.input
            field={@form[:execution_type]}
            type="select"
            label="Execution Type"
            options={[
              {"Synchronous", "sync"},
              {"Asynchronous", "async"}
            ]}
            required
          />
          
          <.input field={@form[:enabled]} type="checkbox" label="Enabled" />
        </div>

        <div class="space-y-4 p-4 bg-gray-50 rounded-lg">
          <h4 class="text-sm font-medium text-gray-900">Security & Monitoring</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <.input field={@form[:rate_limiting_enabled]} type="checkbox" label="Enable Rate Limiting" />
            <.input field={@form[:logging_enabled]} type="checkbox" label="Enable Logging" />
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <.input field={@form[:return_data]} type="checkbox" label="Return Function Data" />
            <.input field={@form[:sandbox_execution]} type="checkbox" label="Sandbox Execution" />
          </div>
        </div>

        <!-- Input Schema Section -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900">Input Schema (JSON)</h4>
            <button
              type="button"
              phx-click="generate_sample_schema"
              phx-target={@myself}
              class="text-sm text-indigo-600 hover:text-indigo-500"
            >
              Generate Sample
            </button>
          </div>
          
          <.input
            field={@form[:input_schema]}
            type="textarea"
            label="JSON Schema"
            rows="8"
            help="Define the expected input parameters using JSON Schema format"
            placeholder={@sample_schema}
          />
          
          <%= if @schema_error do %>
            <div class="text-sm text-red-600">
              <.icon name="hero-exclamation-triangle" class="h-4 w-4 inline mr-1" />
              <%= @schema_error %>
            </div>
          <% end %>
        </div>

        <!-- Rate Limiting Configuration -->
        <%= if Ecto.Changeset.get_field(@form.source, :rate_limiting_enabled) do %>
          <div class="space-y-4 p-4 bg-orange-50 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900">Rate Limiting Configuration</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <.input
                field={@form[:rate_limit_per_minute]}
                type="number"
                label="Requests per Minute"
                min="1"
                max="10000"
                value={@form[:rate_limit_per_minute].value || 100}
              />
              <.input
                field={@form[:rate_limit_burst]}
                type="number"
                label="Burst Limit"
                min="1"
                max="100"
                value={@form[:rate_limit_burst].value || 10}
              />
              <.input
                field={@form[:rate_limit_window_seconds]}
                type="number"
                label="Window (seconds)"
                min="1"
                max="3600"
                value={@form[:rate_limit_window_seconds].value || 60}
              />
            </div>
          </div>
        <% end %>

        <!-- Module and Function Validation -->
        <%= if @validation_status do %>
          <div class={[
            "p-4 rounded-lg",
            case @validation_status.valid do
              true -> "bg-green-50 text-green-800"
              false -> "bg-red-50 text-red-800"
              _ -> "bg-yellow-50 text-yellow-800"
            end
          ]}>
            <div class="flex items-center">
              <.icon name={
                case @validation_status.valid do
                  true -> "hero-check-circle"
                  false -> "hero-x-circle"
                  _ -> "hero-exclamation-triangle"
                end
              } class="h-5 w-5 mr-2" />
              <div>
                <p class="font-medium"><%= @validation_status.message %></p>
                <%= if @validation_status.details do %>
                  <p class="text-sm mt-1"><%= @validation_status.details %></p>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>

        <:actions>
          <.button phx-disable-with="Saving...">Save Trigger</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{trigger: trigger} = assigns, socket) do
    changeset = TriggerManager.change_trigger(trigger || %Trigger{})

    {:ok,
     socket
     |> assign(assigns)
     |> assign_form(changeset)
     |> assign(:validation_status, nil)
     |> assign(:schema_error, nil)
     |> assign(:sample_schema, sample_input_schema())}
  end

  @impl true
  def handle_event("validate", %{"trigger" => trigger_params}, socket) do
    changeset =
      socket.assigns.trigger
      |> TriggerManager.change_trigger(trigger_params)
      |> Map.put(:action, :validate)

    # Validate module and function if provided
    validation_status = validate_module_function(trigger_params)
    
    # Validate JSON schema if provided
    schema_error = validate_json_schema(trigger_params["input_schema"])

    {:noreply,
     socket
     |> assign_form(changeset)
     |> assign(:validation_status, validation_status)
     |> assign(:schema_error, schema_error)}
  end

  @impl true
  def handle_event("save", %{"trigger" => trigger_params}, socket) do
    save_trigger(socket, socket.assigns.action, trigger_params)
  end

  @impl true
  def handle_event("generate_sample_schema", _params, socket) do
    sample_schema = sample_input_schema()
    
    # Update the form with the sample schema
    current_params = socket.assigns.form.params
    updated_params = Map.put(current_params, "input_schema", sample_schema)
    
    changeset =
      socket.assigns.trigger
      |> TriggerManager.change_trigger(updated_params)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  defp save_trigger(socket, :edit, trigger_params) do
    case TriggerManager.update_trigger(socket.assigns.trigger, trigger_params) do
      {:ok, trigger} ->
        notify_parent({:saved, trigger})

        {:noreply,
         socket
         |> put_flash(:info, "Trigger updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp save_trigger(socket, :new, trigger_params) do
    case TriggerManager.create_trigger(trigger_params) do
      {:ok, trigger} ->
        notify_parent({:saved, trigger})

        {:noreply,
         socket
         |> put_flash(:info, "Trigger created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})

  defp validate_module_function(%{"module_name" => module_name, "function_name" => function_name})
       when is_binary(module_name) and is_binary(function_name) and
              module_name != "" and function_name != "" do
    try do
      module_atom = String.to_existing_atom("Elixir." <> module_name)
      function_atom = String.to_existing_atom(function_name)
      
      cond do
        not Code.ensure_loaded?(module_atom) ->
          %{valid: false, message: "Module not found", details: "#{module_name} is not available"}
          
        not function_exported?(module_atom, function_atom, 2) ->
          %{valid: false, message: "Function not found", details: "#{function_name}/2 is not exported by #{module_name}"}
          
        true ->
          %{valid: true, message: "Module and function validated", details: "Function #{module_name}.#{function_name}/2 is available"}
      end
    rescue
      ArgumentError ->
        %{valid: false, message: "Invalid module or function name", details: "Names must be valid Elixir atoms"}
    end
  end

  defp validate_module_function(_), do: nil

  defp validate_json_schema(nil), do: nil
  defp validate_json_schema(""), do: nil
  defp validate_json_schema(schema_string) when is_binary(schema_string) do
    try do
      Jason.decode!(schema_string)
      nil
    rescue
      Jason.DecodeError -> "Invalid JSON format"
    end
  end

  defp sample_input_schema do
    """
    {
      "type": "object",
      "properties": {
        "phone": {
          "type": "string",
          "pattern": "^\\\\+?[1-9]\\\\d{1,14}$",
          "description": "Phone number in international format"
        },
        "message": {
          "type": "string",
          "minLength": 1,
          "maxLength": 500,
          "description": "Message content"
        },
        "priority": {
          "type": "string",
          "enum": ["low", "normal", "high"],
          "default": "normal"
        }
      },
      "required": ["phone", "message"],
      "additionalProperties": false
    }
    """
  end
end