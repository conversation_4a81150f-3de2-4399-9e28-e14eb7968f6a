defmodule ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white border-b border-gray-200 shadow-sm">
      <div class="px-6 py-3">
        <div class="flex items-center justify-between">
          <!-- Left: Current Page Title -->
          <div class="flex items-center space-x-3">
            <h1 class="text-xl font-semibold text-gray-900"><%= @page_title %></h1>
            <%= if assigns[:subtitle] do %>
              <span class="text-sm text-gray-500">·</span>
              <span class="text-sm text-gray-600"><%= @subtitle %></span>
            <% end %>
          </div>

          <!-- Right: Navigation Buttons -->
          <div class="flex items-center space-x-2">
            <!-- Dashboard -->
            <.link
              navigate={~p"/mobileBanking/dynamic-forms"}
              class={[
                "inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == :dashboard, do: "bg-indigo-100 text-indigo-700", else: "text-gray-700 hover:bg-gray-100")
              ]}
              title="Dashboard"
            >
              <.icon name="hero-chart-bar" class="h-4 w-4 mr-2" />
              Dashboard
            </.link>

            <!-- Forms -->
            <.link
              navigate={~p"/mobileBanking/dynamic-forms/forms"}
              class={[
                "inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == :forms, do: "bg-green-100 text-green-700", else: "text-gray-700 hover:bg-gray-100")
              ]}
              title="Forms"
            >
              <.icon name="hero-document-text" class="h-4 w-4 mr-2" />
              Forms
            </.link>

            <!-- Processes/Plugins -->
            <.link
              navigate={~p"/mobileBanking/dynamic-forms/processes"}
              class={[
                "inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == :processes, do: "bg-purple-100 text-purple-700", else: "text-gray-700 hover:bg-gray-100")
              ]}
              title="Plugin Library"
            >
              <.icon name="hero-puzzle-piece" class="h-4 w-4 mr-2" />
              Plugins
            </.link>

            <!-- Code Steps -->
            <.link
              navigate={~p"/mobileBanking/dynamic-forms/code-steps"}
              class={[
                "inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == :code_steps, do: "bg-amber-100 text-amber-700", else: "text-gray-700 hover:bg-gray-100")
              ]}
              title="Code Steps Library"
            >
              <.icon name="hero-squares-plus" class="h-4 w-4 mr-2" />
              Code Steps
            </.link>

            <!-- Routes -->
            <.link
              navigate={~p"/mobileBanking/dynamic-forms/routes"}
              class={[
                "inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == :routes, do: "bg-blue-100 text-blue-700", else: "text-gray-700 hover:bg-gray-100")
              ]}
              title="API Routes"
            >
              <.icon name="hero-globe-alt" class="h-4 w-4 mr-2" />
              Routes
            </.link>

            <!-- Triggers -->
            <.link
              navigate={~p"/mobileBanking/dynamic-forms/triggers"}
              class={[
                "inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == :triggers, do: "bg-indigo-100 text-indigo-700", else: "text-gray-700 hover:bg-gray-100")
              ]}
              title="Dynamic Triggers"
            >
              <.icon name="hero-bolt" class="h-4 w-4 mr-2" />
              Triggers
            </.link>

            <!-- Trigger Monitoring -->
            <.link
              navigate={~p"/mobileBanking/dynamic-forms/triggers/monitoring"}
              class={[
                "inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == :trigger_monitoring, do: "bg-indigo-100 text-indigo-700", else: "text-gray-700 hover:bg-gray-100")
              ]}
              title="Trigger System Monitoring"
            >
              <.icon name="hero-chart-bar-square" class="h-4 w-4 mr-2" />
              Monitoring
            </.link>

            <!-- API Documentation -->
            <.link
              navigate={~p"/api/docs"}
              target="_blank"
              class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100 transition-colors duration-200"
              title="API Documentation"
            >
              <.icon name="hero-book-open" class="h-4 w-4 mr-2" />
              API Docs
            </.link>

            <!-- Settings/Configuration -->
            <div class="relative group">
              <button class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                <.icon name="hero-cog-6-tooth" class="h-4 w-4 mr-2" />
                Settings
                <.icon name="hero-chevron-down" class="h-3 w-3 ml-1" />
              </button>
              
              <!-- Dropdown Menu -->
              <div class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div class="py-1">
                  <.link
                    navigate={~p"/mobileBanking/system-settings"}
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <.icon name="hero-wrench-screwdriver" class="h-4 w-4 mr-2 inline" />
                    System Settings
                  </.link>
                  <.link
                    navigate={~p"/mobileBanking/user-management"}
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <.icon name="hero-users" class="h-4 w-4 mr-2 inline" />
                    User Management
                  </.link>
                  <hr class="my-1" />
                  <.link
                    navigate={~p"/mobileBanking/logs"}
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <.icon name="hero-document-text" class="h-4 w-4 mr-2 inline" />
                    System Logs
                  </.link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Breadcrumb (if provided) -->
        <%= if assigns[:breadcrumb] do %>
          <nav class="flex mt-2" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-1 text-sm text-gray-500">
              <%= for {crumb, index} <- Enum.with_index(@breadcrumb) do %>
                <%= if index > 0 do %>
                  <li>
                    <.icon name="hero-chevron-right" class="h-4 w-4 text-gray-400" />
                  </li>
                <% end %>
                <li>
                  <%= if crumb[:link] do %>
                    <.link
                      navigate={crumb.link}
                      class="text-gray-500 hover:text-gray-700"
                    >
                      <%= crumb.title %>
                    </.link>
                  <% else %>
                    <span class="text-gray-900 font-medium"><%= crumb.title %></span>
                  <% end %>
                </li>
              <% end %>
            </ol>
          </nav>
        <% end %>
      </div>
    </div>
    """
  end
end