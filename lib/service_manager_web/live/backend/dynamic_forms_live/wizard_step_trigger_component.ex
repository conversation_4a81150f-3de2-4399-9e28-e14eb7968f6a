defmodule ServiceManagerWeb.Backend.DynamicFormsLive.WizardStepTriggerComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Triggers.TriggerManager
  alias ServiceManager.Triggers.Schemas.WizardStepTrigger

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Wizard Step Triggers</h3>
          <p class="mt-1 text-sm text-gray-600">
            Configure triggers to execute during wizard form navigation
          </p>
        </div>

        <div class="p-6">
          <!-- Add New Trigger Button -->
          <div class="mb-6">
            <button
              type="button"
              phx-click="add_trigger"
              phx-target={@myself}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Add Trigger
            </button>
          </div>

          <!-- Existing Triggers List -->
          <%= if Enum.empty?(@wizard_step_triggers) do %>
            <div class="text-center py-12">
              <.icon name="hero-bolt" class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">No triggers configured</h3>
              <p class="mt-1 text-sm text-gray-500">
                Add triggers to execute functions during wizard navigation.
              </p>
            </div>
          <% else %>
            <div class="space-y-4">
              <%= for {step_trigger, index} <- Enum.with_index(@wizard_step_triggers) do %>
                <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="flex-shrink-0">
                          <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <.icon name="hero-bolt" class="h-4 w-4 text-indigo-600" />
                          </div>
                        </div>
                        <div>
                          <h4 class="text-sm font-medium text-gray-900">
                            <%= step_trigger.trigger_name || "Select Trigger" %>
                          </h4>
                          <p class="text-xs text-gray-500">
                            Execute <%= step_trigger.execution_timing || "on_enter" %>
                          </p>
                        </div>
                        <div class="flex items-center space-x-2">
                          <span class={[
                            "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",
                            (if step_trigger.enabled, do: "bg-green-100 text-green-800", else: "bg-gray-100 text-gray-800")
                          ]}>
                            <%= if step_trigger.enabled, do: "Enabled", else: "Disabled" %>
                          </span>
                          <button
                            type="button"
                            phx-click="remove_trigger"
                            phx-value-index={index}
                            phx-target={@myself}
                            class="text-red-600 hover:text-red-900"
                            title="Remove trigger"
                          >
                            <.icon name="hero-trash" class="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <!-- Trigger Configuration Form -->
                      <.simple_form
                        for={step_trigger.form}
                        id={"step-trigger-form-#{index}"}
                        phx-change="validate_trigger"
                        phx-target={@myself}
                        phx-value-index={index}
                      >
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <!-- Trigger Selection -->
                          <.input
                            field={step_trigger.form[:trigger_id]}
                            type="select"
                            label="Trigger"
                            options={@available_triggers}
                            prompt="Select a trigger..."
                            required
                          />

                          <!-- Execution Timing -->
                          <.input
                            field={step_trigger.form[:execution_timing]}
                            type="select"
                            label="Execute When"
                            options={[
                              {"On Step Enter", "on_enter"},
                              {"On Step Exit", "on_exit"},
                              {"Conditional", "conditional"}
                            ]}
                            required
                          />
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <!-- Execution Order -->
                          <.input
                            field={step_trigger.form[:execution_order]}
                            type="number"
                            label="Execution Order"
                            min="0"
                            max="100"
                            help="Lower numbers execute first"
                          />

                          <!-- Enabled Toggle -->
                          <div class="flex items-center">
                            <.input
                              field={step_trigger.form[:enabled]}
                              type="checkbox"
                              label="Enabled"
                            />
                          </div>
                        </div>

                        <!-- Input Mapping -->
                        <div class="space-y-3">
                          <label class="block text-sm font-medium text-gray-700">
                            Input Parameter Mapping
                          </label>
                          <p class="text-xs text-gray-500">
                            Map wizard form fields to trigger function parameters
                          </p>
                          
                          <div class="space-y-2" id={"input-mappings-#{index}"}>
                            <%= for {param_name, field_path} <- (step_trigger.input_mapping || %{}) do %>
                              <div class="flex space-x-2 items-center">
                                <div class="flex-1">
                                  <input
                                    type="text"
                                    value={param_name}
                                    placeholder="Parameter name"
                                    phx-change="update_mapping_param"
                                    phx-target={@myself}
                                    phx-value-index={index}
                                    phx-value-old-param={param_name}
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                  />
                                </div>
                                <div class="flex-1">
                                  <input
                                    type="text"
                                    value={field_path}
                                    placeholder="Form field path (e.g., user.email)"
                                    phx-change="update_mapping_field"
                                    phx-target={@myself}
                                    phx-value-index={index}
                                    phx-value-param={param_name}
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                  />
                                </div>
                                <button
                                  type="button"
                                  phx-click="remove_mapping"
                                  phx-target={@myself}
                                  phx-value-index={index}
                                  phx-value-param={param_name}
                                  class="text-red-600 hover:text-red-900"
                                >
                                  <.icon name="hero-x-mark" class="h-4 w-4" />
                                </button>
                              </div>
                            <% end %>
                          </div>

                          <button
                            type="button"
                            phx-click="add_mapping"
                            phx-target={@myself}
                            phx-value-index={index}
                            class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            <.icon name="hero-plus" class="h-3 w-3 mr-1" />
                            Add Mapping
                          </button>
                        </div>

                        <!-- Execution Conditions (for conditional timing) -->
                        <%= if Ecto.Changeset.get_field(step_trigger.form.source, :execution_timing) == :conditional do %>
                          <div class="space-y-3">
                            <label class="block text-sm font-medium text-gray-700">
                              Execution Conditions
                            </label>
                            <p class="text-xs text-gray-500">
                              Define conditions that must be met for trigger execution
                            </p>
                            
                            <.input
                              field={step_trigger.form[:execution_conditions]}
                              type="textarea"
                              rows="3"
                              placeholder={~s|{"field_path": {"eq": "expected_value"}}|}
                              help="JSON object defining field conditions"
                            />
                          </div>
                        <% end %>
                      </.simple_form>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% end %>

          <!-- Save Configuration -->
          <div class="mt-6 pt-6 border-t border-gray-200">
            <div class="flex justify-end space-x-3">
              <button
                type="button"
                phx-click="save_configuration"
                phx-target={@myself}
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <.icon name="hero-check" class="h-4 w-4 mr-2" />
                Save Configuration
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Trigger Execution Preview -->
      <%= if not Enum.empty?(@wizard_step_triggers) do %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Execution Flow Preview</h3>
            <p class="mt-1 text-sm text-gray-600">
              Preview of how triggers will execute during wizard navigation
            </p>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <!-- On Enter Triggers -->
              <%= if has_triggers_for_timing(@wizard_step_triggers, :on_enter) do %>
                <div class="bg-blue-50 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-blue-900 mb-2">
                    <.icon name="hero-arrow-right" class="h-4 w-4 inline mr-1" />
                    On Step Enter
                  </h4>
                  <div class="space-y-2">
                    <%= for trigger <- get_triggers_for_timing(@wizard_step_triggers, :on_enter) do %>
                      <div class="flex items-center text-sm text-blue-800">
                        <span class="w-6 text-center"><%= trigger.execution_order %></span>
                        <.icon name="hero-bolt" class="h-3 w-3 mx-2" />
                        <span><%= trigger.trigger_name %></span>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>

              <!-- On Exit Triggers -->
              <%= if has_triggers_for_timing(@wizard_step_triggers, :on_exit) do %>
                <div class="bg-green-50 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-green-900 mb-2">
                    <.icon name="hero-arrow-left" class="h-4 w-4 inline mr-1" />
                    On Step Exit
                  </h4>
                  <div class="space-y-2">
                    <%= for trigger <- get_triggers_for_timing(@wizard_step_triggers, :on_exit) do %>
                      <div class="flex items-center text-sm text-green-800">
                        <span class="w-6 text-center"><%= trigger.execution_order %></span>
                        <.icon name="hero-bolt" class="h-3 w-3 mx-2" />
                        <span><%= trigger.trigger_name %></span>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>

              <!-- Conditional Triggers -->
              <%= if has_triggers_for_timing(@wizard_step_triggers, :conditional) do %>
                <div class="bg-yellow-50 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-yellow-900 mb-2">
                    <.icon name="hero-question-mark-circle" class="h-4 w-4 inline mr-1" />
                    Conditional
                  </h4>
                  <div class="space-y-2">
                    <%= for trigger <- get_triggers_for_timing(@wizard_step_triggers, :conditional) do %>
                      <div class="flex items-center text-sm text-yellow-800">
                        <span class="w-6 text-center"><%= trigger.execution_order %></span>
                        <.icon name="hero-bolt" class="h-3 w-3 mx-2" />
                        <span><%= trigger.trigger_name %></span>
                        <span class="text-xs ml-2">(when conditions are met)</span>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{wizard_step_id: wizard_step_id} = assigns, socket) do
    # Load existing wizard step triggers
    existing_triggers = TriggerManager.list_wizard_step_triggers(wizard_step_id)
    
    # Load available triggers for selection
    available_triggers = 
      TriggerManager.list_triggers()
      |> Enum.filter(& &1.enabled)
      |> Enum.map(&{&1.name, &1.id})

    # Convert existing triggers to forms
    wizard_step_triggers = 
      existing_triggers
      |> Enum.map(&wizard_step_trigger_to_form/1)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:wizard_step_triggers, wizard_step_triggers)
     |> assign(:available_triggers, available_triggers)}
  end

  @impl true
  def handle_event("add_trigger", _params, socket) do
    new_trigger = %{
      id: nil,
      trigger_id: nil,
      trigger_name: nil,
      execution_timing: :on_enter,
      execution_order: 0,
      enabled: true,
      input_mapping: %{},
      execution_conditions: nil,
      form: to_form(WizardStepTrigger.changeset(%WizardStepTrigger{}, %{}))
    }

    updated_triggers = socket.assigns.wizard_step_triggers ++ [new_trigger]

    {:noreply, assign(socket, :wizard_step_triggers, updated_triggers)}
  end

  @impl true
  def handle_event("remove_trigger", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    updated_triggers = List.delete_at(socket.assigns.wizard_step_triggers, index)

    {:noreply, assign(socket, :wizard_step_triggers, updated_triggers)}
  end

  @impl true
  def handle_event("validate_trigger", %{"index" => index_str} = params, socket) do
    index = String.to_integer(index_str)
    wizard_step_trigger_params = params["wizard_step_trigger"] || %{}
    
    # Get the trigger name if trigger_id is provided
    trigger_name = get_trigger_name(wizard_step_trigger_params["trigger_id"], socket.assigns.available_triggers)
    
    # Update the specific trigger
    updated_triggers = 
      socket.assigns.wizard_step_triggers
      |> List.update_at(index, fn trigger ->
        changeset = WizardStepTrigger.changeset(%WizardStepTrigger{}, wizard_step_trigger_params)
        
        %{trigger | 
          form: to_form(changeset),
          trigger_name: trigger_name,
          trigger_id: wizard_step_trigger_params["trigger_id"],
          execution_timing: parse_execution_timing(wizard_step_trigger_params["execution_timing"]),
          execution_order: parse_integer(wizard_step_trigger_params["execution_order"]),
          enabled: wizard_step_trigger_params["enabled"] == "true"
        }
      end)

    {:noreply, assign(socket, :wizard_step_triggers, updated_triggers)}
  end

  @impl true
  def handle_event("add_mapping", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    
    updated_triggers = 
      socket.assigns.wizard_step_triggers
      |> List.update_at(index, fn trigger ->
        new_mapping = Map.put(trigger.input_mapping || %{}, "new_param", "")
        %{trigger | input_mapping: new_mapping}
      end)

    {:noreply, assign(socket, :wizard_step_triggers, updated_triggers)}
  end

  @impl true
  def handle_event("remove_mapping", %{"index" => index_str, "param" => param}, socket) do
    index = String.to_integer(index_str)
    
    updated_triggers = 
      socket.assigns.wizard_step_triggers
      |> List.update_at(index, fn trigger ->
        updated_mapping = Map.delete(trigger.input_mapping || %{}, param)
        %{trigger | input_mapping: updated_mapping}
      end)

    {:noreply, assign(socket, :wizard_step_triggers, updated_triggers)}
  end

  @impl true
  def handle_event("update_mapping_param", %{"index" => index_str, "old-param" => old_param, "value" => new_param}, socket) do
    index = String.to_integer(index_str)
    
    updated_triggers = 
      socket.assigns.wizard_step_triggers
      |> List.update_at(index, fn trigger ->
        mapping = trigger.input_mapping || %{}
        field_path = mapping[old_param]
        
        updated_mapping = 
          mapping
          |> Map.delete(old_param)
          |> Map.put(new_param, field_path || "")
        
        %{trigger | input_mapping: updated_mapping}
      end)

    {:noreply, assign(socket, :wizard_step_triggers, updated_triggers)}
  end

  @impl true
  def handle_event("update_mapping_field", %{"index" => index_str, "param" => param, "value" => field_path}, socket) do
    index = String.to_integer(index_str)
    
    updated_triggers = 
      socket.assigns.wizard_step_triggers
      |> List.update_at(index, fn trigger ->
        updated_mapping = Map.put(trigger.input_mapping || %{}, param, field_path)
        %{trigger | input_mapping: updated_mapping}
      end)

    {:noreply, assign(socket, :wizard_step_triggers, updated_triggers)}
  end

  @impl true
  def handle_event("save_configuration", _params, socket) do
    wizard_step_id = socket.assigns.wizard_step_id
    
    # Convert forms back to database records
    trigger_params = 
      socket.assigns.wizard_step_triggers
      |> Enum.filter(fn trigger -> trigger.trigger_id end)
      |> Enum.map(&form_to_wizard_step_trigger(&1, wizard_step_id))

    case TriggerManager.update_wizard_step_triggers(wizard_step_id, trigger_params) do
      {:ok, _} ->
        send(self(), {:wizard_triggers_saved, "Wizard step triggers saved successfully"})
        {:noreply, socket}
        
      {:error, reason} ->
        send(self(), {:wizard_triggers_error, "Failed to save triggers: #{inspect(reason)}"})
        {:noreply, socket}
    end
  end

  # Helper functions
  
  defp wizard_step_trigger_to_form(step_trigger) do
    trigger_name = step_trigger.trigger && step_trigger.trigger.name
    
    %{
      id: step_trigger.id,
      trigger_id: step_trigger.trigger_id,
      trigger_name: trigger_name,
      execution_timing: step_trigger.execution_timing,
      execution_order: step_trigger.execution_order,
      enabled: step_trigger.enabled,
      input_mapping: step_trigger.input_mapping || %{},
      execution_conditions: step_trigger.execution_conditions,
      form: to_form(WizardStepTrigger.changeset(step_trigger, %{}))
    }
  end

  defp form_to_wizard_step_trigger(trigger_form, wizard_step_id) do
    %{
      id: trigger_form.id,
      wizard_step_id: wizard_step_id,
      trigger_id: trigger_form.trigger_id,
      execution_timing: trigger_form.execution_timing,
      execution_order: trigger_form.execution_order,
      enabled: trigger_form.enabled,
      input_mapping: trigger_form.input_mapping,
      execution_conditions: trigger_form.execution_conditions
    }
  end

  defp get_trigger_name(trigger_id, available_triggers) when is_binary(trigger_id) do
    available_triggers
    |> Enum.find(fn {_name, id} -> id == trigger_id end)
    |> case do
      {name, _id} -> name
      nil -> nil
    end
  end
  defp get_trigger_name(_, _), do: nil

  defp parse_execution_timing("on_enter"), do: :on_enter
  defp parse_execution_timing("on_exit"), do: :on_exit
  defp parse_execution_timing("conditional"), do: :conditional
  defp parse_execution_timing(_), do: :on_enter

  defp parse_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 0
    end
  end
  defp parse_integer(value) when is_integer(value), do: value
  defp parse_integer(_), do: 0

  defp has_triggers_for_timing(triggers, timing) do
    Enum.any?(triggers, fn trigger -> 
      trigger.execution_timing == timing and trigger.trigger_id
    end)
  end

  defp get_triggers_for_timing(triggers, timing) do
    triggers
    |> Enum.filter(fn trigger -> 
      trigger.execution_timing == timing and trigger.trigger_id
    end)
    |> Enum.sort_by(& &1.execution_order)
  end
end