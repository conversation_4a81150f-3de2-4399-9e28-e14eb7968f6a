defmodule ServiceManagerWeb.Backend.DynamicFormsLive.TriggerMonitoringLive do
  @moduledoc """
  LiveView for comprehensive trigger system monitoring and analytics.
  
  This module provides a real-time monitoring dashboard for the trigger system,
  including:
  
  - **Real-time statistics** - Active triggers, execution counts, success/failure rates
  - **Performance metrics** - Response times, throughput, error rates
  - **System health** - Registry status, rate limiter health, resource usage
  - **Live execution logs** - Stream of trigger executions with filtering
  - **Historical analytics** - Trends, patterns, and performance over time
  - **Alerting dashboard** - Critical issues and threshold violations
  
  ## Features
  
  - Auto-refreshing statistics every 5 seconds
  - Interactive charts and graphs
  - Real-time log streaming
  - Filtering and search capabilities
  - Export functionality for reports
  - Alert configuration and management
  """
  
  use ServiceManagerWeb, :live_view
  
  alias ServiceManager.Triggers.TriggerManager
  alias ServiceManager.Triggers.TriggerRegistry
  alias ServiceManager.Triggers.RateLimiter
  alias ServiceManager.Triggers.Schemas.TriggerExecutionLog
  
  import Ecto.Query
  
  @refresh_interval 5000  # 5 seconds
  @log_stream_limit 50    # Number of recent logs to show
  
  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # Start auto-refresh timer
      :timer.send_interval(@refresh_interval, self(), :refresh_stats)
      
      # Subscribe to real-time trigger execution events
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, "trigger_executions")
    end
    
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:page_title, "Trigger Monitoring Dashboard")
      |> assign(:loading, true)
      |> assign(:refresh_enabled, true)
      |> assign(:selected_time_range, "1h")
      |> assign(:log_filter, "all")
      |> assign(:search_term, "")
      |> load_initial_data()
    
    {:ok, socket}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_refresh", _params, socket) do
    socket = assign(socket, :refresh_enabled, not socket.assigns.refresh_enabled)
    {:noreply, socket}
  end

  def handle_event("change_time_range", %{"range" => range}, socket) do
    socket = 
      socket
      |> assign(:selected_time_range, range)
      |> load_historical_data()
    
    {:noreply, socket}
  end

  def handle_event("change_log_filter", %{"filter" => filter}, socket) do
    socket = 
      socket
      |> assign(:log_filter, filter)
      |> load_execution_logs()
    
    {:noreply, socket}
  end

  def handle_event("search_logs", %{"search" => %{"term" => term}}, socket) do
    socket = 
      socket
      |> assign(:search_term, term)
      |> load_execution_logs()
    
    {:noreply, socket}
  end

  def handle_event("export_data", %{"format" => format}, socket) do
    case export_monitoring_data(format, socket.assigns) do
      {:ok, file_content} ->
        socket = put_flash(socket, :info, "Export ready for download")
        {:noreply, socket}
      
      {:error, reason} ->
        socket = put_flash(socket, :error, "Export failed: #{reason}")
        {:noreply, socket}
    end
  end

  def handle_event("clear_logs", _params, socket) do
    case clear_old_execution_logs() do
      {count, _} when count > 0 ->
        socket = 
          socket
          |> put_flash(:info, "Cleared #{count} old execution logs")
          |> load_execution_logs()
        {:noreply, socket}
      
      _ ->
        socket = put_flash(socket, :info, "No old logs to clear")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info(:refresh_stats, socket) do
    if socket.assigns.refresh_enabled do
      socket = refresh_dashboard_data(socket)
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  # Handle real-time trigger execution events
  def handle_info({:trigger_executed, execution_data}, socket) do
    socket = 
      socket
      |> update_live_execution_log(execution_data)
      |> increment_execution_counters(execution_data)
    
    {:noreply, socket}
  end

  def handle_info({:trigger_failed, failure_data}, socket) do
    socket = 
      socket
      |> update_live_execution_log(failure_data)
      |> increment_failure_counters(failure_data)
    
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="trigger-monitoring-dashboard">
      <!-- Dashboard Header -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Trigger System Monitoring</h1>
          <p class="text-gray-600 mt-2">Real-time monitoring and analytics for the trigger execution system</p>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- Auto-refresh toggle -->
          <button
            phx-click="toggle_refresh"
            class={[
              "px-4 py-2 rounded-md text-sm font-medium transition-colors",
              if(@refresh_enabled, do: "bg-green-100 text-green-800 border border-green-300", else: "bg-gray-100 text-gray-600 border border-gray-300")
            ]}
          >
            <.icon name="hero-arrow-path" class="h-4 w-4 mr-2" />
            <%= if @refresh_enabled, do: "Auto-refresh ON", else: "Auto-refresh OFF" %>
          </button>
          
          <!-- Time range selector -->
          <select 
            phx-change="change_time_range" 
            name="range" 
            value={@selected_time_range}
            class="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          
          <!-- Export button -->
          <div class="relative">
            <button class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">
              <.icon name="hero-document-arrow-down" class="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div :if={@loading} class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-500"></div>
      </div>

      <!-- Dashboard Content -->
      <div :if={not @loading} class="space-y-6">
        <!-- System Health Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Active Triggers -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <.icon name="hero-bolt" class="h-8 w-8 text-blue-500" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Active Triggers</p>
                <p class="text-2xl font-bold text-gray-900"><%= @system_stats.active_triggers %></p>
              </div>
            </div>
          </div>

          <!-- Total Executions -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <.icon name="hero-play" class="h-8 w-8 text-green-500" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Executions</p>
                <p class="text-2xl font-bold text-gray-900"><%= @system_stats.total_executions %></p>
                <p class="text-xs text-gray-500">+<%= @system_stats.executions_last_hour %> in last hour</p>
              </div>
            </div>
          </div>

          <!-- Success Rate -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <.icon name="hero-check-circle" class="h-8 w-8 text-green-500" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Success Rate</p>
                <p class="text-2xl font-bold text-gray-900"><%= @system_stats.success_rate %>%</p>
                <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    class="bg-green-600 h-2 rounded-full"
                    style={"width: #{@system_stats.success_rate}%"}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- System Health -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <.icon 
                  name="hero-heart" 
                  class={[
                    "h-8 w-8",
                    if(@system_health.status == "healthy", do: "text-green-500", else: "text-red-500")
                  ]} 
                />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">System Health</p>
                <p class={[
                  "text-lg font-bold capitalize",
                  if(@system_health.status == "healthy", do: "text-green-600", else: "text-red-600")
                ]}>
                  <%= @system_health.status %>
                </p>
                <p class="text-xs text-gray-500"><%= @system_health.last_checked %></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Execution Timeline Chart -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Execution Timeline</h3>
              <p class="text-sm text-gray-500">Trigger executions over time</p>
            </div>
            <div class="p-6">
              <!-- Chart placeholder - in real implementation, use a charting library -->
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Execution Timeline Chart</p>
                <p class="text-xs text-gray-400 ml-2">(<%= length(@execution_timeline) %> data points)</p>
              </div>
            </div>
          </div>

          <!-- Response Time Distribution -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Response Times</h3>
              <p class="text-sm text-gray-500">Average: <%= @performance_stats.avg_response_time %>ms</p>
            </div>
            <div class="p-6">
              <!-- Response time chart placeholder -->
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-center">
                  <p class="text-gray-500">Response Time Distribution</p>
                  <div class="mt-4 space-y-2">
                    <div class="text-sm">P50: <%= @performance_stats.p50_response_time %>ms</div>
                    <div class="text-sm">P95: <%= @performance_stats.p95_response_time %>ms</div>
                    <div class="text-sm">P99: <%= @performance_stats.p99_response_time %>ms</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Executions and System Components -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Recent Executions Log -->
          <div class="lg:col-span-2 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">Recent Executions</h3>
                  <p class="text-sm text-gray-500">Live execution log stream</p>
                </div>
                <div class="flex items-center space-x-3">
                  <!-- Log filter -->
                  <select 
                    phx-change="change_log_filter" 
                    name="filter" 
                    value={@log_filter}
                    class="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="all">All</option>
                    <option value="success">Success</option>
                    <option value="error">Errors</option>
                    <option value="warning">Warnings</option>
                  </select>
                  
                  <!-- Search -->
                  <form phx-submit="search_logs" class="relative">
                    <input 
                      type="search" 
                      name="search[term]"
                      value={@search_term}
                      placeholder="Search logs..."
                      class="text-sm border border-gray-300 rounded pl-8 pr-3 py-1"
                    />
                    <.icon name="hero-magnifying-glass" class="absolute left-2 top-1.5 h-4 w-4 text-gray-400" />
                  </form>
                </div>
              </div>
            </div>
            
            <div class="overflow-hidden">
              <div class="max-h-96 overflow-y-auto">
                <div :for={log <- @execution_logs} class="px-6 py-3 border-b border-gray-100 hover:bg-gray-50">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <!-- Status indicator -->
                      <div class={[
                        "w-2 h-2 rounded-full",
                        case log.status do
                          "success" -> "bg-green-500"
                          "error" -> "bg-red-500"
                          "warning" -> "bg-yellow-500"
                          _ -> "bg-gray-500"
                        end
                      ]}></div>
                      
                      <div class="min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= log.trigger_name %>
                        </p>
                        <p class="text-xs text-gray-500">
                          <%= log.mount_point %> • <%= format_duration(log.execution_time_ms) %>
                        </p>
                      </div>
                    </div>
                    
                    <div class="text-right">
                      <p class="text-xs text-gray-500">
                        <%= format_time_ago(log.executed_at) %>
                      </p>
                      <p :if={log.error_message} class="text-xs text-red-600 truncate max-w-xs">
                        <%= log.error_message %>
                      </p>
                    </div>
                  </div>
                </div>
                
                <div :if={Enum.empty?(@execution_logs)} class="px-6 py-8 text-center">
                  <p class="text-gray-500">No executions match the current filter</p>
                </div>
              </div>
            </div>
          </div>

          <!-- System Components Status -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">System Components</h3>
              <p class="text-sm text-gray-500">Service health status</p>
            </div>
            
            <div class="p-6 space-y-4">
              <!-- Trigger Registry -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <.icon name="hero-server" class="h-5 w-5 text-gray-400" />
                  <span class="text-sm font-medium">Registry</span>
                </div>
                <div class={[
                  "px-2 py-1 rounded-full text-xs font-medium",
                  if(@component_status.registry_healthy, do: "bg-green-100 text-green-800", else: "bg-red-100 text-red-800")
                ]}>
                  <%= if @component_status.registry_healthy, do: "Healthy", else: "Error" %>
                </div>
              </div>

              <!-- Rate Limiter -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <.icon name="hero-shield-check" class="h-5 w-5 text-gray-400" />
                  <span class="text-sm font-medium">Rate Limiter</span>
                </div>
                <div class={[
                  "px-2 py-1 rounded-full text-xs font-medium",
                  if(@component_status.rate_limiter_healthy, do: "bg-green-100 text-green-800", else: "bg-red-100 text-red-800")
                ]}>
                  <%= if @component_status.rate_limiter_healthy, do: "Healthy", else: "Error" %>
                </div>
              </div>

              <!-- Database -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <.icon name="hero-circle-stack" class="h-5 w-5 text-gray-400" />
                  <span class="text-sm font-medium">Database</span>
                </div>
                <div class={[
                  "px-2 py-1 rounded-full text-xs font-medium",
                  if(@component_status.database_healthy, do: "bg-green-100 text-green-800", else: "bg-red-100 text-red-800")
                ]}>
                  <%= if @component_status.database_healthy, do: "Healthy", else: "Error" %>
                </div>
              </div>

              <!-- Active Triggers Count -->
              <div class="pt-4 border-t">
                <div class="text-sm text-gray-600">
                  <p>Registered Triggers: <span class="font-medium"><%= @component_status.registered_triggers %></span></p>
                  <p>Active Sessions: <span class="font-medium"><%= @component_status.active_sessions %></span></p>
                  <p>Rate Limit Hits: <span class="font-medium"><%= @component_status.rate_limit_hits %></span></p>
                </div>
              </div>

              <!-- Clear Logs Button -->
              <div class="pt-4 border-t">
                <button 
                  phx-click="clear_logs"
                  class="w-full px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                >
                  Clear Old Logs
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Private helper functions

  defp load_initial_data(socket) do
    socket
    |> assign(:loading, true)
    |> load_system_statistics()
    |> load_system_health()
    |> load_performance_statistics()
    |> load_execution_logs()
    |> load_execution_timeline()
    |> load_component_status()
    |> assign(:loading, false)
  end

  defp refresh_dashboard_data(socket) do
    socket
    |> load_system_statistics()
    |> load_system_health()
    |> load_performance_statistics()
    |> load_component_status()
  end

  defp load_system_statistics(socket) do
    stats = %{
      active_triggers: count_active_triggers(),
      total_executions: count_total_executions(),
      executions_last_hour: count_executions_last_hour(),
      success_rate: calculate_success_rate()
    }
    
    assign(socket, :system_stats, stats)
  end

  defp load_system_health(socket) do
    health = %{
      status: determine_system_health(),
      last_checked: DateTime.utc_now() |> Calendar.strftime("%H:%M:%S")
    }
    
    assign(socket, :system_health, health)
  end

  defp load_performance_statistics(socket) do
    stats = %{
      avg_response_time: calculate_average_response_time(),
      p50_response_time: calculate_percentile_response_time(50),
      p95_response_time: calculate_percentile_response_time(95),
      p99_response_time: calculate_percentile_response_time(99)
    }
    
    assign(socket, :performance_stats, stats)
  end

  defp load_execution_logs(socket) do
    logs = fetch_recent_execution_logs(socket.assigns.log_filter, socket.assigns.search_term)
    assign(socket, :execution_logs, logs)
  end

  defp load_execution_timeline(socket) do
    timeline = fetch_execution_timeline(socket.assigns.selected_time_range)
    assign(socket, :execution_timeline, timeline)
  end

  defp load_component_status(socket) do
    status = %{
      registry_healthy: check_registry_health(),
      rate_limiter_healthy: check_rate_limiter_health(),
      database_healthy: check_database_health(),
      registered_triggers: count_registered_triggers(),
      active_sessions: count_active_sessions(),
      rate_limit_hits: count_recent_rate_limit_hits()
    }
    
    assign(socket, :component_status, status)
  end

  defp load_historical_data(socket) do
    socket
    |> load_execution_timeline()
    |> load_system_statistics()
  end

  # Statistics calculation functions

  defp count_active_triggers do
    TriggerManager.list_triggers()
    |> Enum.count(& &1.enabled)
  end

  defp count_total_executions do
    ServiceManager.Repo.aggregate(TriggerExecutionLog, :count, :id)
  end

  defp count_executions_last_hour do
    one_hour_ago = DateTime.utc_now() |> DateTime.add(-3600, :second)
    
    from(log in TriggerExecutionLog,
      where: log.executed_at >= ^one_hour_ago,
      select: count(log.id)
    )
    |> ServiceManager.Repo.one() || 0
  end

  defp calculate_success_rate do
    total = ServiceManager.Repo.aggregate(TriggerExecutionLog, :count, :id)
    
    if total > 0 do
      successful = from(log in TriggerExecutionLog,
        where: log.success == true,
        select: count(log.id)
      ) |> ServiceManager.Repo.one() || 0
      
      Float.round(successful / total * 100, 1)
    else
      0.0
    end
  end

  defp calculate_average_response_time do
    avg = from(log in TriggerExecutionLog,
      where: not is_nil(log.execution_time_ms),
      select: avg(log.execution_time_ms)
    ) |> ServiceManager.Repo.one()
    
    if avg, do: Float.round(avg, 0), else: 0
  end

  defp calculate_percentile_response_time(percentile) do
    # Simplified percentile calculation - in production, use proper percentile functions
    times = from(log in TriggerExecutionLog,
      where: not is_nil(log.execution_time_ms),
      select: log.execution_time_ms,
      order_by: [asc: log.execution_time_ms]
    ) |> ServiceManager.Repo.all()
    
    if Enum.empty?(times) do
      0
    else
      index = trunc(length(times) * percentile / 100)
      Enum.at(times, index, 0)
    end
  end

  defp determine_system_health do
    registry_ok = check_registry_health()
    rate_limiter_ok = check_rate_limiter_health() 
    database_ok = check_database_health()
    
    if registry_ok and rate_limiter_ok and database_ok do
      "healthy"
    else
      "unhealthy"
    end
  end

  defp fetch_recent_execution_logs(filter, search_term) do
    query = from(log in TriggerExecutionLog,
      order_by: [desc: log.executed_at],
      limit: @log_stream_limit,
      preload: [:trigger]
    )
    
    query = case filter do
      "success" -> where(query, [log], log.success == true)
      "error" -> where(query, [log], log.success == false)
      "warning" -> where(query, [log], not is_nil(log.error_message) and log.success == true)
      _ -> query
    end
    
    query = if search_term != "" do
      where(query, [log], 
        ilike(log.trigger_name, ^"%#{search_term}%") or
        ilike(log.mount_point, ^"%#{search_term}%") or
        ilike(log.error_message, ^"%#{search_term}%")
      )
    else
      query
    end
    
    ServiceManager.Repo.all(query)
    |> Enum.map(&format_execution_log/1)
  end

  defp fetch_execution_timeline(time_range) do
    {hours_back, _interval} = case time_range do
      "1h" -> {1, "5 minutes"}
      "6h" -> {6, "30 minutes"}
      "24h" -> {24, "1 hour"}
      "7d" -> {24 * 7, "6 hours"}
      "30d" -> {24 * 30, "1 day"}
      _ -> {1, "5 minutes"}
    end
    
    start_time = DateTime.utc_now() |> DateTime.add(-hours_back * 3600, :second)
    
    # Simplified timeline - in production, use proper time bucketing
    from(log in TriggerExecutionLog,
      where: log.executed_at >= ^start_time,
      group_by: fragment("date_trunc('hour', ?)", log.executed_at),
      select: %{
        time: fragment("date_trunc('hour', ?)", log.executed_at),
        count: count(log.id)
      },
      order_by: [asc: fragment("date_trunc('hour', ?)", log.executed_at)]
    )
    |> ServiceManager.Repo.all()
  end

  defp format_execution_log(log) do
    %{
      id: log.id,
      trigger_name: log.trigger_name || "Unknown Trigger",
      mount_point: log.mount_point || "",
      status: if(log.success, do: "success", else: "error"),
      execution_time_ms: log.execution_time_ms,
      executed_at: log.executed_at,
      error_message: log.error_message
    }
  end

  # Component health checks

  defp check_registry_health do
    try do
      case TriggerRegistry.get_stats() do
        {:ok, _stats} -> true
        _ -> false
      end
    rescue
      _ -> false
    end
  end

  defp check_rate_limiter_health do
    try do
      case RateLimiter.get_stats() do
        {:ok, _stats} -> true
        _ -> false
      end
    rescue
      _ -> false
    end
  end

  defp check_database_health do
    try do
      ServiceManager.Repo.query!("SELECT 1")
      true
    rescue
      _ -> false
    end
  end

  defp count_registered_triggers do
    length(TriggerRegistry.list_registered_triggers())
  rescue
    _ -> 0
  end

  defp count_active_sessions do
    # This would depend on your session tracking implementation
    # For now, return a placeholder
    0
  end

  defp count_recent_rate_limit_hits do
    # This would query rate limiter statistics
    # For now, return a placeholder
    0
  end

  # Real-time update functions

  defp update_live_execution_log(socket, execution_data) do
    new_log = format_execution_data(execution_data)
    current_logs = socket.assigns.execution_logs || []
    updated_logs = [new_log | current_logs] |> Enum.take(@log_stream_limit)
    
    assign(socket, :execution_logs, updated_logs)
  end

  defp increment_execution_counters(socket, _execution_data) do
    current_stats = socket.assigns.system_stats || %{}
    updated_stats = %{current_stats | 
      total_executions: (current_stats.total_executions || 0) + 1
    }
    
    assign(socket, :system_stats, updated_stats)
  end

  defp increment_failure_counters(socket, _failure_data) do
    # Update failure-related statistics
    socket
  end

  defp format_execution_data(data) do
    %{
      id: data[:id] || :rand.uniform(100000),
      trigger_name: data[:trigger_name] || "Unknown",
      mount_point: data[:mount_point] || "",
      status: data[:status] || "success",
      execution_time_ms: data[:execution_time_ms] || 0,
      executed_at: data[:executed_at] || DateTime.utc_now(),
      error_message: data[:error_message]
    }
  end

  # Utility functions

  defp format_duration(ms) when is_number(ms) do
    cond do
      ms < 1000 -> "#{ms}ms"
      ms < 60000 -> "#{Float.round(ms / 1000, 1)}s"
      true -> "#{Float.round(ms / 60000, 1)}m"
    end
  end

  defp format_duration(_), do: "N/A"

  defp format_time_ago(datetime) do
    seconds_ago = DateTime.diff(DateTime.utc_now(), datetime)
    
    cond do
      seconds_ago < 60 -> "#{seconds_ago}s ago"
      seconds_ago < 3600 -> "#{div(seconds_ago, 60)}m ago"
      seconds_ago < 86400 -> "#{div(seconds_ago, 3600)}h ago"
      true -> "#{div(seconds_ago, 86400)}d ago"
    end
  end

  defp export_monitoring_data(format, assigns) do
    # Implementation for exporting monitoring data in various formats
    # This would generate CSV, JSON, or PDF reports
    {:ok, "export_content_placeholder"}
  end

  defp clear_old_execution_logs do
    # Clear execution logs older than 30 days
    thirty_days_ago = DateTime.utc_now() |> DateTime.add(-30 * 24 * 3600, :second)
    
    from(log in TriggerExecutionLog, where: log.executed_at < ^thirty_days_ago)
    |> ServiceManager.Repo.delete_all()
  end
end