defmodule ServiceManagerWeb.Api.TriggerController do
  @moduledoc """
  HTTP API controller for executing triggers via REST endpoints.
  Provides secure access to the trigger system with proper authentication and validation.
  """

  use ServiceManagerWeb, :controller

  alias ServiceManager.Triggers.TriggerExecutor
  alias ServiceManager.Triggers.TriggerRegistry
  require Logger

  @doc """
  Executes a trigger via HTTP POST to /api/triggers/*mount_path
  
  Expected request format:
  ```
  POST /api/triggers/accounts_otp/send_otp
  Content-Type: application/json
  
  {
    "phone": "+**********",
    "message": "Your OTP code",
    "context": {
      "wizard_step_id": "step_123",
      "session_id": "session_456"
    }
  }
  ```
  
  Response format:
  ```json
  {
    "success": true,
    "data": {
      "otp_sent": true,
      "message_id": "msg_789"
    },
    "execution_time_ms": 145,
    "trigger_info": {
      "name": "Send OTP SMS",
      "execution_type": "sync"
    }
  }
  ```
  """
  def execute(conn, %{"mount_path" => mount_path} = params) do
    mount_point = "/service/triggers/#{mount_path}"
    start_time = System.monotonic_time(:millisecond)
    
    with {:ok, context} <- build_execution_context(conn, params),
         {:ok, trigger_params} <- extract_trigger_params(params),
         {:ok, result} <- TriggerExecutor.execute_trigger(mount_point, trigger_params, context) do
      
      execution_time = System.monotonic_time(:millisecond) - start_time
      
      response = build_success_response(result, execution_time, mount_point)
      
      conn
      |> put_status(:ok)
      |> json(response)
    else
      {:error, :unauthorized} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required", code: "UNAUTHORIZED"})
        
      {:error, :rate_limited} ->
        conn
        |> put_status(:too_many_requests)
        |> put_resp_header("retry-after", "60")
        |> json(%{error: "Rate limit exceeded", code: "RATE_LIMITED"})
        
      {:error, :not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Trigger not found: #{mount_path}", code: "TRIGGER_NOT_FOUND"})
        
      {:error, reason} when is_binary(reason) ->
        execution_time = System.monotonic_time(:millisecond) - start_time
        
        conn
        |> put_status(:bad_request)
        |> json(%{
          error: reason, 
          code: "EXECUTION_ERROR",
          execution_time_ms: execution_time
        })
        
      {:error, reason} ->
        Logger.error("TriggerController: Unexpected error executing #{mount_path}: #{inspect(reason)}")
        
        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "Internal server error", code: "INTERNAL_ERROR"})
    end
  end

  @doc """
  Lists all available triggers with their mount points and metadata.
  
  GET /api/triggers
  """
  def list_triggers(conn, params) do
    with {:ok, _user} <- get_authenticated_user(conn) do
      mount_points = TriggerRegistry.list_mount_points()
      
      triggers_info = 
        mount_points
        |> Enum.map(&get_trigger_public_info/1)
        |> Enum.filter(&(&1 != nil))
        |> maybe_filter_triggers(params)
        |> maybe_paginate_triggers(params)
      
      response = %{
        triggers: triggers_info,
        total_count: length(triggers_info),
        registry_stats: TriggerRegistry.get_registry_stats()
      }
      
      json(conn, response)
    else
      {:error, :unauthorized} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
    end
  end

  @doc """
  Gets detailed information about a specific trigger.
  
  GET /api/triggers/info/*mount_path
  """
  def get_trigger_info(conn, %{"mount_path" => mount_path}) do
    mount_point = "/service/triggers/#{mount_path}"
    
    with {:ok, _user} <- get_authenticated_user(conn),
         {:ok, trigger_info} <- TriggerRegistry.get_trigger_info(mount_point) do
      
      public_info = %{
        name: trigger_info.name,
        mount_point: mount_point,
        execution_type: trigger_info.execution_type,
        return_data: trigger_info.return_data,
        rate_limiting_enabled: trigger_info.rate_limiting_enabled,
        input_schema: trigger_info.input_schema,
        description: get_trigger_description(trigger_info),
        example_request: generate_example_request(trigger_info)
      }
      
      json(conn, public_info)
    else
      {:error, :unauthorized} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
        
      {:error, :not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Trigger not found: #{mount_path}"})
    end
  end

  @doc """
  Tests a trigger with provided parameters (development/admin only).
  
  POST /api/triggers/test/*mount_path
  """
  def test_trigger(conn, %{"mount_path" => mount_path} = params) do
    mount_point = "/service/triggers/#{mount_path}"
    
    with {:ok, user} <- get_authenticated_user(conn),
         :ok <- check_admin_access(user),
         {:ok, trigger_params} <- extract_trigger_params(params) do
      
      case TriggerExecutor.test_trigger(mount_point, trigger_params) do
        {:ok, result} ->
          response = %{
            success: true,
            test_result: result,
            message: "Trigger test completed successfully"
          }
          json(conn, response)
          
        {:error, reason} ->
          conn
          |> put_status(:bad_request)
          |> json(%{
            success: false,
            error: reason,
            message: "Trigger test failed"
          })
      end
    else
      {:error, :unauthorized} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
        
      {:error, :forbidden} ->
        conn
        |> put_status(:forbidden)
        |> json(%{error: "Admin access required"})
        
      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: reason})
    end
  end

  @doc """
  Gets rate limiting information for the current user and trigger.
  
  GET /api/triggers/rate-limit/*mount_path
  """
  def get_rate_limit_info(conn, %{"mount_path" => mount_path}) do
    mount_point = "/service/triggers/#{mount_path}"
    
    with {:ok, user} <- get_authenticated_user(conn),
         {:ok, trigger_info} <- TriggerRegistry.get_trigger_info(mount_point) do
      
      if trigger_info.rate_limiting_enabled do
        case ServiceManager.Triggers.RateLimiter.get_rate_limit_info(trigger_info.id, user.id) do
          {:ok, rate_info} ->
            json(conn, %{
              rate_limiting_enabled: true,
              rate_limit_info: rate_info
            })
            
          {:error, reason} ->
            conn
            |> put_status(:internal_server_error)
            |> json(%{error: "Failed to get rate limit info: #{reason}"})
        end
      else
        json(conn, %{
          rate_limiting_enabled: false,
          message: "Rate limiting not enabled for this trigger"
        })
      end
    else
      {:error, :unauthorized} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error: "Authentication required"})
        
      {:error, :not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Trigger not found: #{mount_path}"})
    end
  end

  ## Private Functions

  defp build_execution_context(conn, params) do
    with {:ok, user} <- get_authenticated_user(conn) do
      context = %{
        user_id: user.id,
        request_id: get_request_id(conn),
        session_id: get_session_id(conn),
        ip_address: get_client_ip(conn),
        user_agent: get_req_header(conn, "user-agent") |> List.first(),
        timestamp: DateTime.utc_now()
      }
      
      # Merge any context from request body
      context = case params["context"] do
        context_params when is_map(context_params) ->
          Map.merge(context, sanitize_context_params(context_params))
        _ -> context
      end
      
      {:ok, context}
    end
  end

  defp extract_trigger_params(params) do
    # Remove controller-specific keys
    trigger_params = 
      params
      |> Map.drop(["mount_path", "context", "_format", "_csrf_token"])
      |> sanitize_trigger_params()
    
    {:ok, trigger_params}
  rescue
    error ->
      {:error, "Invalid request parameters: #{inspect(error)}"}
  end

  defp sanitize_context_params(context_params) do
    allowed_keys = ["wizard_step_id", "session_id", "trace_id", "metadata"]
    
    context_params
    |> Map.take(allowed_keys)
    |> Enum.reduce(%{}, fn {key, value}, acc ->
      case sanitize_context_value(value) do
        nil -> acc
        sanitized_value -> Map.put(acc, String.to_atom(key), sanitized_value)
      end
    end)
  end

  defp sanitize_context_value(value) when is_binary(value) and byte_size(value) <= 255 do
    String.trim(value)
  end
  defp sanitize_context_value(value) when is_map(value) and map_size(value) <= 10 do
    value
  end
  defp sanitize_context_value(_), do: nil

  defp sanitize_trigger_params(params) when is_map(params) do
    # Basic sanitization - remove excessively large values
    Enum.reduce(params, %{}, fn {key, value}, acc ->
      case sanitize_param_value(value) do
        nil -> acc
        sanitized_value -> Map.put(acc, key, sanitized_value)
      end
    end)
  end

  defp sanitize_param_value(value) when is_binary(value) do
    if byte_size(value) <= 10_000 do
      String.trim(value)
    else
      String.slice(value, 0, 10_000)
    end
  end
  defp sanitize_param_value(value) when is_number(value), do: value
  defp sanitize_param_value(value) when is_boolean(value), do: value
  defp sanitize_param_value(value) when is_list(value) and length(value) <= 100 do
    Enum.map(value, &sanitize_param_value/1)
  end
  defp sanitize_param_value(value) when is_map(value) and map_size(value) <= 50 do
    Enum.reduce(value, %{}, fn {k, v}, acc ->
      case sanitize_param_value(v) do
        nil -> acc
        sanitized -> Map.put(acc, k, sanitized)
      end
    end)
  end
  defp sanitize_param_value(_), do: nil

  defp build_success_response(result, execution_time, mount_point) do
    base_response = %{
      success: true,
      data: result,
      execution_time_ms: execution_time
    }
    
    # Add trigger metadata if available
    case TriggerRegistry.get_trigger_info(mount_point) do
      {:ok, trigger_info} ->
        Map.put(base_response, :trigger_info, %{
          name: trigger_info.name,
          execution_type: trigger_info.execution_type
        })
      _ ->
        base_response
    end
  end

  defp get_trigger_public_info(mount_point) do
    case TriggerRegistry.get_trigger_info(mount_point) do
      {:ok, trigger_info} ->
        %{
          name: trigger_info.name,
          mount_point: mount_point,
          execution_type: trigger_info.execution_type,
          return_data: trigger_info.return_data,
          rate_limiting_enabled: trigger_info.rate_limiting_enabled,
          has_input_schema: not is_nil(trigger_info.input_schema)
        }
      _ -> nil
    end
  end

  defp maybe_filter_triggers(triggers, %{"filter" => filter}) when filter in ["sync", "async"] do
    filter_atom = String.to_existing_atom(filter)
    Enum.filter(triggers, &(&1.execution_type == filter_atom))
  end
  defp maybe_filter_triggers(triggers, _), do: triggers

  defp maybe_paginate_triggers(triggers, %{"page" => page, "per_page" => per_page}) do
    {page_int, _} = Integer.parse(page)
    {per_page_int, _} = Integer.parse(per_page)
    
    offset = (page_int - 1) * per_page_int
    Enum.slice(triggers, offset, per_page_int)
  end
  defp maybe_paginate_triggers(triggers, _), do: Enum.take(triggers, 100)  # Default limit

  defp get_trigger_description(trigger_info) do
    "#{trigger_info.name} - #{trigger_info.execution_type} execution" <>
    if trigger_info.rate_limiting_enabled, do: " (rate limited)", else: ""
  end

  defp generate_example_request(trigger_info) do
    case trigger_info.input_schema do
      nil -> %{example: "No schema defined"}
      schema -> generate_example_from_schema(schema)
    end
  end

  defp generate_example_from_schema(schema) when is_map(schema) do
    case schema["properties"] do
      properties when is_map(properties) ->
        Enum.reduce(properties, %{}, fn {field, field_schema}, acc ->
          example_value = case field_schema["type"] do
            "string" -> "example_string"
            "number" -> 42
            "integer" -> 42
            "boolean" -> true
            "array" -> []
            "object" -> %{}
            _ -> "example_value"
          end
          Map.put(acc, field, example_value)
        end)
      _ -> %{example: "Complex schema"}
    end
  end

  defp get_authenticated_user(conn) do
    case conn.assigns[:current_user] do
      %{} = user -> {:ok, user}
      _ -> {:error, :unauthorized}
    end
  end

  defp check_admin_access(user) do
    # Implement your admin check logic here
    if Map.get(user, :admin, false) or Map.get(user, :role) == "admin" do
      :ok
    else
      {:error, :forbidden}
    end
  end

  defp get_request_id(conn) do
    get_req_header(conn, "x-request-id") 
    |> List.first() 
    |> case do
      nil -> Ecto.UUID.generate()
      id -> id
    end
  end

  defp get_session_id(conn) do
    get_session(conn, :session_id) || 
    get_req_header(conn, "x-session-id") |> List.first()
  end

  defp get_client_ip(conn) do
    case get_req_header(conn, "x-forwarded-for") do
      [ip | _] -> ip
      [] -> 
        case conn.remote_ip do
          {a, b, c, d} -> "#{a}.#{b}.#{c}.#{d}"
          _ -> "unknown"
        end
    end
  end
end