defmodule ServiceManagerWeb.Api.Services.ProfileServiceController do
  use ServiceManagerWeb, :controller

  import ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Repo
  alias ServiceManager.Accounts.User, as: Profile
  alias ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService, as: RemoteProfile
  alias ServiceManager.Service.TransactionService, as: AccountTransactionService
  alias ServiceManager.WalletAccounts.WalletUser, as: Wallet
  alias ServiceManager.Services.T24.Messages.GetAccountBalance

  def disable_user(user_id) do
    case Repo.get(Profile, user_id) do
      nil ->
        %{
          "message" => "User not found",
          "status" => false,
          "data" => %{
            "errors" => ["User with ID #{user_id} does not exist"]
          }
        }

      user ->
        case user
             |> Profile.update_user(%{disabled: true})
             |> Repo.update() do
          {:ok, profile} ->
            %{
              "message" => "User has been disabled successfully",
              "status" => true,
              "data" => %{
                "profile" => profile
              }
            }

          {:error, changeset} ->
            errors =
              Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
                end)
              end)

            %{
              "message" => "Failed to disable user",
              "status" => false,
              "data" => %{
                "errors" => errors
              }
            }
        end
    end
  end

  def enable_user(user_id) do
    case Repo.get(Profile, user_id) do
      nil ->
        %{
          "message" => "User not found",
          "status" => false,
          "data" => %{
            "errors" => ["User with ID #{user_id} does not exist"]
          }
        }

      user ->
        case user
             |> Profile.update_user(%{disabled: false})
             |> Repo.update() do
          {:ok, profile} ->
            %{
              "message" => "User has been enabled successfully",
              "status" => true,
              "data" => %{
                "profile" => profile
              }
            }

          {:error, changeset} ->
            errors =
              Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
                end)
              end)

            %{
              "message" => "Failed to enable user",
              "status" => false,
              "data" => %{
                "errors" => errors
              }
            }
        end
    end
  end

  def approve(params) do
    user = Repo.get_by(Profile, username: params["email"]) |> Repo.preload(:accounts)

    # Generate a random password (12 characters to meet minimum requirement)
    new_password =
      Enum.map(1..12, fn _ -> Enum.random(0..9) end)
      |> Enum.join()
      |> String.replace(~r/(\d{4})(?=\d)/, "\\1-")

    # First update the approval status
    with {:ok, profile} <-
           user
           |> Profile.update_user(%{approved: true})
           |> Repo.update(),
         # Then update the password
         {:ok, profile_with_password} <-
           profile
           |> Profile.update_password_changeset(%{password: new_password, first_time_login: true})
           |> Repo.update() do
      # Send SMS with new password
      message =
        "Your account has been approved. Your username is #{profile.username} and temporary password is: #{new_password}. Please change it upon your first login."

      # ServiceManager.Services.Notification.OrbitSms.send_message(profile_with_password.phone_number, message)

      ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
        msisdn: profile_with_password.phone_number,
        message: message
      })
      |> IO.inspect()

      {:ok, profile_with_password} |> respond("profile approved")
    else
      {:error, changeset} ->
        errors =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
            Enum.reduce(opts, msg, fn {key, value}, acc ->
              String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
            end)
          end)

        respond({:error, errors})
    end
  end

  def disable(params) do
    user = Repo.get_by(Profile, email: params["email"]) |> Repo.preload(:accounts)

    case user
         |> Profile.update_user(%{approved: false})
         |> Repo.update() do
      {:ok, profile} ->
        {:ok, profile} |> respond("profile disabled")

      {:error, changeset} ->
        errors =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
            Enum.reduce(opts, msg, fn {key, value}, acc ->
              # Convert list to string with Enum.join
              String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
            end)
          end)

        respond({:error, errors})
    end
  end

  def register(_user, profile_params) do
    IO.inspect(profile_params, label: "PROFILE-PARAMS")
    case Repo.get_by(Profile, email: profile_params["username"]) do
      nil ->
        case %Profile{}
             |> Profile.registration_changeset(profile_params)
             |> Repo.insert() do
          {:ok, profile} ->
            profile = Repo.preload(profile, :accounts)

            spawn(fn ->
              profile.id |> ServiceManager.Processors.UserBalanceSyncProcessor.sync_with_timeout()
            end)

            {:ok, profile} |> respond("profile registered succcessfuly")

          {:error, changeset} ->

            errors =
              Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  # Convert list to string with Enum.join
                  String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
                end)
              end)

            respond({:error, errors})
        end

      _user ->
        respond({:error, "Username already exists"})
    end
  end

  def link_bank_account(account_details) do
    # Account details already contains the full name, so we just pass it through
    account_details = %{
      email: account_details.email,
      name: account_details.name,
      initial_balance: account_details.initial_balance,
      currency: account_details.currency,
      account_type: account_details.account_type,
      account_number: account_details.account_number
    }

    case AccountTransactionService.create_account(account_details) do
      {:ok, account} ->
        %{
          "status" => true,
          "message" => "Account created",
          "account" => account
        }

      {:error, %Ecto.Changeset{} = changeset} ->
        %{
          "status" => false,
          "message" => "Failed to create account: #{inspect(changeset.errors)}",
          "account" => %{}
        }

      {:error, _} ->
        %{
          "status" => false,
          "message" => "Failed to create account",
          "account" => %{}
        }
    end
  end

  def update(user, profile_params) do
    profile = Repo.get!(Profile, user.id)

    case Repo.update(update_profile(profile, profile_params)) do
      {:ok, profile} ->
        profile = Repo.preload(profile, :accounts)
        respond({:ok, profile}, "profile updated")

      {:error, %Ecto.Changeset{} = changeset} ->
        errors =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
            Enum.reduce(opts, msg, fn {key, value}, acc ->
              String.replace(acc, "%{#{key}}", to_string(value))
            end)
          end)

        respond({:error, errors})
    end
  end

  def update_password(user, password_params) do
    old_password = password_params["old_password"]
    new_password = password_params["new_password"]
    confirm_new_password = password_params["confirm_new_password"]

    if Bcrypt.verify_pass(old_password, user.hashed_password) do
      if new_password == confirm_new_password do
        changeset =
          Profile.update_password_changeset(user, %{
            password: new_password,
            first_time_login: false
          })

        case Repo.update(changeset) do
          {:ok, user} ->
            ServiceManagerWeb.Api.Services.AuthenticationService.invalidate(
              password_params["access_token"]
            )

            respond({:ok, user}, "Password updated successfully")

          {:error, changeset} ->
            errors =
              changeset
              |> Ecto.Changeset.traverse_errors(fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  String.replace(acc, "%{#{key}}", to_string(Enum.join(List.wrap(value), ", ")))
                end)
              end)
              |> Enum.flat_map(fn {_field, messages} -> messages end)
              |> Enum.join(", ")

            respond({:error, errors})
        end
      else
        respond({:error, "New password and confirm new password do not match"})
      end
    else
      respond({:error, "Old password is incorrect"})
    end
  end

  def update_multi_session_setting(user, params) do
    allow_multi_session = params["allow_multi_session"]

    # Validate that allow_multi_session is a boolean
    case allow_multi_session do
      value when is_boolean(value) ->
        profile = Repo.get!(Profile, user.id)

        case profile
             |> Profile.update_user(%{allow_multi_session: allow_multi_session})
             |> Repo.update() do
          {:ok, updated_profile} ->
            message = if updated_profile.allow_multi_session, do: "enabled", else: "disabled"
            base_response({:ok, %{}}, "Multi-session setting #{message} successfully ")

          {:error, changeset} ->
            errors =
              Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  String.replace(acc, "%{#{key}}", to_string(value))
                end)
              end)

            respond({:error, errors})
        end

      _ ->
        respond({:error, "Invalid value for allow_multi_session. Must be true or false."})
    end
  end

  track do
    def details(user, _profile_params) do
      remote_profile =
        user.account_number
        |> RemoteProfile.get_profile_by_account_number_v3()
        |> case do
          {:ok, remote_response} -> remote_response
          {:error, remote_error} -> remote_error
        end

      updated_balance =
        user.account_balance
        |> to_string()
        |> Decimal.new()
        |> Decimal.to_string(:normal)

      local_profile =
        user
        |> Map.put(:account_balance, updated_balance)
        |> Map.put(:name, "#{user.first_name} #{user.last_name}")
        |> Map.put(:created_at, user.inserted_at)

      # TODO: Update profile of profile details pull
      # |> Map.put(:updated_at, user.updated_at)

      wallet_accounts = Wallet.where(mobile_number: user.phone_number)

      # Get all account IDs for this customer from T24
      # try do


      all_account_ids = case user.tester do
          true -> []
          false ->
            ServiceManager.Services.T24.Messages.PullProfileAccounts.get_account_ids_direct!(
          user.customer_no
        )
      end

      # rescue
      #   e ->
      #     # Log the error but continue with just the main account
      #     IO.inspect(e, label: "Error fetching account IDs")
      #     [user.account_number]
      # end

      # First get the main account
      mb_accounts =
        ServiceManager.Accounts.FundAccounts.where(account_number: user.account_number)

      # Create the main account if it doesn't exist
      mb_accounts =
        case mb_accounts do
          [] ->
            account_details = %{
              email: user.username || "",
              name: "#{user.first_name} #{user.last_name}",
              initial_balance: user.account_balance,
              currency: "MWK",
              account_type: "CURRENT",
              account_number: user.account_number
            }

            case link_bank_account(account_details) do
              %{"status" => true, "account" => account} -> [account]
              _ -> []
            end

          accounts ->
            accounts
        end
        |> IO.inspect(label: "P3")

      # Now check for additional accounts from T24 and add them if they don't exist
      mb_accounts =
        Enum.reduce(all_account_ids, mb_accounts, fn account_id, acc ->
          IO.inspect(account_id, label: "#{account_id}")

          # Skip the main account as it's already handled
          if account_id == user.account_number do
            acc
          else
            # Check if this account already exists
            case ServiceManager.Accounts.FundAccounts.where(account_number: account_id) do
              [] ->
                # Get the account balance from T24
                balance_str =
                  try do
                    # Get the balance and extract the available_balance
                    balance_info =
                      GetAccountBalance.get_account_balance_parsed(account_id)
                      |> IO.inspect(label: "Balance for #{account_id}")

                    case balance_info do
                      %{"available_balance" => available_balance}
                      when not is_nil(available_balance) ->
                        available_balance

                      _ ->
                        # Default if balance info doesn't contain available_balance
                        "0.00"
                    end
                  rescue
                    e ->
                      # Log the error but continue with default balance
                      IO.inspect(e, label: "Error fetching balance for #{account_id}")
                      "0.00"
                  end
                  |> IO.inspect(label: "Balance string for #{account_id}")

                # Convert the balance string to a decimal
                balance =
                  try do
                    Decimal.new(balance_str)
                  rescue
                    _ ->
                      # If conversion fails, use 0.00
                      Decimal.new("0.00")
                  end
                  |> IO.inspect(label: "Decimal balance for #{account_id}")

                # Create a new account record
                account_details = %{
                  email: user.username || "",
                  name: "#{user.first_name} #{user.last_name}",
                  # Use the fetched balance
                  initial_balance: balance,
                  currency: "MWK",
                  account_type: "CURRENT",
                  account_number: account_id
                }

                case link_bank_account(account_details)
                     |> IO.inspect(label: "Link #{account_id}") do
                  %{"status" => true, "account" => account} -> [account | acc]
                  _ -> acc
                end

              existing_accounts ->
                # Add existing accounts to our list if they're not already there
                Enum.reduce(existing_accounts, acc, fn existing, accounts_acc ->
                  if Enum.any?(accounts_acc, fn a -> a.id == existing.id end) do
                    accounts_acc
                  else
                    [existing | accounts_acc]
                  end
                end)
            end
          end
        end)
        |> IO.inspect(label: "P4")

      %{
        "message" => "Profile details fetched",
        "status" => true,
        "data" => %{
          "bank_profile" => remote_profile,
          "profile" => local_profile,
          "wallet_accounts" => wallet_accounts,
          "bank_accounts" => mb_accounts
        }
      }
    end
  end

  # Public function to get an account by its number
  def get_user_by_email(email) do
    # Query the UserAccounts table to get the account by its number
    Repo.get_by(UserAccounts, email: email)
  end

  def get_balance(account_number) do
    url = "https://fdh-esb.ngrok.dev/api/esb/accounts/account/v1/balances/#{account_number}"

    headers = [
      {"Authorization", "Basic YWRtaW46YWRtaW4="}
    ]

    case HTTPoison.get(url, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        case Jason.decode(body) do
          {:ok, response} ->
            # Return only the first item from the body array
            case response["body"] do
              [balance_info | _] -> {:ok, balance_info}
              _ -> {:error, "No balance information found"}
            end

          {:error, _} ->
            {:error, "Failed to parse response"}
        end

      {:error, _} ->
        {:error, "Failed to fetch balance"}

      _ ->
        {:error, "Unexpected response"}
    end
  end

  defp update_profile(profile, attrs) do
    Profile.update_user(profile, attrs)
  end

  defp change_profile(profile, attrs) do
    Profile.registration_changeset(profile, attrs)
  end

  defp respond({:error, error_info}) do
    %{
      "message" => "#{error_info}",
      "status" => false,
      "data" => %{
        "errors" => error_info
      }
    }
  end

  defp respond({:ok, profile}, message \\ "") do
    %{
      "message" => message,
      "status" => true,
      "data" => %{
        "profile" => profile,
        "accounts" => profile.accounts
      }
    }
  end

  defp base_response({:ok, %{}}, message) do
    %{
      "message" => message,
      "status" => true,
      "data" => %{
      }
    }
  end
end
