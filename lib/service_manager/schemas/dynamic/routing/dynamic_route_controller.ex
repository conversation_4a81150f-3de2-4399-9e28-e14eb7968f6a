defmodule ServiceManagerWeb.DynamicRouteController do
    use ServiceManagerWeb, :controller
    alias ServiceManager.Routing.DynamicRouteManager
    alias ServiceManager.Routing.DynamicRouter
    alias ServiceManager.Routing.DynamicRoute
    alias ServiceManager.Forms.DynamicForm
    alias ServiceManager.Forms.DynamicFormsManager
    alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
    alias ServiceManager.Triggers.WizardTriggerExecutor

    import Ecto.Query

    @base_path "/api/dynamic"

    # Process a route with dynamic processes if available
    defp process_route(conn, route, params) do
      # Check if the route has a linked process
      case ProcessManager.get_initial_process(route.id) do
        {:ok, _process} ->
          # Execute the process chain
          case ProcessManager.execute_chain(route.id, params) do
            {:ok, result} ->
              # Process chain executed successfully
              conn
              |> put_status(:ok)
              |> json(%{
                success: true,
                message: "Request processed successfully",
                data: params,
                process_result: result
              })

            {:error, reason} ->
              # Process chain execution failed
              conn
              |> put_status(:internal_server_error)
              |> json(%{success: false, message: "Process execution failed", error: reason})
          end

        {:error, :not_found} ->
          # No process linked to this route, return default success
          conn
          |> put_status(:ok)
          |> json(%{success: true, route: route.name, data: params})
      end
    end

    def handle(conn, _params) do
      # Get the full request path and method
      full_path = conn.request_path
      method = conn.method

      # Remove the base path to get the dynamic part
      dynamic_path = String.replace_prefix(full_path, @base_path, "")

      # If the path is empty, set it to "/"
      dynamic_path = if dynamic_path == "", do: "/", else: dynamic_path

      # Find the route using the find_route function
      case DynamicRouteManager.find_route(method, dynamic_path) do
        {:ok, route} ->
          # First check if this route is linked to a wizard
          case check_wizard_for_route(route) do
            {:ok, wizard, step} ->
              handle_wizard_flow(conn, wizard, step, get_request_params(conn, method))
            :not_wizard ->
              # Handle as regular form route
              route_forms = DynamicFormsManager.get_route_forms(route.id)

              if Enum.empty?(route_forms) do
                # No forms attached to this route
                conn
                |> put_status(:bad_request)
                |> json(%{success: false, message: "No form attached to this route"})
              else
            # Check if there's a form for this route and method
            case DynamicFormsManager.get_form_for_route(route.id, method) do
              nil ->
                # Check if any forms are required for this route
                case check_if_form_required(route.id) do
                  true ->
                    conn
                    |> put_status(:bad_request)
                    |> json(%{success: false, message: "Incomplete API definition: No form defined for this route method"})

                  false ->
                    # Form not required, proceed with success
                    process_route(conn, route, %{})
                end

              form ->
                # Get the appropriate parameters based on HTTP method
                request_params = get_request_params(conn, method)

                # Validate the request against the form
                case DynamicFormsManager.validate_request(form, request_params) do
                  {:ok, validated_params} ->
                    # Form validation successful, continue with processing
                    process_route(conn, route, validated_params)

                  {:error, errors} ->
                    # Form validation failed
                    conn
                    |> put_status(:unprocessable_entity)
                    |> json(%{success: false, message: "Invalid request", errors: errors})
                end
              end
            end
          end

        {:error, :not_found} ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, message: "Route not found"})
      end
    end

    # =============================================================================
    # WIZARD FLOW HANDLING
    # =============================================================================

    # Check if a route is linked to a wizard
    defp check_wizard_for_route(route) do
      case DynamicFormsManager.get_wizard_for_route(route.id) do
        nil ->
          :not_wizard
        wizard ->
          # Get the first step of the wizard
          case DynamicFormsManager.get_first_wizard_step(wizard.id) do
            nil -> {:error, "Wizard has no steps"}
            first_step -> {:ok, wizard, first_step}
          end
      end
    end

    # Handle wizard flow navigation
    defp handle_wizard_flow(conn, wizard, current_step, params) do
      method = conn.method

      case method do
        "GET" ->
          # Check if this is a new session or existing session
          case Map.get(params, "session_token") do
            nil ->
              # New wizard session - start from beginning
              start_new_wizard_session(conn, wizard)

            session_token ->
              # Continue existing session
              continue_wizard_session(conn, session_token)
          end

        "POST" ->
          # Check if this is first step submission or continuing with session
          case Map.get(params, "session_token") do
            nil ->
              # First step submission - validate first form and create session
              handle_first_step_submission(conn, wizard, params)

            session_token ->
              # Continuing existing session
              handle_wizard_post_with_session(conn, session_token, params)
          end

        _ ->
          conn
          |> put_status(:method_not_allowed)
          |> json(%{success: false, message: "Method not supported for wizard flows"})
      end
    end

    # Start a new wizard session
    defp start_new_wizard_session(conn, wizard) do
      client_info = %{
        client_ip: get_client_ip(conn),
        user_agent: get_user_agent(conn)
      }

      case DynamicFormsManager.create_wizard_session(wizard.id, client_info) do
        {:ok, session} ->
          case DynamicFormsManager.get_wizard_session_with_details(session.session_token) do
            {:ok, session_details} ->
              render_wizard_step_response(conn, session_details)

            {:error, reason} ->
              conn
              |> put_status(:internal_server_error)
              |> json(%{success: false, message: "Failed to initialize wizard session", error: reason})
          end

        {:error, reason} ->
          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, message: "Failed to create wizard session", error: reason})
      end
    end

    # Continue existing wizard session
    defp continue_wizard_session(conn, session_token) do
      case DynamicFormsManager.get_wizard_session_with_details(session_token) do
        {:ok, session_details} ->
          render_wizard_step_response(conn, session_details)

        {:error, :session_not_found} ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, message: "Wizard session not found or expired"})

        {:error, :session_expired} ->
          conn
          |> put_status(:unauthorized)
          |> json(%{success: false, message: "Wizard session has expired"})

        {:error, reason} ->
          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, message: "Failed to load wizard session", error: reason})
      end
    end

    # Handle GET request for wizard step
    defp handle_wizard_get_step(conn, wizard, step) do
      if ServiceManager.Forms.FormWizardStep.mobile_form_step?(step) do
        # Load mobile form structure
        case get_mobile_form_structure(step.mobile_form_id) do
          {:ok, form_structure} ->
            # Get validation schema if linked
            validation_schema = case DynamicFormsManager.get_validation_schema_for_mobile_form(step.mobile_form_id) do
              nil -> nil
              schema -> schema.validation_schema
            end

            response = %{
              success: true,
              wizard: %{
                id: wizard.id,
                name: wizard.name,
                current_step: step.step_number,
                total_steps: get_wizard_total_steps(wizard.id),
                is_final: is_final_wizard_step?(wizard.id, step.step_number),
                session_token: generate_wizard_session_token(wizard.id, step.step_number)
              },
              form: form_structure,
              validation_schema: validation_schema
            }

            conn
            |> put_status(:ok)
            |> json(response)

          {:error, reason} ->
            conn
            |> put_status(:internal_server_error)
            |> json(%{success: false, message: "Failed to load mobile form", error: reason})
        end
      else
        # Handle dynamic form step (legacy support)
        handle_dynamic_form_step(conn, wizard, step)
      end
    end

    # Handle first step submission without session token
    defp handle_first_step_submission(conn, wizard, params) do
      with {:ok, first_step} <- get_wizard_first_step(wizard),
           {:ok, validated_data} <- validate_first_step_data(first_step, params) do

        # Create new session with first step data
        client_info = %{
          client_ip: get_client_ip(conn),
          user_agent: get_user_agent(conn)
        }

        case DynamicFormsManager.create_wizard_session(wizard.id, client_info) do
          {:ok, session} ->
            # Update session with first step data and move to next step
            case update_session_and_advance(session, validated_data) do
              {:ok, next_session_details} ->
                render_wizard_step_response(conn, next_session_details)

              {:error, reason} ->
                conn
                |> put_status(:internal_server_error)
                |> json(%{success: false, message: "Failed to advance wizard", error: reason})
            end

          {:error, reason} ->
            conn
            |> put_status(:internal_server_error)
            |> json(%{success: false, message: "Failed to create wizard session", error: reason})
        end
      else
        {:error, :no_first_step} ->
          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, message: "Wizard has no first step configured"})

        {:error, :validation_failed, errors} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{success: false, message: "Missing required fields for first step", missing_fields: errors})

        {:error, reason} ->
          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, message: "Failed to process first step", error: reason})
      end
    end

    # Handle POST request with session token
    defp handle_wizard_post_with_session(conn, session_token, params) do
      with {:ok, session_details} <- DynamicFormsManager.get_wizard_session_with_details(session_token),
           {:ok, validated_data} <- validate_current_step_data(session_details, params),
           {:ok, _} <- validate_step_requirements(session_details, validated_data) do

        # Process step and determine next action
        process_wizard_step_submission(conn, session_details, validated_data)
      else
        {:error, :session_not_found} ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, message: "Wizard session not found or expired"})

        {:error, :session_expired} ->
          conn
          |> put_status(:unauthorized)
          |> json(%{success: false, message: "Wizard session has expired"})

        {:error, :validation_failed, errors} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{success: false, message: "Validation failed", errors: errors})

        {:error, reason} ->
          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, message: "Failed to process wizard step", error: reason})
      end
    end

    # Handle POST request for wizard step
    defp handle_wizard_post_step(conn, wizard, step, params) do
      # Validate session token if provided
      case validate_wizard_session(params, wizard.id, step.step_number) do
        {:error, reason} ->
          conn
          |> put_status(:unauthorized)
          |> json(%{success: false, message: "Invalid wizard session", error: reason})

        :ok ->
          # Validate data using linked validation schema if available
          case validate_wizard_step_data(step, params) do
            {:ok, validated_data} ->
              # Store step data for final processing
              store_wizard_step_data(wizard.id, step.step_number, validated_data)

              # Determine next step or completion
              case get_next_wizard_step(wizard, step, validated_data) do
                {:next_step, next_step} ->
                  # Return next step form data
                  handle_wizard_get_step(conn, wizard, next_step)

                {:complete, _final_data} ->
                  # Execute final processing if this is the last step
                  all_wizard_data = get_all_wizard_step_data(wizard.id)
                  handle_wizard_completion(conn, wizard, step, all_wizard_data)

                {:error, reason} ->
                  conn
                  |> put_status(:internal_server_error)
                  |> json(%{success: false, message: "Failed to navigate wizard", error: reason})
              end

            {:error, validation_errors} ->
              conn
              |> put_status(:unprocessable_entity)
              |> json(%{success: false, message: "Validation failed", errors: validation_errors})
          end
      end
    end

    # Get mobile form structure for API response
    defp get_mobile_form_structure(mobile_form_id) do
      with {:ok, form} <- ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service.get_form(%{"form_id" => mobile_form_id}),
           {:ok, fields} <- ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service.get_form_fields(mobile_form_id) do

        form_structure = %{
          id: form.id,
          name: form.name,
          submit_to: form.submit_to,
          fields: Enum.map(fields, &format_mobile_field_for_api/1)
        }

        {:ok, form_structure}
      end
    end

    # Format mobile form field for API response
    defp format_mobile_field_for_api(field) do
      %{
        name: field.field_name,
        type: field.field_type,
        label: field.label,
        required: field.is_required,
        order: field.field_order,
        options: field.options
      }
    end

    # Validate first step required fields
    defp validate_first_step_required_fields(step, params) do
      if ServiceManager.Forms.FormWizardStep.mobile_form_step?(step) do
        case get_mobile_form_structure(step.mobile_form_id) do
          {:ok, form_structure} ->
            required_fields = form_structure.fields
            |> Enum.filter(& &1.required)
            |> Enum.map(& &1.name)

            missing_fields = Enum.filter(required_fields, fn field ->
              not Map.has_key?(params, field) and not Map.has_key?(params, String.to_atom(field))
            end)

            if Enum.empty?(missing_fields) do
              {:ok, params}
            else
              {:error, missing_fields}
            end

          {:error, _reason} ->
            {:error, ["Failed to load form structure"]}
        end
      else
        # For dynamic forms, use existing validation
        case DynamicFormsManager.get_form(step.form_id) do
          nil -> {:error, ["Form not found"]}
          form ->
            case DynamicFormsManager.validate_request(form, params) do
              {:ok, data} -> {:ok, data}
              {:error, error} -> {:error, [error]}
            end
        end
      end
    end

    # Validate wizard step data
    defp validate_wizard_step_data(step, params) do
      if ServiceManager.Forms.FormWizardStep.mobile_form_step?(step) do
        DynamicFormsManager.validate_mobile_form_data(step.mobile_form_id, params)
      else
        # Validate using dynamic form (legacy)
        case DynamicFormsManager.get_form(step.form_id) do
          nil -> {:error, "Form not found"}
          form -> DynamicFormsManager.validate_request(form, params)
        end
      end
    end

    # Get next wizard step
    defp get_next_wizard_step(wizard, current_step, _validated_data) do
      steps = DynamicFormsManager.get_wizard_steps_with_forms(wizard.id)
      current_step_number = current_step.step_number
      total_steps = length(steps)

      if current_step_number >= total_steps do
        {:complete, %{wizard_completed: true}}
      else
        next_step = Enum.find(steps, &(&1.step_number == current_step_number + 1))
        if next_step do
          {:next_step, next_step}
        else
          {:error, "Next step not found"}
        end
      end
    end

    # Handle wizard completion
    defp handle_wizard_completion(conn, wizard, step, final_data) do
      # Clean up wizard session data
      cleanup_wizard_session_data(wizard.id)

      # If this step has a dynamic process, execute it
      if step.dynamic_process_id do
        # Execute the linked dynamic process with all collected data
        case ProcessManager.execute_process(step.dynamic_process_id, final_data) do
          {:ok, result} ->
            conn
            |> put_status(:ok)
            |> json(%{
              success: true,
              wizard_completed: true,
              result: result,
              message: "Wizard completed successfully"
            })

          {:error, reason} ->
            conn
            |> put_status(:internal_server_error)
            |> json(%{success: false, message: "Final processing failed", error: reason})
        end
      else
        # No final processing, just return success
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          wizard_completed: true,
          message: "Wizard completed successfully",
          data: final_data
        })
      end
    end

    # Handle dynamic form step (legacy support)
    defp handle_dynamic_form_step(conn, wizard, step) do
      case DynamicFormsManager.get_form(step.form_id) do
        nil ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, message: "Form not found"})

        form ->
          response = %{
            success: true,
            wizard: %{
              id: wizard.id,
              name: wizard.name,
              current_step: step.step_number,
              total_steps: ServiceManager.Forms.FormWizard.step_count(wizard)
            },
            form: form.form,
            validation_schema: form.validation_schema
          }

          conn
          |> put_status(:ok)
          |> json(response)
      end
    end

    # =============================================================================
    # WIZARD SESSION MANAGEMENT
    # =============================================================================

    # Generate a session token for wizard progress tracking
    defp generate_wizard_session_token(wizard_id, step_number) do
      timestamp = System.system_time(:second)
      data = "#{wizard_id}:#{step_number}:#{timestamp}"
      :crypto.hash(:sha256, data) |> Base.encode16(case: :lower)
    end

    # Render wizard step response with all necessary data
    defp render_wizard_step_response(conn, session_details) do
      %{session: session, current_step: current_step} = session_details

      # Execute on_enter triggers for the current wizard step
      execute_wizard_on_enter_triggers(current_step.id, session_details)

      if ServiceManager.Forms.FormWizardStep.mobile_form_step?(current_step) do
        # Load mobile form structure
        case get_mobile_form_structure(current_step.mobile_form_id) do
          {:ok, form_structure} ->
            response = %{
              success: true,
              session_token: session.session_token,
              wizard: %{
                id: session.wizard.id,
                name: session.wizard.name,
                current_step: session.current_step_number,
                total_steps: session_details.total_steps,
                is_final_step: session_details.is_final_step,
                progress_percentage: session_details.progress_percentage
              },
              form: form_structure,
              previous_data: session.form_data,
              completed_steps: session.completed_steps
            }

            conn
            |> put_status(:ok)
            |> json(response)

          {:error, reason} ->
            conn
            |> put_status(:internal_server_error)
            |> json(%{success: false, message: "Failed to load form structure", error: reason})
        end
      else
        # Handle dynamic form step (legacy support)
        response = %{
          success: true,
          session_token: session.session_token,
          wizard: %{
            id: session.wizard.id,
            name: session.wizard.name,
            current_step: session.current_step_number,
            total_steps: session_details.total_steps,
            is_final_step: session_details.is_final_step,
            progress_percentage: session_details.progress_percentage
          },
          form: current_step.form.form,
          validation_schema: current_step.form.validation_schema,
          previous_data: session.form_data,
          completed_steps: session.completed_steps
        }

        conn
        |> put_status(:ok)
        |> json(response)
      end
    end

    # Validate current step data
    defp validate_current_step_data(session_details, params) do
      current_step = session_details.current_step

      if ServiceManager.Forms.FormWizardStep.mobile_form_step?(current_step) do
        DynamicFormsManager.validate_mobile_form_data(current_step.mobile_form_id, params)
      else
        # Validate using dynamic form (legacy)
        case DynamicFormsManager.get_form(current_step.form_id) do
          nil -> {:error, "Form not found"}
          form -> DynamicFormsManager.validate_request(form, params)
        end
      end
    end

    # Validate step-specific requirements (e.g., first step validation)
    defp validate_step_requirements(session_details, validated_data) do
      current_step = session_details.current_step
      session = session_details.session

      # For first step, validate required fields are present
      if session.current_step_number == 1 do
        case validate_first_step_required_fields(current_step, validated_data) do
          {:ok, _} -> {:ok, validated_data}
          {:error, missing_fields} -> {:error, :validation_failed, missing_fields}
        end
      else
        {:ok, validated_data}
      end
    end

    # Process wizard step submission
    defp process_wizard_step_submission(conn, session_details, validated_data) do
      session = session_details.session

      # Execute on_exit and conditional triggers for the current step
      # execute_wizard_step_completion_triggers(session_details.current_step.id, session_details, validated_data)

      if session_details.is_final_step do
        # Complete the wizard
        complete_wizard_flow(conn, session, validated_data)
      else
        # Move to next step
        advance_to_next_step(conn, session_details, validated_data)
      end
    end

    # Complete wizard flow
    defp complete_wizard_flow(conn, session, validated_data) do
      # Update session with final step data
      case DynamicFormsManager.update_wizard_session(session.session_token, validated_data) do
        {:ok, updated_session} ->
          # Mark session as completed
          DynamicFormsManager.complete_wizard_session(session.session_token)

          # Combine all collected data from all steps into a single body
          combined_data = combine_wizard_step_data(updated_session.form_data)

          # Get the route to check for attached dynamic process
          case DynamicRouteManager.find_route(conn.method, get_dynamic_path(conn)) do
            {:ok, route} ->
              # Process the combined data through any attached dynamic processes
              process_combined_wizard_data(conn, route, combined_data, session.session_token)

            {:error, _} ->
              # Return success response with collected data (fallback)
              conn
              |> put_status(:ok)
              |> json(%{
                success: true,
                message: "Wizard completed successfully",
                data: %{
                  wizard_completed: true,
                  session_token: session.session_token,
                  collected_data: combined_data
                }
              })
          end

        {:error, reason} ->
          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, message: "Failed to complete wizard", error: reason})
      end
    end

    # Advance to next step
    defp advance_to_next_step(conn, session_details, validated_data) do
      session = session_details.session
      next_step_number = session.current_step_number + 1

      # Update session with current step data and advance to next step
      case DynamicFormsManager.update_wizard_session(session.session_token, validated_data, next_step_number) do
        {:ok, _updated_session} ->
          # Return next step data
          case DynamicFormsManager.get_wizard_session_with_details(session.session_token) do
            {:ok, next_session_details} ->
              render_wizard_step_response(conn, next_session_details)

            {:error, reason} ->
              conn
              |> put_status(:internal_server_error)
              |> json(%{success: false, message: "Failed to load next step", error: reason})
          end

        {:error, reason} ->
          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, message: "Failed to advance to next step", error: reason})
      end
    end

    # Get client IP address
    defp get_client_ip(conn) do
      case Plug.Conn.get_req_header(conn, "x-forwarded-for") do
        [ip | _] -> ip
        [] ->
          case conn.remote_ip do
            {a, b, c, d} -> "#{a}.#{b}.#{c}.#{d}"
            _ -> "unknown"
          end
      end
    end

    # Get user agent
    defp get_user_agent(conn) do
      case Plug.Conn.get_req_header(conn, "user-agent") do
        [ua | _] -> ua
        [] -> "unknown"
      end
    end

    # Get wizard first step
    defp get_wizard_first_step(wizard) do
      case DynamicFormsManager.get_first_wizard_step(wizard.id) do
        nil -> {:error, :no_first_step}
        step -> {:ok, step}
      end
    end

    # Validate first step data (without session)
    defp validate_first_step_data(first_step, params) do
      # First validate the form data structure
      validation_result = if ServiceManager.Forms.FormWizardStep.mobile_form_step?(first_step) do
        DynamicFormsManager.validate_mobile_form_data(first_step.mobile_form_id, params)
      else
        # Validate using dynamic form (legacy)
        case DynamicFormsManager.get_form(first_step.form_id) do
          nil -> {:error, "Form not found"}
          form -> DynamicFormsManager.validate_request(form, params)
        end
      end

      case validation_result do
        {:ok, validated_data} ->
          # Additional first-step specific validation
          case validate_first_step_required_fields(first_step, validated_data) do
            {:ok, _} -> {:ok, validated_data}
            {:error, missing_fields} -> {:error, :validation_failed, missing_fields}
          end

        {:error, reason} -> {:error, reason}
      end
    end

    # Update session with first step data and advance to next step
    defp update_session_and_advance(session, validated_data) do
      next_step_number = 2  # Always advance to step 2 from first step

      case DynamicFormsManager.update_wizard_session(session.session_token, validated_data, next_step_number) do
        {:ok, _updated_session} ->
          # Get updated session details for response
          DynamicFormsManager.get_wizard_session_with_details(session.session_token)

        {:error, reason} -> {:error, reason}
      end
    end

    # Combine wizard step data into a single flat structure
    defp combine_wizard_step_data(form_data) do
      form_data
      |> Enum.reduce(%{}, fn {step_key, step_data}, acc ->
        case step_data do
          data when is_map(data) -> Map.merge(acc, data)
          _ -> acc
        end
      end)
    end

    # Process combined wizard data through dynamic processes
    defp process_combined_wizard_data(conn, route, combined_data, session_token) do
      # Check if the route has a linked process
      case ProcessManager.get_initial_process(route.id) do
        {:ok, _process} ->
          # Execute the process chain with combined wizard data
          case ProcessManager.execute_chain(route.id, combined_data) do
            {:ok, result} ->
              # Process chain executed successfully
              conn
              |> put_status(:ok)
              |> json(%{
                success: true,
                wizard_completed: true,
                message: "Wizard completed and processed successfully",
                session_token: session_token,
                collected_data: combined_data,
                process_result: result
              })

            {:error, reason} ->
              # Process chain execution failed
              conn
              |> put_status(:internal_server_error)
              |> json(%{
                success: false,
                wizard_completed: true,
                message: "Wizard completed but process execution failed",
                session_token: session_token,
                collected_data: combined_data,
                error: reason
              })
          end

        {:error, :not_found} ->
          # No process linked to this route, return success with collected data
          conn
          |> put_status(:ok)
          |> json(%{
            success: true,
            wizard_completed: true,
            message: "Wizard completed successfully",
            session_token: session_token,
            collected_data: combined_data
          })
      end
    end

    # Get the dynamic path from connection
    defp get_dynamic_path(conn) do
      full_path = conn.request_path
      dynamic_path = String.replace_prefix(full_path, @base_path, "")
      if dynamic_path == "", do: "/", else: dynamic_path
    end

    # Validate wizard session token (legacy - keeping for backward compatibility)
    defp validate_wizard_session(params, wizard_id, expected_step) do
      case Map.get(params, "wizard_session_token") do
        nil -> :ok  # Allow requests without session token for first step
        token ->
          # For now, just validate that token format is correct
          # In production, you might want to store and validate actual sessions
          if String.length(token) == 64 do
            :ok
          else
            {:error, "Invalid session token format"}
          end
      end
    end

    # Store wizard step data in memory/cache for session duration
    defp store_wizard_step_data(wizard_id, step_number, data) do
      # Using process dictionary for simplicity - in production use ETS or Redis
      key = "wizard_#{wizard_id}_step_#{step_number}"
      Process.put(key, data)
    end

    # Get all stored wizard step data
    defp get_all_wizard_step_data(wizard_id) do
      Process.get_keys()
      |> Enum.filter(&String.starts_with?(to_string(&1), "wizard_#{wizard_id}_step_"))
      |> Enum.map(&{&1, Process.get(&1)})
      |> Enum.reduce(%{}, fn {key, value}, acc ->
        step_number = key |> to_string() |> String.split("_") |> List.last() |> String.to_integer()
        Map.put(acc, "step_#{step_number}", value)
      end)
    end

    # Clean up wizard session data
    defp cleanup_wizard_session_data(wizard_id) do
      Process.get_keys()
      |> Enum.filter(&String.starts_with?(to_string(&1), "wizard_#{wizard_id}_step_"))
      |> Enum.each(&Process.delete(&1))
    end

    # Get total steps for a wizard
    defp get_wizard_total_steps(wizard_id) do
      steps = DynamicFormsManager.get_wizard_steps_with_forms(wizard_id)
      length(steps)
    end

    # Check if this is the final step in the wizard
    defp is_final_wizard_step?(wizard_id, current_step_number) do
      total_steps = get_wizard_total_steps(wizard_id)
      current_step_number >= total_steps
    end

    # =============================================================================
    # EXISTING FUNCTIONS
    # =============================================================================

    # Check if any form is required for this route
    defp check_if_form_required(route_id) do
      route_forms = DynamicFormsManager.get_route_forms(route_id)
      Enum.any?(route_forms, fn form -> form.required end)
    end

    # Extract request parameters based on HTTP method
    defp get_request_params(conn, method) do
      case method do
        "GET" -> conn.query_params
        _ ->
          case conn.body_params do
            %Plug.Conn.Unfetched{} -> %{}
            params -> params
          end
      end
    end

    # List Forms
    def forms(conn, _params) do

      forms = DynamicForm.all()

      conn
      |> json(%{
        forms: forms
        })
    end

    # List registered routes
    def dynamic_routes(conn, _params) do
      routes = list_dynamic_routes()

      conn
      |> json(%{
        routes: routes
      })
    end

    defp list_dynamic_routes() do
      query = """
        SELECT
          routes.*,
          forms.id AS form_id,
          forms.name AS form_name,
          forms.description,
          forms.http_method,
          forms.form,
          forms.validation_schema,
          forms.required,
          forms.inserted_at AS form_inserted_at,
          forms.updated_at AS form_updated_at
        FROM dynamic_routes AS routes
        LEFT JOIN dynamic_route_forms AS route_forms ON routes.id = route_forms.route_id
        LEFT JOIN dynamic_forms AS forms ON route_forms.form_id = forms.id
        ORDER BY routes.id
      """

      result = Ecto.Adapters.SQL.query!(ServiceManager.Repo, query, [])

      # Process the raw results into structured format
      result.rows
      |> Enum.map(fn row ->
        # Convert row tuple to map with string keys
        Enum.zip(result.columns, row)
        |> Map.new(fn {k, v} -> {k, v} end)
      end)
      |> Enum.group_by(& &1["id"], & &1)
      |> Enum.map(fn {route_id, rows} ->
        base_route = %{
          "id" => route_id,
          "name" => hd(rows)["name"],
          "method" => hd(rows)["method"],
          "path" => hd(rows)["path"],
          "enabled" => hd(rows)["enabled"],
          "parts" => hd(rows)["parts"],
          "flat_parts" => hd(rows)["flat_parts"],
          "inserted_at" => hd(rows)["inserted_at"],
          "updated_at" => hd(rows)["updated_at"]
        }

        forms = Enum.filter_map(rows, & &1["form_id"], fn form ->
          %{
            name: form["form_name"],
            description: form["description"],
            http_method: form["http_method"],
            form: form["form"],
            validation_schema: form["validation_schema"],
            required: form["required"],
            inserted_at: form["form_inserted_at"],
            updated_at: form["form_updated_at"]
          }
        end)

        Map.put(base_route, :form, forms)
      end)
    end

    # =============================================================================
    # WIZARD TRIGGER INTEGRATION
    # =============================================================================

    # Execute on_enter triggers when displaying a wizard step
    defp execute_wizard_on_enter_triggers(wizard_step_id, session_details) do
      context = WizardTriggerExecutor.build_wizard_context(session_details)

      case WizardTriggerExecutor.execute_on_enter_triggers(wizard_step_id, context) do
        {:ok, _results} ->
          # Triggers executed successfully - continue normally
          :ok
        {:error, failed_triggers} ->
          # Log trigger failures but don't block wizard flow
          require Logger
          Logger.warning("Wizard on_enter triggers failed",
            wizard_step_id: wizard_step_id,
            session_token: session_details.session.session_token,
            failed_triggers: failed_triggers
          )
          :ok
      end
    end

    # Execute step completion triggers (on_exit and conditional)
    defp execute_wizard_step_completion_triggers(wizard_step_id, session_details, step_data) do
      context = WizardTriggerExecutor.build_wizard_context(session_details, step_data)

      case WizardTriggerExecutor.execute_step_completion_triggers(wizard_step_id, context) do
        {:ok, _results} ->
          # Triggers executed successfully - continue normally
          :ok
        {:error, failed_triggers} ->
          # Log trigger failures but don't block wizard flow
          require Logger
          Logger.warning("Wizard step completion triggers failed",
            wizard_step_id: wizard_step_id,
            session_token: session_details.session.session_token,
            failed_triggers: failed_triggers
          )
          :ok
      end
    end

  end
