defmodule ServiceManager.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application
  # import Cachex.Spec
  alias ServiceManager.Services.T24.Config
  alias ServiceManager.TelegramNotify

  @impl true
  def start(_type, _args) do
    # Send notification that application is initializing
    TelegramNotify.notify_app_initializing()

    # Create ETS table for registration cache
    :ets.new(:registration_cache, [:named_table, :public, :set])

    children =
      [
        ServiceManagerWeb.Telemetry,
        ServiceManager.Repo,
        {DNSCluster, query: Application.get_env(:service_manager, :dns_cluster_query) || :ignore},
        {Phoenix.PubSub, name: ServiceManager.PubSub, adapter: Phoenix.PubSub.PG2},
        # Start Presence after PubSub
        ServiceManagerWeb.Presence,
        # Start the Finch HTTP client for sending emails
        {Finch, name: ServiceManager.Finch},
        # Start Finch for general request pool
        {<PERSON>,
         name: ServiceManagerFinch,
         pools: %{
           default: [
             # Number of connections per pool
             size: 50,
             # Number of pools
             count: 5,
             # Use HTTP/1.1 for better compatibility
             protocol: :http1,
             conn_opts: [
               transport_opts: [
                 # For development/testing with self-signed certs
                 verify: :verify_none,
                 # Supported TLS versions
                 versions: [:"tlsv1.2", :"tlsv1.3"],
                 ciphers: :ssl.cipher_suites(:default, :"tlsv1.3"),
                 secure_renegotiate: true,
                 reuse_sessions: true,
                 honor_cipher_order: true,
                 client_renegotiation: false
               ]
             ]
           ]
         }},
        # Start Oban using configuration from config.exs
        {Oban, Application.fetch_env!(:service_manager, Oban)},
        # Start a worker by calling: ServiceManager.Worker.start_link(arg)
        # {ServiceManager.Worker, arg},
        # Start the DynamicSupervisor for feature-controlled processors
        {DynamicSupervisor, strategy: :one_for_one, name: ServiceManager.DynamicSupervisor},
        # Start the Transaction Processors Supervisor

        # Start to serve requests, typically the last entry
        ServiceManagerWeb.Endpoint,
        {Finch, name: T24Finch, pools: finch_pools()},
        # Start the System Metrics Server
        ServiceManager.SystemMetricsServer,

        # ServiceManager.Processors.Supervisor,
        # Start the Uptime Monitor
        ServiceManager.Monitoring.UptimeMonitor
      ] ++
        [
          ServiceManager.Processors.UserBalanceSyncProcessor,
          #          ServiceManager.Processors.AccountTransactionProcessor,
          #          ServiceManager.Processors.WalletTransactionProcessor,
          ServiceManager.Processors.CallbackProcessor
        ] ++
        [
          ServiceManager.Services.Notification.SMSWorker,
          ServiceManager.Notifications.Dispatcher
        ] ++
        [
          ServiceManager.Services.MerchantCallbackWorker,
          ServiceManager.Services.TransferCallbackServer
        ] ++
        [
          ServiceManager.Cache.RouteCache,
          ServiceManager.Cache.ConfigCache,
          ServiceManager.Cache.IpWhitelistCache,
          ServiceManager.Statistics.Cache,
          Supervisor.child_spec({Cachex, name: :otp_cache}, id: :otp_cache),
          Supervisor.child_spec({Cachex, name: :settings}, id: :settings),
          Supervisor.child_spec({Cachex, name: :registration}, id: :registration)
        ] ++
        [
          # Trigger System Processes
          ServiceManager.Triggers.TriggerRegistry,
          ServiceManager.Triggers.RateLimiter
        ]
#        maybe_start_mcp_server()

    # Send notification that critical services are ready
    TelegramNotify.notify_critical_services_ready()

    :fuse.install(:t24_service, Config.circuit_breaker_config())

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: ServiceManager.Supervisor]
    result = Supervisor.start_link(children, opts)

    # Send notification that application is fully started
    TelegramNotify.notify_app_ready()

    result
  end

  # Add MCP server to children if enabled
  defp maybe_start_mcp_server() do
    mcp_config = Application.get_env(:service_manager, :mcp_server, %{})

    case mcp_config do
      %{enabled: true} ->
        Logger.info("🚀 MCP Server is enabled - adding to supervision tree")
        Logger.info("   MCP Config: #{inspect(mcp_config)}")
        [ServiceManager.MCP.Supervisor]
      config ->
        Logger.info("⏸️  MCP Server is disabled")
        Logger.debug("   MCP Config: #{inspect(config)}")
        []
    end
  end

  defp finch_pools do
    %{
      default: [
        size: 100,
        count: 10
      ]
    }
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    ServiceManagerWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
