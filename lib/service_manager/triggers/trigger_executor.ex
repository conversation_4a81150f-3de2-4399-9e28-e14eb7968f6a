defmodule ServiceManager.Triggers.TriggerExecutor do
  @moduledoc """
  Executes triggers with logging, error handling, rate limiting, and security measures.
  Integrates with the existing FunctionTracker system for comprehensive monitoring.
  """

  use ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Triggers.{TriggerRegistry, TriggerValidator, RateLimiter}
  alias ServiceManager.Triggers.TriggerManager
  alias ServiceManager.Triggers.Schemas.TriggerExecutionLog
  alias ServiceManager.Repo
  require Logger

  @default_timeout 30_000  # 30 seconds

  @track do
    @doc """
    Executes a trigger by mount point with full security, validation and logging.
    """
    def execute_trigger(mount_point, params, context \\ %{}) do
      _chain_id = context[:chain_id] || Ecto.UUID.generate()
      start_time = System.monotonic_time(:millisecond)

      with {:ok, trigger_info} <- TriggerRegistry.get_trigger_info(mount_point),
           :ok <- check_rate_limiting(trigger_info, context),
           {:ok, validated_params} <- TriggerValidator.validate_input(trigger_info, params),
           :ok <- TriggerValidator.validate_security_context(context, trigger_info),
           {:ok, result} <- execute_trigger_function(trigger_info, validated_params, context) do

        execution_time = System.monotonic_time(:millisecond) - start_time
        log_successful_execution(trigger_info, params, result, execution_time, context)

        format_execution_result(result, trigger_info)
      else
        {:error, :not_found} ->
          error_msg = "Trigger not found: #{mount_point}"
          log_trigger_error(mount_point, params, error_msg, context)
          {:error, error_msg}

        {:error, :rate_limited} ->
          error_msg = "Rate limit exceeded for trigger: #{mount_point}"
          log_trigger_error(mount_point, params, error_msg, context)
          {:error, error_msg}

        {:error, validation_errors} when is_binary(validation_errors) ->
          error_msg = "Validation failed: #{validation_errors}"
          log_trigger_error(mount_point, params, error_msg, context)
          {:error, error_msg}

        {:error, reason} ->
          error_msg = format_error_reason(reason)
          log_trigger_error(mount_point, params, error_msg, context)
          {:error, error_msg}
      end
    end
  end

  @track do
    @doc """
    Executes multiple triggers in sequence for a wizard step.
    """
    def execute_wizard_step_triggers(wizard_step_id, wizard_step_data, execution_timing, context \\ %{}) do
      chain_id = context[:chain_id] || Ecto.UUID.generate()

      wizard_step_id
      |> TriggerManager.get_executable_step_triggers(execution_timing, wizard_step_data)
      |> Enum.map(&execute_step_trigger(&1, wizard_step_data, Map.put(context, :chain_id, chain_id)))
      |> collect_trigger_results()
    end
  end

  @doc """
  Executes a trigger asynchronously and returns immediately.
  """
  def execute_trigger_async(mount_point, params, context \\ %{}) do
    task = Task.async(fn ->
      execute_trigger(mount_point, params, context)
    end)

    {:ok, %{task_ref: task.ref, status: "async_started"}}
  end

  @doc """
  Gets the result of an async trigger execution.
  """
  def get_async_result(task, timeout \\ 5000) when is_struct(task, Task) do
    try do
      Task.await(task, timeout)
    catch
      :exit, {:timeout, _} -> {:error, :timeout}
      :exit, reason -> {:error, {:task_failed, reason}}
    end
  end

  ## Private Functions

  defp execute_step_trigger(step_trigger, wizard_step_data, context) do
    # Map wizard step data to trigger input parameters
    trigger_params = ServiceManager.Triggers.Schemas.WizardStepTrigger.map_wizard_data_to_trigger_inputs(
      wizard_step_data,
      step_trigger.input_mapping
    )

    # Execute the trigger
    result = execute_trigger(
      step_trigger.trigger.mount_point,
      trigger_params,
      Map.merge(context, %{
        wizard_step_id: step_trigger.wizard_step_id,
        wizard_step_trigger_id: step_trigger.id
      })
    )

    {step_trigger, result}
  end

  defp collect_trigger_results(results) do
    {successful_results, errors} = 
      Enum.split_with(results, fn {_step_trigger, result} ->
        match?({:ok, _}, result)
      end)

    success_data = 
      successful_results
      |> Enum.filter(fn {step_trigger, _result} -> step_trigger.trigger.return_data end)
      |> Enum.reduce(%{}, fn {step_trigger, {:ok, result}}, acc ->
        trigger_key = "trigger_#{step_trigger.trigger.id}"
        Map.put(acc, trigger_key, result)
      end)

    error_data = 
      Enum.map(errors, fn {step_trigger, {:error, reason}} ->
        %{trigger_name: step_trigger.trigger.name, error: reason}
      end)

    case {success_data, error_data} do
      {data, []} -> {:ok, data}
      {data, errors} -> {:partial_success, %{success: data, errors: errors}}
    end
  end

  defp execute_trigger_function(trigger_info, params, context) do
    try do
      case trigger_info.execution_type do
        :sync -> 
          execute_sync_trigger(trigger_info, params, context)
        
        :async ->
          execute_async_trigger_function(trigger_info, params, context)
      end
    rescue
      error ->
        {:error, "Execution error: #{format_execution_error(error)}"}
    catch
      :exit, reason ->
        {:error, "Process exit: #{inspect(reason)}"}
      
      :throw, value ->
        {:error, "Uncaught throw: #{inspect(value)}"}
    end
  end

  defp execute_sync_trigger(trigger_info, params, context) do
    if trigger_info.sandbox_execution do
      execute_in_sandbox(trigger_info, params, context)
    else
      execute_directly(trigger_info, params, context)
    end
  end

  defp execute_async_trigger_function(trigger_info, params, context) do
    Task.start(fn -> 
      execute_sync_trigger(trigger_info, params, context)
    end)
    {:ok, %{status: "async_started", trigger_id: trigger_info.id}}
  end

  defp execute_in_sandbox(trigger_info, params, context) do
    # Execute with timeout and resource constraints
    Task.async(fn ->
      execute_directly(trigger_info, params, context)
    end)
    |> Task.await(@default_timeout)
  rescue
    error ->
      {:error, "Sandbox execution failed: #{format_execution_error(error)}"}
  catch
    :exit, {:timeout, _} ->
      {:error, "Execution timed out after #{@default_timeout}ms"}
  end

  defp execute_directly(trigger_info, params, context) do
    apply(trigger_info.module, trigger_info.function, [params, context])
    |> case do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
      result -> {:ok, result}  # Wrap bare results
    end
  end

  defp check_rate_limiting(trigger_info, context) do
    if trigger_info.rate_limiting_enabled do
      case Map.get(context, :user_id) do
        nil -> {:error, "User ID required for rate-limited trigger"}
        user_id -> RateLimiter.check_rate_limit(trigger_info.id, user_id)
      end
    else
      :ok
    end
  end

  defp format_execution_result(result, trigger_info) do
    if trigger_info.return_data do
      {:ok, result}
    else
      {:ok, %{status: "success"}}
    end
  end

  defp log_successful_execution(trigger_info, params, result, execution_time, context) do
    if trigger_info.logging_enabled do
      log_attrs = TriggerExecutionLog.create_success_log(trigger_info.id, %{
        wizard_step_id: context[:wizard_step_id],
        user_id: context[:user_id],
        execution_type: trigger_info.execution_type,
        request_payload: TriggerExecutionLog.sanitize_payload(params),
        response_payload: TriggerExecutionLog.sanitize_payload(result),
        execution_time_ms: execution_time
      })

      case Repo.insert(log_attrs) do
        {:ok, _log} -> :ok
        {:error, changeset} -> 
          Logger.warn("Failed to log trigger execution: #{inspect(changeset.errors)}")
      end
    end
  end

  defp log_trigger_error(mount_point, params, error_message, context) do
    # Create structured error details for FunctionTracker
    error_details = %{
      type: "trigger_error",
      mount_point: mount_point,
      error_message: error_message,
      context: context,
      request_params: TriggerExecutionLog.sanitize_payload(params),
      timestamp: DateTime.utc_now(),
      severity: determine_error_severity(error_message)
    }

    # Log using existing FunctionTracker system
    Logger.error("Trigger Execution Error: #{error_message}", extra: error_details)

    # Also log to database if we can find the trigger
    case TriggerRegistry.get_trigger_info(mount_point) do
      {:ok, trigger_info} when trigger_info.logging_enabled ->
        log_attrs = TriggerExecutionLog.create_error_log(trigger_info.id, error_message, %{
          wizard_step_id: context[:wizard_step_id],
          user_id: context[:user_id],
          execution_type: trigger_info.execution_type,
          request_payload: TriggerExecutionLog.sanitize_payload(params),
          error_details: error_details
        })

        case Repo.insert(log_attrs) do
          {:ok, _log} -> :ok
          {:error, changeset} -> 
            Logger.warn("Failed to log trigger error: #{inspect(changeset.errors)}")
        end
      
      _ -> :ok
    end
  end

  defp determine_error_severity(error_message) do
    cond do
      String.contains?(error_message, ["timeout", "rate limit"]) -> "warning"
      String.contains?(error_message, ["not found", "validation"]) -> "error"
      String.contains?(error_message, ["execution error", "process exit"]) -> "critical"
      true -> "error"
    end
  end

  defp format_error_reason(reason) when is_binary(reason), do: reason
  defp format_error_reason(reason) when is_atom(reason), do: Atom.to_string(reason)
  defp format_error_reason(reason), do: inspect(reason)

  defp format_execution_error(%{message: message}), do: message
  defp format_execution_error(error) when is_binary(error), do: error
  defp format_execution_error(error), do: inspect(error)

  ## Public Utility Functions

  @doc """
  Tests a trigger execution with safe parameters.
  Useful for development and debugging.
  """
  def test_trigger(mount_point, test_params \\ %{}) do
    context = %{
      user_id: "test-user-#{System.unique_integer()}",
      wizard_step_id: "test-step-#{System.unique_integer()}",
      test_mode: true,
      chain_id: "test-#{System.unique_integer()}"
    }

    case execute_trigger(mount_point, test_params, context) do
      {:ok, result} ->
        IO.puts("✅ Trigger test successful for #{mount_point}")
        IO.inspect(result, label: "Result", pretty: true)
        {:ok, result}

      {:error, reason} ->
        IO.puts("❌ Trigger test failed for #{mount_point}: #{reason}")
        {:error, reason}
    end
  end

  @doc """
  Validates that a trigger's module and function are available and callable.
  """
  def validate_trigger_availability(mount_point) do
    case TriggerRegistry.get_trigger_info(mount_point) do
      {:ok, trigger_info} ->
        if function_exported?(trigger_info.module, trigger_info.function, 2) do
          IO.puts("✅ Trigger #{mount_point} is available and callable")
          {:ok, trigger_info}
        else
          error = "Function #{trigger_info.function}/2 not exported by #{trigger_info.module}"
          IO.puts("❌ #{error}")
          {:error, error}
        end

      {:error, :not_found} ->
        error = "Trigger not found in registry: #{mount_point}"
        IO.puts("❌ #{error}")
        {:error, error}
    end
  end

  @doc """
  Gets execution statistics for monitoring dashboards.
  """
  def get_execution_stats(time_period \\ :last_hour) do
    from_datetime = case time_period do
      :last_hour -> DateTime.add(DateTime.utc_now(), -3600, :second)
      :last_day -> DateTime.add(DateTime.utc_now(), -86400, :second)
      :last_week -> DateTime.add(DateTime.utc_now(), -604800, :second)
      datetime when is_struct(datetime, DateTime) -> datetime
    end

    TriggerManager.get_execution_statistics(from_datetime)
  end
end