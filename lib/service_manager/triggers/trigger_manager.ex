defmodule ServiceManager.Triggers.TriggerManager do
  @moduledoc """
  Context module for managing triggers and their associated operations.
  Provides CRUD operations, validation, and business logic for the trigger system.
  """

  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManager.Triggers.Schemas.{<PERSON><PERSON>, WizardStepTrigger, TriggerExecutionLog}

  ## Trigger CRUD Operations

  @doc """
  Returns the list of triggers with optional filtering.
  
  ## Options
  * `:enabled` - Filter by enabled status (true/false)
  * `:execution_type` - Filter by execution type (:sync/:async)
  * `:preload` - List of associations to preload
  """
  def list_triggers(opts \\ []) do
    Trigger
    |> filter_by_enabled(opts[:enabled])
    |> filter_by_execution_type(opts[:execution_type])
    |> maybe_preload(opts[:preload])
    |> order_by([t], asc: t.name)
    |> Repo.all()
  end

  @doc """
  Gets a single trigger by ID.
  """
  def get_trigger(id) do
    Repo.get(Trigger, id)
  end

  @doc """
  Gets a single trigger by ID, raising an exception if not found.
  """
  def get_trigger!(id) do
    Repo.get!(<PERSON>gger, id)
  end

  @doc """
  Gets a trigger by mount point. Returns enabled triggers only.
  """
  def get_trigger_by_mount_point(mount_point) do
    Repo.get_by(Trigger, mount_point: mount_point, enabled: true)
  end

  @doc """
  Creates a new trigger.
  """
  def create_trigger(attrs \\ %{}) do
    %Trigger{}
    |> Trigger.changeset(attrs)
    |> Repo.insert()
    |> case do
      {:ok, trigger} ->
        # Notify registry to reload triggers
        notify_registry_reload()
        {:ok, trigger}
      error ->
        error
    end
  end

  @doc """
  Updates an existing trigger.
  """
  def update_trigger(%Trigger{} = trigger, attrs) do
    trigger
    |> Trigger.changeset(attrs)
    |> Repo.update()
    |> case do
      {:ok, updated_trigger} ->
        # Notify registry to reload triggers
        notify_registry_reload()
        {:ok, updated_trigger}
      error ->
        error
    end
  end

  @doc """
  Deletes a trigger.
  """
  def delete_trigger(%Trigger{} = trigger) do
    case Repo.delete(trigger) do
      {:ok, deleted_trigger} ->
        # Notify registry to reload triggers
        notify_registry_reload()
        {:ok, deleted_trigger}
      error ->
        error
    end
  end

  @doc """
  Returns an %Ecto.Changeset{} for tracking trigger changes.
  """
  def change_trigger(%Trigger{} = trigger, attrs \\ %{}) do
    Trigger.changeset(trigger, attrs)
  end

  def change_trigger(nil, attrs) do
    change_trigger(%Trigger{}, attrs)
  end

  @doc """
  Validates if a trigger module and function exist and are callable.
  """
  def validate_trigger_implementation(trigger) do
    Trigger.validate_module_function(trigger.module_name, trigger.function_name)
  end

  ## Wizard Step Trigger Operations

  @doc """
  Lists all wizard step triggers for a given wizard step, ordered by execution_order.
  """
  def list_wizard_step_triggers(wizard_step_id, opts \\ []) do
    WizardStepTrigger
    |> where([wst], wst.wizard_step_id == ^wizard_step_id)
    |> filter_step_triggers_by_enabled(opts[:enabled])
    |> filter_step_triggers_by_timing(opts[:execution_timing])
    |> maybe_preload(opts[:preload] || [:trigger])
    |> order_by([wst], asc: wst.execution_order, asc: wst.inserted_at)
    |> Repo.all()
  end

  @doc """
  Creates a new wizard step trigger.
  """
  def create_wizard_step_trigger(attrs \\ %{}) do
    %WizardStepTrigger{}
    |> WizardStepTrigger.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an existing wizard step trigger.
  """
  def update_wizard_step_trigger(%WizardStepTrigger{} = wizard_step_trigger, attrs) do
    wizard_step_trigger
    |> WizardStepTrigger.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a wizard step trigger.
  """
  def delete_wizard_step_trigger(%WizardStepTrigger{} = wizard_step_trigger) do
    Repo.delete(wizard_step_trigger)
  end

  @doc """
  Gets wizard step triggers that should execute for a given timing.
  """
  def get_executable_step_triggers(wizard_step_id, execution_timing, wizard_step_data \\ %{}) do
    wizard_step_id
    |> list_wizard_step_triggers(enabled: true, execution_timing: execution_timing, preload: [:trigger])
    |> Enum.filter(fn step_trigger ->
      step_trigger.trigger.enabled and
        WizardStepTrigger.evaluate_conditions(step_trigger.execution_conditions, wizard_step_data)
    end)
  end

  ## Execution Log Operations

  @doc """
  Creates a new execution log entry.
  """
  def create_execution_log(attrs \\ %{}) do
    %TriggerExecutionLog{}
    |> TriggerExecutionLog.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets recent execution logs with optional filtering.
  """
  def get_recent_execution_logs(opts \\ []) do
    limit = opts[:limit] || 50
    from_datetime = opts[:from_datetime] || DateTime.add(DateTime.utc_now(), -3600, :second) # 1 hour ago

    TriggerExecutionLog
    |> where([log], log.executed_at >= ^from_datetime)
    |> maybe_filter_by_status(opts[:status])
    |> maybe_filter_by_trigger(opts[:trigger_id])
    |> order_by([log], desc: log.executed_at)
    |> limit(^limit)
    |> preload(:trigger)
    |> Repo.all()
  end

  @doc """
  Gets execution statistics for a time period.
  """
  def get_execution_statistics(from_datetime, to_datetime \\ nil) do
    to_datetime = to_datetime || DateTime.utc_now()

    base_stats = %{
      total_executions: get_total_executions(from_datetime, to_datetime),
      successful_executions: get_successful_executions(from_datetime, to_datetime),
      failed_executions: get_failed_executions(from_datetime, to_datetime),
      avg_execution_time: get_avg_execution_time(from_datetime, to_datetime)
    }

    # Add time-specific stats
    one_hour_ago = DateTime.add(DateTime.utc_now(), -3600, :second)
    Map.put(base_stats, :executions_last_hour, get_total_executions(one_hour_ago))
  end

  @doc """
  Gets error logs for debugging and monitoring.
  """
  def get_error_logs(opts \\ []) do
    limit = opts[:limit] || 100
    from_datetime = opts[:from_datetime] || DateTime.add(DateTime.utc_now(), -86400, :second) # 24 hours ago

    TriggerExecutionLog.error_logs_query(from_datetime, limit)
    |> Repo.all()
  end

  @doc """
  Update wizard step triggers for a wizard step.
  """
  def update_wizard_step_triggers(wizard_step_id, trigger_params_list) do
    Repo.transaction(fn ->
      # Delete existing triggers
      from(wst in WizardStepTrigger, where: wst.wizard_step_id == ^wizard_step_id)
      |> Repo.delete_all()

      # Insert new triggers
      Enum.each(trigger_params_list, fn params ->
        %WizardStepTrigger{}
        |> WizardStepTrigger.changeset(params)
        |> Repo.insert!()
      end)

      :ok
    end)
  end

  ## Statistics and Analytics

  @doc """
  Gets trigger usage statistics - most used triggers.
  """
  def get_trigger_usage_stats(limit \\ 10) do
    one_week_ago = DateTime.add(DateTime.utc_now(), -604800, :second) # 1 week ago

    from(log in TriggerExecutionLog,
      join: trigger in assoc(log, :trigger),
      where: log.executed_at >= ^one_week_ago,
      group_by: [trigger.id, trigger.name],
      select: %{
        trigger_id: trigger.id,
        trigger_name: trigger.name,
        execution_count: count(log.id),
        success_rate: fragment("CAST(SUM(CASE WHEN ? = 'success' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*)", log.status)
      },
      order_by: [desc: count(log.id)],
      limit: ^limit
    )
    |> Repo.all()
  end

  @doc """
  Gets performance data for a specific trigger.
  """
  def get_trigger_performance(trigger_id, from_datetime \\ nil) do
    from_datetime = from_datetime || DateTime.add(DateTime.utc_now(), -86400, :second) # 24 hours ago

    TriggerExecutionLog.trigger_performance_query(trigger_id, from_datetime)
    |> Repo.all()
  end

  ## Private Helper Functions

  defp filter_by_enabled(query, nil), do: query
  defp filter_by_enabled(query, enabled), do: where(query, [t], t.enabled == ^enabled)

  defp filter_by_execution_type(query, nil), do: query
  defp filter_by_execution_type(query, execution_type), do: where(query, [t], t.execution_type == ^execution_type)

  defp filter_step_triggers_by_enabled(query, nil), do: query
  defp filter_step_triggers_by_enabled(query, enabled), do: where(query, [wst], wst.enabled == ^enabled)

  defp filter_step_triggers_by_timing(query, nil), do: query
  defp filter_step_triggers_by_timing(query, timing), do: where(query, [wst], wst.execution_timing == ^timing)

  defp maybe_preload(query, nil), do: query
  defp maybe_preload(query, preloads), do: preload(query, ^preloads)

  defp maybe_filter_by_status(query, nil), do: query
  defp maybe_filter_by_status(query, status), do: where(query, [log], log.status == ^status)

  defp maybe_filter_by_trigger(query, nil), do: query
  defp maybe_filter_by_trigger(query, trigger_id), do: where(query, [log], log.trigger_id == ^trigger_id)

  defp get_total_executions(from_datetime, to_datetime \\ nil) do
    to_datetime = to_datetime || DateTime.utc_now()
    
    from(log in TriggerExecutionLog,
      where: log.executed_at >= ^from_datetime and log.executed_at <= ^to_datetime,
      select: count(log.id)
    )
    |> Repo.one() || 0
  end

  defp get_successful_executions(from_datetime, to_datetime \\ nil) do
    to_datetime = to_datetime || DateTime.utc_now()
    
    from(log in TriggerExecutionLog,
      where: log.executed_at >= ^from_datetime and log.executed_at <= ^to_datetime and log.status == :success,
      select: count(log.id)
    )
    |> Repo.one() || 0
  end

  defp get_failed_executions(from_datetime, to_datetime \\ nil) do
    to_datetime = to_datetime || DateTime.utc_now()
    
    from(log in TriggerExecutionLog,
      where: log.executed_at >= ^from_datetime and log.executed_at <= ^to_datetime and log.status == :error,
      select: count(log.id)
    )
    |> Repo.one() || 0
  end

  defp get_avg_execution_time(from_datetime, to_datetime \\ nil) do
    to_datetime = to_datetime || DateTime.utc_now()
    
    from(log in TriggerExecutionLog,
      where: log.executed_at >= ^from_datetime and log.executed_at <= ^to_datetime and 
             log.status == :success and not is_nil(log.execution_time_ms),
      select: avg(log.execution_time_ms)
    )
    |> Repo.one() || 0.0
  end

  defp notify_registry_reload do
    # Send message to registry to reload triggers
    case Process.whereis(ServiceManager.Triggers.TriggerRegistry) do
      nil -> :ok  # Registry not running
      pid -> GenServer.call(pid, :reload)
    end
  end
end