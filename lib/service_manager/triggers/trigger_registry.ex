defmodule ServiceManager.Triggers.TriggerRegistry do
  @moduledoc """
  GenServer that maintains an in-memory registry of active triggers and their mount points.
  Provides fast lookup capabilities for trigger execution and automatic reloading.
  """

  use GenServer
  alias ServiceManager.Triggers.TriggerManager
  require Logger

  @ets_table :trigger_registry
  @reload_interval 300_000  # 5 minutes

  ## Client API

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Gets trigger information by mount point.
  Returns {:ok, trigger_info} or {:error, :not_found}.
  """
  def get_trigger_info(mount_point) do
    case :ets.lookup(@ets_table, mount_point) do
      [{^mount_point, trigger_info}] -> {:ok, trigger_info}
      [] -> {:error, :not_found}
    end
  end

  @doc """
  Lists all registered mount points.
  """
  def list_mount_points do
    :ets.select(@ets_table, [{{:"$1", :_}, [], [:"$1"]}])
  end

  @doc """
  Gets the count of registered triggers.
  """
  def count_registered_triggers do
    :ets.info(@ets_table, :size)
  end

  @doc """
  Forces a reload of all triggers from the database.
  """
  def reload_triggers do
    GenServer.call(__MODULE__, :reload)
  end

  @doc """
  Gets registry statistics for monitoring.
  """
  def get_registry_stats do
    GenServer.call(__MODULE__, :get_stats)
  end

  ## Server Callbacks

  @impl true
  def init(_opts) do
    # Create ETS table for fast lookups
    :ets.new(@ets_table, [
      :set, 
      :public, 
      :named_table, 
      read_concurrency: true,
      write_concurrency: false
    ])

    # Schedule periodic reload
    schedule_reload()
    
    # Initial load
    case load_triggers() do
      :ok ->
        Logger.info("TriggerRegistry: Initialized with #{count_registered_triggers()} triggers")
        {:ok, %{last_reload: DateTime.utc_now(), reload_count: 1}}
      
      {:error, reason} ->
        Logger.error("TriggerRegistry: Failed to initialize - #{inspect(reason)}")
        {:ok, %{last_reload: nil, reload_count: 0}}
    end
  end

  @impl true
  def handle_call(:reload, _from, state) do
    case load_triggers() do
      :ok ->
        new_state = %{
          last_reload: DateTime.utc_now(),
          reload_count: state.reload_count + 1
        }
        Logger.info("TriggerRegistry: Reloaded #{count_registered_triggers()} triggers")
        {:reply, :ok, new_state}
      
      {:error, reason} ->
        Logger.error("TriggerRegistry: Failed to reload - #{inspect(reason)}")
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    stats = %{
      registered_triggers: count_registered_triggers(),
      last_reload: state.last_reload,
      reload_count: state.reload_count,
      ets_memory_usage: :ets.info(@ets_table, :memory) * :erlang.system_info(:wordsize)
    }
    {:reply, stats, state}
  end

  @impl true
  def handle_info(:reload_triggers, state) do
    case load_triggers() do
      :ok ->
        new_state = %{state | reload_count: state.reload_count + 1}
        schedule_reload()
        {:noreply, new_state}
      
      {:error, reason} ->
        Logger.error("TriggerRegistry: Periodic reload failed - #{inspect(reason)}")
        schedule_reload()
        {:noreply, state}
    end
  end

  @impl true
  def handle_info(_msg, state) do
    {:noreply, state}
  end

  ## Private Functions

  defp load_triggers do
    try do
      # Clear existing entries
      :ets.delete_all_objects(@ets_table)
      
      # Load enabled triggers from database
      triggers = TriggerManager.list_triggers(enabled: true)
      
      # Convert to registry format and insert
      Enum.each(triggers, &insert_trigger_into_registry/1)
      
      Logger.debug("TriggerRegistry: Loaded #{length(triggers)} triggers")
      :ok
    rescue
      error ->
        Logger.error("TriggerRegistry: Error loading triggers - #{inspect(error)}")
        {:error, error}
    end
  end

  defp insert_trigger_into_registry(trigger) do
    # Validate that the module and function exist
    case validate_trigger_implementation(trigger) do
      :ok ->
        trigger_info = %{
          id: trigger.id,
          name: trigger.name,
          module: safe_string_to_atom(trigger.module_name),
          function: safe_string_to_atom(trigger.function_name),
          execution_type: trigger.execution_type,
          return_data: trigger.return_data,
          logging_enabled: trigger.logging_enabled,
          rate_limiting_enabled: trigger.rate_limiting_enabled,
          audit_logging_enabled: trigger.audit_logging_enabled,
          sandbox_execution: trigger.sandbox_execution,
          input_schema: trigger.input_schema,
          inserted_at: trigger.inserted_at
        }
        
        :ets.insert(@ets_table, {trigger.mount_point, trigger_info})
        Logger.debug("TriggerRegistry: Registered #{trigger.mount_point}")
      
      {:error, reason} ->
        Logger.warn("TriggerRegistry: Skipping invalid trigger #{trigger.name} - #{reason}")
    end
  end

  defp validate_trigger_implementation(trigger) do
    try do
      module = String.to_existing_atom(trigger.module_name)
      function = String.to_existing_atom(trigger.function_name)
      
      if function_exported?(module, function, 2) do
        :ok
      else
        {:error, "Function #{trigger.function_name}/2 not exported by #{trigger.module_name}"}
      end
    rescue
      ArgumentError ->
        {:error, "Module #{trigger.module_name} does not exist"}
    end
  end

  defp safe_string_to_atom(string) when is_binary(string) do
    try do
      String.to_existing_atom(string)
    rescue
      ArgumentError ->
        Logger.warn("TriggerRegistry: Converting non-existing atom #{string}")
        String.to_atom(string)
    end
  end

  defp schedule_reload do
    Process.send_after(self(), :reload_triggers, @reload_interval)
  end

  ## Utility Functions for Monitoring

  @doc """
  Returns detailed information about all registered triggers for debugging.
  """
  def debug_registry_contents do
    :ets.tab2list(@ets_table)
    |> Enum.map(fn {mount_point, trigger_info} ->
      %{
        mount_point: mount_point,
        trigger_name: trigger_info.name,
        module: trigger_info.module,
        function: trigger_info.function,
        execution_type: trigger_info.execution_type,
        registered_at: trigger_info.inserted_at
      }
    end)
    |> Enum.sort_by(& &1.mount_point)
  end

  @doc """
  Checks if a specific mount point is registered.
  """
  def mount_point_registered?(mount_point) do
    case get_trigger_info(mount_point) do
      {:ok, _} -> true
      {:error, :not_found} -> false
    end
  end

  @doc """
  Gets triggers grouped by execution type for analytics.
  """
  def get_triggers_by_execution_type do
    :ets.tab2list(@ets_table)
    |> Enum.group_by(fn {_mount_point, trigger_info} -> trigger_info.execution_type end)
    |> Enum.map(fn {execution_type, triggers} ->
      {execution_type, length(triggers)}
    end)
    |> Enum.into(%{})
  end

  @doc """
  Gets triggers that have specific features enabled.
  """
  def get_triggers_with_features do
    :ets.tab2list(@ets_table)
    |> Enum.reduce(%{logging: 0, rate_limiting: 0, audit_logging: 0, return_data: 0}, fn 
      {_mount_point, trigger_info}, acc ->
        acc
        |> update_if_enabled(:logging, trigger_info.logging_enabled)
        |> update_if_enabled(:rate_limiting, trigger_info.rate_limiting_enabled)
        |> update_if_enabled(:audit_logging, trigger_info.audit_logging_enabled)
        |> update_if_enabled(:return_data, trigger_info.return_data)
    end)
  end

  defp update_if_enabled(acc, feature, true), do: Map.update!(acc, feature, &(&1 + 1))
  defp update_if_enabled(acc, _feature, false), do: acc
end