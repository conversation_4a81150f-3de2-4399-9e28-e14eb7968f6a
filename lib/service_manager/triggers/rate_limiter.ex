defmodule ServiceManager.Triggers.RateLimiter do
  @moduledoc """
  GenServer-based rate limiter for trigger executions.
  Implements sliding window rate limiting with configurable limits per trigger and user.
  """

  use GenServer
  require Logger

  # Default rate limits
  @default_rate_limit 100  # requests per minute
  @default_burst_limit 10  # burst requests
  @cleanup_interval 60_000  # 1 minute cleanup interval
  @window_size 60  # 60 seconds sliding window

  ## Client API

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Checks if a request is within rate limits.
  Returns :ok if allowed, {:error, :rate_limited} if denied.
  """
  def check_rate_limit(trigger_id, user_id, opts \\ []) do
    limit = opts[:limit] || @default_rate_limit
    window_size = opts[:window_size] || @window_size

    GenServer.call(__MODULE__, {:check_rate_limit, trigger_id, user_id, limit, window_size})
  end

  @doc """
  Gets current rate limit statistics for monitoring.
  """
  def get_stats do
    GenServer.call(__MODULE__, :get_stats)
  end

  @doc """
  Gets rate limit information for a specific trigger/user combination.
  """
  def get_rate_limit_info(trigger_id, user_id) do
    GenServer.call(__MODULE__, {:get_rate_limit_info, trigger_id, user_id})
  end

  @doc """
  Resets rate limits for a specific trigger/user combination.
  Useful for administrative actions.
  """
  def reset_rate_limit(trigger_id, user_id) do
    GenServer.call(__MODULE__, {:reset_rate_limit, trigger_id, user_id})
  end

  @doc """
  Configures custom rate limits for specific triggers.
  """
  def configure_trigger_limits(trigger_id, limits) do
    GenServer.call(__MODULE__, {:configure_limits, trigger_id, limits})
  end

  ## Server Callbacks

  @impl true
  def init(opts) do
    # Schedule periodic cleanup
    schedule_cleanup()
    
    initial_state = %{
      # Request tracking: {trigger_id, user_id} => %{requests: [timestamps], config: %{}}
      rate_limits: %{},
      # Custom trigger configurations
      trigger_configs: %{},
      # Global statistics
      stats: %{
        total_requests: 0,
        blocked_requests: 0,
        active_limits: 0,
        last_cleanup: DateTime.utc_now()
      }
    }

    Logger.info("RateLimiter: Started with cleanup interval #{@cleanup_interval}ms")
    {:ok, initial_state}
  end

  @impl true
  def handle_call({:check_rate_limit, trigger_id, user_id, limit, window_size}, _from, state) do
    key = {trigger_id, user_id}
    current_time = System.system_time(:second)
    
    # Get or create rate limit entry
    rate_limit_entry = get_or_create_rate_limit_entry(state.rate_limits, key, limit, window_size)
    
    # Clean old requests outside the window
    cleaned_requests = clean_old_requests(rate_limit_entry.requests, current_time, window_size)
    
    # Check if we're within limits
    case length(cleaned_requests) do
      count when count >= limit ->
        # Rate limited
        updated_state = %{
          state | 
          rate_limits: Map.put(state.rate_limits, key, %{rate_limit_entry | requests: cleaned_requests}),
          stats: update_stats(state.stats, :blocked)
        }
        
        {:reply, {:error, :rate_limited}, updated_state}
      
      count ->
        # Allow request and add timestamp
        new_requests = [current_time | cleaned_requests]
        updated_entry = %{rate_limit_entry | requests: new_requests, last_request: current_time}
        
        updated_state = %{
          state |
          rate_limits: Map.put(state.rate_limits, key, updated_entry),
          stats: update_stats(state.stats, :allowed)
        }
        
        {:reply, :ok, updated_state}
    end
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    enhanced_stats = Map.merge(state.stats, %{
      active_limits: map_size(state.rate_limits),
      configured_triggers: map_size(state.trigger_configs)
    })
    
    {:reply, enhanced_stats, state}
  end

  @impl true
  def handle_call({:get_rate_limit_info, trigger_id, user_id}, _from, state) do
    key = {trigger_id, user_id}
    current_time = System.system_time(:second)
    
    case Map.get(state.rate_limits, key) do
      nil ->
        {:reply, {:ok, %{requests_made: 0, limit: @default_rate_limit, window_size: @window_size}}, state}
      
      entry ->
        cleaned_requests = clean_old_requests(entry.requests, current_time, entry.window_size)
        info = %{
          requests_made: length(cleaned_requests),
          limit: entry.limit,
          window_size: entry.window_size,
          last_request: entry.last_request,
          time_until_reset: calculate_time_until_reset(cleaned_requests, entry.window_size)
        }
        {:reply, {:ok, info}, state}
    end
  end

  @impl true
  def handle_call({:reset_rate_limit, trigger_id, user_id}, _from, state) do
    key = {trigger_id, user_id}
    updated_rate_limits = Map.delete(state.rate_limits, key)
    updated_state = %{state | rate_limits: updated_rate_limits}
    
    Logger.info("RateLimiter: Reset rate limit for trigger #{trigger_id}, user #{user_id}")
    {:reply, :ok, updated_state}
  end

  @impl true
  def handle_call({:configure_limits, trigger_id, limits}, _from, state) do
    updated_configs = Map.put(state.trigger_configs, trigger_id, limits)
    updated_state = %{state | trigger_configs: updated_configs}
    
    Logger.info("RateLimiter: Configured custom limits for trigger #{trigger_id}: #{inspect(limits)}")
    {:reply, :ok, updated_state}
  end

  @impl true
  def handle_info(:cleanup, state) do
    current_time = System.system_time(:second)
    
    # Clean up old rate limit entries
    cleaned_rate_limits = 
      state.rate_limits
      |> Enum.filter(fn {_key, entry} ->
        # Keep entries that have had requests in the last 5 minutes
        entry.last_request > current_time - 300
      end)
      |> Enum.into(%{})
    
    # Update stats
    cleaned_count = map_size(state.rate_limits) - map_size(cleaned_rate_limits)
    updated_stats = Map.put(state.stats, :last_cleanup, DateTime.utc_now())
    
    if cleaned_count > 0 do
      Logger.debug("RateLimiter: Cleaned up #{cleaned_count} stale rate limit entries")
    end
    
    schedule_cleanup()
    {:noreply, %{state | rate_limits: cleaned_rate_limits, stats: updated_stats}}
  end

  @impl true
  def handle_info(msg, state) do
    Logger.warn("RateLimiter: Received unexpected message: #{inspect(msg)}")
    {:noreply, state}
  end

  ## Private Functions

  defp get_or_create_rate_limit_entry(rate_limits, key, limit, window_size) do
    case Map.get(rate_limits, key) do
      nil ->
        %{
          requests: [],
          limit: limit,
          window_size: window_size,
          created_at: System.system_time(:second),
          last_request: nil
        }
      
      existing ->
        # Update limit and window_size if they've changed
        %{existing | limit: limit, window_size: window_size}
    end
  end

  defp clean_old_requests(requests, current_time, window_size) do
    cutoff_time = current_time - window_size
    Enum.filter(requests, fn timestamp -> timestamp > cutoff_time end)
  end

  defp calculate_time_until_reset(requests, window_size) do
    case requests do
      [] -> 0
      [oldest | _] -> 
        current_time = System.system_time(:second)
        reset_time = oldest + window_size
        max(0, reset_time - current_time)
    end
  end

  defp update_stats(stats, :allowed) do
    %{stats | total_requests: stats.total_requests + 1}
  end

  defp update_stats(stats, :blocked) do
    %{
      stats | 
      total_requests: stats.total_requests + 1,
      blocked_requests: stats.blocked_requests + 1
    }
  end

  defp schedule_cleanup do
    Process.send_after(self(), :cleanup, @cleanup_interval)
  end

  ## Advanced Rate Limiting Strategies

  @doc """
  Implements token bucket rate limiting for burst allowances.
  """
  def check_token_bucket_limit(trigger_id, user_id, opts \\ []) do
    bucket_size = opts[:bucket_size] || @default_burst_limit
    refill_rate = opts[:refill_rate] || 1  # tokens per second
    
    GenServer.call(__MODULE__, {:check_token_bucket, trigger_id, user_id, bucket_size, refill_rate})
  end

  @impl true
  def handle_call({:check_token_bucket, trigger_id, user_id, bucket_size, refill_rate}, _from, state) do
    key = {:token_bucket, trigger_id, user_id}
    current_time = System.system_time(:second)
    
    bucket = get_or_create_token_bucket(state, key, bucket_size, refill_rate, current_time)
    updated_bucket = refill_tokens(bucket, current_time, refill_rate, bucket_size)
    
    case updated_bucket.tokens do
      tokens when tokens >= 1 ->
        # Allow request, consume token
        consumed_bucket = %{updated_bucket | tokens: tokens - 1, last_request: current_time}
        updated_state = put_in(state, [:token_buckets, key], consumed_bucket)
        {:reply, :ok, updated_state}
      
      _ ->
        # No tokens available
        updated_state = put_in(state, [:token_buckets, key], updated_bucket)
        {:reply, {:error, :rate_limited}, updated_state}
    end
  end

  defp get_or_create_token_bucket(state, key, bucket_size, refill_rate, current_time) do
    case get_in(state, [:token_buckets, key]) do
      nil ->
        %{
          tokens: bucket_size,
          bucket_size: bucket_size,
          refill_rate: refill_rate,
          last_refill: current_time,
          last_request: nil
        }
      
      existing -> existing
    end
  end

  defp refill_tokens(bucket, current_time, refill_rate, bucket_size) do
    time_passed = current_time - bucket.last_refill
    tokens_to_add = time_passed * refill_rate
    new_token_count = min(bucket.tokens + tokens_to_add, bucket_size)
    
    %{bucket | tokens: new_token_count, last_refill: current_time}
  end

  ## Monitoring and Analytics

  @doc """
  Gets detailed rate limiting analytics for monitoring dashboards.
  """
  def get_detailed_analytics do
    GenServer.call(__MODULE__, :get_detailed_analytics)
  end

  @impl true
  def handle_call(:get_detailed_analytics, _from, state) do
    current_time = System.system_time(:second)
    
    # Analyze rate limit patterns
    trigger_stats = 
      state.rate_limits
      |> Enum.group_by(fn {{trigger_id, _user_id}, _entry} -> trigger_id end)
      |> Enum.map(fn {trigger_id, entries} ->
        total_requests = 
          entries
          |> Enum.map(fn {_key, entry} -> length(entry.requests) end)
          |> Enum.sum()
        
        active_users = length(entries)
        
        {trigger_id, %{active_users: active_users, total_requests: total_requests}}
      end)
      |> Enum.into(%{})
    
    # Top rate-limited triggers
    top_limited_triggers = 
      trigger_stats
      |> Enum.sort_by(fn {_trigger_id, stats} -> stats.total_requests end, :desc)
      |> Enum.take(10)
    
    analytics = %{
      global_stats: state.stats,
      trigger_stats: trigger_stats,
      top_limited_triggers: top_limited_triggers,
      active_rate_limits: map_size(state.rate_limits),
      configured_triggers: map_size(state.trigger_configs),
      generated_at: DateTime.utc_now()
    }
    
    {:reply, analytics, state}
  end

  ## Administrative Functions

  @doc """
  Temporarily disables rate limiting for a specific trigger (emergency use).
  """
  def disable_rate_limiting(trigger_id, duration_seconds \\ 300) do
    GenServer.call(__MODULE__, {:disable_rate_limiting, trigger_id, duration_seconds})
  end

  @impl true
  def handle_call({:disable_rate_limiting, trigger_id, duration_seconds}, _from, state) do
    # Set a very high limit temporarily
    emergency_config = %{
      limit: 999_999,
      window_size: @window_size,
      expires_at: System.system_time(:second) + duration_seconds
    }
    
    updated_configs = Map.put(state.trigger_configs, trigger_id, emergency_config)
    updated_state = %{state | trigger_configs: updated_configs}
    
    # Schedule re-enable
    Process.send_after(self(), {:re_enable_rate_limiting, trigger_id}, duration_seconds * 1000)
    
    Logger.warn("RateLimiter: EMERGENCY - Rate limiting disabled for trigger #{trigger_id} for #{duration_seconds}s")
    {:reply, :ok, updated_state}
  end

  @impl true
  def handle_info({:re_enable_rate_limiting, trigger_id}, state) do
    updated_configs = Map.delete(state.trigger_configs, trigger_id)
    updated_state = %{state | trigger_configs: updated_configs}
    
    Logger.info("RateLimiter: Rate limiting re-enabled for trigger #{trigger_id}")
    {:noreply, updated_state}
  end
end