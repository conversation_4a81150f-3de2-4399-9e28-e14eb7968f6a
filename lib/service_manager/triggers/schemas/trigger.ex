defmodule ServiceManager.Triggers.Schemas.Trigger do
  @moduledoc """
  Schema for trigger definitions that define executable mount points 
  linking to module functions in the codebase.
  """
  
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "triggers" do
    field :name, :string
    field :module_name, :string
    field :function_name, :string
    field :mount_point, :string
    field :input_schema, :map
    field :execution_type, Ecto.Enum, values: [:sync, :async]
    field :return_data, :boolean, default: false
    field :logging_enabled, :boolean, default: false
    field :rate_limiting_enabled, :boolean, default: false
    field :audit_logging_enabled, :boolean, default: false
    field :sandbox_execution, :boolean, default: true
    field :description, :string
    field :enabled, :boolean, default: true

    has_many :wizard_step_triggers, ServiceManager.Triggers.Schemas.WizardStepTrigger
    has_many :execution_logs, ServiceManager.Triggers.Schemas.TriggerExecutionLog

    timestamps()
  end

  @doc """
  Changeset for creating and updating triggers.
  Automatically generates mount_point from module_name and function_name.
  """
  def changeset(trigger, attrs) do
    trigger
    |> cast(attrs, [
      :name, :module_name, :function_name, :input_schema, 
      :execution_type, :return_data, :logging_enabled, 
      :rate_limiting_enabled, :audit_logging_enabled, 
      :sandbox_execution, :description, :enabled
    ])
    |> validate_required([:name, :module_name, :function_name, :execution_type])
    |> validate_length(:name, max: 255)
    |> validate_length(:description, max: 1000)
    |> validate_format(:module_name, ~r/^[A-Z][a-zA-Z0-9.]*$/, 
                      message: "must be a valid Elixir module name")
    |> validate_format(:function_name, ~r/^[a-z_][a-zA-Z0-9_]*$/, 
                      message: "must be a valid Elixir function name")
    |> validate_input_schema()
    |> generate_mount_point()
    |> unique_constraint(:mount_point)
  end

  defp validate_input_schema(changeset) do
    case get_change(changeset, :input_schema) do
      nil -> changeset
      schema when is_map(schema) ->
        case validate_json_schema(schema) do
          :ok -> changeset
          {:error, reason} -> add_error(changeset, :input_schema, "invalid JSON schema: #{reason}")
        end
      _ -> add_error(changeset, :input_schema, "must be a valid JSON schema object")
    end
  end

  defp validate_json_schema(schema) when is_map(schema) do
    # Basic JSON schema validation - could be enhanced with ExJsonSchema
    required_props = ["type"]
    
    if Enum.any?(required_props, fn prop -> Map.has_key?(schema, prop) end) do
      :ok
    else
      {:error, "missing required properties"}
    end
  end

  defp generate_mount_point(changeset) do
    case {get_change(changeset, :module_name), get_change(changeset, :function_name)} do
      {module, function} when is_binary(module) and is_binary(function) ->
        mount_point = "/service/triggers/#{module_to_path(module)}/#{function}"
        put_change(changeset, :mount_point, mount_point)
      _ ->
        changeset
    end
  end

  defp module_to_path(module_name) do
    module_name
    |> String.replace("ServiceManager.Triggers.", "")
    |> String.replace(".", "_")
    |> String.downcase()
  end

  @doc """
  Returns a list of available execution types for forms and dropdowns.
  """
  def execution_types do
    [
      {"Synchronous", :sync},
      {"Asynchronous", :async}
    ]
  end

  @doc """
  Validates if a module and function exist and are callable.
  """
  def validate_module_function(module_name, function_name) do
    try do
      module = String.to_existing_atom(module_name)
      function = String.to_existing_atom(function_name)
      
      if function_exported?(module, function, 2) do
        :ok
      else
        {:error, "Function #{function_name}/2 not found in module #{module_name}"}
      end
    rescue
      ArgumentError ->
        {:error, "Module #{module_name} does not exist"}
    end
  end
end