defmodule ServiceManager.Triggers.Schemas.TriggerExecutionLog do
  @moduledoc """
  Schema for logging trigger executions for monitoring, debugging, and audit purposes.
  Stores execution details, performance metrics, and error information.
  """
  
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "trigger_execution_logs" do
    field :wizard_step_id, :binary_id
    field :user_id, :binary_id
    field :execution_type, Ecto.Enum, values: [:sync, :async]
    field :request_payload, :map
    field :response_payload, :map
    field :execution_time_ms, :integer
    field :status, Ecto.Enum, values: [:success, :error, :timeout]
    field :error_message, :string
    field :error_details, :map
    field :executed_at, :utc_datetime

    belongs_to :trigger, ServiceManager.Triggers.Schemas.Trigger

    timestamps()
  end

  @doc """
  Changeset for creating trigger execution logs.
  Most fields are optional to handle various execution scenarios.
  """
  def changeset(trigger_execution_log, attrs) do
    trigger_execution_log
    |> cast(attrs, [
      :trigger_id, :wizard_step_id, :user_id, :execution_type,
      :request_payload, :response_payload, :execution_time_ms,
      :status, :error_message, :error_details, :executed_at
    ])
    |> validate_required([:trigger_id, :execution_type, :status, :executed_at])
    |> validate_number(:execution_time_ms, greater_than_or_equal_to: 0)
    |> validate_length(:error_message, max: 5000)
    |> foreign_key_constraint(:trigger_id)
    |> put_executed_at_if_nil()
  end

  defp put_executed_at_if_nil(changeset) do
    case get_field(changeset, :executed_at) do
      nil -> put_change(changeset, :executed_at, DateTime.utc_now())
      _ -> changeset
    end
  end

  @doc """
  Creates a success log entry for a trigger execution.
  """
  def create_success_log(trigger_id, attrs \\ %{}) do
    base_attrs = %{
      trigger_id: trigger_id,
      status: :success,
      executed_at: DateTime.utc_now()
    }
    
    attrs = Map.merge(base_attrs, attrs)
    changeset(%__MODULE__{}, attrs)
  end

  @doc """
  Creates an error log entry for a trigger execution.
  """
  def create_error_log(trigger_id, error_message, attrs \\ %{}) do
    base_attrs = %{
      trigger_id: trigger_id,
      status: :error,
      error_message: error_message,
      executed_at: DateTime.utc_now()
    }
    
    attrs = Map.merge(base_attrs, attrs)
    changeset(%__MODULE__{}, attrs)
  end

  @doc """
  Creates a timeout log entry for a trigger execution.
  """
  def create_timeout_log(trigger_id, timeout_duration, attrs \\ %{}) do
    base_attrs = %{
      trigger_id: trigger_id,
      status: :timeout,
      execution_time_ms: timeout_duration,
      error_message: "Execution timed out after #{timeout_duration}ms",
      executed_at: DateTime.utc_now()
    }
    
    attrs = Map.merge(base_attrs, attrs)
    changeset(%__MODULE__{}, attrs)
  end

  @doc """
  Returns available status values for forms and filtering.
  """
  def status_values do
    [
      {"Success", :success},
      {"Error", :error},
      {"Timeout", :timeout}
    ]
  end

  @doc """
  Calculates execution statistics for a given time period.
  """
  def execution_stats_query(from_datetime, to_datetime \\ nil) do
    import Ecto.Query
    
    to_datetime = to_datetime || DateTime.utc_now()
    
    from(log in __MODULE__,
      where: log.executed_at >= ^from_datetime and log.executed_at <= ^to_datetime,
      group_by: log.status,
      select: %{
        status: log.status,
        count: count(log.id),
        avg_execution_time: avg(log.execution_time_ms)
      }
    )
  end

  @doc """
  Returns query for recent executions with trigger information.
  """
  def recent_executions_query(limit \\ 50) do
    import Ecto.Query
    
    from(log in __MODULE__,
      join: trigger in assoc(log, :trigger),
      order_by: [desc: log.executed_at],
      limit: ^limit,
      preload: [trigger: trigger]
    )
  end

  @doc """
  Returns query for error logs within a time period.
  """
  def error_logs_query(from_datetime, limit \\ 100) do
    import Ecto.Query
    
    from(log in __MODULE__,
      join: trigger in assoc(log, :trigger),
      where: log.status == :error and log.executed_at >= ^from_datetime,
      order_by: [desc: log.executed_at],
      limit: ^limit,
      preload: [trigger: trigger]
    )
  end

  @doc """
  Returns query for trigger performance analysis.
  """
  def trigger_performance_query(trigger_id, from_datetime) do
    import Ecto.Query
    
    from(log in __MODULE__,
      where: log.trigger_id == ^trigger_id and 
             log.executed_at >= ^from_datetime and
             not is_nil(log.execution_time_ms),
      select: %{
        executed_at: log.executed_at,
        execution_time_ms: log.execution_time_ms,
        status: log.status
      },
      order_by: [asc: log.executed_at]
    )
  end

  @doc """
  Formats error details for display in logs and monitoring dashboards.
  """
  def format_error_details(error_details) when is_map(error_details) do
    error_details
    |> Enum.map(fn {key, value} ->
      "#{key}: #{inspect(value)}"
    end)
    |> Enum.join(", ")
  end

  def format_error_details(error_details) when is_binary(error_details) do
    error_details
  end

  def format_error_details(_), do: "Unknown error format"

  @doc """
  Sanitizes sensitive data from request/response payloads before logging.
  Removes or masks fields that might contain sensitive information.
  """
  def sanitize_payload(payload) when is_map(payload) do
    sensitive_fields = ["password", "pin", "token", "secret", "api_key", "otp_code"]
    
    Enum.reduce(payload, %{}, fn {key, value}, acc ->
      sanitized_key = String.downcase(to_string(key))
      
      if Enum.any?(sensitive_fields, fn field -> String.contains?(sanitized_key, field) end) do
        Map.put(acc, key, "[REDACTED]")
      else
        Map.put(acc, key, sanitize_nested_value(value))
      end
    end)
  end

  def sanitize_payload(payload), do: payload

  defp sanitize_nested_value(value) when is_map(value), do: sanitize_payload(value)
  defp sanitize_nested_value(value) when is_list(value) do
    Enum.map(value, &sanitize_nested_value/1)
  end
  defp sanitize_nested_value(value), do: value
end