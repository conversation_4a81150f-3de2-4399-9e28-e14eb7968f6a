defmodule ServiceManager.Triggers.Schemas.WizardStepTrigger do
  @moduledoc """
  Schema for linking triggers to wizard steps with execution configuration.
  Defines when and how triggers should be executed during wizard navigation.
  """
  
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "wizard_step_triggers" do
    field :wizard_step_id, :binary_id
    field :execution_timing, Ecto.Enum, values: [:on_enter, :on_exit, :conditional]
    field :execution_conditions, :map
    field :input_mapping, :map
    field :execution_order, :integer, default: 0
    field :enabled, :boolean, default: true

    belongs_to :trigger, ServiceManager.Triggers.Schemas.Trigger

    timestamps()
  end

  @doc """
  Changeset for creating and updating wizard step triggers.
  Validates that input_mapping is a valid JSON object and execution_conditions
  follow the expected structure.
  """
  def changeset(wizard_step_trigger, attrs) do
    wizard_step_trigger
    |> cast(attrs, [
      :wizard_step_id, :trigger_id, :execution_timing, 
      :execution_conditions, :input_mapping, :execution_order, :enabled
    ])
    |> validate_required([:wizard_step_id, :trigger_id, :execution_timing, :input_mapping])
    |> validate_number(:execution_order, greater_than_or_equal_to: 0)
    |> validate_input_mapping()
    |> validate_execution_conditions()
    |> foreign_key_constraint(:trigger_id)
    |> unique_constraint([:wizard_step_id, :trigger_id], 
                        name: :wizard_step_triggers_unique_step_trigger)
  end

  defp validate_input_mapping(changeset) do
    case get_change(changeset, :input_mapping) do
      nil -> changeset
      mapping when is_map(mapping) ->
        if valid_input_mapping?(mapping) do
          changeset
        else
          add_error(changeset, :input_mapping, "invalid input mapping structure")
        end
      _ -> add_error(changeset, :input_mapping, "must be a valid JSON object")
    end
  end

  defp validate_execution_conditions(changeset) do
    case get_change(changeset, :execution_conditions) do
      nil -> changeset
      conditions when is_map(conditions) ->
        if valid_execution_conditions?(conditions) do
          changeset
        else
          add_error(changeset, :execution_conditions, "invalid execution conditions structure")
        end
      _ -> add_error(changeset, :execution_conditions, "must be a valid JSON object")
    end
  end

  defp valid_input_mapping?(mapping) when is_map(mapping) do
    # Validate that all values are strings (field paths)
    Enum.all?(mapping, fn {_key, value} -> is_binary(value) end)
  end

  defp valid_execution_conditions?(conditions) when is_map(conditions) do
    # Validate basic condition structure
    Enum.all?(conditions, fn {_field_path, condition} ->
      is_map(condition) and valid_condition_operators?(condition)
    end)
  end

  defp valid_condition_operators?(condition) when is_map(condition) do
    valid_operators = ["eq", "ne", "gt", "lt", "gte", "lte", "in", "not_in", "exists"]
    
    Enum.all?(Map.keys(condition), fn key ->
      key in valid_operators
    end)
  end

  @doc """
  Returns available execution timing options for forms and dropdowns.
  """
  def execution_timings do
    [
      {"On Step Enter", :on_enter},
      {"On Step Exit", :on_exit},
      {"Conditional", :conditional}
    ]
  end

  @doc """
  Evaluates if execution conditions are met based on wizard step data.
  Returns true if conditions are empty (no conditions to check).
  """
  def evaluate_conditions(conditions, wizard_step_data) when conditions == %{} or conditions == nil do
    true
  end

  def evaluate_conditions(conditions, wizard_step_data) when is_map(conditions) do
    Enum.all?(conditions, fn {field_path, condition} ->
      field_value = get_field_value(wizard_step_data, field_path)
      evaluate_single_condition(field_value, condition)
    end)
  end

  defp get_field_value(data, field_path) when is_binary(field_path) do
    field_path
    |> String.split(".")
    |> Enum.reduce(data, fn key, acc ->
      case acc do
        %{} -> Map.get(acc, key)
        _ -> nil
      end
    end)
  end

  defp evaluate_single_condition(value, %{"eq" => expected}), do: value == expected
  defp evaluate_single_condition(value, %{"ne" => expected}), do: value != expected
  defp evaluate_single_condition(value, %{"gt" => expected}), do: compare_values(value, expected, :gt)
  defp evaluate_single_condition(value, %{"lt" => expected}), do: compare_values(value, expected, :lt)
  defp evaluate_single_condition(value, %{"gte" => expected}), do: compare_values(value, expected, :gte)
  defp evaluate_single_condition(value, %{"lte" => expected}), do: compare_values(value, expected, :lte)
  defp evaluate_single_condition(value, %{"in" => expected}) when is_list(expected), do: value in expected
  defp evaluate_single_condition(value, %{"not_in" => expected}) when is_list(expected), do: value not in expected
  defp evaluate_single_condition(value, %{"exists" => true}), do: value != nil
  defp evaluate_single_condition(value, %{"exists" => false}), do: value == nil
  defp evaluate_single_condition(_, _), do: false

  defp compare_values(value, expected, operation) do
    with {:ok, val} <- parse_comparable_value(value),
         {:ok, exp} <- parse_comparable_value(expected) do
      case operation do
        :gt -> val > exp
        :lt -> val < exp
        :gte -> val >= exp
        :lte -> val <= exp
      end
    else
      _ -> false
    end
  end

  defp parse_comparable_value(value) when is_number(value), do: {:ok, value}
  defp parse_comparable_value(value) when is_binary(value) do
    case Float.parse(value) do
      {float_val, ""} -> {:ok, float_val}
      _ -> {:ok, value}  # Treat as string for lexicographic comparison
    end
  end
  defp parse_comparable_value(_), do: {:error, :not_comparable}

  @doc """
  Maps wizard step data to trigger input parameters based on input_mapping configuration.
  """
  def map_wizard_data_to_trigger_inputs(wizard_step_data, input_mapping) when is_map(input_mapping) do
    Enum.reduce(input_mapping, %{}, fn {trigger_param, wizard_field_path}, acc ->
      case get_field_value(wizard_step_data, wizard_field_path) do
        nil -> acc
        value -> Map.put(acc, trigger_param, value)
      end
    end)
  end
end