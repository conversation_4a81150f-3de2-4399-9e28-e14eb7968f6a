defmodule ServiceManager.Triggers.WizardTriggerExecutor do
  @moduledoc """
  Handles execution of triggers during wizard flow processing.

  This module integrates with the wizard form processing system to execute
  configured triggers at the appropriate times:

  - **on_enter**: Triggers execute when entering a wizard step
  - **on_exit**: Triggers execute when leaving a wizard step (after validation)
  - **conditional**: Triggers execute based on form data conditions

  ## Integration Points

  This module is called from:
  - `DynamicRouteController` during wizard step navigation
  - Wizard session management during step transitions
  - Form validation processes for conditional triggers

  ## Execution Context

  Each trigger execution includes context information:
  - `wizard_id`: ID of the wizard being processed
  - `step_id`: ID of the current wizard step
  - `session_token`: Unique session identifier
  - `step_data`: Form data from the current step
  - `all_wizard_data`: Combined data from all completed steps
  - `execution_timing`: When the trigger is being executed ("on_enter", "on_exit", "conditional")
  """

  use ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Triggers.TriggerManager
  alias ServiceManager.Triggers.TriggerExecutor
  alias ServiceManager.Triggers.Schemas.TriggerExecutionLog

  require Logger

  @doc """
  Executes triggers configured for a wizard step when entering the step.

  Called when a user navigates to a new wizard step, before the form is displayed.

  ## Parameters

  - `wizard_step_id` - ID of the wizard step being entered
  - `context` - Execution context map containing:
    - `:wizard_id` - ID of the wizard
    - `:session_token` - Session identifier
    - `:step_data` - Any existing step data
    - `:all_wizard_data` - Data from all completed steps
    - `:user_info` - User information if available

  ## Returns

  - `{:ok, results}` - All triggers executed successfully
  - `{:error, failed_triggers}` - Some triggers failed
  - `{:ok, []}` - No triggers configured for this step

  ## Example

      context = %{
        wizard_id: 123,
        session_token: "abc123",
        step_data: %{},
        all_wizard_data: %{"step_1" => %{"name" => "John"}},
        user_info: %{user_id: 42}
      }

      execute_on_enter_triggers(456, context)
      {:ok, [%{trigger_id: 789, result: %{success: true}}]}
  """
  @track log_level: :info, category: "wizard_trigger_on_enter"
  def execute_on_enter_triggers(wizard_step_id, context) do
    triggers = TriggerManager.list_wizard_step_triggers(wizard_step_id)
    on_enter_triggers = Enum.filter(triggers, & &1.execution_timing == "on_enter")

    execute_wizard_triggers(on_enter_triggers, context, "on_enter")
  end

  @doc """
  Executes triggers configured for a wizard step when exiting the step.

  Called after step validation is successful, before moving to the next step.

  ## Parameters

  - `wizard_step_id` - ID of the wizard step being exited
  - `context` - Execution context (same structure as `execute_on_enter_triggers/2`)

  ## Returns

  Same return format as `execute_on_enter_triggers/2`

  ## Example

      context = %{
        wizard_id: 123,
        session_token: "abc123",
        step_data: %{"email" => "<EMAIL>"},
        all_wizard_data: %{"step_1" => %{"name" => "John"}},
        user_info: %{user_id: 42}
      }

      execute_on_exit_triggers(456, context)
      {:ok, [%{trigger_id: 789, result: %{otp_sent: true}}]}
  """
  @track log_level: :info, category: "wizard_trigger_on_exit"
  def execute_on_exit_triggers(wizard_step_id, context) do
    triggers = TriggerManager.list_wizard_step_triggers(wizard_step_id)
    on_exit_triggers = Enum.filter(triggers, & &1.execution_timing == "on_exit")

    execute_wizard_triggers(on_exit_triggers, context, "on_exit")
  end

  @doc """
  Executes conditional triggers for a wizard step.

  Called after step validation to check if any conditional triggers should execute
  based on the form data and configured conditions.

  ## Parameters

  - `wizard_step_id` - ID of the wizard step
  - `context` - Execution context (same structure as other execute functions)

  ## Returns

  Same return format as other execute functions

  ## Example

      # Conditional trigger might execute if email domain is "company.com"
      context = %{
        step_data: %{"email" => "<EMAIL>"},
        # ... other context
      }

      execute_conditional_triggers(456, context)
      {:ok, [%{trigger_id: 790, result: %{special_processing: true}}]}
  """
  @track log_level: :info, category: "wizard_trigger_conditional"
  def execute_conditional_triggers(wizard_step_id, context) do
    triggers = TriggerManager.list_wizard_step_triggers(wizard_step_id)
    conditional_triggers = Enum.filter(triggers, & &1.execution_timing == "conditional")

    # Filter triggers based on their conditions
    matching_triggers = Enum.filter(conditional_triggers, fn trigger ->
      evaluate_trigger_condition(trigger, context)
    end)

    execute_wizard_triggers(matching_triggers, context, "conditional")
  end

  @doc """
  Executes all triggers for a wizard step completion.

  This is a convenience function that executes on_exit and conditional triggers
  when a step is being completed. Used during step transitions.

  ## Parameters

  - `wizard_step_id` - ID of the wizard step being completed
  - `context` - Execution context

  ## Returns

  - `{:ok, %{on_exit: results, conditional: results}}` - All triggers executed
  - `{:error, failed_results}` - Some triggers failed
  """
  @track log_level: :info, category: "wizard_step_completion"
  def execute_step_completion_triggers(wizard_step_id, context) do
    with {:ok, on_exit_results} <- execute_on_exit_triggers(wizard_step_id, context),
         {:ok, conditional_results} <- execute_conditional_triggers(wizard_step_id, context) do

      {:ok, %{
        on_exit: on_exit_results,
        conditional: conditional_results,
        total_executed: length(on_exit_results) + length(conditional_results)
      }}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # Private helper functions

  @doc false
  defp execute_wizard_triggers([], _context, _timing), do: {:ok, []}

  defp execute_wizard_triggers(triggers, context, timing) do
    Logger.info("Executing #{length(triggers)} #{timing} triggers for wizard step")

    results = Enum.map(triggers, fn trigger ->
      execute_single_wizard_trigger(trigger, context, timing)
    end)

    # Separate successful and failed executions
    {successful, failed} = Enum.split_with(results, fn
      {:ok, _} -> true
      {:error, _} -> false
    end)

    if Enum.empty?(failed) do
      success_results = Enum.map(successful, fn {:ok, result} -> result end)
      {:ok, success_results}
    else
      {:error, %{
        successful: length(successful),
        failed: length(failed),
        failed_triggers: failed,
        timing: timing
      }}
    end
  end

  defp execute_single_wizard_trigger(wizard_step_trigger, context, timing) do
    trigger = wizard_step_trigger.trigger

    # Prepare trigger parameters by mapping wizard context to trigger inputs
    trigger_params = prepare_trigger_parameters(wizard_step_trigger, context)

    # Build execution context for the trigger
    trigger_context = %{
      triggered_by: "wizard",
      wizard_id: context[:wizard_id],
      wizard_step_id: wizard_step_trigger.wizard_step_id,
      session_token: context[:session_token],
      execution_timing: timing,
      user_info: context[:user_info] || %{}
    }

    # Execute the trigger
    case TriggerExecutor.execute_trigger(trigger, trigger_params, trigger_context) do
      {:ok, result} ->
        {:ok, %{
          trigger_id: trigger.id,
          trigger_name: trigger.name,
          wizard_step_trigger_id: wizard_step_trigger.id,
          execution_timing: timing,
          result: result,
          executed_at: DateTime.utc_now()
        }}

      {:error, reason} ->
        Logger.warning("Wizard trigger execution failed: #{inspect(reason)}",
          trigger_id: trigger.id,
          wizard_step_id: wizard_step_trigger.wizard_step_id,
          timing: timing
        )

        {:error, %{
          trigger_id: trigger.id,
          trigger_name: trigger.name,
          wizard_step_trigger_id: wizard_step_trigger.id,
          execution_timing: timing,
          error: reason,
          failed_at: DateTime.utc_now()
        }}
    end
  end

  defp prepare_trigger_parameters(wizard_step_trigger, context) do
    step_data = context[:step_data] || %{}
    all_wizard_data = context[:all_wizard_data] || %{}

    # Parse the input parameter mapping from the wizard step trigger configuration
    input_mapping = parse_input_mapping(wizard_step_trigger.input_parameters)

    # Map wizard data to trigger parameters based on the configuration
    Enum.reduce(input_mapping, %{}, fn {trigger_param, wizard_source}, acc ->
      value = extract_value_from_wizard_data(wizard_source, step_data, all_wizard_data, context)
      Map.put(acc, trigger_param, value)
    end)
  end

  defp parse_input_mapping(nil), do: %{}
  defp parse_input_mapping(""), do: %{}

  defp parse_input_mapping(input_parameters) when is_binary(input_parameters) do
    try do
      Jason.decode!(input_parameters)
    rescue
      _ -> %{}
    end
  end

  defp parse_input_mapping(input_parameters) when is_map(input_parameters) do
    input_parameters
  end

  defp extract_value_from_wizard_data(source, step_data, all_wizard_data, context) do
    case source do
      # Direct step field reference
      %{"type" => "step_field", "field" => field_name} ->
        Map.get(step_data, field_name)

      # Previous step field reference
      %{"type" => "previous_step", "step" => step_name, "field" => field_name} ->
        get_in(all_wizard_data, [step_name, field_name])

      # Context value reference
      %{"type" => "context", "key" => context_key} ->
        Map.get(context, String.to_atom(context_key))

      # Static value
      %{"type" => "static", "value" => value} ->
        value

      # Session token
      %{"type" => "session_token"} ->
        context[:session_token]

      # Wizard ID
      %{"type" => "wizard_id"} ->
        context[:wizard_id]

      # Unknown source type - return nil
      _ ->
        nil
    end
  end

  defp evaluate_trigger_condition(wizard_step_trigger, context) do
    condition = wizard_step_trigger.execution_conditions

    case condition do
      nil -> true  # No condition means always execute
      "" -> true   # Empty condition means always execute
      condition_json when is_binary(condition_json) ->
        try do
          condition_map = Jason.decode!(condition_json)
          evaluate_condition_map(condition_map, context)
        rescue
          _ -> false  # Invalid JSON condition means don't execute
        end
      condition_map when is_map(condition_map) ->
        evaluate_condition_map(condition_map, context)
      _ -> false
    end
  end

  defp evaluate_condition_map(condition, context) do
    step_data = context[:step_data] || %{}
    all_wizard_data = context[:all_wizard_data] || %{}

    case condition do
      # Field equals value
      %{"type" => "field_equals", "field" => field, "value" => expected_value} ->
        Map.get(step_data, field) == expected_value

      # Field contains value
      %{"type" => "field_contains", "field" => field, "value" => substring} ->
        field_value = Map.get(step_data, field, "")
        String.contains?(to_string(field_value), to_string(substring))

      # Previous step field equals value
      %{"type" => "previous_step_equals", "step" => step_name, "field" => field, "value" => expected_value} ->
        get_in(all_wizard_data, [step_name, field]) == expected_value

      # Field is present (not nil and not empty)
      %{"type" => "field_present", "field" => field} ->
        value = Map.get(step_data, field)
        value != nil && value != ""

      # AND condition
      %{"type" => "and", "conditions" => conditions} when is_list(conditions) ->
        Enum.all?(conditions, fn cond -> evaluate_condition_map(cond, context) end)

      # OR condition
      %{"type" => "or", "conditions" => conditions} when is_list(conditions) ->
        Enum.any?(conditions, fn cond -> evaluate_condition_map(cond, context) end)

      # NOT condition
      %{"type" => "not", "condition" => inner_condition} ->
        not evaluate_condition_map(inner_condition, context)

      # Unknown condition type
      _ ->
        false
    end
  end

  @doc """
  Helper function to build wizard execution context from session details.

  This is used by the DynamicRouteController to create the context map
  needed for trigger execution.
  """
  def build_wizard_context(session_details, step_data \\ %{}, user_info \\ %{}) do
    %{
      wizard_id: session_details.session.id,
      session_token: session_details.session.session_token,
      step_data: step_data,
      all_wizard_data: session_details.session.form_data || %{},
      user_info: user_info,
      current_step_number: session_details.session.current_step_number,
      is_final_step: session_details.is_final_step
    }
  end
end
