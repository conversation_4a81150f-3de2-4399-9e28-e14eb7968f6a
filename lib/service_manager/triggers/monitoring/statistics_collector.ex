defmodule ServiceManager.Triggers.Monitoring.StatisticsCollector do
  @moduledoc """
  Collects and maintains real-time statistics for the trigger monitoring dashboard.
  
  This module provides functionality to gather comprehensive metrics about trigger
  system performance and health, including:
  
  - Execution statistics and trends
  - Performance metrics (response times, throughput)
  - System health indicators
  - Error rates and failure analysis
  - Resource utilization metrics
  
  ## Usage
  
  Statistics are collected both on-demand and through periodic background collection.
  The module integrates with the monitoring dashboard to provide real-time insights.
  """

  use GenServer
  
  alias ServiceManager.Triggers.Schemas.TriggerExecutionLog
  alias ServiceManager.Triggers.TriggerManager
  alias ServiceManager.Triggers.TriggerRegistry
  alias ServiceManager.Triggers.RateLimiter
  
  import Ecto.Query
  
  @collection_interval 30_000  # 30 seconds
  @stats_retention_hours 24    # Keep 24 hours of statistics
  
  defstruct [
    :execution_stats,
    :performance_stats, 
    :system_health,
    :error_stats,
    :trend_data,
    :last_updated
  ]
  
  # Client API
  
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end
  
  @doc """
  Gets the current comprehensive statistics.
  
  ## Returns
  
  - `{:ok, stats}` - Current statistics map
  - `{:error, reason}` - Error retrieving statistics
  
  ## Example
  
      {:ok, stats} = StatisticsCollector.get_current_stats()
      %{
        execution_stats: %{total: 1500, success_rate: 98.5, last_hour: 45},
        performance_stats: %{avg_response_time: 120, p95_response_time: 350},
        system_health: %{status: "healthy", components: %{}},
        error_stats: %{total_errors: 23, error_rate: 1.5},
        trend_data: [...]
      }
  """
  def get_current_stats do
    GenServer.call(__MODULE__, :get_current_stats)
  end
  
  @doc """
  Gets execution statistics for a specific time range.
  
  ## Parameters
  
  - `time_range` - Time range specification ("1h", "6h", "24h", "7d", "30d")
  
  ## Returns
  
  Statistics filtered to the specified time range.
  """
  def get_stats_for_range(time_range) do
    GenServer.call(__MODULE__, {:get_stats_for_range, time_range})
  end
  
  @doc """
  Records a trigger execution for statistics tracking.
  
  This is typically called automatically by the trigger execution system.
  """
  def record_execution(execution_data) do
    GenServer.cast(__MODULE__, {:record_execution, execution_data})
  end
  
  @doc """
  Forces an immediate statistics collection and update.
  """
  def refresh_stats do
    GenServer.cast(__MODULE__, :refresh_stats)
  end

  # Server Callbacks
  
  @impl true
  def init(_opts) do
    # Schedule periodic statistics collection
    schedule_collection()
    
    # Initialize with empty stats
    state = %__MODULE__{
      execution_stats: %{},
      performance_stats: %{},
      system_health: %{},
      error_stats: %{},
      trend_data: [],
      last_updated: DateTime.utc_now()
    }
    
    # Collect initial statistics
    {:ok, collect_all_statistics(state)}
  end

  @impl true
  def handle_call(:get_current_stats, _from, state) do
    stats = %{
      execution_stats: state.execution_stats,
      performance_stats: state.performance_stats,
      system_health: state.system_health,
      error_stats: state.error_stats,
      trend_data: state.trend_data,
      last_updated: state.last_updated
    }
    
    {:reply, {:ok, stats}, state}
  end

  def handle_call({:get_stats_for_range, time_range}, _from, state) do
    stats = get_range_statistics(time_range)
    {:reply, {:ok, stats}, state}
  end

  @impl true
  def handle_cast({:record_execution, execution_data}, state) do
    # Update real-time execution counters
    updated_state = update_execution_counters(state, execution_data)
    
    # Broadcast to monitoring dashboard subscribers
    Phoenix.PubSub.broadcast(
      ServiceManager.PubSub,
      "trigger_executions",
      {:trigger_executed, execution_data}
    )
    
    {:noreply, updated_state}
  end

  def handle_cast(:refresh_stats, state) do
    {:noreply, collect_all_statistics(state)}
  end

  @impl true
  def handle_info(:collect_stats, state) do
    schedule_collection()
    {:noreply, collect_all_statistics(state)}
  end

  # Private Functions
  
  defp schedule_collection do
    Process.send_after(self(), :collect_stats, @collection_interval)
  end
  
  defp collect_all_statistics(state) do
    %{state |
      execution_stats: collect_execution_statistics(),
      performance_stats: collect_performance_statistics(),
      system_health: collect_system_health(),
      error_stats: collect_error_statistics(),
      trend_data: collect_trend_data(),
      last_updated: DateTime.utc_now()
    }
  end
  
  defp collect_execution_statistics do
    now = DateTime.utc_now()
    one_hour_ago = DateTime.add(now, -3600, :second)
    twenty_four_hours_ago = DateTime.add(now, -86400, :second)
    
    total_executions = ServiceManager.Repo.aggregate(TriggerExecutionLog, :count, :id)
    
    executions_last_hour = from(log in TriggerExecutionLog,
      where: log.executed_at >= ^one_hour_ago,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    executions_last_24h = from(log in TriggerExecutionLog,
      where: log.executed_at >= ^twenty_four_hours_ago,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    successful_executions = from(log in TriggerExecutionLog,
      where: log.success == true,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    success_rate = if total_executions > 0 do
      Float.round(successful_executions / total_executions * 100, 1)
    else
      0.0
    end
    
    active_triggers = TriggerManager.list_triggers()
    |> Enum.count(& &1.enabled)
    
    %{
      total_executions: total_executions,
      executions_last_hour: executions_last_hour,
      executions_last_24h: executions_last_24h,
      successful_executions: successful_executions,
      success_rate: success_rate,
      active_triggers: active_triggers
    }
  end
  
  defp collect_performance_statistics do
    # Calculate response time statistics
    response_times = from(log in TriggerExecutionLog,
      where: not is_nil(log.execution_time_ms),
      select: log.execution_time_ms
    ) |> ServiceManager.Repo.all()
    
    if Enum.empty?(response_times) do
      %{
        avg_response_time: 0,
        min_response_time: 0,
        max_response_time: 0,
        p50_response_time: 0,
        p95_response_time: 0,
        p99_response_time: 0
      }
    else
      sorted_times = Enum.sort(response_times)
      count = length(sorted_times)
      
      %{
        avg_response_time: Float.round(Enum.sum(response_times) / count, 0),
        min_response_time: Enum.min(response_times),
        max_response_time: Enum.max(response_times),
        p50_response_time: percentile(sorted_times, 50),
        p95_response_time: percentile(sorted_times, 95),
        p99_response_time: percentile(sorted_times, 99)
      }
    end
  end
  
  defp collect_system_health do
    components = %{
      registry: check_component_health(:registry),
      rate_limiter: check_component_health(:rate_limiter),
      database: check_component_health(:database)
    }
    
    overall_healthy = Enum.all?(Map.values(components), & &1.healthy)
    
    %{
      status: if(overall_healthy, do: "healthy", else: "unhealthy"),
      components: components,
      last_checked: DateTime.utc_now()
    }
  end
  
  defp collect_error_statistics do
    now = DateTime.utc_now()
    one_hour_ago = DateTime.add(now, -3600, :second)
    twenty_four_hours_ago = DateTime.add(now, -86400, :second)
    
    total_errors = from(log in TriggerExecutionLog,
      where: log.success == false,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    errors_last_hour = from(log in TriggerExecutionLog,
      where: log.success == false and log.executed_at >= ^one_hour_ago,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    errors_last_24h = from(log in TriggerExecutionLog,
      where: log.success == false and log.executed_at >= ^twenty_four_hours_ago,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    # Get error distribution by trigger
    error_distribution = from(log in TriggerExecutionLog,
      where: log.success == false and log.executed_at >= ^twenty_four_hours_ago,
      group_by: log.trigger_name,
      select: {log.trigger_name, count(log.id)},
      order_by: [desc: count(log.id)]
    ) |> ServiceManager.Repo.all()
    |> Enum.map(fn {trigger_name, count} -> %{trigger_name: trigger_name, error_count: count} end)
    
    total_executions_24h = from(log in TriggerExecutionLog,
      where: log.executed_at >= ^twenty_four_hours_ago,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    error_rate_24h = if total_executions_24h > 0 do
      Float.round(errors_last_24h / total_executions_24h * 100, 2)
    else
      0.0
    end
    
    %{
      total_errors: total_errors,
      errors_last_hour: errors_last_hour,
      errors_last_24h: errors_last_24h,
      error_rate_24h: error_rate_24h,
      error_distribution: error_distribution
    }
  end
  
  defp collect_trend_data do
    # Collect hourly execution counts for the last 24 hours
    twenty_four_hours_ago = DateTime.utc_now() |> DateTime.add(-86400, :second)
    
    from(log in TriggerExecutionLog,
      where: log.executed_at >= ^twenty_four_hours_ago,
      group_by: fragment("date_trunc('hour', ?)", log.executed_at),
      select: %{
        hour: fragment("date_trunc('hour', ?)", log.executed_at),
        total_count: count(log.id),
        success_count: sum(fragment("case when ? then 1 else 0 end", log.success)),
        error_count: sum(fragment("case when ? then 0 else 1 end", log.success))
      },
      order_by: [asc: fragment("date_trunc('hour', ?)", log.executed_at)]
    )
    |> ServiceManager.Repo.all()
  end
  
  defp get_range_statistics(time_range) do
    {hours_back, _} = case time_range do
      "1h" -> {1, "hour"}
      "6h" -> {6, "6 hours"}
      "24h" -> {24, "day"}
      "7d" -> {24 * 7, "week"}
      "30d" -> {24 * 30, "month"}
      _ -> {1, "hour"}
    end
    
    start_time = DateTime.utc_now() |> DateTime.add(-hours_back * 3600, :second)
    
    # Get statistics for the specified range
    total_executions = from(log in TriggerExecutionLog,
      where: log.executed_at >= ^start_time,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    successful_executions = from(log in TriggerExecutionLog,
      where: log.executed_at >= ^start_time and log.success == true,
      select: count(log.id)
    ) |> ServiceManager.Repo.one() || 0
    
    success_rate = if total_executions > 0 do
      Float.round(successful_executions / total_executions * 100, 1)
    else
      0.0
    end
    
    avg_response_time = from(log in TriggerExecutionLog,
      where: log.executed_at >= ^start_time and not is_nil(log.execution_time_ms),
      select: avg(log.execution_time_ms)
    ) |> ServiceManager.Repo.one()
    |> case do
      nil -> 0
      time -> Float.round(time, 0)
    end
    
    %{
      time_range: time_range,
      total_executions: total_executions,
      successful_executions: successful_executions,
      success_rate: success_rate,
      avg_response_time: avg_response_time,
      start_time: start_time
    }
  end
  
  defp update_execution_counters(state, execution_data) do
    # Update real-time counters based on new execution
    current_stats = state.execution_stats || %{}
    
    updated_stats = %{current_stats |
      total_executions: (current_stats[:total_executions] || 0) + 1
    }
    
    %{state | execution_stats: updated_stats}
  end
  
  defp check_component_health(:registry) do
    try do
      case TriggerRegistry.get_stats() do
        {:ok, stats} -> 
          %{healthy: true, status: "operational", stats: stats}
        {:error, reason} -> 
          %{healthy: false, status: "error", error: reason}
      end
    rescue
      error -> %{healthy: false, status: "error", error: inspect(error)}
    end
  end
  
  defp check_component_health(:rate_limiter) do
    try do
      case RateLimiter.get_stats() do
        {:ok, stats} -> 
          %{healthy: true, status: "operational", stats: stats}
        {:error, reason} -> 
          %{healthy: false, status: "error", error: reason}
      end
    rescue
      error -> %{healthy: false, status: "error", error: inspect(error)}
    end
  end
  
  defp check_component_health(:database) do
    try do
      ServiceManager.Repo.query!("SELECT 1")
      %{healthy: true, status: "operational"}
    rescue
      error -> %{healthy: false, status: "error", error: inspect(error)}
    end
  end
  
  defp percentile(sorted_list, percentile) do
    count = length(sorted_list)
    index = trunc(count * percentile / 100)
    
    # Ensure index is within bounds
    safe_index = max(0, min(index, count - 1))
    Enum.at(sorted_list, safe_index, 0)
  end
end