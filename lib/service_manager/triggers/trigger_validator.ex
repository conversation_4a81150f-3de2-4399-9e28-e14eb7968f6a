defmodule ServiceManager.Triggers.TriggerValidator do
  @moduledoc """
  Validates trigger inputs using JSON schemas and sanitizes data for secure execution.
  Integrates with the existing dynamic form validation system.
  """

  require Logger

  @doc """
  Validates trigger input parameters against the configured JSON schema.
  Returns {:ok, validated_params} or {:error, validation_errors}.
  """
  def validate_input(trigger_info, params) when is_map(params) do
    case trigger_info.input_schema do
      nil -> 
        # No schema defined, perform basic validation
        basic_validation(params)
      
      schema when is_map(schema) ->
        # Validate against JSON schema
        validate_against_schema(params, schema)
    end
  end

  def validate_input(_trigger_info, params) do
    {:error, "Invalid input format: expected map, got #{inspect(params)}"}
  end

  @doc """
  Sanitizes input parameters by removing potentially dangerous values
  and ensuring data types match expectations.
  """
  def sanitize_input(params) when is_map(params) do
    params
    |> remove_system_fields()
    |> sanitize_string_values()
    |> limit_collection_sizes()
    |> remove_nil_values()
  end

  def sanitize_input(params), do: params

  @doc """
  Validates that required trigger context fields are present and valid.
  """
  def validate_context(context) when is_map(context) do
    required_fields = [:user_id, :wizard_step_id]
    optional_fields = [:session_id, :request_id, :trace_id]
    
    errors = 
      required_fields
      |> Enum.reduce([], fn field, acc ->
        case Map.get(context, field) do
          nil -> ["#{field} is required" | acc]
          value when is_binary(value) and byte_size(value) > 0 -> acc
          _ -> ["#{field} must be a non-empty string" | acc]
        end
      end)
    
    case errors do
      [] -> {:ok, context}
      errors -> {:error, Enum.reverse(errors)}
    end
  end

  def validate_context(_context) do
    {:error, "Context must be a map"}
  end

  ## Private Functions

  defp basic_validation(params) do
    cond do
      map_size(params) > 50 ->
        {:error, "Too many parameters (maximum 50)"}
      
      has_deeply_nested_structures?(params, 5) ->
        {:error, "Parameters are too deeply nested (maximum depth 5)"}
      
      has_oversized_values?(params) ->
        {:error, "Parameter values are too large"}
      
      true ->
        {:ok, sanitize_input(params)}
    end
  end

  defp validate_against_schema(params, schema) do
    case get_json_schema_validator() do
      {:ok, validator_module} ->
        case validator_module.validate(schema, params) do
          :ok -> 
            {:ok, sanitize_input(params)}
          
          {:error, errors} -> 
            {:error, format_validation_errors(errors)}
        end
      
      {:error, :no_validator} ->
        Logger.warn("TriggerValidator: No JSON schema validator available, falling back to basic validation")
        basic_validation(params)
    end
  end

  defp get_json_schema_validator do
    # Try to use ExJsonSchema if available
    if Code.ensure_loaded?(ExJsonSchema.Validator) do
      {:ok, ExJsonSchema.Validator}
    else
      {:error, :no_validator}
    end
  end

  defp format_validation_errors(errors) when is_list(errors) do
    errors
    |> Enum.map(&format_single_error/1)
    |> Enum.join(", ")
  end

  defp format_validation_errors(error), do: inspect(error)

  defp format_single_error({path, message}) when is_list(path) do
    path_string = Enum.join(path, ".")
    "#{path_string}: #{message}"
  end

  defp format_single_error({path, message}) do
    "#{path}: #{message}"
  end

  defp format_single_error(error), do: inspect(error)

  defp has_deeply_nested_structures?(value, max_depth, current_depth \\ 0)
  defp has_deeply_nested_structures?(_value, max_depth, current_depth) when current_depth >= max_depth, do: true
  defp has_deeply_nested_structures?(value, max_depth, current_depth) when is_map(value) do
    Enum.any?(value, fn {_key, val} -> 
      has_deeply_nested_structures?(val, max_depth, current_depth + 1)
    end)
  end
  defp has_deeply_nested_structures?(value, max_depth, current_depth) when is_list(value) do
    Enum.any?(value, fn val -> 
      has_deeply_nested_structures?(val, max_depth, current_depth + 1)
    end)
  end
  defp has_deeply_nested_structures?(_value, _max_depth, _current_depth), do: false

  defp has_oversized_values?(params) when is_map(params) do
    Enum.any?(params, fn {_key, value} -> value_too_large?(value) end)
  end

  defp value_too_large?(value) when is_binary(value), do: byte_size(value) > 10_000
  defp value_too_large?(value) when is_list(value), do: length(value) > 1000 or Enum.any?(value, &value_too_large?/1)
  defp value_too_large?(value) when is_map(value), do: map_size(value) > 100 or Enum.any?(value, fn {_k, v} -> value_too_large?(v) end)
  defp value_too_large?(_value), do: false

  defp remove_system_fields(params) do
    system_fields = [
      "__struct__", "__module__", "__meta__", 
      "_csrf_token", "_method", "_action"
    ]
    
    Map.drop(params, system_fields)
  end

  defp sanitize_string_values(params) when is_map(params) do
    Enum.reduce(params, %{}, fn {key, value}, acc ->
      sanitized_value = sanitize_value(value)
      Map.put(acc, key, sanitized_value)
    end)
  end

  defp sanitize_value(value) when is_binary(value) do
    value
    |> String.trim()
    |> remove_control_characters()
    |> truncate_if_too_long(5000)
  end
  
  defp sanitize_value(value) when is_list(value) do
    value
    |> Enum.take(100)  # Limit list size
    |> Enum.map(&sanitize_value/1)
  end
  
  defp sanitize_value(value) when is_map(value) do
    if map_size(value) <= 50 do
      Enum.reduce(value, %{}, fn {k, v}, acc ->
        Map.put(acc, k, sanitize_value(v))
      end)
    else
      value
      |> Enum.take(50)
      |> Enum.reduce(%{}, fn {k, v}, acc ->
        Map.put(acc, k, sanitize_value(v))
      end)
    end
  end
  
  defp sanitize_value(value), do: value

  defp remove_control_characters(string) do
    String.replace(string, ~r/[\x00-\x08\x0B-\x0C\x0E-\x1F\x7F]/, "")
  end

  defp truncate_if_too_long(string, max_length) do
    if String.length(string) > max_length do
      String.slice(string, 0, max_length)
    else
      string
    end
  end

  defp limit_collection_sizes(params) when is_map(params) do
    if map_size(params) <= 100 do
      params
    else
      params
      |> Enum.take(100)
      |> Enum.into(%{})
    end
  end

  defp remove_nil_values(params) when is_map(params) do
    Enum.reduce(params, %{}, fn {key, value}, acc ->
      case value do
        nil -> acc
        [] -> acc
        "" -> acc
        _ -> Map.put(acc, key, value)
      end
    end)
  end

  ## Specialized Validation Functions

  @doc """
  Validates wizard step data mapping configuration.
  Ensures that field paths are valid and data types are compatible.
  """
  def validate_wizard_data_mapping(wizard_data, input_mapping) when is_map(wizard_data) and is_map(input_mapping) do
    errors = 
      input_mapping
      |> Enum.reduce([], fn {trigger_param, wizard_field_path}, acc ->
        case get_nested_value(wizard_data, wizard_field_path) do
          nil -> ["Missing field: #{wizard_field_path}" | acc]
          value when is_function(value) -> ["Invalid field type (function): #{wizard_field_path}" | acc]
          value when is_pid(value) -> ["Invalid field type (process): #{wizard_field_path}" | acc]
          value when is_port(value) -> ["Invalid field type (port): #{wizard_field_path}" | acc]
          value when is_reference(value) -> ["Invalid field type (reference): #{wizard_field_path}" | acc]
          _valid_value -> acc
        end
      end)
    
    case errors do
      [] -> :ok
      errors -> {:error, Enum.reverse(errors)}
    end
  end

  def validate_wizard_data_mapping(_wizard_data, _input_mapping) do
    {:error, "Wizard data and input mapping must both be maps"}
  end

  defp get_nested_value(data, field_path) when is_binary(field_path) do
    field_path
    |> String.split(".")
    |> Enum.reduce(data, fn key, acc ->
      case acc do
        %{} -> Map.get(acc, key) || Map.get(acc, String.to_atom(key))
        _ -> nil
      end
    end)
  end

  @doc """
  Creates a default JSON schema for basic parameter validation.
  """
  def create_default_schema(field_specs \\ []) do
    properties = 
      field_specs
      |> Enum.reduce(%{}, fn {field_name, field_type}, acc ->
        schema = create_field_schema(field_type)
        Map.put(acc, field_name, schema)
      end)
    
    %{
      "type" => "object",
      "properties" => properties,
      "additionalProperties" => false
    }
  end

  defp create_field_schema(:string), do: %{"type" => "string", "maxLength" => 1000}
  defp create_field_schema(:integer), do: %{"type" => "integer"}
  defp create_field_schema(:number), do: %{"type" => "number"}
  defp create_field_schema(:boolean), do: %{"type" => "boolean"}
  defp create_field_schema(:array), do: %{"type" => "array", "maxItems" => 100}
  defp create_field_schema(:object), do: %{"type" => "object"}
  defp create_field_schema(_), do: %{"type" => "string"}

  @doc """
  Validates that a trigger execution context contains all required security information.
  """
  def validate_security_context(context, trigger_info) do
    errors = []
    
    # Check if rate limiting is enabled and user ID is present
    errors = if trigger_info.rate_limiting_enabled and not Map.has_key?(context, :user_id) do
      ["user_id required for rate-limited triggers" | errors]
    else
      errors
    end
    
    # Check if audit logging is enabled and required fields are present
    errors = if trigger_info.audit_logging_enabled do
      required_audit_fields = [:user_id, :wizard_step_id]
      missing_fields = 
        required_audit_fields
        |> Enum.filter(fn field -> not Map.has_key?(context, field) end)
      
      case missing_fields do
        [] -> errors
        fields -> ["Audit logging requires: #{Enum.join(fields, ", ")}" | errors]
      end
    else
      errors
    end
    
    case errors do
      [] -> :ok
      errors -> {:error, Enum.reverse(errors)}
    end
  end
end