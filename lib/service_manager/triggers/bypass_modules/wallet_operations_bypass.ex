defmodule ServiceManager.Triggers.BypassModules.WalletOperationsBypass do
  @moduledoc """
  Bypass module for wallet operations.
  
  This module wraps wallet-related functions for trigger execution,
  demonstrating how to handle financial operations safely through the trigger system.
  It includes proper transaction handling, balance validation, and audit logging.
  
  ## Available Functions
  
  - `get_wallet_balance/2` - Get current wallet balance
  - `check_wallet_status/2` - Check wallet status and flags
  - `get_transaction_history/2` - Get recent transaction history
  - `validate_transaction_amount/2` - Validate if a transaction amount is allowed
  
  ## Security Notes
  
  This bypass module implements financial security best practices:
  - Read-only operations for safety (no direct balance modifications)
  - Amount validation without executing transactions
  - Comprehensive audit logging for all operations
  - Balance masking for sensitive operations
  
  ## Usage in Triggers
  
  Register these functions as triggers with mount points like:
  - `/api/triggers/wallet_operations/get_balance`
  - `/api/triggers/wallet_operations/check_status`
  - `/api/triggers/wallet_operations/get_history`
  - `/api/triggers/wallet_operations/validate_amount`
  """
  
  use ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Contexts.WalletsContext
  alias ServiceManager.Contexts.WalletTransactionsContext

  @doc """
  Retrieves the current wallet balance for a user.
  
  ## Parameters
  
  - `params` - Map containing wallet identification
    - `:user_id` (required) - User ID (integer)
    - `:wallet_id` (optional) - Specific wallet ID, if not provided uses primary wallet
    - `:include_pending` (optional) - Include pending transactions (default: false)
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, balance_info}` - Wallet balance information
  - `{:error, :wallet_not_found}` - Wallet not found
  - `{:error, :user_not_found}` - User not found
  - `{:error, reason}` - Other error
  
  ## Example
  
      get_wallet_balance(%{user_id: 42}, %{})
      {:ok, %{
        wallet_id: 123,
        user_id: 42,
        available_balance: "1000.50",
        pending_balance: "50.00",
        currency: "USD",
        last_transaction_at: ~U[2023-04-09 10:30:00Z],
        balance_as_of: ~U[2023-04-09 12:00:00Z]
      }}
  """
  @track log_level: :info, category: "wallet_balance_inquiry"
  def get_wallet_balance(params, context) do
    with {:ok, user_id} <- extract_user_id(params),
         {:ok, wallet} <- find_wallet(user_id, params["wallet_id"]) do
      
      balance_info = %{
        wallet_id: wallet.id,
        user_id: wallet.user_id,
        available_balance: format_amount(wallet.available_balance),
        pending_balance: format_amount(wallet.pending_balance || 0),
        currency: wallet.currency || "USD",
        wallet_status: wallet.status || "active",
        last_transaction_at: wallet.last_transaction_at,
        balance_as_of: DateTime.utc_now()
      }
      
      {:ok, balance_info}
    else
      error -> error
    end
  end

  @doc """
  Checks wallet status and important flags.
  
  ## Parameters
  
  - `params` - Map containing wallet identification
    - `:user_id` (required) - User ID (integer)
    - `:wallet_id` (optional) - Specific wallet ID
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, status_info}` - Wallet status information
  - `{:error, :wallet_not_found}` - Wallet not found
  - `{:error, reason}` - Other error
  
  ## Example
  
      check_wallet_status(%{user_id: 42}, %{})
      {:ok, %{
        wallet_id: 123,
        status: "active",
        is_locked: false,
        is_suspended: false,
        can_receive: true,
        can_send: true,
        kyc_verified: true,
        created_at: ~U[2023-01-01 00:00:00Z],
        status_checked_at: ~U[2023-04-09 12:00:00Z]
      }}
  """
  @track log_level: :info, category: "wallet_status_check"
  def check_wallet_status(params, context) do
    with {:ok, user_id} <- extract_user_id(params),
         {:ok, wallet} <- find_wallet(user_id, params["wallet_id"]) do
      
      status_info = %{
        wallet_id: wallet.id,
        user_id: wallet.user_id,
        status: wallet.status || "unknown",
        is_locked: wallet.is_locked || false,
        is_suspended: wallet.is_suspended || false,
        can_receive: wallet.can_receive || true,
        can_send: wallet.can_send || true,
        kyc_verified: wallet.kyc_verified || false,
        created_at: wallet.inserted_at,
        updated_at: wallet.updated_at,
        status_checked_at: DateTime.utc_now()
      }
      
      {:ok, status_info}
    else
      error -> error
    end
  end

  @doc """
  Retrieves recent transaction history for a wallet.
  
  ## Parameters
  
  - `params` - Map containing request parameters
    - `:user_id` (required) - User ID (integer)
    - `:wallet_id` (optional) - Specific wallet ID
    - `:limit` (optional) - Number of transactions to return (default: 10, max: 50)
    - `:status` (optional) - Filter by transaction status
    - `:transaction_type` (optional) - Filter by transaction type
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, history_info}` - Transaction history
  - `{:error, :wallet_not_found}` - Wallet not found
  - `{:error, reason}` - Other error
  
  ## Example
  
      get_transaction_history(%{user_id: 42, limit: 5}, %{})
      {:ok, %{
        wallet_id: 123,
        transactions: [
          %{
            id: 456,
            amount: "100.00",
            type: "credit",
            status: "completed",
            description: "Transfer from user 789",
            created_at: ~U[2023-04-09 10:00:00Z]
          }
        ],
        total_count: 25,
        retrieved_at: ~U[2023-04-09 12:00:00Z]
      }}
  """
  @track log_level: :info, category: "wallet_transaction_history"
  def get_transaction_history(params, context) do
    with {:ok, user_id} <- extract_user_id(params),
         {:ok, wallet} <- find_wallet(user_id, params["wallet_id"]),
         {:ok, limit} <- extract_limit(params),
         {:ok, transactions} <- fetch_transactions(wallet.id, limit, params) do
      
      history_info = %{
        wallet_id: wallet.id,
        user_id: wallet.user_id,
        transactions: format_transactions(transactions),
        limit_applied: limit,
        retrieved_at: DateTime.utc_now()
      }
      
      {:ok, history_info}
    else
      error -> error
    end
  end

  @doc """
  Validates if a transaction amount is allowed for a wallet.
  
  Does not execute the transaction, only validates business rules.
  
  ## Parameters
  
  - `params` - Map containing validation parameters
    - `:user_id` (required) - User ID (integer)
    - `:amount` (required) - Transaction amount as string or number
    - `:transaction_type` (required) - Type of transaction ("debit", "credit")
    - `:wallet_id` (optional) - Specific wallet ID
    - `:currency` (optional) - Currency code (default: wallet currency)
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, validation_result}` - Validation completed
  - `{:error, :wallet_not_found}` - Wallet not found
  - `{:error, :invalid_amount}` - Invalid amount format
  - `{:error, reason}` - Other error
  
  ## Example
  
      validate_transaction_amount(%{
        user_id: 42,
        amount: "500.00",
        transaction_type: "debit"
      }, %{})
      {:ok, %{
        valid: true,
        amount: "500.00",
        currency: "USD",
        transaction_type: "debit",
        available_balance: "1000.50",
        sufficient_funds: true,
        within_limits: true,
        validated_at: ~U[2023-04-09 12:00:00Z]
      }}
  """
  @track log_level: :info, category: "transaction_amount_validation"
  def validate_transaction_amount(params, context) do
    with {:ok, user_id} <- extract_user_id(params),
         {:ok, amount} <- extract_and_validate_amount(params),
         {:ok, transaction_type} <- extract_transaction_type(params),
         {:ok, wallet} <- find_wallet(user_id, params["wallet_id"]) do
      
      validation_result = perform_amount_validation(wallet, amount, transaction_type)
      
      {:ok, Map.put(validation_result, :validated_at, DateTime.utc_now())}
    else
      error -> error
    end
  end

  # Private helper functions

  defp extract_user_id(%{"user_id" => user_id}) when is_integer(user_id) do
    {:ok, user_id}
  end

  defp extract_user_id(%{"user_id" => user_id_str}) when is_binary(user_id_str) do
    case Integer.parse(user_id_str) do
      {user_id, ""} -> {:ok, user_id}
      _ -> {:error, :invalid_user_id}
    end
  end

  defp extract_user_id(_params) do
    {:error, :missing_user_id}
  end

  defp find_wallet(user_id, nil) do
    # Find primary wallet for user
    try do
      case WalletsContext.get_data!(user_id) do
        nil -> {:error, :wallet_not_found}
        wallet -> {:ok, wallet}
      end
    rescue
      Ecto.NoResultsError -> {:error, :wallet_not_found}
    end
  end

  defp find_wallet(user_id, wallet_id) when is_binary(wallet_id) do
    case Integer.parse(wallet_id) do
      {wallet_id_int, ""} -> find_wallet(user_id, wallet_id_int)
      _ -> {:error, :invalid_wallet_id}
    end
  end

  defp find_wallet(user_id, wallet_id) when is_integer(wallet_id) do
    try do
      wallet = WalletsContext.get_data!(wallet_id)
      if wallet.user_id == user_id do
        {:ok, wallet}
      else
        {:error, :wallet_not_found}
      end
    rescue
      Ecto.NoResultsError -> {:error, :wallet_not_found}
    end
  end

  defp extract_limit(%{"limit" => limit}) when is_integer(limit) and limit > 0 and limit <= 50 do
    {:ok, limit}
  end

  defp extract_limit(%{"limit" => limit_str}) when is_binary(limit_str) do
    case Integer.parse(limit_str) do
      {limit, ""} when limit > 0 and limit <= 50 -> {:ok, limit}
      _ -> {:ok, 10}  # Default limit
    end
  end

  defp extract_limit(_params) do
    {:ok, 10}  # Default limit
  end

  defp fetch_transactions(wallet_id, limit, filters) do
    try do
      # This would typically use the wallet transactions context
      # For now, return an empty list as an example
      transactions = []  # WalletTransactionsContext.get_recent_transactions(wallet_id, limit, filters)
      {:ok, transactions}
    rescue
      error ->
        {:error, "Failed to fetch transactions: #{inspect(error)}"}
    end
  end

  defp extract_and_validate_amount(%{"amount" => amount}) when is_number(amount) and amount > 0 do
    {:ok, Decimal.new(amount)}
  end

  defp extract_and_validate_amount(%{"amount" => amount_str}) when is_binary(amount_str) do
    try do
      amount = Decimal.new(amount_str)
      if Decimal.positive?(amount) do
        {:ok, amount}
      else
        {:error, :invalid_amount}
      end
    rescue
      _ -> {:error, :invalid_amount}
    end
  end

  defp extract_and_validate_amount(_params) do
    {:error, :missing_amount}
  end

  defp extract_transaction_type(%{"transaction_type" => type}) when type in ["debit", "credit"] do
    {:ok, type}
  end

  defp extract_transaction_type(_params) do
    {:error, :invalid_transaction_type}
  end

  defp perform_amount_validation(wallet, amount, transaction_type) do
    available_balance = Decimal.new(wallet.available_balance || 0)
    
    validation_checks = %{
      valid: true,
      amount: Decimal.to_string(amount),
      currency: wallet.currency || "USD",
      transaction_type: transaction_type,
      available_balance: Decimal.to_string(available_balance),
      sufficient_funds: check_sufficient_funds(available_balance, amount, transaction_type),
      within_limits: check_transaction_limits(amount, transaction_type),
      wallet_active: wallet.status == "active"
    }
    
    # Overall validation result
    overall_valid = validation_checks.sufficient_funds and 
                   validation_checks.within_limits and 
                   validation_checks.wallet_active
    
    %{validation_checks | valid: overall_valid}
  end

  defp check_sufficient_funds(available_balance, amount, "debit") do
    Decimal.compare(available_balance, amount) != :lt
  end

  defp check_sufficient_funds(_available_balance, _amount, "credit") do
    true  # Credits don't require balance validation
  end

  defp check_transaction_limits(amount, _transaction_type) do
    # Example limits - in real implementation, these would come from configuration
    max_amount = Decimal.new("10000.00")
    min_amount = Decimal.new("0.01")
    
    Decimal.compare(amount, min_amount) != :lt and Decimal.compare(amount, max_amount) != :gt
  end

  defp format_amount(amount) when is_nil(amount), do: "0.00"
  defp format_amount(amount) do
    amount
    |> Decimal.new()
    |> Decimal.round(2)
    |> Decimal.to_string()
  end

  defp format_transactions(transactions) do
    Enum.map(transactions, fn transaction ->
      %{
        id: transaction.id,
        amount: format_amount(transaction.amount),
        type: transaction.transaction_type,
        status: transaction.status,
        description: transaction.description || "No description",
        reference: transaction.reference,
        created_at: transaction.inserted_at,
        updated_at: transaction.updated_at
      }
    end)
  end
end