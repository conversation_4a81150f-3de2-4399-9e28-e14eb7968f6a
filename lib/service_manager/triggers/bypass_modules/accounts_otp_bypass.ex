defmodule ServiceManager.Triggers.BypassModules.AccountsOtpBypass do
  @moduledoc """
  Bypass module for account OTP operations.
  
  This module wraps OTP generation and validation functions from the 
  SystemAuthorizationContext for trigger execution. It demonstrates how to:
  
  - Properly validate and sanitize input parameters
  - Call existing business logic safely
  - Return standardized responses
  - Handle errors appropriately
  - Integrate with the FunctionTracker for audit logging
  
  ## Available Functions
  
  - `generate_otp/2` - Generate OTP for user by email or account number
  - `validate_otp/2` - Validate OTP for user
  
  ## Usage in Triggers
  
  Register these functions as triggers with mount points like:
  - `/api/triggers/accounts_otp/generate_otp`
  - `/api/triggers/accounts_otp/validate_otp`
  """
  
  use ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Context.SystemAuthorizationContext
  alias ServiceManager.Triggers.Schemas.TriggerExecutionLog

  @doc """
  Generates an OTP for a user identified by email or account number.
  
  ## Parameters
  
  - `params` - Map containing user identification
    - `:email` (optional) - User email address
    - `:account_number` (optional) - User account number
    - One of email or account_number must be provided
  - `context` - Execution context map (can contain user info, request metadata, etc.)
  
  ## Returns
  
  - `{:ok, %{otp: string, expires_at: DateTime.t}}` - OTP generated successfully
  - `{:error, :user_not_found}` - User not found
  - `{:error, :missing_identifier}` - Neither email nor account_number provided
  - `{:error, reason}` - Other error
  
  ## Example
  
      # Generate OTP by email
      generate_otp(%{email: "<EMAIL>"}, %{})
      {:ok, %{otp: "123456", expires_at: ~U[2023-04-09 12:34:56Z]}}
      
      # Generate OTP by account number
      generate_otp(%{account_number: "**********"}, %{})
      {:ok, %{otp: "654321", expires_at: ~U[2023-04-09 12:34:56Z]}}
  """
  @track log_level: :info, category: "otp_generation"
  def generate_otp(params, context) do
    with {:ok, user} <- find_user(params),
         {:ok, otp, expires_at} <- SystemAuthorizationContext.generate_otp_for_user(user) do
      {:ok, %{
        otp: otp,
        expires_at: expires_at,
        user_id: user.id,
        generated_at: DateTime.utc_now()
      }}
    else
      {:error, reason} ->
        {:error, reason}
      error ->
        {:error, "Failed to generate OTP: #{inspect(error)}"}
    end
  end

  @doc """
  Validates an OTP for a user identified by email or account number.
  
  ## Parameters
  
  - `params` - Map containing user identification and OTP
    - `:email` (optional) - User email address
    - `:account_number` (optional) - User account number
    - `:otp` (required) - OTP code to validate
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, %{valid: true, user_id: integer}}` - OTP is valid
  - `{:error, :invalid_otp}` - OTP is invalid or expired
  - `{:error, :user_not_found}` - User not found
  - `{:error, :missing_otp}` - OTP not provided
  - `{:error, reason}` - Other error
  
  ## Example
  
      validate_otp(%{email: "<EMAIL>", otp: "123456"}, %{})
      {:ok, %{valid: true, user_id: 42}}
      
      validate_otp(%{email: "<EMAIL>", otp: "invalid"}, %{})
      {:error, :invalid_otp}
  """
  @track log_level: :info, category: "otp_validation"
  def validate_otp(params, context) do
    with {:ok, otp} <- extract_otp(params),
         {:ok, user} <- find_user(params),
         {:ok, result} <- SystemAuthorizationContext.validate_otp_for_user(user, otp) do
      {:ok, %{
        valid: result == :valid,
        user_id: user.id,
        validated_at: DateTime.utc_now()
      }}
    else
      {:error, reason} ->
        {:error, reason}
      error ->
        {:error, "Failed to validate OTP: #{inspect(error)}"}
    end
  end

  # Private helper functions

  defp find_user(%{"email" => email}) when is_binary(email) and email != "" do
    case SystemAuthorizationContext.get_user_by_email(email) do
      nil -> {:error, :user_not_found}
      user -> {:ok, user}
    end
  end

  defp find_user(%{"account_number" => account_number}) when is_binary(account_number) and account_number != "" do
    case SystemAuthorizationContext.user_by_account_number(account_number) do
      nil -> {:error, :user_not_found}
      user -> {:ok, user}
    end
  end

  defp find_user(_params) do
    {:error, :missing_identifier}
  end

  defp extract_otp(%{"otp" => otp}) when is_binary(otp) and otp != "" do
    {:ok, otp}
  end

  defp extract_otp(_params) do
    {:error, :missing_otp}
  end
end