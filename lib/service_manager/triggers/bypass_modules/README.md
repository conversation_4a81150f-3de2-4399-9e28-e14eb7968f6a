# Trigger Bypass Modules

This directory contains example bypass modules that demonstrate how to wrap existing business logic functions for use with the Triggers system. Bypass modules provide a secure, standardized way to expose internal functions as HTTP-accessible trigger endpoints.

## Overview

Bypass modules serve as a bridge between the trigger system and your existing business logic. They provide:

- **Security Layer**: Input validation and sanitization
- **Standardized Interface**: Consistent function signatures and response formats  
- **Error Handling**: Proper error wrapping and logging
- **Audit Integration**: Automatic logging via FunctionTracker
- **Parameter Mapping**: Translation between HTTP parameters and function arguments

## Module Structure

Every bypass module should follow this pattern:

```elixir
defmodule YourApp.Triggers.BypassModules.YourModuleBypass do
  @moduledoc """
  Bypass module for [domain] operations.
  
  Brief description of what this module wraps and its purpose.
  """
  
  use ServiceManager.Logging.FunctionTracker

  # Function definitions with @track decorators
  @track log_level: :info, category: "operation_category"
  def your_function(params, context) do
    # Implementation
  end
end
```

## Function Signature Requirements

All bypass module functions must follow this signature:

```elixir
@spec function_name(params :: map(), context :: map()) :: 
  {:ok, result :: any()} | {:error, reason :: any()}
```

### Parameters

- **`params`**: Map containing the input parameters from the HTTP request
- **`context`**: Map containing execution context (user info, request metadata, etc.)

### Return Values

Functions must return either:
- `{:ok, result}` - Success with result data
- `{:error, reason}` - Error with reason (atom or string)

## Example Implementations

### 1. AccountsOtpBypass

Located in `accounts_otp_bypass.ex`, this module demonstrates:

- **User Lookup**: Finding users by email or account number
- **Input Validation**: Extracting and validating required parameters
- **Business Logic Integration**: Calling existing OTP functions
- **Privacy Protection**: Not exposing sensitive user data
- **Error Handling**: Comprehensive error management

**Functions:**
- `generate_otp/2` - Generate OTP for user
- `validate_otp/2` - Validate OTP code

### 2. UserLookupBypass  

Located in `user_lookup_bypass.ex`, this module demonstrates:

- **Multiple Lookup Methods**: Email, account number, or user ID
- **Data Masking**: Protecting sensitive information in responses
- **Status Checking**: Non-sensitive user status information
- **Existence Checking**: Validating user existence without data exposure

**Functions:**
- `get_user_info/2` - Get basic user information
- `check_user_exists/2` - Check if user exists
- `get_user_status/2` - Get user status flags

### 3. NotificationSettingsBypass

Located in `notification_settings_bypass.ex`, this module demonstrates:

- **Settings Management**: Retrieving and updating configuration
- **Preference Handling**: Managing notification preferences
- **Configuration Validation**: Validating settings changes
- **Toggle Operations**: Simple on/off toggles

**Functions:**
- `get_notification_settings/2` - Get current settings
- `update_notification_preference/2` - Update specific preference
- `toggle_notification_type/2` - Toggle notification type

### 4. WalletOperationsBypass

Located in `wallet_operations_bypass.ex`, this module demonstrates:

- **Financial Operations**: Safe read-only wallet operations
- **Amount Validation**: Transaction amount validation without execution
- **Balance Inquiry**: Secure balance checking
- **Transaction History**: Recent transaction retrieval
- **Business Rules**: Implementing financial validation rules

**Functions:**
- `get_wallet_balance/2` - Get wallet balance
- `check_wallet_status/2` - Check wallet status
- `get_transaction_history/2` - Get transaction history
- `validate_transaction_amount/2` - Validate transaction amounts

## Creating Your Own Bypass Module

### Step 1: Create Module File

Create a new file in this directory following the naming pattern:
`[domain]_[purpose]_bypass.ex`

### Step 2: Define Module Structure

```elixir
defmodule ServiceManager.Triggers.BypassModules.YourDomainBypass do
  @moduledoc """
  Bypass module for [your domain] operations.
  
  Describe what this module wraps and provides examples.
  """
  
  use ServiceManager.Logging.FunctionTracker
  
  # Add your imports and aliases here
  alias ServiceManager.Contexts.YourContext
end
```

### Step 3: Implement Functions

For each function you want to expose:

```elixir
@doc """
Clear documentation of what the function does.

## Parameters
- Document each expected parameter
- Include examples

## Returns  
- Document return value formats
- Include error cases

## Example
    your_function(%{param: "value"}, %{})
    {:ok, %{result: "data"}}
"""
@track log_level: :info, category: "your_category", 
       sensitive_params: [:password, :secret]  # Optional: mark sensitive params
def your_function(params, context) do
  with {:ok, validated_input} <- validate_input(params),
       {:ok, result} <- YourContext.your_business_function(validated_input) do
    {:ok, format_result(result)}
  else
    {:error, reason} -> {:error, reason}
    error -> {:error, "Unexpected error: #{inspect(error)}"}
  end
end

# Private helper functions
defp validate_input(params) do
  # Validation logic
end

defp format_result(result) do
  # Result formatting
end
```

### Step 4: Add Input Validation

Always validate inputs thoroughly:

```elixir
defp extract_required_param(params, key) do
  case Map.get(params, key) do
    nil -> {:error, :"missing_#{key}"}
    "" -> {:error, :"empty_#{key}"}
    value -> {:ok, value}
  end
end

defp validate_email(email) do
  if String.contains?(email, "@") do
    {:ok, email}
  else
    {:error, :invalid_email}
  end
end
```

### Step 5: Register as Trigger

After creating your bypass module, register the functions as triggers in the admin interface:

1. Go to the Triggers management page
2. Create new trigger
3. Set module name: `ServiceManager.Triggers.BypassModules.YourDomainBypass`
4. Set function name: `your_function`
5. Configure mount point: `/api/triggers/your_domain/your_function`
6. Set up rate limiting and other options as needed

## Best Practices

### Security

1. **Input Validation**: Always validate all inputs
2. **Sanitization**: Clean user inputs before processing
3. **Authorization**: Check permissions in context if needed
4. **Rate Limiting**: Use appropriate rate limiting for each function
5. **Sensitive Data**: Mark sensitive parameters in @track decorators

### Error Handling

1. **Consistent Errors**: Use standardized error formats
2. **Informative Messages**: Provide clear error descriptions
3. **Error Logging**: Let FunctionTracker handle logging
4. **Graceful Degradation**: Handle edge cases gracefully

### Performance

1. **Read-Only Operations**: Prefer read operations over writes
2. **Efficient Queries**: Optimize database queries
3. **Pagination**: Implement pagination for list operations
4. **Caching**: Consider caching for frequently accessed data

### Documentation

1. **Function Documentation**: Document all public functions thoroughly
2. **Parameter Examples**: Provide clear parameter examples
3. **Return Examples**: Show example return values
4. **Error Cases**: Document possible error conditions

## Testing Bypass Modules

Create tests for your bypass modules:

```elixir
defmodule ServiceManager.Triggers.BypassModules.YourDomainBypassTest do
  use ServiceManager.DataCase
  
  alias ServiceManager.Triggers.BypassModules.YourDomainBypass
  
  describe "your_function/2" do
    test "returns success with valid params" do
      params = %{"required_param" => "value"}
      context = %{}
      
      assert {:ok, result} = YourDomainBypass.your_function(params, context)
      assert Map.has_key?(result, :expected_field)
    end
    
    test "returns error with missing params" do
      params = %{}
      context = %{}
      
      assert {:error, :missing_required_param} = 
        YourDomainBypass.your_function(params, context)
    end
  end
end
```

## Integration with Trigger System

Once your bypass module is complete:

1. **Module Validation**: The system will validate that your module exports the required functions
2. **Mount Point Generation**: Automatic generation of API endpoints
3. **Parameter Validation**: Built-in JSON schema validation
4. **Rate Limiting**: Configurable rate limiting per trigger
5. **Monitoring**: Automatic execution logging and metrics
6. **Error Tracking**: Built-in error tracking and alerting

## Available Context Information

The `context` parameter may contain:

- `user_id` - ID of the authenticated user (if applicable)
- `request_ip` - IP address of the request
- `user_agent` - User agent string
- `execution_id` - Unique execution identifier
- `triggered_by` - How the trigger was invoked ("api", "wizard", "system")
- `wizard_step_id` - If triggered from wizard step
- `custom_headers` - Any custom headers passed

## Example API Calls

Once registered, your triggers can be called via HTTP:

```bash
# Generate OTP
curl -X POST http://localhost:4000/api/triggers/accounts_otp/generate_otp \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"email": "<EMAIL>"}'

# Check user exists
curl -X POST http://localhost:4000/api/triggers/user_lookup/check_user_exists \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"email": "<EMAIL>"}'
```

## Troubleshooting

### Common Issues

1. **Module not found**: Ensure the module name is correct and the file is in the right location
2. **Function not exported**: Make sure your function is public (not defp)
3. **Invalid signature**: Check that your function takes exactly 2 parameters
4. **Validation errors**: Verify your input validation logic
5. **Context errors**: Make sure you're handling the context parameter properly

### Debugging

Enable debug logging to trace execution:

```elixir
@track log_level: :debug, category: "debug_category"
def your_function(params, context) do
  # Your function implementation
end
```

This will log detailed execution information for troubleshooting.