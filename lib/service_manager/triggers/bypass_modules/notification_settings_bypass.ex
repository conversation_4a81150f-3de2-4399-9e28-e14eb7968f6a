defmodule ServiceManager.Triggers.BypassModules.NotificationSettingsBypass do
  @moduledoc """
  Bypass module for notification settings operations.
  
  This module wraps notification configuration functions for trigger execution.
  It demonstrates how to handle settings and configuration changes through
  the trigger system while maintaining data integrity and audit trails.
  
  ## Available Functions
  
  - `get_notification_settings/2` - Retrieve current notification settings
  - `update_notification_preference/2` - Update specific notification preference
  - `toggle_notification_type/2` - Enable/disable a notification type
  
  ## Usage in Triggers
  
  Register these functions as triggers with mount points like:
  - `/api/triggers/notification_settings/get_settings`
  - `/api/triggers/notification_settings/update_preference`
  - `/api/triggers/notification_settings/toggle_type`
  """
  
  use ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Contexts.NotificationsConfigContext

  @doc """
  Retrieves the current notification settings configuration.
  
  ## Parameters
  
  - `params` - Map (can be empty for this function)
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, settings_map}` - Current notification settings
  - `{:error, reason}` - Error retrieving settings
  
  ## Example
  
      get_notification_settings(%{}, %{})
      {:ok, %{
        email_notifications_enabled: true,
        sms_notifications_enabled: true,
        push_notifications_enabled: false,
        email_config: %{...},
        sms_config: %{...},
        last_updated: ~U[2023-04-09 10:00:00Z]
      }}
  """
  @track log_level: :info, category: "notification_settings_retrieval"
  def get_notification_settings(params, context) do
    try do
      settings = NotificationsConfigContext.get_setting_schema()
      
      settings_map = %{
        email_notifications_enabled: get_setting_value(settings, :email_notifications_enabled, false),
        sms_notifications_enabled: get_setting_value(settings, :sms_notifications_enabled, false),
        push_notifications_enabled: get_setting_value(settings, :push_notifications_enabled, false),
        email_config: extract_email_config(settings),
        sms_config: extract_sms_config(settings),
        push_config: extract_push_config(settings),
        last_updated: settings.updated_at || DateTime.utc_now(),
        settings_id: settings.id
      }
      
      {:ok, settings_map}
    rescue
      error ->
        {:error, "Failed to retrieve notification settings: #{inspect(error)}"}
    end
  end

  @doc """
  Updates a specific notification preference.
  
  ## Parameters
  
  - `params` - Map containing preference update
    - `:preference_type` (required) - Type of preference ("email", "sms", "push")
    - `:enabled` (required) - Boolean value to set
    - `:config` (optional) - Additional configuration for the preference type
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, updated_settings}` - Preference updated successfully
  - `{:error, :invalid_preference_type}` - Invalid preference type
  - `{:error, :missing_enabled_flag}` - Missing enabled parameter
  - `{:error, reason}` - Other error
  
  ## Example
  
      update_notification_preference(%{
        preference_type: "email",
        enabled: true,
        config: %{smtp_host: "smtp.example.com"}
      }, %{})
      {:ok, %{preference_type: "email", enabled: true, updated_at: ~U[...]}}
  """
  @track log_level: :info, category: "notification_preference_update"
  def update_notification_preference(params, context) do
    with {:ok, preference_type} <- extract_preference_type(params),
         {:ok, enabled} <- extract_enabled_flag(params),
         {:ok, current_settings} <- get_current_settings(),
         {:ok, updated_settings} <- update_preference_in_settings(current_settings, preference_type, enabled, params["config"]),
         {:ok, saved_settings} <- save_updated_settings(updated_settings) do
      
      {:ok, %{
        preference_type: preference_type,
        enabled: enabled,
        updated_at: DateTime.utc_now(),
        settings_id: saved_settings.id
      }}
    else
      error -> error
    end
  end

  @doc """
  Toggles a notification type on or off.
  
  ## Parameters
  
  - `params` - Map containing toggle information
    - `:notification_type` (required) - Type to toggle ("email", "sms", "push")
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, toggle_result}` - Toggle completed successfully
  - `{:error, :invalid_notification_type}` - Invalid notification type
  - `{:error, reason}` - Other error
  
  ## Example
  
      toggle_notification_type(%{notification_type: "sms"}, %{})
      {:ok, %{
        notification_type: "sms",
        previous_state: false,
        new_state: true,
        toggled_at: ~U[2023-04-09 12:00:00Z]
      }}
  """
  @track log_level: :info, category: "notification_type_toggle"
  def toggle_notification_type(params, context) do
    with {:ok, notification_type} <- extract_notification_type(params),
         {:ok, current_settings} <- get_current_settings(),
         {:ok, current_state} <- get_current_state(current_settings, notification_type),
         {:ok, updated_settings} <- toggle_setting_state(current_settings, notification_type, current_state),
         {:ok, saved_settings} <- save_updated_settings(updated_settings) do
      
      {:ok, %{
        notification_type: notification_type,
        previous_state: current_state,
        new_state: not current_state,
        toggled_at: DateTime.utc_now(),
        settings_id: saved_settings.id
      }}
    else
      error -> error
    end
  end

  # Private helper functions

  defp get_setting_value(settings, key, default) do
    case Map.get(settings, key) do
      nil -> default
      value -> value
    end
  end

  defp extract_email_config(settings) do
    %{
      enabled: get_setting_value(settings, :email_notifications_enabled, false),
      smtp_configured: not is_nil(settings.smtp_host),
      from_address: settings.from_email
    }
  end

  defp extract_sms_config(settings) do
    %{
      enabled: get_setting_value(settings, :sms_notifications_enabled, false),
      provider_configured: not is_nil(settings.sms_provider),
      provider: settings.sms_provider
    }
  end

  defp extract_push_config(settings) do
    %{
      enabled: get_setting_value(settings, :push_notifications_enabled, false),
      service_configured: not is_nil(settings.push_service),
      service: settings.push_service
    }
  end

  defp extract_preference_type(%{"preference_type" => type}) when type in ["email", "sms", "push"] do
    {:ok, type}
  end

  defp extract_preference_type(_params) do
    {:error, :invalid_preference_type}
  end

  defp extract_enabled_flag(%{"enabled" => enabled}) when is_boolean(enabled) do
    {:ok, enabled}
  end

  defp extract_enabled_flag(%{"enabled" => "true"}), do: {:ok, true}
  defp extract_enabled_flag(%{"enabled" => "false"}), do: {:ok, false}

  defp extract_enabled_flag(_params) do
    {:error, :missing_enabled_flag}
  end

  defp extract_notification_type(%{"notification_type" => type}) when type in ["email", "sms", "push"] do
    {:ok, type}
  end

  defp extract_notification_type(_params) do
    {:error, :invalid_notification_type}
  end

  defp get_current_settings do
    try do
      settings = NotificationsConfigContext.get_setting_schema()
      {:ok, settings}
    rescue
      error ->
        {:error, "Failed to get current settings: #{inspect(error)}"}
    end
  end

  defp update_preference_in_settings(settings, "email", enabled, config) do
    updated_settings = %{settings | email_notifications_enabled: enabled}
    
    updated_settings = 
      if config do
        Map.merge(updated_settings, parse_email_config(config))
      else
        updated_settings
      end
    
    {:ok, updated_settings}
  end

  defp update_preference_in_settings(settings, "sms", enabled, config) do
    updated_settings = %{settings | sms_notifications_enabled: enabled}
    
    updated_settings = 
      if config do
        Map.merge(updated_settings, parse_sms_config(config))
      else
        updated_settings
      end
    
    {:ok, updated_settings}
  end

  defp update_preference_in_settings(settings, "push", enabled, config) do
    updated_settings = %{settings | push_notifications_enabled: enabled}
    
    updated_settings = 
      if config do
        Map.merge(updated_settings, parse_push_config(config))
      else
        updated_settings
      end
    
    {:ok, updated_settings}
  end

  defp get_current_state(settings, "email") do
    {:ok, get_setting_value(settings, :email_notifications_enabled, false)}
  end

  defp get_current_state(settings, "sms") do
    {:ok, get_setting_value(settings, :sms_notifications_enabled, false)}
  end

  defp get_current_state(settings, "push") do
    {:ok, get_setting_value(settings, :push_notifications_enabled, false)}
  end

  defp toggle_setting_state(settings, "email", current_state) do
    {:ok, %{settings | email_notifications_enabled: not current_state}}
  end

  defp toggle_setting_state(settings, "sms", current_state) do
    {:ok, %{settings | sms_notifications_enabled: not current_state}}
  end

  defp toggle_setting_state(settings, "push", current_state) do
    {:ok, %{settings | push_notifications_enabled: not current_state}}
  end

  defp save_updated_settings(settings) do
    try do
      # Convert to map for saving
      settings_attrs = Map.from_struct(settings)
      case NotificationsConfigContext.create_setting(settings_attrs) do
        {:ok, saved_settings} -> {:ok, saved_settings}
        {:error, changeset} -> {:error, "Failed to save settings: #{inspect(changeset.errors)}"}
      end
    rescue
      error ->
        {:error, "Failed to save updated settings: #{inspect(error)}"}
    end
  end

  defp parse_email_config(config) when is_map(config) do
    Map.take(config, ["smtp_host", "smtp_port", "from_email"])
  end

  defp parse_email_config(_), do: %{}

  defp parse_sms_config(config) when is_map(config) do
    Map.take(config, ["sms_provider", "api_key", "sender_id"])
  end

  defp parse_sms_config(_), do: %{}

  defp parse_push_config(config) when is_map(config) do
    Map.take(config, ["push_service", "api_key", "app_id"])
  end

  defp parse_push_config(_), do: %{}
end