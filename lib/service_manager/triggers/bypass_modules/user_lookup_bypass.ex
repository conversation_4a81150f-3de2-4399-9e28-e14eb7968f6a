defmodule ServiceManager.Triggers.BypassModules.UserLookupBypass do
  @moduledoc """
  Bypass module for user lookup operations.
  
  This module wraps user retrieval functions from various contexts for trigger execution.
  It demonstrates how to safely expose user lookup functionality while maintaining
  security and data privacy controls.
  
  ## Available Functions
  
  - `get_user_info/2` - Get basic user information by identifier
  - `check_user_exists/2` - Check if user exists without returning sensitive data
  - `get_user_status/2` - Get user account status and basic flags
  
  ## Security Notes
  
  This bypass module is designed with privacy in mind:
  - Does not expose sensitive data like passwords or full personal details
  - Returns only essential information needed for business logic
  - Includes rate limiting considerations for user enumeration protection
  
  ## Usage in Triggers
  
  Register these functions as triggers with mount points like:
  - `/api/triggers/user_lookup/get_user_info`
  - `/api/triggers/user_lookup/check_user_exists`
  - `/api/triggers/user_lookup/get_user_status`
  """
  
  use ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Context.SystemAuthorizationContext
  alias ServiceManager.Contexts.UserManagementContext

  @doc """
  Retrieves basic user information by email or account number.
  
  Returns only non-sensitive information suitable for business logic use.
  
  ## Parameters
  
  - `params` - Map containing user identification
    - `:email` (optional) - User email address
    - `:account_number` (optional) - User account number
    - `:user_id` (optional) - User ID (integer)
    - One identifier must be provided
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, user_info}` - User found, returns basic info map
  - `{:error, :user_not_found}` - User not found
  - `{:error, :missing_identifier}` - No valid identifier provided
  
  ## Example
  
      get_user_info(%{email: "<EMAIL>"}, %{})
      {:ok, %{
        id: 42,
        email: "<EMAIL>",
        first_name: "John",
        last_name: "Doe",
        account_number: "**********",
        account_status: "active",
        created_at: ~U[2023-01-01 00:00:00Z]
      }}
  """
  @track log_level: :info, category: "user_lookup", 
         sensitive_params: [:email, :account_number]
  def get_user_info(params, context) do
    with {:ok, user} <- find_user_safely(params) do
      user_info = %{
        id: user.id,
        email: mask_email(user.email),
        first_name: user.first_name,
        last_name: user.last_name,
        account_number: mask_account_number(user.account_number),
        account_status: user.account_status || "unknown",
        is_active: user.is_active || false,
        created_at: user.inserted_at,
        updated_at: user.updated_at
      }
      
      {:ok, user_info}
    else
      error -> error
    end
  end

  @doc """
  Checks if a user exists without returning sensitive information.
  
  Useful for validation workflows where you only need to know if a user exists.
  
  ## Parameters
  
  - `params` - Map containing user identification
    - Same identifier options as `get_user_info/2`
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, %{exists: true, user_id: integer}}` - User exists
  - `{:ok, %{exists: false}}` - User does not exist
  - `{:error, :missing_identifier}` - No valid identifier provided
  
  ## Example
  
      check_user_exists(%{email: "<EMAIL>"}, %{})
      {:ok, %{exists: true, user_id: 42}}
      
      check_user_exists(%{email: "<EMAIL>"}, %{})
      {:ok, %{exists: false}}
  """
  @track log_level: :info, category: "user_existence_check",
         sensitive_params: [:email, :account_number]
  def check_user_exists(params, context) do
    case find_user_safely(params) do
      {:ok, user} ->
        {:ok, %{exists: true, user_id: user.id}}
      {:error, :user_not_found} ->
        {:ok, %{exists: false}}
      error ->
        error
    end
  end

  @doc """
  Gets user account status and important flags.
  
  Returns status information needed for business rules and access control.
  
  ## Parameters
  
  - `params` - Map containing user identification
    - Same identifier options as `get_user_info/2`
  - `context` - Execution context map
  
  ## Returns
  
  - `{:ok, status_info}` - User found, returns status map
  - `{:error, :user_not_found}` - User not found
  - `{:error, :missing_identifier}` - No valid identifier provided
  
  ## Example
  
      get_user_status(%{user_id: 42}, %{})
      {:ok, %{
        user_id: 42,
        account_status: "active",
        is_active: true,
        is_verified: true,
        is_locked: false,
        deletion_status: false,
        last_login_at: ~U[2023-04-08 10:30:00Z]
      }}
  """
  @track log_level: :info, category: "user_status_check",
         sensitive_params: [:email, :account_number]
  def get_user_status(params, context) do
    with {:ok, user} <- find_user_safely(params) do
      status_info = %{
        user_id: user.id,
        account_status: user.account_status || "unknown",
        is_active: user.is_active || false,
        is_verified: user.is_verified || false,
        is_locked: user.is_locked || false,
        deletion_status: user.deletion_status || false,
        last_login_at: user.last_login_at,
        status_updated_at: DateTime.utc_now()
      }
      
      {:ok, status_info}
    else
      error -> error
    end
  end

  # Private helper functions

  defp find_user_safely(%{"email" => email}) when is_binary(email) and email != "" do
    case SystemAuthorizationContext.get_user_by_email(email) do
      nil -> {:error, :user_not_found}
      user -> {:ok, user}
    end
  end

  defp find_user_safely(%{"account_number" => account_number}) when is_binary(account_number) and account_number != "" do
    case SystemAuthorizationContext.user_by_account_number(account_number) do
      nil -> {:error, :user_not_found}
      user -> {:ok, user}
    end
  end

  defp find_user_safely(%{"user_id" => user_id}) when is_integer(user_id) do
    try do
      user = UserManagementContext.get_data!(user_id)
      {:ok, user}
    rescue
      Ecto.NoResultsError -> {:error, :user_not_found}
    end
  end

  defp find_user_safely(%{"user_id" => user_id_str}) when is_binary(user_id_str) do
    case Integer.parse(user_id_str) do
      {user_id, ""} -> find_user_safely(%{"user_id" => user_id})
      _ -> {:error, :invalid_user_id}
    end
  end

  defp find_user_safely(_params) do
    {:error, :missing_identifier}
  end

  # Privacy helper functions - mask sensitive data in responses
  
  defp mask_email(email) when is_binary(email) do
    case String.split(email, "@") do
      [username, domain] when byte_size(username) > 2 ->
        masked_username = String.slice(username, 0, 2) <> "***"
        "#{masked_username}@#{domain}"
      _ ->
        "***@***"
    end
  end

  defp mask_email(_), do: "***@***"

  defp mask_account_number(account_number) when is_binary(account_number) do
    case String.length(account_number) do
      len when len > 4 ->
        last_four = String.slice(account_number, -4, 4)
        "****#{last_four}"
      _ ->
        "****"
    end
  end

  defp mask_account_number(_), do: "****"
end