openapi: 3.0.1
info:
  title: User_Details
  version: v1
servers:
  - url: /
security:
  - default: []
paths:
  /*:
    post:
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                access_token:
                  type: string
                  description: JWT access token
              required:
                - access_token
            example:
              access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJKb2tlbiIsImV4cCI6MTcyNzgyMzg0MiwiaWF0IjoxNzI3ODE2NjQyLCJpc3MiOiJKb2tlbiIsImp0aSI6IjJ2dDcwNTg0Y3Y3OWJwMGprbzAwMDFtMSIsIm5iZiI6MTcyNzgxNjY0Mn0.eotGnSgWfmtMJLCYute67DSLEWIZ1GRK0kx1C7PgW-4"
        required: true
      responses:
        '200':
          description: OK
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-wso2-application-security:
        security-types:
          - oauth2
        optional: false
components:
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: 'https://test.com'
          scopes: {}
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - Internal-Key
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - 'https://mobile-banking-mock.abakula.com/api/profile/details'
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - 'https://mobile-banking-mock.abakula.com/api/profile/details'
  type: http
x-wso2-basePath: /api/profile/details/v1
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
