# Transaction Reversal Implementation Guide

## Overview

This document provides a comprehensive guide to the transaction reversal functionality implemented in the ServiceManager application. The implementation follows database normalization principles by separating historical data (transaction status history and balance snapshots) into dedicated tables.

## Key Components

### 1. Schema Modules

#### Transaction and WalletTransactions Schemas
- Added "reversal" as a valid transaction type
- Added "reversed" as a valid transaction status
- Implemented status tracking for audit purposes

#### TransactionStatusHistory Schema
- Records all status changes for transactions
- Stores metadata about each status change
- Enables audit trail for transaction lifecycle

#### BalanceSnapshot Schema
- Records account balances before and after transactions
- Enables point-in-time balance verification
- Supports financial reconciliation processes

### 2. Service Modules

#### WalletReversal and Reversal Modules
- Handle the reversal process for wallet and regular transactions
- Create reversal transactions with appropriate attributes
- Update original transaction status to "reversed"
- Adjust account balances appropriately

#### TransactionStatusHistoryService
- Records status changes in the transaction_status_history table
- Provides methods to retrieve status history for a transaction

#### BalanceSnapshotService
- Creates balance snapshots before and after transactions
- Provides methods to retrieve balance history for accounts

## Reversal Process Flow

1. **Initiation**: User selects a transaction to reverse and provides a reason
2. **Validation**: System validates that the transaction can be reversed
3. **Pre-Reversal Snapshots**: System creates balance snapshots of affected accounts
4. **Reversal Transaction Creation**: System creates a new transaction with:
   - Type: "reversal"
   - Amount: Same as original transaction
   - Direction: Opposite of original transaction
   - Reference to original transaction
   - Opening and closing balances
5. **Original Transaction Update**: Original transaction status is updated to "reversed"
6. **Post-Reversal Snapshots**: System creates new balance snapshots after reversal
7. **Status History**: All status changes are recorded in transaction_status_history

## How to Use the Reversal Functionality

### 1. Through the Admin UI

Administrators can reverse transactions through the web interface:

#### For Wallet Transactions

1. Navigate to the Wallet Transactions page
2. Find the transaction to reverse
3. Click the "Options" dropdown and select "Reverse"
4. Enter a reason for the reversal
5. Confirm the reversal

#### For Regular Transactions

1. Navigate to the Transactions page
2. Find the transaction to reverse
3. Click the "Options" dropdown and select "Reverse"
4. Enter a reason for the reversal
5. Confirm the reversal

### 2. Programmatically

#### For Wallet Transactions

```elixir
alias ServiceManager.Transactions.WalletReversal
alias ServiceManager.Repo
alias ServiceManager.Transactions.WalletTransactions

# Get the wallet transaction to reverse
wallet_transaction = Repo.get!(WalletTransactions, wallet_transaction_id)

# Reverse the wallet transaction
case WalletReversal.reverse_transaction(wallet_transaction, "Customer request") do
  {:ok, reversal} ->
    # Reversal successful
    IO.puts("Transaction reversed successfully. Reversal ID: #{reversal.id}")

  {:error, reason} ->
    # Reversal failed
    IO.puts("Transaction reversal failed: #{inspect(reason)}")
end
```

#### For Regular Transactions

```elixir
alias ServiceManager.Transactions.Reversal
alias ServiceManager.Repo
alias ServiceManager.Transactions.Transaction

# Get the transaction to reverse
transaction = Repo.get!(Transaction, transaction_id)

# Reverse the transaction
case Reversal.reverse_transaction(transaction, "Customer request") do
  {:ok, reversal} ->
    # Reversal successful
    IO.puts("Transaction reversed successfully. Reversal ID: #{reversal.id}")

  {:error, reason} ->
    # Reversal failed
    IO.puts("Transaction reversal failed: #{inspect(reason)}")
end
```

## Database Schema

### transaction_status_history Table

| Column         | Type      | Description                                |
|----------------|-----------|--------------------------------------------|
| id             | id      | Primary key                                |
| transaction_id | uuid      | Reference to the transaction               |
| from_status    | string    | Previous status of the transaction         |
| to_status      | string    | New status of the transaction              |
| changed_by     | uuid      | User who made the change (optional)        |
| notes          | text      | Additional notes about the change          |
| metadata       | jsonb     | Additional metadata about the change       |
| inserted_at    | timestamp | When the record was created                |
| updated_at     | timestamp | When the record was last updated           |

### balance_snapshots Table

| Column         | Type      | Description                                |
|----------------|-----------|--------------------------------------------|
| id             | id        | Primary key                                |
| account_id     | uuid      | Reference to the account                   |
| account_type   | string    | Type of account (wallet, bank)             |
| balance        | decimal   | Balance at the time of snapshot            |
| transaction_id | uuid      | Reference to the transaction               |
| snapshot_type  | string    | Type of snapshot (pre/post transaction)    |
| metadata       | jsonb     | Additional metadata about the snapshot     |
| inserted_at    | timestamp | When the record was created                |
| updated_at     | timestamp | When the record was last updated           |

## Error Handling

The reversal process includes comprehensive error handling:

1. **Transaction Not Found**: Returns appropriate error if transaction doesn't exist
2. **Already Reversed**: Prevents reversing a transaction that's already reversed
3. **Invalid Status**: Only allows reversing transactions in certain statuses
4. **Balance Issues**: Validates sufficient balance for the reversal
5. **Database Errors**: Wraps all operations in a transaction for atomicity

## Audit and Compliance

The implementation supports audit and compliance requirements by:

1. Recording all status changes with timestamps and user information
2. Creating balance snapshots before and after transactions
3. Maintaining references between original and reversal transactions
4. Storing reason codes for all reversals

## Conclusion

This transaction reversal implementation provides a robust, auditable way to handle transaction reversals while maintaining data integrity and supporting financial reconciliation processes.
