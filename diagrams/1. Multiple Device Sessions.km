{"root": {"data": {"id": "daep3r47e200", "created": *************, "text": "Mobile banking"}, "children": [{"data": {"id": "daep4gkuwl40", "created": *************, "text": "Device 2"}, "children": []}, {"data": {"id": "daep4b7f9a00", "created": *************, "text": "Device 1"}, "children": []}, {"data": {"id": "daep5dyrb6w0", "created": *************, "text": "1. Device ID + Session access token\n2. Multi session flag on user details with api to turn on and off\n3. Note to add token auto refresh on usage\n4. ", "layout_mind_offset": {"x": 316.*************, "y": 235.**************}}, "children": []}]}, "template": "default", "theme": "fresh-blue", "version": "1.4.43"}