# Dynamic Forms MCP Server - Complete Guide

## Overview

The Dynamic Forms MCP Server is a sophisticated Model Context Protocol server designed for managing dynamic API routes, forms, and plugins in a banking/financial services platform. It provides AI assistants with powerful tools to create, manage, and optimize banking workflows.

## Server Information

- **Name**: Dynamic Forms MCP Server
- **Version**: 1.0.0
- **Author**: ServiceManager Team
- **License**: MIT
- **Protocol**: MCP 2024-11-05
- **Default Port**: 8080
- **Host**: localhost

## Core Capabilities

### 🔧 **Resources (5 types)**
Resources provide read-only access to system data:

1. **Dynamic Routes** (`mcp://dynamic-forms/routes`)
   - All API routes with metadata
   - Filtering by method, category, enabled status

2. **Dynamic Forms** (`mcp://dynamic-forms/forms`)
   - Form schemas with validation rules
   - Filtering by HTTP method, required status

3. **Dynamic Plugins** (`mcp://dynamic-forms/plugins`)
   - Business logic plugins/processes
   - Filtering by category, type

4. **Entity Connections** (`mcp://dynamic-forms/connections`)
   - Relationships between routes, forms, and plugins
   - Route-form links, route-plugin links, plugin chains

5. **System Statistics** (`mcp://dynamic-forms/stats`)
   - Usage analytics and system metrics
   - Counts by category, method, type

### 🛠️ **Tools (15 tools)**
Tools perform CRUD operations and system management:

#### Route Management (4 tools)
- `create_route` - Create new API routes
- `update_route` - Modify existing routes
- `delete_route` - Remove routes
- `toggle_route` - Enable/disable routes

#### Form Management (4 tools)
- `create_form` - Create form schemas with validation
- `update_form` - Modify form definitions
- `delete_form` - Remove forms
- `validate_form_data` - Test data against form validation

#### Plugin Management (4 tools)
- `create_plugin` - Create business logic plugins
- `update_plugin` - Modify plugin code and metadata
- `delete_plugin` - Remove plugins
- `test_plugin` - Execute plugin with test parameters

#### Entity Linking (3 tools)
- `link_route_form` - Connect routes to forms
- `link_route_plugin` - Connect routes to plugins
- `unlink_route_plugin` - Disconnect route-plugin links
- `get_entity_connections` - View all connections for an entity

### 💡 **Prompts (10 AI assistants)**
Intelligent prompts for guided development:

1. **route_designer** - Design optimal API routes
2. **form_builder** - Generate comprehensive form schemas
3. **plugin_generator** - Create business logic plugins
4. **workflow_creator** - Build end-to-end workflows
5. **route_validator** - Validate and improve routes
6. **form_optimizer** - Optimize form UX and performance
7. **plugin_tester** - Generate comprehensive test suites
8. **api_documenter** - Create API documentation
9. **integration_helper** - Assist with component integration
10. **troubleshooter** - Diagnose and fix system issues

## Banking-Specific Features

### Security & Compliance
- PII handling and validation
- KYC/AML compliance checks
- Audit trail generation
- Multi-currency support
- Transaction integrity validation

### Data Validation
- Banking-specific field types (account numbers, amounts)
- Regulatory compliance validation
- Fraud prevention measures
- Input sanitization and security

### Integration Patterns
- T24 core banking system integration
- External API connectivity
- Real-time processing capabilities
- Error handling and rollback strategies

## Connection Methods

### For LM Studio
```json
{
  "mcpServers": {
    "dynamic-forms": {
      "command": "bash",
      "args": ["scripts/start_mcp_for_lm_studio.sh"],
      "cwd": "/path/to/service_manager",
      "env": {
        "MIX_ENV": "dev"
      }
    }
  }
}
```

### For Direct Testing
```bash
# Start the MCP server
mix mcp start

# Start stdio server for MCP clients
mix mcp stdio

# Check server status
mix mcp status

# List resources
mix mcp resources

# List tools
mix mcp tools

# List prompts
mix mcp prompts
```

### Interactive Explorer
```bash
# Run the interactive explorer
elixir scripts/mcp_explorer.exs
```

## Example Usage Scenarios

### 1. Creating a New Banking API Endpoint

1. **Design the Route** using `route_designer` prompt:
   ```json
   {
     "purpose": "Customer account balance inquiry",
     "data_type": "account information",
     "http_method": "GET"
   }
   ```

2. **Create the Route** using `create_route` tool:
   ```json
   {
     "name": "account_balance",
     "method": "GET",
     "path": "/api/v1/accounts/{account_id}/balance",
     "category": "Account Services",
     "description": "Retrieve customer account balance"
   }
   ```

3. **Generate Form Schema** using `form_builder` prompt:
   ```json
   {
     "api_purpose": "Account balance validation",
     "data_fields": "account_id, customer_id",
     "validation_level": "strict"
   }
   ```

4. **Create Business Logic** using `plugin_generator` prompt:
   ```json
   {
     "functionality": "Validate account access and retrieve balance",
     "input_data": "account_id, customer_id",
     "external_apis": "T24 core banking"
   }
   ```

### 2. System Analysis and Optimization

1. **Get System Statistics**:
   - Read `mcp://dynamic-forms/stats` resource
   - Analyze route usage patterns
   - Identify optimization opportunities

2. **Validate Existing Routes**:
   - Use `route_validator` prompt for security review
   - Use `form_optimizer` for UX improvements
   - Use `troubleshooter` for issue diagnosis

### 3. Integration and Documentation

1. **Document APIs** using `api_documenter` prompt
2. **Create Integration Plans** using `integration_helper` prompt
3. **Generate Test Suites** using `plugin_tester` prompt

## Error Handling

The server provides comprehensive error handling with specific error codes:

- `-32700`: Parse error (invalid JSON)
- `-32600`: Invalid Request
- `-32601`: Method not found
- `-32602`: Invalid params
- `-32000`: Connection closed

## Monitoring and Logging

The server includes extensive logging for:
- Startup and initialization stages
- Request/response tracking
- Error diagnosis
- Performance monitoring
- Security events

## Configuration

```elixir
config :service_manager, :mcp_server,
  enabled: true,
  port: 8080,
  host: "localhost",
  auth_required: false,
  max_connections: 100
```

## Support and Troubleshooting

1. **Check server status**: `mix mcp status`
2. **View logs**: Application logs show detailed startup and operation info
3. **Test components**: Use the interactive explorer script
4. **Verify configuration**: Ensure MCP server is enabled
5. **Database connectivity**: Verify database is accessible

For detailed troubleshooting, the server provides comprehensive diagnostic information and suggested solutions for common issues.
