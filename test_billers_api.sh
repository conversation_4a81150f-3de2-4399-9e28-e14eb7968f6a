#!/bin/bash

# Billers API Test Script
# This script provides quick curl commands to test all biller endpoints

# Configuration
BASE_URL="http://localhost:4000"
AUTH_TOKEN="YOUR_BEARER_TOKEN_HERE"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to make API calls with proper formatting
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: ${description}${NC}"
    echo -e "${YELLOW}${method} ${endpoint}${NC}"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X ${method} \
            -H "Authorization: Bearer ${AUTH_TOKEN}" \
            -H "Content-Type: application/json" \
            -d "${data}" \
            "${BASE_URL}${endpoint}")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X ${method} \
            -H "Authorization: Bearer ${AUTH_TOKEN}" \
            -H "Content-Type: application/json" \
            "${BASE_URL}${endpoint}")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_CODE:")
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo -e "${GREEN}✓ Success (${http_code})${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    else
        echo -e "${RED}✗ Failed (${http_code})${NC}"
        echo "$body"
    fi
    
    echo -e "\n${YELLOW}---${NC}\n"
}

echo -e "${GREEN}Billers API Test Suite${NC}"
echo -e "${GREEN}=====================${NC}\n"

# Check if jq is available for JSON formatting
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}Note: Install 'jq' for better JSON formatting${NC}\n"
fi

# Test 1: BWB Account Details
api_call "GET" "/api/billers/account-details/bwb_postpaid/********" "" "BWB Postpaid Account Details"

# Test 2: LWB Account Details
api_call "GET" "/api/billers/account-details/lwb_postpaid/********" "" "LWB Postpaid Account Details"

# Test 3: MASM Account Details with Account Type
api_call "GET" "/api/billers/account-details/masm/********?account_type=M" "" "MASM Account Details"

# Test 4: BWB Payment
payment_data='{
  "biller_type": "bwb_postpaid",
  "account_number": "********",
  "amount": "500.00",
  "currency": "MWK",
  "credit_account": "**********",
  "credit_account_type": "account",
  "debit_account": "**********",
  "debit_account_type": "wallet",
  "description": "Water bill payment"
}'
api_call "POST" "/api/billers/payments" "$payment_data" "BWB Payment Transaction"

# Test 5: Register General Invoice
api_call "GET" "/api/billers/invoices/register_general/REG123456" "" "Register General Invoice"

# Test 6: Invoice Confirmation
invoice_confirm_data='{
  "biller_type": "register_general",
  "account_number": "REG123456",
  "amount": "1000.00",
  "currency": "MWK",
  "credit_account": "**********",
  "credit_account_type": "account",
  "debit_account": "**********",
  "debit_account_type": "wallet",
  "invoice_reference": "INV789012345"
}'
api_call "POST" "/api/billers/invoices/confirm" "$invoice_confirm_data" "Invoice Confirmation"

# Test 7: TNM Bundle Details
api_call "GET" "/api/billers/bundles/239678" "" "TNM Bundle Details"

# Test 8: Bundle Purchase Confirmation
bundle_data='{
  "bundle_id": "239678",
  "account_number": "**********",
  "amount": "2500.00",
  "currency": "MWK",
  "credit_account": "TNM_BUNDLES",
  "credit_account_type": "account",
  "debit_account": "**********",
  "debit_account_type": "wallet"
}'
api_call "POST" "/api/billers/bundles/confirm" "$bundle_data" "Bundle Purchase Confirmation"

# Test 9: Airtel Validation
api_call "GET" "/api/billers/validate/**********" "" "Airtel Money Validation"

# Test 10: List All Transactions
api_call "GET" "/api/billers/transactions?limit=5" "" "List Recent Transactions"

# Test 11: List Transactions by Biller Type
api_call "GET" "/api/billers/transactions?biller_type=bwb_postpaid&limit=3" "" "List BWB Transactions"

# Test 12: List Transactions by Status
api_call "GET" "/api/billers/transactions?status=completed&limit=3" "" "List Completed Transactions"

echo -e "${GREEN}Wallet Scope Tests${NC}"
echo -e "${GREEN}==================${NC}\n"

# Test 13: Wallet Scope - Account Details
api_call "GET" "/api/wallets/billers/account-details/lwb_postpaid/********" "" "Wallet - LWB Account Details"

# Test 14: Wallet Scope - Payment
wallet_payment_data='{
  "biller_type": "srwb_postpaid",
  "account_number": "********",
  "amount": "300.00",
  "currency": "MWK",
  "credit_account": "**********",
  "credit_account_type": "account",
  "debit_account": "**********",
  "debit_account_type": "wallet"
}'
api_call "POST" "/api/wallets/billers/payments" "$wallet_payment_data" "Wallet - SRWB Payment"

# Test 15: Wallet Scope - Validation
api_call "GET" "/api/wallets/billers/validate/**********" "" "Wallet - Airtel Validation"

# Test 16: Wallet Scope - Transactions
api_call "GET" "/api/wallets/billers/transactions?limit=3" "" "Wallet - List Transactions"

echo -e "${GREEN}Test Suite Completed!${NC}"
echo -e "${YELLOW}Notes:${NC}"
echo -e "1. Update AUTH_TOKEN variable with a valid bearer token"
echo -e "2. Ensure the service is running on ${BASE_URL}"
echo -e "3. Account numbers used are for testing - replace with valid ones"
echo -e "4. Some tests may fail if dependencies (like biller configurations) are not properly set up"