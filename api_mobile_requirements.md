# API Requirements for Banking App

This document outlines the API requirements for the banking app based on the functionality in the controllers.

## Table of Contents
1. [Profile API](#1-profile-api)
2. [Accounts API](#2-accounts-api)
3. [Cards API](#3-cards-api)
4. [Merchants API](#4-merchants-api)
5. [Biometrics API](#5-biometrics-api)
6. [Beneficiaries API](#6-beneficiaries-api)
7. [Bills API](#7-bills-api)
8. [Transfer API](#8-transfer-api)
9. [Notes for Backend Team](#notes-for-backend-team)

## 1. Profile API

### 1.1 GET /api/profile

Purpose: Retrieve user profile data

Response structure:
```json
{
  "data": {
    "user": {
      "fullname": "string",
      "email": "string",
      "phone": "string"
    }
  }
}
```

### 1.2 PUT /api/profile

Purpose: Update user profile data

Request parameters:
```json
{
  "fullname": "string",
  "email": "string",
  "phone": "string"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "user": {
    "fullname": "string",
    "email": "string",
    "phone": "string"
  }
}
```

## 2. Accounts API

### 2.1 GET /api/accounts

Purpose: Retrieve user's bank accounts

Response structure:
```json
{
  "accounts": [
    {
      "tag": "string",
      "name": "string",
      "number": "string",
      "type": "string",
      "currency": "string",
      "balance": "number"
    }
  ]
}
```

## 3. Cards API

### 3.1 GET /api/cards

Purpose: Retrieve user's debit cards

Response structure:
```json
{
  "cards": [
    {
      "cardNumber": "string",
      "cardHolderName": "string",
      "expiryDate": "string",
      "balance": "number"
    }
  ]
}
```

### 3.2 GET /api/cards/{cardId}

Purpose: Retrieve details of a specific card

Response structure:
```json
{
  "card": {
    "id": "string",
    "cardNumber": "string",
    "cardHolderName": "string",
    "expiryDate": "string",
    "cvv": "string",
    "isLocked": "boolean",
    "isFrozen": "boolean",
    "isBlocked": "boolean",
    "spendingLimit": "number",
    "withdrawalLimit": "number"
  }
}
```

### 3.3 PUT /api/cards/{cardId}/lock

Purpose: Lock or unlock a card

Request parameters:
```json
{
  "isLocked": "boolean"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string"
}
```

### 3.4 PUT /api/cards/{cardId}/freeze

Purpose: Temporarily freeze a card

Response structure:
```json
{
  "success": "boolean",
  "message": "string"
}
```

### 3.5 PUT /api/cards/{cardId}/block

Purpose: Permanently block a card

Response structure:
```json
{
  "success": "boolean",
  "message": "string"
}
```

### 3.6 PUT /api/cards/{cardId}/limits

Purpose: Adjust card spending and withdrawal limits

Request parameters:
```json
{
  "spendingLimit": "number",
  "withdrawalLimit": "number"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "updatedLimits": {
    "spendingLimit": "number",
    "withdrawalLimit": "number"
  }
}
```

## 4. Merchants API

### 4.1 GET /api/merchants

Purpose: Retrieve list of merchants

Response structure:
```json
{
  "merchants": [
    {
      "name": "string",
      "image": "string",
      "category": "string"
    }
  ]
}
```

### 4.2 POST /api/merchants/pay

Purpose: Process payment to a merchant

Request parameters:
```json
{
  "merchantName": "string",
  "amount": "number"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "transactionId": "string"
}
```

## 5. Biometrics API

### 5.1 POST /api/biometrics/enable

Purpose: Enable biometric authentication for the user

Request parameters:
```json
{
  "userId": "string",
  "biometricData": "string"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string"
}
```

### 5.2 GET /api/biometrics/status

Purpose: Check if biometric authentication is enabled for the user

Response structure:
```json
{
  "isEnabled": "boolean",
  "biometricTypes": ["string"]
}
```

### 5.3 DELETE /api/biometrics

Purpose: Disable biometric authentication for the user

Response structure:
```json
{
  "success": "boolean",
  "message": "string"
}
```

## 6. Beneficiaries API

### 6.1 GET /api/beneficiaries

Purpose: Retrieve list of beneficiaries

Response structure:
```json
{
  "beneficiaries": [
    {
      "id": "string",
      "name": "string",
      "account": "string",
      "bank": "string",
      "nickName": "string",
      "branch": "string",
      "image": "string"
    }
  ]
}
```

### 6.2 POST /api/beneficiaries

Purpose: Add a new beneficiary

Request parameters:
```json
{
  "name": "string",
  "account": "string",
  "bank": "string",
  "nickName": "string",
  "branch": "string",
  "image": "string"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "beneficiary": {
    "id": "string",
    "name": "string",
    "account": "string",
    "bank": "string",
    "nickName": "string",
    "branch": "string",
    "image": "string"
  }
}
```

### 6.3 PUT /api/beneficiaries/{id}

Purpose: Update an existing beneficiary

Request parameters:
```json
{
  "name": "string",
  "account": "string",
  "bank": "string",
  "nickName": "string",
  "branch": "string",
  "image": "string"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "beneficiary": {
    "id": "string",
    "name": "string",
    "account": "string",
    "bank": "string",
    "nickName": "string",
    "branch": "string",
    "image": "string"
  }
}
```

### 6.4 DELETE /api/beneficiaries/{id}

Purpose: Remove a beneficiary

Response structure:
```json
{
  "success": "boolean",
  "message": "string"
}
```

### 6.5 GET /api/beneficiaries/search

Purpose: Search beneficiaries

Query parameters:
- query: string (name or account number to search for)

Response structure:
```json
{
  "beneficiaries": [
    {
      "id": "string",
      "name": "string",
      "account": "string",
      "bank": "string",
      "nickName": "string",
      "branch": "string",
      "image": "string"
    }
  ]
}
```

## 7. Bills API

### 7.1 POST /api/bills/verify

Purpose: Verify bill number and retrieve bill details

Request parameters:
```json
{
  "billNumber": "string",
  "billType": "string"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "billDetails": {
    "name": "string",
    "amount": "number"
  }
}
```

### 7.2 POST /api/bills/pay

Purpose: Process bill payment

Request parameters:
```json
{
  "billNumber": "string",
  "billType": "string",
  "amount": "number",
  "accountId": "string"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "transactionId": "string",
  "fee": "number",
  "totalAmount": "number"
}
```

### 7.3 GET /api/bills/types

Purpose: Retrieve available bill types

Response structure:
```json
{
  "billTypes": [
    {
      "id": "string",
      "name": "string",
      "description": "string"
    }
  ]
}
```

### 7.4 GET /api/bills/fees

Purpose: Retrieve fee structure for bill payments

Query parameters:
- amount: number
- billType: string

Response structure:
```json
{
  "fee": "number",
  "limitMin": "number",
  "limitMax": "number",
  "percentCharge": "number",
  "fixedCharge": "number",
  "rate": "number",
  "totalFee": "number",
  "baseCurrency": "string"
}
```

## 8. Transfer API

### 8.1 POST /api/transfers

Purpose: Process a transfer between accounts

Request parameters:
```json
{
  "fromAccountId": "string",
  "toAccountId": "string",
  "amount": "number",
  "currency": "string",
  "description": "string",
  "transferType": "string",
  "beneficiaryId": "string",
  "bankCode": "string",
  "scheduleDate": "string",
  "recurringFrequency": "string"
}
```

Response structure:
```json
{
  "success": "boolean",
  "message": "string",
  "transactionId": "string",
  "fee": "number",
  "exchangeRate": "number",
  "totalAmount": "number"
}
```

### 8.2 GET /api/transfers/fees

Purpose: Retrieve fee structure for transfers

Query parameters:
- amount: number
- transferType: string
- toBank: string (optional, for external transfers)

Response structure:
```json
{
  "percentCharge": "number",
  "fixedCharge": "number",
  "rate": "number",
  "limitMin": "number",
  "limitMax": "number",
  "totalFee": "number"
}
```

### 8.3 GET /api/transfers/institutions

Purpose: Retrieve list of supported financial institutions for transfers

Response structure:
```json
{
  "institutions": [
    {
      "id": "string",
      "name": "string",
      "code": "string",
      "logo": "string"
    }
  ]
}
```

### 8.4 POST /api/transfers/validate

Purpose: Validate transfer details before processing

Request parameters:
```json
{
  "fromAccountId": "string",
  "toAccountId": "string",
  "amount": "number",
  "transferType": "string"
}
```

Response structure:
```json
{
  "isValid": "boolean",
  "message": "string",
  "fee": "number",
  "exchangeRate": "number",
  "totalAmount": "number"
}
```

## Notes for Backend Team

1. Implement proper authentication and authorization for all endpoints.
2. Ensure data validation and sanitization for all input parameters.
3. Implement error handling and provide meaningful error messages in the API responses.
4. Consider implementing pagination for endpoints that may return large datasets (e.g., merchants list, beneficiaries list, transfer history).
5. Ensure that sensitive data (like full account numbers) are properly masked or encrypted in responses.
6. Implement rate limiting to prevent abuse of the API.
7. Consider versioning the API to allow for future updates without breaking existing client implementations.
8. Implement logging for all API calls for debugging and auditing purposes.
9. Ensure that all endpoints follow RESTful principles and use appropriate HTTP methods (GET, POST, PUT, DELETE).
10. Implement HTTPS for all API communications to ensure data security in transit.
11. For transfer operations, ensure proper integration with various banking systems (internal, external, wallet) and implement necessary error handling for cases where transfers may fail.
12. Implement proper transaction management for transfers to ensure consistency in case of partial failures.
13. Consider implementing webhooks or push notifications for real-time transfer status updates.
14. Implement proper audit trails for all transfer-related actions.
15. For scheduled and recurring transfers, ensure proper handling of future-dated transactions and implement a robust scheduling system.
16. For profile and biometric-related operations, ensure proper security measures are in place to protect sensitive user information.
17. Implement proper error handling for cases where biometric authentication might not be available or fails.
18. Consider implementing a mechanism to periodically refresh the user's profile data to ensure the app has the most up-to-date information.
