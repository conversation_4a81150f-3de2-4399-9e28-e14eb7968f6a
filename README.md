# Service Manager

A Phoenix-based financial service management platform handling transactions, wallets, and banking operations.

## Development Setup

### 1. Elixir & Erlang Requirements
This application requires:
- Elixir 1.14+ (as specified in `mix.exs`)
- Erlang/OTP 25+

**Recommended Installation using asdf-vm:**
```bash
# Install Erlang
asdf plugin-add erlang
asdf install erlang ********

# Install Elixir 
asdf plugin-add elixir
asdf install elixir 1.14.5

# Verify installations
elixir -v
```

### 2. System Dependencies
- PostgreSQL 13+
- Node.js 18+ (for assets)
- Inotify-tools (for file system watchers)

### 3. Application Setup
```bash
# Clone repository
git clone https://github.com/your-org/service_manager.git
cd service_manager
git checkout t24-stream-v2

# Install mix dependencies
mix deps.get

# Set up database (skip this step, database is already setup if you are connecting to cloud)
mix ecto.setup

# Install JS dependencies and build assets (skip this step, database is already setup if you are connecting to cloud)
mix assets.setup
mix assets.build

# Start Phoenix server (Start in interactive mode)
iex -S mix phx.server
```

The application will be available at `http://localhost:4000`

### 4. Initial Configuration
1. Database: Configure credentials in `config/dev.exs`
2. SMTP: Set up mailer settings in `config/config.exs`
3. Third-party APIs: Add T24 banking system credentials

### 5. Running Tests
```bash
MIX_ENV=test mix ecto.create
mix test
```

Would you like me to add specific sections about the API structure, transaction handling, or wallet management features next?