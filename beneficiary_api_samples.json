{"create_beneficiary": {"method": "POST", "endpoint": "/api/beneficiaries", "request": {"name": "<PERSON>", "account_number": "**********", "bank_code": "ABCDEFGH", "beneficiary_type": "mobile_banking", "currency": "USD", "description": "Friend's account for transfers"}}, "create_beneficiary_other_type": {"method": "POST", "endpoint": "/api/beneficiaries", "request": {"name": "<PERSON>", "account_number": "**********", "bank_code": "XYZBANK", "beneficiary_type": "other", "currency": "EUR", "description": "External service provider account"}}, "get_beneficiary": {"method": "GET", "endpoint": "/api/beneficiaries/:id", "params": {"id": "123e4567-e89b-12d3-a456-************"}}, "list_beneficiaries": {"method": "GET", "endpoint": "/api/beneficiaries", "query_params": {"page": 1, "limit": 10}}, "update_beneficiary": {"method": "POST", "endpoint": "/api/beneficiaries/:id", "params": {"id": "123e4567-e89b-12d3-a456-************"}, "request": {"name": "<PERSON>", "description": "Updated description"}}, "remove_beneficiary": {"method": "POST", "endpoint": "/api/beneficiaries/:id/remove", "params": {"id": "123e4567-e89b-12d3-a456-************"}}, "validate_beneficiary": {"method": "POST", "endpoint": "/api/beneficiaries/validate", "request": {"account_number": "**********", "bank_code": "ABCDEFGH"}}, "get_beneficiary_status": {"method": "POST", "endpoint": "/api/beneficiaries/status", "request": {"id": "123e4567-e89b-12d3-a456-************"}}, "set_default_beneficiary": {"method": "POST", "endpoint": "/api/beneficiaries/:id/set_default", "params": {"id": "123e4567-e89b-12d3-a456-************"}}, "search_beneficiaries": {"method": "POST", "endpoint": "/api/beneficiaries/search", "request": {"name": "<PERSON>", "account_number": "1234", "bank_code": "ABC", "currency": "USD", "status": "active", "page": 1, "limit": 10}}}